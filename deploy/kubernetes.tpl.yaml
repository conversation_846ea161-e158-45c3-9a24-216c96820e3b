---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: yptt-personalized-api
  labels:
    app: yptt-personalized-api
spec:
  selector:
    matchLabels:
      app: yptt-personalized-api
  template:
    metadata:
      labels:
        app: yptt-personalized-api
    spec:
      imagePullSecrets:
      - name: '{{ .env.IMAGE_PULL_SECRET }}'
      volumes:
        - name: skywalking
          emptyDir: {}
        - name: skywalking-agent-starter
          configMap:
            name: yptt-personalized-api-env
            defaultMode: 420
            items:
            - key: docker-entrypoint.sh
              path: docker-entrypoint.sh
      initContainers:
        - name: skywalking-agent-getter
          image: 'apache/skywalking-java-agent:9.1.0-java8'
          command:
          - /bin/sh
          args:
          - '-c'
          - cp -R /skywalking/agent /agent/
          resources: {}
          volumeMounts:
          - name: skywalking
            mountPath: /agent
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      containers:
      - name: yptt-personalized-api
        image: swr.cn-south-1.myhuaweicloud.com/cloud-cqcyit/yptt/yptt-personalized-api:latest
        imagePullPolicy: Always
        volumeMounts:
        - name: skywalking
          mountPath: /skywalking
        - name: skywalking-agent-starter
          mountPath: /workspace/docker-entrypoint.sh
          subPath: docker-entrypoint.sh
        command: [ /bin/sh, docker-entrypoint.sh ]
        env:
        - name: LCC_SERVICE_NAME
          value: yptt-personalized-api
        - name: DRONE_COMMIT
          value: '{{ .env.DRONE_COMMIT }}'
        - name: DRONE_BUILD_NUMBER
          value: '{{ .env.DRONE_BUILD_NUMBER }}'
        envFrom:
        - configMapRef:
            name: yptt-personalized-api-env
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 20001
            scheme: HTTP
          initialDelaySeconds: 20
          timeoutSeconds: 5
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 20001
            scheme: HTTP
          initialDelaySeconds: 20
          timeoutSeconds: 2
          periodSeconds: 5
          successThreshold: 5
          failureThreshold: 1
