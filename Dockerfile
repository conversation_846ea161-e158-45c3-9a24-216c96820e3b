FROM kit101z/amazoncorretto:8u402-alpine3.19 as builder
WORKDIR /build
ARG JAR_FILE=target/yptt-personalized-api.jar
COPY ${JAR_FILE} app.jar
RUN java -Djarmode=layertools -jar app.jar extract && rm app.jar
#RUN apk add --no-cache font-dejavu fontconfig

FROM kit101z/amazoncorretto:8u402-alpine3.19
LABEL maintainer="<EMAIL>"
ENV TZ=Asia/Shanghai JAVA_OPTS="-Xms128m -Xmx512m -Djava.security.egd=file:/dev/./urandom"
WORKDIR /workspace
EXPOSE 20001
#RUN apk add --no-cache font-dejavu fontconfig

COPY --from=builder /build/spring-boot-loader/ ./
COPY --from=builder /build/dependencies/ ./
COPY --from=builder /build/snapshot-dependencies/ ./
COPY --from=builder /build/application/ ./

ENTRYPOINT java $JAVA_OPTS org.springframework.boot.loader.JarLauncher