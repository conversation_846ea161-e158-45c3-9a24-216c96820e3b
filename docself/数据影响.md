# YPTT工具：字段级数据流与连锁反应详细解析

本文档旨在提供一份关于YPTT工具自动化逻辑的“底层说明书”。我们将聚焦于“如果A发生变化，那么B、C、D会如何变化”这一核心问题，详细拆解数据在模块间的流动与计算过程。

我们将整个流程分为三大阶段：
1.  **静态基础数据的建立** (项目初始设定)
2.  **核心动态流** (由交付进度驱动的自动化)
3.  **财务核销的连锁反应** (最终的财务闭环)

---

## 阶段一：静态基础数据的建立与初次校验

在项目开始时，用户需要手动输入项目的“静态”基本盘。此时系统会进行第一次，也是最基础的一次数据校验。

| 操作模块 | 用户动作 (Action) | 连锁反应 (Reaction) & 数据流 | 最终产出 / 监控点 |
| :--- | :--- | :--- | :--- |
| **Y1 & Y2** | 1. 在 `Y1` 中输入 `Y119 (站点价值)`。<br>2. 在 `Y2` 中输入 `Y215 (PO 价值)`。 | 系统会**立刻**读取这两个字段的值，并进行计算：<br> `Y119` - `Y215` -> **写入** -> `Y216 (PO 缺口)` | **`Y216 (PO 缺口)`** <br>如果该值不为0，将触发 **A-3 告警**。这是系统在提醒：“内部估值与外部合同价不符，请核实！” |

---

## 阶段二：核心动态流：交付进度的连锁反应

这是整个工具的灵魂所在。**`Y3 (站点交付信息)` 模块是唯一的“手动油门”**，一旦踩下（即更新日期），就会驱动整个收入和成本两条线的自动化计算。

我们将以一个典型的**中期交付节点**为例，来展示完整的连锁反应。

### **核心触发器：项目经理更新了第二个交付节点的完成日期**

#### **用户动作 (The Single Trigger):**

项目经理在 **`Y3`** 模块中，为某个站点条目填写或更新了 **`Y320 (第二次可结算时间)`** 的日期。

---

#### **连锁反应 1：收入与产值流 (Income & Productivity Stream)**

*当 `Y320` 的日期被填入后，系统会瞬间执行以下一系列自动化操作：*

1.  **数据感知与传递 (Y3 -> Y5):**
    *   系统监测到 `Y320` 字段发生了变化。
    *   **数据流:** `Y320` 的日期值被**自动复制并写入**到 **`Y5`** 模块的 **`Y507 (第二次可结算时间)`** 字段。

2.  **查找与计算 (Y2 -> Y5):**
    *   `Y507` 的更新触发了金额计算。
    *   系统**自动查找**并**读取**以下两个值：
        *   **`Y2`** 模块中的 **`Y215 (PO 价值)`** (总合同金额)。
        *   **`Y2`** 模块中的 **`Y219 (结算里程碑二比例)`** (第二个节点的结算百分比)。
    *   **数据流:** `Y215` * `Y219` -> **计算并写入** -> **`Y5`** 模块的 **`Y509 (第二次可结算金额)`**。

3.  **实时汇总 (Y5 内部):**
    *   `Y509` 的生成会立刻触发汇总金额的更新。
    *   **数据流:** `Y506(第一次金额)` + `Y509(新生成的第二次金额)` + ... -> **计算并更新** -> **`Y5`** 模块的 **`Y516 (可结算总金额)`**。

4.  **产值镜像同步 (Y5 -> Y6):**
    *   系统将 `Y5` 的结算结果**完全镜像**到 `Y6` 产值模块。
    *   **数据流:**
        *   `Y507` 的日期值 -> **复制并写入** -> **`Y6`** 模块的 **`Y603 (第二次产值申报日期)`**。
        *   `Y509` 的金额值 -> **复制并写入** -> **`Y6`** 模块的 **`Y604 (第二次产值申报金额)`**。
    *   这两个新值的写入，会进一步触发 `Y6` 内部的汇总更新：
        *   `Y602` + `Y604` + ... -> **计算并更新** -> **`Y609 (产值申报总金额)`**。

5.  **最终产出 / 监控点 (WIP 的动态变化):**
    *   `Y516 (可结算总金额)` 的每一次更新，都会**立刻**影响到 `Y9` 模块中的一个关键监控字段。
    *   系统会**实时重新计算**：`Y516` - `Y922 (发票总金额)` -> **更新** -> **`Y923 (发票总缺口)`**。
    *   **解读：** 每当你的团队完成一项工作，系统的自动化会让 `Y923` (你的应收未收账款) 的潜在值增加，清晰地量化了你的劳动成果。

---

#### **连锁反应 2：成本流 (Cost Stream)**

*如果该任务外包给了分包商，上述同一个 `Y3` 的更新动作，还会并行触发成本侧的连锁反应。*

1.  **数据感知与传递 (Y3 -> Y7):**
    *   系统监测到与该节点关联的 **`Y324 (分包商满足第二次结算 milestone 的日期)`** 被更新。
    *   **数据流:** `Y324` 的日期值 -> **自动复制并写入** -> **`Y7`** 模块的 **`Y704 (分包商第二次可结算日期)`**。

2.  **查找与计算 (Y4 -> Y7):**
    *   `Y704` 的更新触发了应付金额计算。
    *   系统**自动查找**并**读取**：
        *   **`Y4`** 模块中的 **`Y411 (分包商-PO 金额)`**。
        *   **`Y4`** 模块中的 **`Y413 (分包结算里程碑二比例)`**。
    *   **数据流:** `Y411` * `Y413` -> **计算并写入** -> **`Y7`** 模块的 **`Y706 (分包商第二次可结算金额)`**。

3.  **实时汇总 (Y7 内部):**
    *   `Y706` 的生成会立刻触发应付总账的更新。
    *   **数据流:** `Y703` + `Y706` + ... -> **计算并更新** -> **`Y7`** 模块的 **`Y713 (分包商可结算总金额)`**。

4.  **最终产出 / 监控点 (应付未付款的动态变化):**
    *   `Y713 (分包商可结算总金额)` 的每一次更新，都会**立刻**影响到 `Y8` 模块中的关键监控字段。
    *   系统会**实时重新计算**：`Y713` - `Y814 (分包商支付总金额)` -> **更新** -> **`Y815 (分包商未支付总金额)`**。
    *   **解读：** 当分包商完成工作，系统会自动增加 `Y815` (你的应付未付账款)，提醒你需要支付的款项增加了。

---

## 阶段三：财务核销的连锁反应

当系统自动算好了“应收”和“应付”后，财务人员的介入将完成最后的闭环。

### **场景一：财务向客户开具发票**

*   **用户动作 (Action):** 财务人员在 **`Y9`** 模块中，输入了第二笔发票的金额 **`Y908 (第二次结算发票金额)`**。
*   **连锁反应 (Reaction):**
    1.  系统**立刻**将新的发票金额累加到总额中： `Y903` + `Y908` + ... -> **更新** -> **`Y922 (发票总金额)`**。
    2.  `Y922` 的变化，会**瞬间**影响到核心监控指标：`Y516 (可结算总金额)` - `Y922 (新的发票总金额)` -> **更新** -> **`Y923 (发票总缺口)`**。
*   **最终产出 / 解读:** 每当财务开出一笔发票，`Y923` (WIP) 的金额就会相应减少。当它减到0时，意味着所有已完成工作的发票都已开出。

### **场景二：财务向分包商支付款项**

*   **用户动作 (Action):** 财务人员在 **`Y8`** 模块中，输入了第二笔付款的金额 **`Y805 (分包商第二次支付金额)`**。
*   **连锁反应 (Reaction):**
    1.  系统**立刻**将新的支付金额累加到总额中： `Y802` + `Y805` + ... -> **更新** -> **`Y814 (分包商支付总金额)`**。
    2.  `Y814` 的变化，会**瞬间**影响到核心监控指标：`Y713 (分包商可结算总金额)` - `Y814 (新的支付总金额)` -> **更新** -> **`Y815 (分包商未支付总金额)`**。
*   **最终产出 / 解读:** 每当财务支付一笔款项，`Y815` (应付未付款) 的金额就会相应减少。当它减到0时，意味着所有分包商的应付款项都已结清。

---

## 最终状态的判定：“关闭”的连锁条件

**`Y103 (站点状态)`** 是所有连锁反应的终点。它不是由用户直接修改的，而是系统基于以下**所有**最终监控指标的**实时逻辑判断**：

*   系统**持续不断地**检查以下三个“缺口”字段：
    1.  `Y216 (PO 缺口)`
    2.  `Y923 (发票总缺口)`
    3.  `Y815 (分包商未支付总金额)`
*   **连锁条件:** **当且仅当**这三个字段的值**全部为0**时，系统会自动将 **`Y103`** 的状态从“未关闭”**翻转**为“关闭”。

这份详尽的地图描绘了从一个微小的日期更新，到整个项目财务状态变化的完整路径，希望能帮助您彻底理解工具背后的精密设计。