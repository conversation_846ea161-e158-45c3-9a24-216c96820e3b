# YPTT接口详细代码逻辑分析

## 前言

这份文档用大白话详细解释YPTT系统中每个接口的具体代码逻辑，让你能够深入理解每个接口到底在做什么，以及它们之间的关联关系。

## 1. 数据管理模块 (DataMangeController)

### 1.1 数据导入接口

#### 1.1.1 上传数据表 - `/data-mange/import/upload-data-table`

**这个接口是干什么的？**
简单说就是用户上传一个Excel文件，系统把Excel里的数据导入到数据库中。

**具体代码逻辑：**

```java
@PostMapping("/import/upload-data-table")
public R<Object> importDataTable(YPTTBatchImportDTO param) {
    return dataMangeService.importDataTable(param);
}
```

**Service层的详细逻辑：**

1. **获取用户信息和文件**
   ```java
   PigUser user = SecurityUtils.getUser();  // 获取当前登录用户
   MultipartFile file = param.getFile();    // 获取上传的Excel文件
   String moduleType = param.getModuleType(); // 获取模块类型(Y1,Y2,Y3等)
   ```

2. **读取Excel文件**
   ```java
   List<Map<String, Object>> maps = read2Map(file);  // 把Excel转换成Map列表
   Assert.isTrue(maps.size() < 10000, "超过上传数据限制10000条!");  // 检查数据量
   ```

3. **数据预处理**
   ```java
   // 创建转换上下文，包含用户ID、参数、数据等
   Transformer.TransformContext context = new Transformer.TransformContext(
       user.getId(), param, maps, GlobalConstants.Import.IMPORT_DATA);
   ```

4. **数据转换和验证**
   ```java
   for (Map<String, Object> map : maps) {
       // 处理日期字段
       if (ArrayUtil.contains(datetime_fields, k)) {
           v = MetaDataUtil.dateStr2LocalDateTime(v.toString());
       }
       // 处理百分比字段
       else if (ArrayUtil.contains(percentage_fields, k)) {
           v = MetaDataUtil.percentageStr2BigDecimal(v.toString(), 4, false);
       }
       // 处理金额字段
       else if (ArrayUtil.contains(amount_fields, k)) {
           v = MetaDataUtil.numberStr2BigDecimal(v.toString());
       }
   }
   ```

5. **保存主视图数据**
   ```java
   Long mainDataId = saveMainViewData(maps, file, appId, mainViewGroupId, mainViewId);
   ```

6. **异步执行导入任务**
   ```java
   importTaskExecutor.execute(new ImportTask(0, new ProxyAuthenticateContext(user), 
       this, transformManager, dataPermissionsService, importMapper, 
       viewConfProperties, remoteFileService, mainDataId,
       ImportTaskEvent.builder().dto(param).mainDataId(mainDataId).build()));
   ```

**为什么要异步执行？**
因为导入大量数据很耗时，如果同步执行会让用户等很久，异步执行可以立即返回结果，后台慢慢处理。

#### 1.1.2 检查上传数据 - `/data-mange/import/check-upload-data`

**这个接口是干什么的？**
在真正导入数据之前，先检查一下Excel文件的数据格式是否正确，有没有错误。

**具体代码逻辑：**

1. **检查线程池状态**
   ```java
   BlockingQueue<Runnable> taskExecutorQueue = importTaskExecutor.getQueue();
   if (CollUtil.isNotEmpty(taskExecutorQueue) && 
       taskExecutorQueue.size() >= Math.min(properties.getQueueCapacity(), 24)) {
       return R.failed(Boolean.FALSE, YpttBizCode.IMPORT_TASK_FULL_ERROR);
   }
   ```
   **大白话：** 检查当前有没有太多导入任务在排队，如果队列满了就不让再提交新任务。

2. **读取和验证数据**
   ```java
   List<Map<String, Object>> maps = read2Map(param.getFile());
   for (Map<String, Object> map : maps) {
       ImportResultVO resultVO = transformManager.validate(context, index, map);
       // 检查部门信息
       Object department = map.get("Department");
       if (Objects.nonNull(department) && StrUtil.isNotBlank(department.toString())) {
           String deptName = department.toString();
           // 验证部门是否存在
       }
   }
   ```

3. **返回验证结果**
   每一行数据都会生成一个ImportResultVO，告诉你这行数据是否有问题。

#### 1.1.3 下载导入模板 - `/data-mange/import/download-template`

**这个接口是干什么的？**
下载Excel导入模板，用户可以按照模板格式填写数据。

**具体代码逻辑：**

```java
@SneakyThrows
public ResponseEntity<byte[]> downloadTemplate(String type) {
    // 检查模块类型是否合法
    Assert.isTrue(GlobalConstants.ALL_ITEM_REL.contains(type), 
        "非法的导入模板参数:{}", type);
    
    // 从classpath读取模板文件
    ClassPathResource resource = new ClassPathResource("import-template/" + type + ".xlsx");
    String fileName = URLEncoder.encode(GlobalConstants.ExcelUtil.IMPORT_TEMPLATE + type, 
        CharEncoding.UTF_8);
    byte[] fileBytes = FileUtil.readBytes(resource.getFile());
    
    // 设置HTTP响应头
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment", fileName + ".xlsx");
    
    return ResponseEntity.ok().headers(headers).body(fileBytes);
}
```

**大白话：** 根据模块类型(Y1、Y2、Y3等)，从项目资源文件夹里找到对应的Excel模板，然后下载给用户。

### 1.2 分包商外部成本管理

#### 1.2.1 新增外部成本 - `/data-mange/subcon/external-cost/add`

**这个接口是干什么的？**
当分包商有额外的成本支出时，通过这个接口记录这些成本。

**具体代码逻辑：**

```java
public Boolean addExternalCost(OperationInsertDTO dto) {
    // 获取外部成本金额
    Object externalCost = dto.getValue("ExternalCost");
    BigDecimal init = Objects.isNull(externalCost) ? 
        BigDecimal.ZERO : new BigDecimal(externalCost.toString());
    
    // 更新分包商PO的外部成本
    return updateSubconPoExternalCost(dto.getRootDataId(), init, dto.getDataId());
}
```

**updateSubconPoExternalCost方法的逻辑：**

```java
public Boolean updateSubconPoExternalCost(Long rootId, BigDecimal init, Long dataId) {
    // 查询该分包商PO下的所有外部成本记录
    List<Map<String, Object>> maps = importMapper.selectSubconPoExternalCost(rootId);
    BigDecimal resTotal = init;
    
    // 计算总的外部成本(排除当前记录)
    if (Objects.nonNull(maps) && maps.size() > 0) {
        for (Map<String, Object> map : maps) {
            if (Objects.equals(map.get("id"), dataId)) {
                continue;  // 跳过当前记录
            }
            Object externalCost = map.get("ExternalCost");
            BigDecimal decimal = Objects.isNull(externalCost) ? 
                BigDecimal.ZERO : new BigDecimal(externalCost.toString());
            resTotal = resTotal.add(decimal);
        }
    }
    
    // 更新分包商PO的总外部成本
    return importMapper.updateSubconPoExternalCost(resTotal, rootId) > 0;
}
```

**大白话：** 当新增一个外部成本记录时，系统会重新计算这个分包商PO下所有外部成本的总和，然后更新到主记录上。

#### 1.2.2 更新外部成本 - `/data-mange/subcon/external-cost/update`

**这个接口是干什么的？**
修改已有的外部成本记录。

**具体代码逻辑：**
和新增逻辑基本一样，也是重新计算总成本并更新。

### 1.3 Y3模块数据处理

#### 1.3.1 Y3数据导入 - `/data-mange/import/update-y3`

**这个接口是干什么的？**
专门用于导入Y3模块(生产力报告)的数据。

**具体代码逻辑：**

```java
public R<List<ImportResultVO>> updateY3(MultipartFile file, String key) {
    return R.ok(dataMangeService.updateY3(file, key));
}
```

**Service层逻辑：**

1. **读取Excel文件**
   ```java
   List<Map<String, Object>> mapList = ExcelUtil.readExcelToMap(file);
   ```

2. **数据验证**
   ```java
   for (Map<String, Object> map : mapList) {
       ImportResultVO importResultVO = new ImportResultVO();
       // 验证日期字段格式
       map.forEach((k, v) -> {
           if (ArrayUtil.contains(DataMangeService.datetime_fields, k) && Objects.nonNull(v)) {
               try {
                   String dateStr = v instanceof LocalDateTime ? 
                       ((LocalDateTime) v).toLocalDate().toString() : v.toString();
                   MetaDataUtil.dateStr2LocalDateTime(dateStr);
               } catch (Exception e) {
                   importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                   importResultVO.addWrongReason("日期格式错误: " + k);
               }
           }
       });
   }
   ```

3. **时间锁定检查**
   ```java
   LockTimeV2Util lockTimeUtil = new LockTimeV2Util();
   boolean isLocked = lockTimeUtil.checkTimeLock(importResultVO, projectCode, settlementDate);
   if (isLocked) {
       importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
       importResultVO.addWrongReason("该时间段数据已被锁定，无法修改");
   }
   ```

**大白话：** Y3模块的导入比较特殊，需要检查时间锁定。如果某个时间段的数据被锁定了，就不能修改。

#### 1.3.2 查询Y3导入进度 - `/data-mange/import/query-progress-y3`

**这个接口是干什么的？**
查看Y3数据导入的进度，告诉用户导入了多少条，成功了多少条，失败了多少条。

### 1.4 批量删除功能

#### 1.4.1 批量删除 - `/data-mange/delete/batch`

**这个接口是干什么的？**
通过上传Excel文件，批量删除指定的数据记录。

**具体代码逻辑：**

1. **读取删除列表**
   ```java
   List<Map<String, Object>> mapList = ExcelUtil.readExcelToMap(file);
   ```

2. **时间锁定检查**
   ```java
   LockTimeV3Util lockTimeV3Util = new LockTimeV3Util();
   if (lockTimeV3Util.checkTimeLock(importResultVO, projectCode, null)) {
       importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
       importResultVO.addWrongReason("该项目数据已被锁定，无法删除");
   }
   ```

3. **执行删除**
   ```java
   public Boolean deleteData(String projectId, String projectCode, String type, 
                           String region, String siteId, String itemCode, 
                           String phase, String PONumber, String unId) {
       // 使用Redis分布式锁防止并发删除
       RLock redissonClientLock = redissonClient.getLock("delete:" + projectCode);
       boolean lock = redissonClientLock.tryLock(0, 120, TimeUnit.SECONDS);
       
       if (!lock) {
           throw new BizException(RefreshPerCode.LOCK_TIMED_OUT);
       }
       
       try {
           // 根据不同模块类型执行不同的删除逻辑
           if ("Y1".equals(type)) {
               basicMapper.deleteSiteItem(projectId, region, siteId, itemCode, phase, unId);
           } else if ("Y2".equals(type)) {
               basicMapper.deletePoItem(projectId, region, siteId, itemCode, phase, PONumber, unId);
           }
           // ... 其他模块类型的删除逻辑
           
           // 清空相关的警告信息
           basicMapper.deleteAllWarnInfo(projectId);
           
       } finally {
           if (lock && redissonClientLock.isHeldByCurrentThread()) {
               redissonClientLock.unlock();
           }
       }
   }
   ```

**大白话：** 删除数据时要特别小心，所以用了分布式锁确保同一时间只有一个人能删除同一个项目的数据。删除完成后还要清理相关的警告信息。

## 2. BI面板模块 (BiPanelController)

### 2.1 收支状态统计

#### 2.1.1 收支统计查询 - `/Bi-panel/income-expenditure-status`

**这个接口是干什么的？**
统计项目在指定时间范围内的收入和支出情况，生成财务报表。

**具体代码逻辑：**

1. **权限检查**
   
   ```java
   if (!biPanelService.authorityCheck(GlobalConstants.BiPanel.B2, projectId)) {
       log.info("[{}]无权限访问BI-2面板信息", SecurityUtils.getUser().getName());
       return R.ok(Collections.emptyList());
   }
```
   
2. **获取统计数据**
   ```java
   List<IncomeAndExpenditureVO> voList = biPanelService.incomeExpenditureStatus(
       startTime, endTime, projectId, type, projectCycle);
   ```

**Service层的详细逻辑：**

```java
public List<IncomeAndExpenditureVO> incomeExpenditureStatus(Date startTime, Date endTime, 
                                                           Long projectId, String type, 
                                                           Integer projectCycle) {
    List<IncomeAndExpenditureVO> res = new ArrayList<>();
    
    if (Objects.equals(1, projectCycle)) {
        // 自选周期时间：使用用户指定的开始和结束时间
        res = basicMapper.incomeExpenditureStatus(startTime, endTime, projectId, type);
    } else {
        // 项目完整周期时间：查询项目的完整生命周期数据
        res = basicMapper.incomeExpenditureStatusProjectCycle(projectId, type);
    }
    
    return res;
}
```

**大白话：** 这个接口有两种模式：
- 模式1：用户指定时间范围，统计这个时间段内的收支情况
- 模式2：统计整个项目从开始到结束的完整收支情况

3. **项目周期数据汇总**
   
   ```java
   if (Objects.equals(0, projectCycle) && CollUtil.isNotEmpty(voList)) {
       return R.ok(projectCycleBuild(voList));
   }
   ```

**projectCycleBuild方法的逻辑：**
```java
private List<IncomeAndExpenditureVO> projectCycleBuild(List<IncomeAndExpenditureVO> voList) {
    IncomeAndExpenditureVO vo = new IncomeAndExpenditureVO();
    vo.setProjectName("项目周期汇总");
    
    // 汇总所有项目的各项金额
    BigDecimal siteItemValueTotal = new BigDecimal(0);
    BigDecimal poItemValueTotal = new BigDecimal(0);
    // ... 其他金额字段
    
    for (IncomeAndExpenditureVO expenditureVO : voList) {
        siteItemValueTotal = siteItemValueTotal.add(expenditureVO.getSiteItemValueTotal());
        poItemValueTotal = poItemValueTotal.add(expenditureVO.getPoItemValueTotal());
        // ... 累加其他金额
    }
    
    // 设置汇总结果
    vo.setSiteItemValueTotal(siteItemValueTotal);
    vo.setPoItemValueTotal(poItemValueTotal);
    // ... 设置其他汇总金额
    
    return Arrays.asList(vo);
}
```

**大白话：** 当选择项目完整周期模式时，系统会把所有相关数据汇总成一条记录，方便查看项目的整体财务状况。

#### 2.1.2 收支统计导出 - `/Bi-panel/income-expenditure-export`

**这个接口是干什么的？**
把收支统计数据导出成Excel文件下载。

**具体代码逻辑：**

1. **获取用户权限范围内的项目**
   ```java
   List<IncomeAndExpenditureVO> dataList = new ArrayList<>();
   PigUser pigUser = SecurityUtils.getUser();
   List<Long> roles = SecurityUtils.getRoles();
   
   if (Objects.isNull(projectId)) {
       // 如果没有指定项目ID，获取用户有权限的所有项目
       List<Long> ids = basicMapper.getProjectIdList(pigUser.getId(), roles);
       for (Long id : ids) {
           dataList.addAll(biPanelService.incomeExpenditureStatus(
               startTime, endTime, id, type, projectCycle));
       }
   } else {
       // 检查用户是否有权限访问指定项目
       if (!biPanelService.authorityCheck(GlobalConstants.BiPanel.B2, projectId)) {
           return R.failed("无权限访问！");
       }
       dataList = biPanelService.incomeExpenditureStatus(
           startTime, endTime, projectId, type, projectCycle);
   }
   ```

2. **导出Excel**
   ```java
   ClassPathResource resource = new ClassPathResource("import-template/BI-2-template.xlsx");
   ExcelUtil.exportTemplateList(response, "BI-2", resource, dataList);
   ```

**大白话：** 这个接口会根据用户的权限，导出他能看到的所有项目的收支数据。如果用户是管理员，能看到所有项目；如果是普通用户，只能看到自己参与的项目。

### 2.2 站点统计

#### 2.2.1 站点关闭率统计 - `/Bi-panel/site-item-statistics`

**这个接口是干什么的？**
统计各个项目下站点的关闭情况，包括已关闭、未关闭、无效的站点数量。

**具体代码逻辑：**

```java
public List<SiteItemStatisticsVO> siteItemStatistics(String appId, String projectId) {
    List<SiteItemStatisticsVO> res = new ArrayList<>();
    PigUser user = SecurityUtils.getUser();
    
    List<SiteItem> siteItems = new ArrayList<>();
    List<ProjectStandingBookDTO> projectIds = new ArrayList<>();
    List<Long> roleIdList = user.getRoleList().stream()
        .map(SysRole::getRoleId).collect(Collectors.toList());
    
    // 根据用户角色获取数据
    if (roleIdList.contains(SYSTEM_ADMIN_ID)) {
        // 系统管理员：获取所有站点和项目
        siteItems = roleMapper.getAllSiteItems();
        projectIds = roleMapper.getAllProjectsById(projectId);
    } else {
        // 普通用户：只获取有权限的站点和项目
        siteItems = roleMapper.getSiteItem(user.getId());
        projectIds = roleMapper.getProject(user.getId(), projectId);
    }
    
    // 按项目统计站点状态
    if (CollUtil.isNotEmpty(projectIds)) {
        for (ProjectStandingBookDTO project : projectIds) {
            SiteItemStatisticsVO siteItemStatisticsVO = new SiteItemStatisticsVO();
            siteItemStatisticsVO.setProjectName(project.getProjectName());
            
            // 统计总数
            long count = siteItems.stream()
                .filter(o -> Objects.equals(o.getProjectId(), project.getProjectId()))
                .count();
            siteItemStatisticsVO.setTotal(count);
            
            // 统计已关闭站点
            long close = siteItems.stream()
                .filter(o -> Objects.equals(o.getProjectId(), project.getProjectId())
                        && Objects.equals("[\"close\"]", o.getStatus()))
                .count();
            siteItemStatisticsVO.setClose(close);
            
            // 统计未关闭站点
            long unClose = siteItems.stream()
                .filter(o -> Objects.equals(o.getProjectId(), project.getProjectId())
                        && Objects.equals("[\"unclose\"]", o.getStatus()))
                .count();
            siteItemStatisticsVO.setUnclose(unClose);
            
            // 统计无效站点
            long invalid = siteItems.stream()
                .filter(o -> Objects.equals(o.getProjectId(), project.getProjectId())
                        && Objects.equals("[\"invalid\"]", o.getStatus()))
                .count();
            siteItemStatisticsVO.setInvalid(invalid);
            
            res.add(siteItemStatisticsVO);
        }
    }
    
    return res;
}
```

**大白话：** 这个接口会统计每个项目下站点的状态分布。站点有三种状态：
- close：已关闭（工作完成）
- unclose：未关闭（还在进行中）
- invalid：无效（可能是取消的站点）

系统会按项目分组，统计每种状态的站点数量。

### 2.3 金额统计

#### 2.3.1 总金额统计 - `/Bi-panel/total-amount`

**这个接口是干什么的？**
统计指定时间范围内的各种金额，包括PO金额(Y2)、产值决算金额(Y6)、开票金额(Y9)。

**具体代码逻辑：**

```java
public R<TotalAmountVO> totalAmount(String projectId, String dateStrStart, 
                                   String dateStrEnd, String dateType) {
    return R.ok(getData(projectId, dateStrStart, dateStrEnd, dateType));
}
```

**getData方法的异步处理逻辑：**

```java
private TotalAmountVO getData(String projectId, String dateStrStart, 
                             String dateStrEnd, String dateType) {
    TotalAmountVO totalAmountVO = new TotalAmountVO();
    
    // 使用异步方式并行查询三种金额
    CompletableFuture<AmountY2VO> futureY2 = CompletableFuture.supplyAsync(() -> {
        if (StringUtils.isBlank(dateType) || "PO_Received_date".equals(dateType)) {
            return basicMapper.totalY2(projectId, dateStrStart, dateStrEnd, dateType);
        }
        return new AmountY2VO();
    });
    
    CompletableFuture<AmountY6VO> futureY6 = CompletableFuture.supplyAsync(() -> {
        if (StringUtils.isBlank(dateType) || "Productivity_report_date".equals(dateType)
                || "KPI_Archive_date".equals(dateType)) {
            return basicMapper.totalY6(projectId, dateStrStart, dateStrEnd, dateType);
        }
        return new AmountY6VO();
    });
    
    CompletableFuture<AmountY9VO> futureY9 = CompletableFuture.supplyAsync(() -> {
        if (StringUtils.isBlank(dateType) || "Invoice_Date".equals(dateType)) {
            return basicMapper.totalY9(projectId, dateStrStart, dateStrEnd, dateType);
        }
        return new AmountY9VO();
    });
    
    try {
        // 等待所有异步任务完成
        AmountY2VO amountY2VO = futureY2.get();
        AmountY6VO amountY6VO = futureY6.get();
        AmountY9VO amountY9VO = futureY9.get();
        
        // 组装结果
        setAmount(totalAmountVO, amountY2VO, amountY6VO, amountY9VO);
        
    } catch (InterruptedException | ExecutionException e) {
        e.printStackTrace();
    }
    
    return totalAmountVO;
}
```

**大白话：** 这个接口很聪明，它同时查询三种不同的金额数据，而不是一个一个查询。这样可以大大提高查询速度。

**为什么要根据dateType判断？**
因为不同的金额类型有不同的日期字段：
- Y2(PO金额)：按PO接收日期统计
- Y6(产值决算)：按生产力报告日期或KPI归档日期统计  
- Y9(开票金额)：按开票日期统计

如果用户指定了dateType，就只查询对应类型的数据；如果没指定，就查询所有类型。

### 2.4 报表数据查询和导出

#### 2.4.1 报表数据查询 - `/Bi-panel/get-report-filed`

**这个接口是干什么的？**
获取综合报表数据，这是一个复杂的查询，会关联多个模块的数据。

**具体代码逻辑：**

```java
@GetMapping("/get-report-filed")
public R<Page<Map<String, Object>>> getReportFiled(
    @RequestParam(defaultValue = "1") Integer page,
    @RequestParam(value = "projectId", required = false) String projectId,
    @RequestParam(name = "dateStrStart", required = false) String dateStrStart,
    @RequestParam(name = "dateStrEnd", required = false) String dateStrEnd,
    @RequestParam(name = "dateType", required = false) String dateType,
    @RequestParam(name = "area", required = false) String area,
    @RequestParam(name = "projectIds", required = false) String projectIds,
    @RequestParam(name = "moduleTypes", required = false) String moduleTypes,
    @RequestParam(name = "nation", required = false) String nation,
    @RequestParam(name = "unId", required = false) String unId) {

    // 检查Redis中的查询方式配置
    Object o = redisTemplate.opsForValue().get(redisKey);
    String queryDataWay = null;
    if (o != null) {
        queryDataWay = o.toString();
    }
    
    // 根据配置选择查询方式
    if (queryDataWay != null && !"".equals(queryDataWay) && "OLD".equals(queryDataWay)) {
        // 使用旧的查询方式
        return R.ok(biPanelService.getReportFiled(page, projectId, dateStrStart, 
            dateStrEnd, dateType, area, projectIds, moduleTypes, nation));
    } else {
        // 使用优化后的查询方式
        return R.ok(optimizeBiPanelService.getReportFiled(page, projectId, dateStrStart, 
            dateStrEnd, dateType, area, projectIds, moduleTypes, nation, unId));
    }
}
```

**大白话：** 这个接口有两套查询逻辑：
- 旧版本：查询速度较慢，但稳定
- 新版本：查询速度更快，但可能还在优化中

系统通过Redis配置来控制使用哪种查询方式，可以随时切换。

#### 2.4.2 切换查询方式 - `/Bi-panel/switchBi3Query`

**这个接口是干什么的？**
在新旧两种查询方式之间切换。

**具体代码逻辑：**

```java
@GetMapping("switchBi3Query")
public R<?> switchBi3Query() {
    try {
        if (redisTemplate.hasKey(redisKey)) {
            // 如果Redis中有配置，删除它（切换到新方式）
            redisTemplate.delete(redisKey);
        } else {
            // 如果Redis中没有配置，设置为OLD（切换到旧方式）
            redisTemplate.opsForValue().set(redisKey, "OLD");
        }
    } catch (Exception e) {
        e.printStackTrace();
    }
    return R.ok("切换成功");
}
```

**大白话：** 这是一个开关，管理员可以用它来切换查询方式。如果发现新的查询方式有问题，可以立即切换回旧方式。

## 3. 工作流模块 (WorkFlow相关)

### 3.1 工作流提交拦截 (WorkFlowSubmitInterceptController)

**这些接口是干什么的？**
在用户提交各种报销申请时，系统会先拦截检查，确保数据正确后才允许提交到审批流程。

#### 3.1.1 综合报销提交拦截 - `/workflow/fs/submit-intercept/other-reimbursement`

**具体代码逻辑：**

```java
public WorkFlowApiRes otherReimbursement(RequestInfo requestInfo) {
    TableInfo tableInfo = requestInfo.getTableInfo();

    // 获取三个关键金额
    String sum = tableInfo.getValue("bxhj");    // 报销合计
    String num1 = tableInfo.getValue("zfhj");   // 支付合计
    String num2 = tableInfo.getValue("cxhj");   // 冲销合计

    // 检查金额是否平衡：支付金额 + 冲销金额 = 报销金额
    if(!ReimbursementAmountCheck(sum, num1, num2)){
        return WorkFlowApiRes.error("支付金额+冲销金额必须等于报销金额");
    }

    // 收集所有发票号码
    List<String> invoiceNums = new ArrayList<>();
    for (TableInfo subInfo: tableInfo.getSub()){
        if(subInfo.getModelName().equals("com_expense_dt1")){
            String invoiceNum = subInfo.getValue("fphm");  // 发票号码
            if(!StrUtil.isBlank(invoiceNum)){
                invoiceNums.add(invoiceNum);
            }
        }
    }

    // 检查发票是否已经被报销过
    if(reimburseInvoiceService.isDuplicateInvoiceNums(invoiceNums)){
        return WorkFlowApiRes.error("存在已报销发票");
    }

    return WorkFlowApiRes.ok();
}
```

**ReimbursementAmountCheck方法的逻辑：**

```java
private boolean ReimbursementAmountCheck(String sum, String num1, String num2){
    double v1 = 0.0;  // 报销总金额
    double v2 = 0.0;  // 支付金额
    double v3 = 0.0;  // 冲销金额

    try {
        v1 = Double.parseDouble(sum);
        v2 = Double.parseDouble(num1);
        v3 = Double.parseDouble(num2);
    } catch (Exception e){
        throw new RuntimeException("金额格式错误");
    }

    double total = NumberUtil.add(v2, v3);  // 支付金额 + 冲销金额

    return NumberUtil.compare(total, v1) == 0;  // 检查是否等于报销金额
}
```

**大白话：** 这个接口做两件事：
1. **金额平衡检查**：确保"支付金额 + 冲销金额 = 报销总金额"，防止金额不匹配
2. **发票重复检查**：确保同一张发票不会被重复报销

#### 3.1.2 劳务/材料报销提交拦截 - `/workflow/fs/submit-intercept/sub-con-and-material-Reimbursement`

**具体代码逻辑：**
和综合报销基本一样，只是字段名稍有不同：

```java
public WorkFlowApiRes SubConAndMaterialReimbursement(RequestInfo requestInfo) {
    TableInfo tableInfo = requestInfo.getTableInfo();
    String sum = tableInfo.getValue("bxhj");      // 报销合计
    String num1 = tableInfo.getValue("zfhj_bz");  // 支付合计(本币)
    String num2 = tableInfo.getValue("cxhj");     // 冲销合计

    // 同样的金额平衡检查和发票重复检查
    // ...
}
```

#### 3.1.3 差旅报销提交拦截 - `/workflow/fs/submit-intercept/travel-Reimbursement`

**具体代码逻辑：**

```java
public WorkFlowApiRes travelReimbursement(RequestInfo requestInfo) {
    TableInfo tableInfo = requestInfo.getTableInfo();
    String sum = tableInfo.getValue("bxhj");      // 报销合计
    String num1 = tableInfo.getValue("zfhjje");   // 支付合计金额
    String num2 = tableInfo.getValue("cxhjje");   // 冲销合计金额

    // 只做金额平衡检查，不检查发票（差旅报销可能没有发票）
    if(!ReimbursementAmountCheck(sum, num1, num2)){
        return WorkFlowApiRes.error("支付金额+冲销金额必须等于报销金额");
    }

    return WorkFlowApiRes.ok();
}
```

#### 3.1.4 财务付款申请提交拦截 - `/workflow/fs/submit-intercept/financial-payments`

**具体代码逻辑：**

```java
public WorkFlowApiRes financePayment(RequestInfo requestInfo) {
    return WorkFlowApiRes.ok();  // 目前没有特殊检查逻辑
}
```

**大白话：** 财务付款申请目前没有特殊的提交检查，直接通过。

### 3.2 工作流结束拦截 (WorkFlowEndInterceptController)

**这些接口是干什么的？**
当审批流程结束（通过）后，系统需要执行一些后续的业务操作，比如记录付款信息、更新账务等。

#### 3.2.1 综合报销结束拦截 - `/workflow/end-intercept/other-reimbursement`

**具体代码逻辑：**

```java
@Transactional
public WorkFlowApiRes otherReimbursement(RequestInfo requestInfo){
    List<PendingPaymentBill> pendingPaymentBills = new ArrayList<>();  // 挂账支付单
    List<ReimburseInvoiceEntity> reimburseInvoiceEntities = new ArrayList<>();  // 已报销发票记录

    TableInfo tableInfo = requestInfo.getTableInfo();
    List<TableInfo> sub = tableInfo.getSub();

    if(sub.isEmpty()){
        return WorkFlowApiRes.ok();
    }

    // 处理子表数据
    for (TableInfo subInfo : sub){
        if(subInfo.getModelName().equals("com_expense_dt1")){
            // 处理费用明细，记录已报销发票
            ReimburseInvoiceEntity reimburseInvoiceEntity = new ReimburseInvoiceEntity();
            reimburseInvoiceEntity.setInvoiceNum(subInfo.getValue("fphm"));  // 发票号码
            reimburseInvoiceEntity.setAmount(BigDecimal.valueOf(subInfo.getDoubleValue("je")));  // 金额
            reimburseInvoiceEntity.setReimburseDate(new Date());
            reimburseInvoiceEntities.add(reimburseInvoiceEntity);

        } else if(subInfo.getModelName().equals("com_expense_dt2")){
            // 处理支付明细，创建挂账支付单
            PendingPaymentBill pendingPaymentBill = newPendingPaymentBill(tableInfo, subInfo);
            pendingPaymentBill.setPaymentMoney(BigDecimal.valueOf(subInfo.getDoubleValue("zfje")));
            pendingPaymentBills.add(pendingPaymentBill);

        } else if(subInfo.getModelName().equals("com_expense_dt3")){
            // 处理冲销明细
            Long dataId = Long.parseLong(subInfo.getJsonArrayValue("jkjl").get(0));
            BigDecimal money = BigDecimal.valueOf(subInfo.getDoubleValue("cjje"));
            if(!personalLoansAndPrepaymentsService.writeOffLoan(dataId, money)){
                return WorkFlowApiRes.error("冲销失败");
            }
        }
    }

    // 保存挂账支付单
    if(!addPendingPaymentBills(pendingPaymentBills)){
        return WorkFlowApiRes.error("添加挂账支付失败");
    }

    // 保存已报销发票记录
    if(!reimburseInvoiceService.addReimburseInvoice(reimburseInvoiceEntities)){
        return WorkFlowApiRes.error("记录已报销发票失败");
    }

    return WorkFlowApiRes.ok();
}
```

**大白话：** 当综合报销审批通过后，系统要做三件事：
1. **记录已报销发票**：防止同一张发票被重复报销
2. **创建挂账支付单**：记录需要支付的金额，等待财务付款
3. **处理借款冲销**：如果有借款需要冲销，更新借款记录

#### 3.2.2 劳务/材料报销结束拦截 - `/workflow/end-intercept/sub-con-and-material-reimbursement`

**具体代码逻辑：**
和综合报销类似，但处理的子表模型不同：

```java
@Transactional
public WorkFlowApiRes SubConAndMaterialReimbursement(RequestInfo requestInfo){
    // 类似的逻辑，但处理不同的子表模型
    for (TableInfo subInfo : sub){
        if(subInfo.getModelName().equals("com_expense_dt1")){
            // 处理费用明细
        } else if(subInfo.getModelName().equals("sub_expense_dt2")){
            // 处理分包商支付明细
        } else if(subInfo.getModelName().equals("sub_expense_dt3")){
            // 处理冲销明细
        }
    }
    // ...
}
```

#### 3.2.3 差旅报销结束拦截 - `/workflow/end-intercept/travel-reimbursement`

**具体代码逻辑：**

```java
@Transactional
public WorkFlowApiRes travelReimbursement(RequestInfo requestInfo){
    List<PendingPaymentBill> pendingPaymentBills = new ArrayList<>();
    TableInfo tableInfo = requestInfo.getTableInfo();
    List<TableInfo> sub = tableInfo.getSub();

    for (TableInfo subInfo : sub){
        if(subInfo.getModelName().equals("out_expense_dt4")){
            // 处理差旅支付明细
            PendingPaymentBill pendingPaymentBill = newPendingPaymentBill(tableInfo, subInfo);
            pendingPaymentBill.setPaymentMoney(BigDecimal.valueOf(subInfo.getDoubleValue("zfje")));
            pendingPaymentBills.add(pendingPaymentBill);

        } else if (subInfo.getModelName().equals("out_expense_dt5")){
            // 处理借款冲销
            Long dataId = Long.parseLong(subInfo.getJsonArrayValue("jkjl").get(0));
            BigDecimal money = BigDecimal.valueOf(subInfo.getDoubleValue("cjje"));
            if(!personalLoansAndPrepaymentsService.writeOffLoan(dataId, money)){
                return WorkFlowApiRes.error("冲销失败");
            }
        }
    }

    if(!addPendingPaymentBills(pendingPaymentBills)){
        return WorkFlowApiRes.error("添加挂账支付失败");
    }

    return WorkFlowApiRes.ok();
}
```

**大白话：** 差旅报销不需要记录发票（因为很多差旅费用没有发票），主要是处理支付和借款冲销。

#### 3.2.4 预借付款申请结束拦截 - `/workflow/end-intercept/advance-payments-and-personal-loans`

**具体代码逻辑：**

```java
public WorkFlowApiRes advancePaymentsAndPersonalLoans(RequestInfo requestInfo){
    TableInfo tableInfo = requestInfo.getTableInfo();
    List<TableInfo> sub = tableInfo.getSub();

    List<PaymentApplication> paymentApplications = new ArrayList<>();
    for (TableInfo subInfo : sub){
        if(subInfo.getModelName().equals("person_loan_dt2")){
            // 创建付款申请单
            PaymentApplication paymentApplication = newPaymentApplication(tableInfo, subInfo);

            paymentApplication.setSy(tableInfo.getValue("jksqsy"));  // 借款申请事由
            paymentApplication.setCurrency(tableInfo.getValue("money_type"));  // 币种
            paymentApplication.setPaymentMoney(BigDecimal.valueOf(subInfo.getDoubleValue("zfje")));  // 支付金额

            paymentApplications.add(paymentApplication);
        }
    }

    // 保存付款申请单
    if(!addPayApp(paymentApplications)){
        return WorkFlowApiRes.error("新增付款申请单失败！");
    }

    return WorkFlowApiRes.ok();
}
```

**大白话：** 当借款申请审批通过后，系统会创建付款申请单，财务部门根据这个单子给员工打款。

#### 3.2.5 财务付款申请结束拦截 - `/workflow/end-intercept/financial-payments`

**具体代码逻辑：**

```java
@Transactional
public WorkFlowApiRes financialPayments(RequestInfo requestInfo){
    TableInfo tableInfo = requestInfo.getTableInfo();
    List<TableInfo> sub = tableInfo.getSub();

    List<PaymentApplication> paymentApplications = new ArrayList<>();
    boolean flag = true;

    for (TableInfo subInfo : sub){
        if(subInfo.getModelName().equals("finance_payment_dt")){
            // 创建付款申请单
            PaymentApplication paymentApplication = newPaymentApplication(tableInfo, subInfo);
            paymentApplication.setPaymentMoney(BigDecimal.valueOf(subInfo.getDoubleValue("zfje_bz")));
            paymentApplications.add(paymentApplication);

            // 如果选择了挂账流程，需要冻结相应金额
            String dataId = subInfo.getValue("gzlc");
            if(StrUtil.isNotBlank(dataId)){
                flag = accountsSuspenseService.reducePaymentMoney(
                    Long.valueOf(dataId), BigDecimal.valueOf(subInfo.getDoubleValue("zfje_bz")));

                if(!flag){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return WorkFlowApiRes.error("财务挂账支付金额冻结失败！");
                }
            }
        }
    }

    // 保存付款申请单
    flag = addPayApp(paymentApplications);
    if(!flag){
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        return WorkFlowApiRes.error("新增付款申请单失败！");
    }

    return WorkFlowApiRes.ok();
}
```

**大白话：** 财务付款申请通过后，系统会：
1. 创建付款申请单
2. 如果涉及挂账，会冻结相应的挂账金额，防止重复支付

### 3.3 用户入职工作流 (WorkFlowUserEntryController)

#### 3.3.1 新增用户 - `/workflow-user-entry/add`

**这个接口是干什么的？**
当有新员工入职时，通过工作流审批后，自动在系统中创建用户账号。

**具体代码逻辑：**

```java
public WorkFlowApiRes add(RequestInfo requestInfo) {
    TableInfo tableInfo = requestInfo.getTableInfo();
    log.info("新增用户");

    AddTenantUserDTO body = new AddTenantUserDTO();
    body.setTenantId(1694550407300681729L);  // 设置租户ID

    // 验证手机号
    if(!StrUtil.isNotBlank(tableInfo.getValue("phone"))){
        throw new IllegalArgumentException("手机号不能为空！");
    }
    if(!StrUtil.isNumeric(tableInfo.getValue("phone"))){
        throw new IllegalArgumentException("手机号格式不正确！");
    }

    // 设置用户基本信息
    body.setPhone(tableInfo.getValue("phone"));
    body.setUsername(tableInfo.getValue("username"));
    body.setName(tableInfo.getValue("name"));
    body.setEmail(tableInfo.getValue("email"));

    // 设置部门信息
    Optional<List<Long>> deptIdsOptional = Optional.of(tableInfo.getJsonArrayValue("dept"))
        .filter(list -> !list.isEmpty())
        .map(list -> list.stream().map(Long::parseLong).collect(Collectors.toList()));
    deptIdsOptional.ifPresent(body::setDeptIds);

    // 设置角色信息
    Optional<List<Long>> roleIdsOptional = Optional.of(tableInfo.getJsonArrayValue("role"))
        .filter(list -> !list.isEmpty())
        .map(list -> list.stream().map(Long::parseLong).collect(Collectors.toList()));
    roleIdsOptional.ifPresent(body::setRoleIds);

    // 设置岗位信息
    Optional<List<Long>> postIdsOptional = Optional.of(tableInfo.getJsonArrayValue("post"))
        .filter(list -> !list.isEmpty())
        .map(list -> list.stream().map(Long::parseLong).collect(Collectors.toList()));
    postIdsOptional.ifPresent(body::setPostIds);

    // 检查用户是否已存在
    SysUser sysUser = null;
    try {
        sysUser = remoteUserServiceV2.getUserByPhone(body.getPhone(), "Y");
    } catch (Exception e){
        log.error("查询用户信息异常！", e.getMessage());
        return WorkFlowApiRes.error("查询用户信息异常！");
    }

    if(sysUser != null){
        // 用户已存在，更新用户信息
        body.setUserId(sysUser.getUserId());
        R<Boolean> booleanR = remoteUserServiceV2.updateUser(body);
        if(booleanR.getCode() != 0){
            return WorkFlowApiRes.error("更新用户失败！");
        }
    } else {
        // 用户不存在，创建新用户
        R<Boolean> booleanR = remoteUserServiceV2.saveUser(body);
        if(booleanR.getCode() != 0){
            return WorkFlowApiRes.error("新增用户失败！");
        }
    }

    return WorkFlowApiRes.ok();
}
```

**大白话：** 这个接口会：
1. **验证基本信息**：检查手机号格式是否正确
2. **检查用户是否存在**：根据手机号查询用户
3. **创建或更新用户**：如果用户不存在就创建新用户，如果存在就更新用户信息
4. **设置权限**：给用户分配部门、角色、岗位等权限

## 4. 接口间的关联关系

### 4.1 数据导入流程的关联

```
用户上传Excel → 检查数据格式 → 异步导入数据 → 查询导入进度
     ↓              ↓              ↓              ↓
download-template  check-upload   upload-data    query-progress
```

### 4.2 工作流审批的关联

```
提交申请 → 提交拦截检查 → 审批流程 → 结束拦截处理 → 后续业务操作
   ↓           ↓           ↓           ↓            ↓
用户填表   submit-intercept  审批系统   end-intercept  付款/记账等
```

### 4.3 BI数据查询的关联

```
权限检查 → 数据查询 → 结果处理 → 导出Excel
   ↓         ↓         ↓         ↓
authorityCheck → 各种统计方法 → 数据汇总 → exportTemplate
```

## 5. 警告信息模块 (WarningInfoController)

### 5.1 警告信息更新

#### 5.1.1 更新警告状态 - `/warning/update`

**这个接口是干什么的？**
定期检查各种业务数据，发现问题时生成警告信息，提醒相关人员处理。

**具体代码逻辑：**

```java
@GetMapping("/update")
@Inner(value = false)
public R<Boolean> updateStatus(@RequestParam(value = "listCode", required = false) List<String> codes,
                              @RequestParam("yptt-task-token") String ypttTaskToken) {
    // 验证任务令牌
    Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");

    // 异步执行警告更新任务
    CompletableFuture.runAsync(() -> warningInfoService.updateByProjectCode(codes));

    return R.ok(Boolean.TRUE);
}
```

**Service层的详细逻辑：**

```java
public void updateByProjectCode(List<String> projectCodes) {
    if (CollUtil.isEmpty(projectCodes)) {
        // 如果没有指定项目，获取所有项目代码
        projectCodes = basicMapper.getProjectCodes();
    }

    if (CollUtil.isEmpty(projectCodes)) {
        return;
    }

    for (String projectCode : projectCodes) {
        // 更新站点条目警告状态
        updateSiteItemStatus(projectCode);

        // 更新站点交付警告状态
        updateSiteDeliveryStatus(projectCode);

        // 更新采购订单警告状态
        updatePoStatus(projectCode);

        // 更新分包商支付警告状态
        updateSubsconStatus(projectCode);

        // 更新YPTT结算警告状态
        updateYPTTSettlementStatus(projectCode);
    }
}
```

**大白话：** 这个接口会检查5种不同类型的警告：
1. **站点条目警告**：检查站点是否按时完成
2. **站点交付警告**：检查站点是否按时开工
3. **采购订单警告**：检查PO是否有延期或金额错误
4. **分包商支付警告**：检查分包商付款是否超期
5. **YPTT结算警告**：检查结算是否有问题

#### 5.1.2 采购订单警告更新详细逻辑

```java
public void updatePoStatus(String projectCode) {
    Integer pageSize = viewConfProperties.getPageSize();

    // 获取警告阈值配置
    String siteDelayWarning = warningMapper.getThresholdByCode(projectCode, "Site_Delay_Warning");
    String amountErrorWarning = warningMapper.getThresholdByCode(projectCode, "Amount_Error_Warning");

    // 获取已存在的警告数据ID
    List<Long> siteDelayId = warningMapper.getWarnDataIdByCode(projectCode, SITE_DELAY);
    List<Long> amountDelayId = warningMapper.getWarnDataIdByCode(projectCode, AMOUNT_ERROR);

    List<WarningMessage> warningMessages = new ArrayList<>();

    // 站点延期警告检查
    BiConsumer<Integer, Boolean> siteDelay = (i, hasNext) -> {
        while (hasNext) {
            // 查询需要警告的PO数据
            List<WarnTemp> tempList = warningMapper.selectPoWarnTemp(i, pageSize, projectCode, siteDelayWarning);

            if (CollUtil.isNotEmpty(tempList)) {
                List<Long> idList = new ArrayList<>();
                for (WarnTemp temp : tempList) {
                    // 如果这个数据还没有警告记录，创建新的警告
                    if (CollUtil.isEmpty(siteDelayId) || !siteDelayId.contains(temp.getId())) {
                        WarningMessage warningMessage = new WarningMessage();
                        warningMessage.setWarningType(SITE_DELAY);
                        warningMessage.setWarningMsg("Tips: Sites that have been assigned need to be delivered in time!");
                        warningMessage.setWarningDataId(temp.getId());
                        warningMessage.setProjectName(temp.getProjectName());
                        warningMessage.setUniquenessField(temp.getUniquenessField());
                        // ... 设置其他字段
                        warningMessages.add(warningMessage);
                    }
                    idList.add(temp.getId());
                }
                // 更新PO的警告状态
                warningMapper.updatePoWarning("Site_Delay_Warning", idList);
                hasNext = tempList.size() >= pageSize;
                i += pageSize;
            } else {
                hasNext = false;
            }
        }
    };

    // 金额错误警告检查
    BiConsumer<Integer, Boolean> amountError = (i, hasNext) -> {
        while (hasNext) {
            // 查询金额有问题的PO数据
            List<WarnTemp> tempList = warningMapper.selectPoAmountWarnTemp(i, pageSize, projectCode, amountErrorWarning);

            if (CollUtil.isNotEmpty(tempList)) {
                List<Long> idList = new ArrayList<>();
                for (WarnTemp temp : tempList) {
                    if (CollUtil.isEmpty(amountDelayId) || !amountDelayId.contains(temp.getId())) {
                        WarningMessage warningMessage = new WarningMessage();
                        warningMessage.setWarningType(AMOUNT_ERROR);
                        warningMessage.setWarningMsg("Tips: PO value and site value do not match!");
                        warningMessage.setWarningDataId(temp.getId());
                        warningMessage.setProjectName(temp.getProjectName());
                        warningMessage.setUniquenessField(temp.getUniquenessField());
                        // ... 设置其他字段
                        warningMessages.add(warningMessage);
                    }
                    idList.add(temp.getId());
                }
                warningMapper.updatePoWarning("Amount_Error_Warning", idList);
                hasNext = tempList.size() >= pageSize;
                i += pageSize;
            } else {
                hasNext = false;
            }
        }
    };

    // 执行警告检查
    siteDelay.accept(0, Boolean.TRUE);
    amountError.accept(0, Boolean.TRUE);

    // 保存警告信息数据
    saveWarningMessage(warningMessages);

    // 清理已解决的警告
    transactionTemplate.execute(status -> {
        // 查询已经解决的站点延期问题
        List<Long> removeSiteIdList = warningMapper.selectRemovePoId(projectCode, siteDelayWarning);
        List<WarningMessage> remove = new ArrayList<>();

        if (CollUtil.isNotEmpty(removeSiteIdList)) {
            // 更新状态为正常
            warningMapper.updatePoWarning("Site_Delay_Normal", removeSiteIdList);

            // 创建要删除的警告记录
            for (Long aLong : removeSiteIdList) {
                WarningMessage warningMessage = new WarningMessage();
                warningMessage.setWarningDataId(aLong);
                warningMessage.setWarningType(SITE_DELAY);
                remove.add(warningMessage);
            }
        }

        // 移除警告
        removeWarningMessageByWarningType(remove);
        return Boolean.TRUE;
    });
}
```

**大白话：** 这个方法做了三件事：
1. **检查新问题**：找出有延期或金额错误的PO，生成新的警告
2. **更新状态**：把有问题的PO标记为警告状态
3. **清理旧警告**：把已经解决的问题从警告列表中删除

### 5.2 警告信息查询

#### 5.2.1 分页查询警告 - `/warning/page`

**这个接口是干什么的？**
分页查询警告信息，支持按项目、时间、警告类型等条件筛选。

**具体代码逻辑：**

```java
@GetMapping("/page")
public R<IPage<WarningMessage>> page(Integer size, Integer cur,
                                    @RequestParam(value = "projectCode", required = false) String projectCode,
                                    @RequestParam(value = "projectName", required = false) String projectName,
                                    @RequestParam(value = "uniquenessField", required = false) String uniquenessField,
                                    @RequestParam(value = "warnType", required = false) List<String> warnType,
                                    @RequestParam(value = "startTime", required = false) Date startTime,
                                    @RequestParam(value = "endTime", required = false) Date endTime) {

    return R.ok(warningInfoService.page(size, cur, projectCode, projectName,
                                       uniquenessField, warnType, startTime, endTime));
}
```

**Service层逻辑：**

```java
public IPage<WarningMessage> page(Integer size, Integer cur, String projectCode, String projectName,
                                 String uniquenessField, List<String> warnType, Date startTime, Date endTime) {
    int current = Objects.isNull(cur) || cur < 0 ? 1 : cur;
    int sizePage = Objects.isNull(size) || size < 0 ? 10 : size;

    // 获取当前用户的角色权限
    List<WarningPageDTO> currentRole = getCurrentRole();
    if (CollUtil.isEmpty(currentRole)) {
        return new Page<>();  // 没有权限返回空页面
    }

    // 根据权限查询警告信息
    return warningMapper.warnPage(Page.of(current, sizePage), currentRole, projectCode,
                                 projectName, uniquenessField, warnType, startTime, endTime);
}
```

**大白话：** 这个接口会根据用户的权限，只显示他有权限看到的项目的警告信息。

#### 5.2.2 警告统计 - `/warning/statistics`

**这个接口是干什么的？**
统计各种类型警告的数量，生成警告统计报表。

#### 5.2.3 警告信息导出 - `/warning/export`

**这个接口是干什么的？**
把警告信息导出成Excel文件，方便离线查看和处理。

## 6. 任务管理模块 (TaskController)

### 6.1 台账更新任务

#### 6.1.1 更新项目台账 - `/task/update-project-standing-book`

**这个接口是干什么的？**
定期更新各种台账数据，确保台账信息与业务数据保持同步。

**具体代码逻辑：**

```java
@GetMapping(value = { "/update-project-standing-book", "update-standing-books" })
@Inner(value = false)
public R<StandingBookUpdateTaskResultDTO> updateProjectStandingBook(
        @RequestParam("yptt-task-token") String ypttTaskToken) {

    // 验证任务令牌
    Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");

    // 执行台账更新
    StandingBookUpdateTaskResultDTO updateResult = standingBookUpdateTaskService.update();

    // 根据更新结果返回不同的响应
    return updateResult.isAllSuccessful() ? R.ok(updateResult) : R.failed(updateResult, "Partial failure");
}
```

**大白话：** 这个接口会更新多种台账：
- 项目台账：汇总项目级别的数据
- 站点台账：汇总站点级别的数据
- PO台账：汇总采购订单级别的数据
- 分包商台账：汇总分包商级别的数据

如果所有台账都更新成功，返回成功；如果有部分失败，返回部分失败的状态。

### 6.2 导入任务检查

#### 6.2.1 检查导入任务 - `/task/check-import-task`

**这个接口是干什么的？**
检查导入任务的状态，清理超时或异常的导入任务。

**具体代码逻辑：**

```java
@GetMapping("/check-import-task")
@Inner(value = false)
public void checkImportTask(@RequestParam("yptt-task-token") String ypttTaskToken) {
    Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
    dataMangeService.checkImportTask();
}
```

**大白话：** 这个接口会：
1. 检查正在进行的导入任务
2. 清理超时的导入任务
3. 释放被占用的资源
4. 更新任务状态

### 6.3 收入修正任务

#### 6.3.1 收入修正 - `/task/income-correction-task`

**这个接口是干什么的？**
修正Y6模块（产值决算）中的收入数据，确保财务数据准确。

**具体代码逻辑：**

```java
@GetMapping("/income-correction-task")
@Inner(value = false)
public R<Boolean> incomeCorrectionTask(@RequestParam("yptt-task-token") String ypttTaskToken) {
    Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
    dataMangeService.incomeCorrection();
    return R.ok(Boolean.TRUE);
}
```

### 6.4 站点状态更新

#### 6.4.1 更新站点状态 - `/task/update-site-state`

**这个接口是干什么的？**
根据业务规则自动更新站点的状态（关闭、未关闭、无效等）。

**具体代码逻辑：**

```java
@GetMapping("/update-site-state")
@Inner(value = false)
public R<Boolean> updateSiteState(@RequestParam("yptt-task-token") String ypttTaskToken) {
    Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
    dataMangeService.updateSiteState();
    return R.ok(Boolean.TRUE);
}
```

**大白话：** 这个接口会根据站点的完成情况、交付时间等因素，自动判断站点应该是什么状态，并更新到数据库中。

## 7. 金额调整模块 (AdjustController)

### 7.1 站点条目金额调整

#### 7.1.1 Y1金额调整 - `/adjust/amount-siteItem`

**这个接口是干什么的？**
当Y1模块（站点条目）的金额发生变化时，触发相关的业务逻辑处理。

**具体代码逻辑：**

```java
@PostMapping("amount-siteItem")
@Inner(value = false)
public ApiRes adjustAmountSite(@RequestBody OperationUpdateDTO obj,
                              @RequestParam("yptt-task-token") String ypttTaskToken) {
    Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
    return adjustService.adjustAmountSite(obj);
}
```

**大白话：** 当站点条目的金额被修改时，这个接口会：
1. 重新计算相关的汇总数据
2. 更新台账信息
3. 检查是否需要生成新的警告
4. 同步相关的财务数据

### 7.2 PO条目金额调整

#### 7.2.1 Y2金额调整 - `/adjust/amount-poItem`

**这个接口是干什么的？**
当Y2模块（PO条目）的金额发生变化时，触发相关的业务逻辑处理。

### 7.3 分包商PO金额调整

#### 7.3.1 Y4金额调整 - `/adjust/amount-subPoItem`

**这个接口是干什么的？**
当Y4模块（分包商PO条目）的金额发生变化时，触发相关的业务逻辑处理。

## 8. Excel调整模块 (AdjustExcelController)

### 8.1 Y1数据Excel调整

#### 8.1.1 Y1数据导入更新 - `/adjust-excel/import/update-y1`

**这个接口是干什么的？**
通过上传Excel文件批量调整Y1模块的数据。

**具体代码逻辑：**

```java
@PostMapping("/import/update-y1")
public R<List<ImportResultVO>> updateY1(MultipartFile file, String key) {
    return R.ok(adjustService.updateY1New(file, key));
}
```

**大白话：** 这个接口允许用户：
1. 上传包含调整数据的Excel文件
2. 系统验证数据格式和业务规则
3. 批量更新Y1模块的数据
4. 返回每条数据的处理结果

#### 8.1.2 查询Y1导入进度 - `/adjust-excel/import/query-progress-y1`

**这个接口是干什么的？**
查询Y1数据调整的进度，告诉用户处理了多少条，成功了多少条，失败了多少条。

#### 8.1.3 下载Y1调整模板 - `/adjust-excel/download-template-y1`

**这个接口是干什么的？**
下载Y1数据调整的Excel模板，用户可以按照模板格式填写要调整的数据。

### 8.2 Y2和Y4数据Excel调整

类似Y1的逻辑，分别处理Y2（PO条目）和Y4（分包商PO条目）的Excel批量调整。

## 9. 财务支付模块 (PaymentApplicationController)

### 9.1 付款状态管理

#### 9.1.1 新增后完成付款 - `/fs/payment-application/payment-end-by-after-add`

**这个接口是干什么的？**
当新增付款明细后，自动检查是否已经付清全部款项，如果付清了就更新付款申请单的状态。

**具体代码逻辑：**

```java
@PostMapping("payment-end-by-after-add")
@Inner(value = false)
public ApiRes paymentEndByAfterAdd(@RequestBody BatchOperationParams.Payload params,
                                  @RequestParam("yptt-task-token") String ypttTaskToken) {
    Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");

    // 获取付款金额
    BigDecimal paymentMoney = new BigDecimal(params.getData().get("payment_money").toString());

    // 更新付款状态
    return paymentApplicationService.paymentEndByMainDataId(
        params.getMainDataId(), params.getDataId(), paymentMoney);
}
```

**Service层的详细逻辑：**

```java
public ApiRes paymentEndByMainDataId(Long mainDataId, List<Long> dataId, BigDecimal paymentMoney) {
    // 获取关联表名
    String modelRelTableName = viewModelRelService.getModelRelTableNameByModelName(
        leftModelName, rightModelName);
    String rightModelTable = viewModelRelService.getModelTableNameByModelName(rightModelName);
    String leftModelTable = viewModelRelService.getModelTableNameByModelName(leftModelName);

    // 更新付款状态
    if (!UpdatePayStatus(mainDataId, modelRelTableName, leftModelTable, rightModelTable, params)){
        return ApiRes.failed("修改支付状态失败");
    }

    return ApiRes.ok("");
}
```

**UpdatePayStatus方法的核心逻辑：**

```java
private Boolean UpdatePayStatus(Long mainDataId, String modelRelTableName,
                               String leftModelTable, String rightModelTable,
                               Map<String,Object> params) {
    // 1. 获取付款明细数据ID列表
    List<Long> dataIds = viewModelRel.getRightModelDataIdByLeftModelDataId(mainDataId, modelRelTableName);

    // 2. 计算实际已付金额
    BigDecimal realPaymentMoney = BigDecimal.ZERO;
    if (params != null) {
        realPaymentMoney = realPaymentMoney.add((BigDecimal) params.get("paymentMoney"));
    }

    if (dataIds.size() > 0) {
        // 累加其他付款明细的金额
        realPaymentMoney = realPaymentMoney.add(
            paymentApplicationMapper.getRealPaymentMoney(rightModelTable, dataIds, excludeDataId));
    }

    // 3. 获取应付金额
    BigDecimal paymentMoney = paymentApplicationMapper.getPaymentMoney(leftModelTable, mainDataId);

    // 4. 判断是否付清
    String status = "[\"0\"]";  // 未付清
    if (realPaymentMoney.compareTo(paymentMoney) >= 0) {
        status = "[\"1\"]";     // 已付清
        log.info("付款完成");
    }

    // 5. 更新付款申请单状态
    return paymentApplicationMapper.updateStatus(leftModelTable, status, mainDataId);
}
```

**大白话：** 这个接口的逻辑是：
1. 当财务人员新增一笔付款记录时
2. 系统自动计算这个付款申请单的总实付金额
3. 和应付金额比较，如果实付 >= 应付，就标记为"已付清"
4. 这样可以自动跟踪付款进度

#### 9.1.2 删除后完成付款 - `/fs/payment-application/payment-end-by-after-del`

**这个接口是干什么的？**
当删除付款明细后，重新计算付款状态。

**具体代码逻辑：**
```java
public ApiRes paymentEndByAfterDel(@RequestBody BatchOperationParams.Payload params,
                                  @RequestParam("yptt-task-token") String ypttTaskToken) {
    // 删除时付款金额设为0，重新计算状态
    return paymentApplicationService.paymentEndByMainDataId(
        params.getMainDataId(), params.getDataId(), new BigDecimal(0));
}
```

#### 9.1.3 更新后完成付款 - `/fs/payment-application/payment-end-by-after-update`

**这个接口是干什么的？**
当修改付款明细后，重新计算付款状态。

## 10. 数据连接器模块 (Connector2codeController)

### 10.1 数据连接器的作用

**这些接口是干什么的？**
当用户在前端修改数据时，这些接口会自动触发，执行一些额外的业务逻辑，比如汇率转换、金额计算等。

#### 10.1.1 Y1数据连接器 - `/connector2code/y1`

**具体代码逻辑：**

```java
@RequestMapping("/y1")
public ApiRes connector2codeY1(@RequestBody OperationUpdateDTO o,
                              @RequestParam("yptt-task-token") String ypttTaskToken) {
    // 异步执行，不阻塞用户操作
    CompletableFuture.runAsync(() -> {
        Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");

        // 包装数据
        MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(o);

        // 执行Y1连接器逻辑
        y1Transformer.y1connector(wrapper);

        // 移除虚拟属性
        wrapper.removeValue("Currency");

        // 更新数据库
        basicMapper.updateItemData(wrapper.toMap(),
            viewConfProperties.getSiteItem().getTableName());
    });

    return ApiRes.ok(Boolean.TRUE);
}
```

**Y1Transformer.y1connector的逻辑：**
```java
public void y1connector(MetaDataDTOWrapper wrapper) {
    // 1. 获取币种信息
    String currency = wrapper.getValue("Currency");

    // 2. 如果有单价和数量，计算总价
    BigDecimal unitPrice = wrapper.getBigDecimalValue("Unit_price");
    BigDecimal quantity = wrapper.getBigDecimalValue("quantity");
    if (unitPrice != null && quantity != null) {
        BigDecimal siteValue = unitPrice.multiply(quantity);
        wrapper.setValue("Site_value", siteValue);
    }

    // 3. 如果涉及汇率转换，进行货币转换
    if (StringUtils.isNotBlank(currency) && !"USD".equals(currency)) {
        // 调用汇率转换服务
        BigDecimal convertedAmount = moneyExchangeService.convert(
            wrapper.getBigDecimalValue("Site_value"), currency, "USD");
        wrapper.setValue("Site_value_USD", convertedAmount);
    }

    // 4. 更新相关的汇总数据
    updateRelatedSummary(wrapper);
}
```

**大白话：** Y1连接器会：
1. 当用户修改单价或数量时，自动计算总价
2. 如果涉及外币，自动转换成美元
3. 更新相关的汇总统计数据

#### 10.1.2 Y2数据连接器 - `/connector2code/y2`

类似Y1，但处理PO条目的业务逻辑。

#### 10.1.3 Y3数据连接器 - `/connector2code/y3`

**具体代码逻辑：**

```java
@RequestMapping("/y3")
public ApiRes connector2codeY3(@RequestBody OperationUpdateDTO o,
                              @RequestParam("yptt-task-token") String ypttTaskToken) {
    Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
    return connector2codeService.y3Connector2code(o);
}
```

**大白话：** Y3连接器处理生产力报告的相关计算，比如完成百分比、产值计算等。

#### 10.1.4 Y4数据连接器 - `/connector2code/y4`

类似Y1，但处理分包商PO条目的业务逻辑。

## 11. 货币兑换模块 (MoneyExchangeController)

### 11.1 货币转换

#### 11.1.1 货币兑换 - `/money-exchange/transfer`

**这个接口是干什么的？**
提供货币汇率转换功能，支持各种货币之间的兑换计算。

**具体代码逻辑：**

```java
@GetMapping("transfer")
public ApiRes transfer(@RequestParam String moneyInput,
                      @RequestParam String moneyOutput,
                      @RequestParam BigDecimal Amount) {
    return moneyExchangeService.transfer(moneyInput, moneyOutput, Amount);
}
```

**Service层的详细逻辑：**

```java
public ApiRes transfer(String moneyInput, String moneyOutput, BigDecimal amount) {
    // 1. 参数校验
    if (StringUtils.isBlank(moneyInput) || StringUtils.isBlank(moneyOutput)) {
        return ApiRes.failed("货币对不能为空");
    }

    if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
        return ApiRes.failed("金额必须是正数");
    }

    // 2. 尝试获取正向汇率 (如 USD -> CNY)
    List<Map<String, Object>> rates = moneyExchangeMapper.selectByParis(moneyInput, moneyOutput);
    boolean isReverseRate = false;

    // 3. 如果正向汇率不存在，尝试获取反向汇率 (如 CNY -> USD)
    if (rates == null || rates.isEmpty()) {
        rates = moneyExchangeMapper.selectByParis(moneyOutput, moneyInput);
        if (rates == null || rates.isEmpty()) {
            return ApiRes.failed("找不到该货币对的汇率信息");
        }
        isReverseRate = true;
    }

    // 4. 提取汇率
    Map<String, Object> rateInfo = rates.get(0);
    BigDecimal exchangeRate = (BigDecimal) rateInfo.get("exchange_rate");

    if (exchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
        return ApiRes.failed("汇率必须是正数");
    }

    // 5. 计算兑换金额
    BigDecimal result;
    if (isReverseRate) {
        // 反向汇率计算：金额 × 汇率
        result = amount.multiply(exchangeRate).setScale(6, RoundingMode.HALF_UP);
    } else {
        // 正向汇率计算：金额 ÷ 汇率
        result = amount.divide(exchangeRate, 6, RoundingMode.HALF_UP);
    }

    return ApiRes.ok(result);
}
```

**大白话：** 这个接口很智能：
1. 首先查找直接的汇率（比如USD到CNY）
2. 如果找不到，就查找反向汇率（CNY到USD），然后倒算
3. 根据汇率类型选择乘法或除法计算
4. 保留6位小数，确保精度

## 12. 数据锁定模块 (LockDataTimeController)

### 12.1 数据锁定管理

#### 12.1.1 锁定数据 - `/lock-data-time/lock`

**这个接口是干什么的？**
锁定特定时间段的数据，防止用户修改已经确认的历史数据。

**具体代码逻辑：**

```java
@PostMapping("exec-data")
public ApiRes execData(@RequestBody LockDataDTO lockDataDTO){
    return lockDataTimeService.execData(lockDataDTO);
}
```

**大白话：** 比如财务已经确认了2023年12月的数据，就可以锁定这个时间段，防止用户再修改12月的数据。

#### 12.1.2 解锁数据 - `/lock-data-time/unlock`

**这个接口是干什么的？**
解除数据锁定，允许用户重新修改数据。

#### 12.1.3 获取锁定列表 - `/lock-data-time/list`

**这个接口是干什么的？**
查看当前有哪些时间段的数据被锁定了。

#### 12.1.4 添加操作时间段 - `/lock-data-time/abilityOperateDate`

**这个接口是干什么的？**
设置允许操作的时间段，比如只允许修改最近3个月的数据。

**具体代码逻辑：**

```java
@PostMapping("abilityOperateDate")
public ApiRes abilityOperateDate(@RequestBody AbilityOperateDateDTO abilityOperateDateDTO) {
    return lockDataTimeService.abilityOperateDate(abilityOperateDateDTO);
}
```

## 13. 完整业务流程梳理

### 13.1 数据导入流程

```
1. 用户下载模板
   ↓
2. 填写Excel数据
   ↓
3. 上传Excel文件 (upload-data-table)
   ↓
4. 系统检查数据格式 (check-upload-data)
   ↓
5. 异步执行导入任务
   ↓
6. 数据转换和验证
   ↓
7. 批量保存到数据库
   ↓
8. 触发数据连接器 (connector2code)
   ↓
9. 更新相关汇总数据
   ↓
10. 生成导入结果报告
```

### 13.2 工作流审批流程

```
1. 用户填写报销/付款申请
   ↓
2. 提交时触发拦截检查 (submit-intercept)
   ↓
3. 验证金额平衡、发票重复等
   ↓
4. 进入审批流程
   ↓
5. 审批通过后触发结束拦截 (end-intercept)
   ↓
6. 创建付款申请单/挂账支付单
   ↓
7. 记录已报销发票
   ↓
8. 处理借款冲销
   ↓
9. 财务执行付款
   ↓
10. 更新付款状态 (payment-end-by-*)
```

### 13.3 BI数据统计流程

```
1. 用户访问BI面板
   ↓
2. 权限检查 (authorityCheck)
   ↓
3. 根据权限获取项目列表
   ↓
4. 异步查询各种金额数据
   ↓
5. 数据汇总和计算
   ↓
6. 返回统计结果
   ↓
7. 可选择导出Excel
```

### 13.4 警告信息处理流程

```
1. 定时任务触发 (warning/update)
   ↓
2. 检查各种业务数据
   ↓
3. 发现问题生成警告
   ↓
4. 更新数据状态
   ↓
5. 清理已解决的警告
   ↓
6. 用户查看警告列表 (warning/page)
   ↓
7. 处理问题
   ↓
8. 下次检查时自动清除警告
```

### 13.5 台账更新流程

```
1. 定时任务触发 (task/update-standing-books)
   ↓
2. 汇总各模块数据
   ↓
3. 更新项目台账
   ↓
4. 更新站点台账
   ↓
5. 更新PO台账
   ↓
6. 更新分包商台账
   ↓
7. 返回更新结果
```

### 13.6 数据锁定流程

```
1. 管理员设置锁定规则
   ↓
2. 用户修改数据时检查锁定状态
   ↓
3. 如果数据被锁定，拒绝修改
   ↓
4. 如果在允许时间段内，允许修改
   ↓
5. 修改后触发相关业务逻辑
```

## 14. 接口间的复杂关联关系

### 14.1 数据修改的连锁反应

```
用户修改Y1数据
    ↓
触发Y1数据连接器 (connector2code/y1)
    ↓
重新计算汇总金额
    ↓
触发金额调整接口 (adjust/amount-siteItem)
    ↓
更新相关台账数据
    ↓
检查是否需要生成警告
    ↓
更新BI面板统计数据
```

### 14.2 工作流与财务的关联

```
报销申请审批通过
    ↓
创建挂账支付单 (end-intercept)
    ↓
财务创建付款申请单
    ↓
财务执行付款
    ↓
更新付款状态 (payment-end-by-*)
    ↓
影响BI面板的支出统计
    ↓
可能触发新的警告检查
```

### 14.3 定时任务的协调

```
警告更新任务 (warning/update)
    ↓
台账更新任务 (task/update-standing-books)
    ↓
导入任务检查 (task/check-import-task)
    ↓
收入修正任务 (task/income-correction-task)
    ↓
站点状态更新 (task/update-site-state)
```

**大白话总结：**

这个YPTT系统是一个复杂的项目管理系统，主要处理海外项目的财务和进度管理。整个系统的核心是：

1. **数据管理**：通过Excel导入各种业务数据
2. **工作流管理**：处理各种报销和付款申请的审批
3. **财务管理**：跟踪付款状态和财务数据
4. **数据分析**：提供各种统计报表和BI面板
5. **预警系统**：自动检查业务问题并生成警告
6. **权限控制**：确保用户只能访问有权限的数据

所有这些功能通过复杂的接口关联在一起，形成一个完整的业务闭环。
