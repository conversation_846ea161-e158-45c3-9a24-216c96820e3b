# YPTT 项目管理工具 (YPMS-Ipro) 说明书总结

## 一、 系统总体结构

YPTT项目管理工具通过 **9个核心数据板块**、**系统告警功能** 和 **BI分析展示功能**，构成了一个全面的项目管理体系。

- **数据录入方式**:
    - **板块1, 2, 3, 4**: 数据导入 + 手工录入。
    - **板块8, 9**: 模板导入。
    - **板块5, 6, 7**: 工具自动更新。
- **编号规则**: `Y-板块编号+两位序列号` (例如: Y101)。

---

## 二、 各模块字段详解

### **Y1 板块 - 站点基础信息 (Site Basic Information)**
*此板块是项目的基础，记录了站点的核心初始信息，是后续所有流程的起点。*

| 字段ID | 字段名称 (中/英) | 定义与说明 |
| :--- | :--- | :--- |
| **Y101** | 序列号 / NO. | 记录数据行数的唯一序列号，遵循Excel标准，删除后不重用。 |
| **Y102** | 站点录入日期 / Site Register Date | 站点首次录入系统的日期，用于识别更新及时性。 |
| **Y103** | 站点状态 / Site Status | 记录站点交付状态（关闭/未关闭）。**关闭条件**：`Y119=Y215=Y516=Y922` 且 `Y411=Y814`。 |
| **Y104** | 部门 / Department | 站点所属部门（如：印尼一部、马来RNO），项目立项时定义。 |
| **Y105** | 项目编号 / YPTT Project Code | 项目的唯一识别码，用于关联所有相关数据。 |
| **Y106** | 项目名称 / YPTT Project Name | 项目的具体名称，通常格式为：年份+客户+区域+类型。 |
| **Y107** | 采购地区 / Purchase Region | 合同规定的采购区域，用于识别项目价格系数。 |
| **Y108** | 项目区域 / Project Area | 站点所属的行政区或城市。 |
| **Y109** | 站点编号 / Site ID | 客户或运营商定义的站点唯一ID，是识别站点的第一关键信息。 |
| **Y110** | 站点名称 / Site Name | 客户定义的站点名称。 |
| **Y111** | 站点分配日期 / Site Allocation Date | 客户正式分配任务的日期，以邮件或PO为准。 |
| **Y112** | 阶段 / Phase | 区分项目不同阶段（Phase 1, Phase 2），用于识别重复站点的不同批次。 |
| **Y113** | 业务类型 / Type Of Service | 站点所属业务板块（如: TI, RNO, Civil）。 |
| **Y114** | 站点模型 / Site Model | 站点工作类型（如: TSS, TSSR, MW）。 |
| **Y115** | 业务条目代码 / Item Code | 客户采购合同的条目编号，若无则需自定义。 |
| **Y116** | 条目名称 / BOQ Item | 合同中定义的BOQ条目，是识别工作内容的重要信息。 |
| **Y117** | 条目数量 / Quantity | 站点条目的界面数量（如：租赁“月”数，产品“个”数）。 |
| **Y118** | 条目单价 / Unit Price | 单位为1的条目价格。 |
| **Y119** | 站点价值 / Item Value | `条目单价 * 数量`，等同于PO单条目总价。 |
| **Y120** | 备注 / Remark | 对当前板块信息的修改或特殊情况的备注。 |
| **Y121** | **唯一性关键识别字段** | **`Y105 & Y107 & Y109 & Y112 & Y115`** 的组合，用于检查数据唯一性。 |

### **Y2 板块 - 站点PO数据 (PO Information)**
*此板块管理客户发出的采购订单（PO）信息，是项目收入确认的依据。*

| 字段ID | 字段名称 (中/英) | 定义与说明 |
| :--- | :--- | :--- |
| **Y201** | PO收取时间 / PO Received-Date | 正式收到客户PO的日期。 |
| **Y202** | PO号码 / PO Number | 客户PO订单号。 |
| **Y203** | 客户合同号码 / Contract Number | PO信息中的合同编号。 |
| **Y205** | 项目编号 / YPTT Project Code | 等同于 `Y105`。 |
| **Y208** | 站点编号 / Site ID | 等同于 `Y109`。 |
| **Y215** | PO价值 / PO Value | PO中单个条目释放的PO总金额（不含VAT）。 |
| **Y216** | PO缺口 / PO GAP | 站点价值与实际收到PO金额的差额 (`Y119 - Y215`)。 |
| **Y217**| 预付款 / Pre payment Milestone | PO中定义的预付款比例。 |
| **Y218-Y221** | 结算里程碑 (1-4) / Site Settlement Milestone (1st-4th) % | PO中定义的各结算节点的比例。 |
| **Y223** | **唯一性关键识别字段** | **`Y205 & Y206 & Y208 & Y207 & Y210`** 的组合。 |

### **Y3 板块 - 站点交付信息 (Site Delivery Information)**
*此板块跟踪站点的实际交付进度和责任人，是过程管理的核心。*

| 字段ID | 字段名称 (中/英) | 定义与说明 |
| :--- | :--- | :--- |
| **Y301-Y305**| 项目/站点/BOQ等信息 | 直接关联 `Y105`, `Y106`, `Y107`, `Y109`, `Y116`。 |
| **Y306** | 站点归属 / Site belong to | 交付团队的公司名称（YPTT自有/分包商/freelance）。 |
| **Y307-Y310**| 负责人 (队长/工程师等) / Team Leader, Site engineer, PIC | 明确项目各角色的负责人。 |
| **Y311-Y317**| **关键日期节点** | `A-开工` -> `B-完成` -> `C-开通` -> `D-自检` -> `E-初验` -> `F-终验` -> `G-保障期`。 |
| **Y319-Y322**| 第(1-4)次可结算时间 / Ready for settlement (1st-4th) | 根据站点完工状态，满足结算条件的日期。 |
| **Y323-Y326**| 分包商可结算日期 (1-4) / Subcon-settlement date (1st-4th) | 分包商可申请结算的日期。 |

### **Y4 板块 - 分包商PO信息 (Subcontractor PO Information)**
*管理向分包商下发的PO，是成本控制和分包商管理的关键。*

| 字段ID | 字段名称 (中/英) | 定义与说明 |
| :--- | :--- | :--- |
| **Y401** | 分包商 / Subcon Name | 接收PO的分包商公司全称。 |
| **Y402** | 分包商-PO号码 / Subcon-PO number | YPTT释放给分包商的PO编号。 |
| **Y411** | 分包商-PO金额 / Subcon-PO amount | YPTT释放给分包商的PO条目总金额（不含VAT）。 |
| **Y412-Y415**| 分包结算里程碑 (1-4) / Subcon settlement Milestone (1st-4th) % | 分包PO中定义的各结算节点比例。 |
| **Y418** | **唯一性关键识别字段** | **`Y401 & Y402 & Y404 & Y407 & Y411`** 的组合。 |

### **Y5 板块 - 结算管理信息 (Settlement Management Information)**
*此板块为系统自动更新，基于Y2和Y3的数据，计算客户结算的详细信息。*

| 字段ID | 字段名称 (中/英) | 定义与说明 |
| :--- | :--- | :--- |
| **Y501** | 预付款结算日期 / Pre payment | 自动取值PO收到日期 `Y201`。 |
| **Y503** | 预付款金额 / Pre payment amount | `Y215 * Y502` (PO金额 * 预付款比例)。 |
| **Y504-Y515**| 第(1-4)次可结算时间/比例/金额 | 关联 `Y3`的日期和`Y2`的比例，自动计算金额。 |
| **Y516** | **可结算总金额 / Ready For Settlement Amount** | 四次可结算金额之和 (`Y506+Y509+Y512+Y515`)。 |
| **Y517** | 不可结算金额 / NY ready for settlement amount | `Y215 - Y516` (PO总金额 - 已可结算总金额)，即WIP。 |

### **Y6 板块 - 产值管理 (Productivity Management)**
*此板块自动更新，用于内部产值核算，反映项目进度和价值实现。*

| 字段ID | 字段名称 (中/英) | 定义与说明 |
| :--- | :--- | :--- |
| **Y601-Y608**| 第(1-4)次产值申报日期/金额 | 完全等同于 `Y5` 板块的第(1-4)次可结算日期和金额。 |
| **Y609** | **产值申报总金额 / Productivity Amount of already reported** | 四次产值申报金额之和，理论上等于`Y516`。 |
| **Y610** | 产值申报总比例 / Productivity declaration ratio% | `Y609 / Y215`，用于评估PO完工和申报比例。 |

### **Y7 板块 - 分包商结算信息管理 (Subcontractor Settlement Information Management)**
*此板块自动更新，基于Y3和Y4数据，计算应付给分包商的结算款项。*

| 字段ID | 字段名称 (中/英) | 定义与说明 |
| :--- | :--- | :--- |
| **Y701-Y712**| 分包商第(1-4)次可结算日期/比例/金额 | 关联 `Y3`的分包商可结算日期和`Y4`的比例，自动计算金额。 |
| **Y713** | **分包商可结算总金额 / Subcon-Totally settlement amount** | 四次分包商可结算金额之和。 |
| **Y714** | 分包商不可结算金额 / Subcon-Totally Settlement Gap | `Y411 - Y713` (分包PO总额 - 已可结算总额)。 |

### **Y8 板块 - 分包商支付信息 (Subcontractor Payment Information)**
*此板块通过模板导入，记录对分包商的实际付款情况。*

| 字段ID | 字段名称 (中/英) | 定义与说明 |
| :--- | :--- | :--- |
| **Y801-Y812**| 分包商第(1-4)次支付日期/金额/发票号 | 记录YPTT向分包商支付的四次款项的详细信息。 |
| **Y814** | **分包商支付总金额 / Subcon-Totally payment amount** | 四次付款总金额之和。 |
| **Y815** | 分包商未支付总金额 / Subcon-Totally payment amount-gap | `Y713 - Y814` (应付总额 - 已付总额)，是判断应付未付成本的核心。 |

### **Y9 板块 - YPTT结算信息 (YPTT Settlement Information)**
*此板块通过模板导入，记录向客户开具发票和回款的信息。*

| 字段ID | 字段名称 (中/英) | 定义与说明 |
| :--- | :--- | :--- |
| **Y901-Y920**| 第(1-4)次结算发票日期/号码/金额/差值/备注 | 记录YPTT向客户提交的四次结算发票的详细信息。 |
| **Y922** | **发票总金额 / Totally Invoice amount** | 四次发票总金额之和。 |
| **Y923** | 发票总缺口 / Invoice amount-GAP | `Y516 - Y922` (可结算总金额 - 已开发票总金额)，即应收未收。 |

---

## 三、 系统告警功能 (System Alarm Function)

系统通过设定规则，自动监控关键流程节点，并通过红色填充等方式发出告警。

| 告警ID | 告警名称 | 监控逻辑 | 目的 |
| :--- | :--- | :--- | :--- |
| **A-1** | 站点信息delay | 收到PO (`Y201`) 后3天内未更新站点基础信息。 | 监督及时更新站点状态。 |
| **A-2** | 站点PO delay | 站点分配 (`Y111`) 后7天内未更新PO信息。 | 监督及时追踪和更新PO。 |
| **A-3** | PO金额错误 | PO缺口 `Y216` 不为0。 | 确保工作界面与PO价格匹配。 |
| **A-4** | 开工delay | 站点分配 (`Y111`) 后7天内未更新开工日期 (`Y311`)。 | 推动已分配站点及时开工。 |
| **A-5** | 验收delay | 开工 (`Y311`) 后超过阈值（如30天）未完成验收 (`Y315`)。 | 推动已开工站点及时完工验收。 |
| **A-6** | 分包PO delay | 非YPTT交付的站点开工 (`Y311`) 后7天内未更新分包PO信息。 | 监督及时向分包商下发PO。 |
| **A-7** | 分包支付delay | 应付未付金额 (`Y814-Y713`) 小于0，且最新支付日期超过30天。 | 监控对分包商的付款延迟风险。 |
| **A-8** | 发票delay | 应收未收金额 (`Y922-Y516`) 小于0，且最新发票日期超过30天。 | 监控向客户开票的延迟风险。 |

---

## 四、 BI分析展示功能 (BI Analysis Display Function)

| BI ID | 展示名称 | 数据源 | 展示目的与方式 |
| :--- | :--- | :--- | :--- |
| **BI-1** | 站点关闭率 | `Y103` | 柱状图展示站点关闭/未关闭数量及关闭率，统计项目整体关闭情况。 |
| **BI-2** | 项目收支状态统计 | `Y119`, `Y215`, `Y411`, `Y516`, `Y609`, `Y713`, `Y814`, `Y922` | 柱状图按天/月/年维度展示8项核心金额（站点价值、PO价值、分包成本、可结算、已产值、应付、已付、已开票）的总额，宏观展示项目健康度。 |
| **BI-3** | 项目数据库 | `Y1-Y9` | 提供完整的项目数据查询与导出功能。 |

---

## 五、 模块与模板间的关系总结

1.  **数据流起点**：**Y1 (基础信息)** 和 **Y2 (客户PO)** 是项目启动的核心，大部分数据通过手动导入。
2.  **成本确认**：**Y4 (分包商PO)** 定义了项目的外部成本。
3.  **进度驱动**：**Y3 (交付信息)** 是整个数据流的“发动机”，其关键日期节点的更新，会触发后续模块的自动计算。
4.  **自动计算核心**：
    - **Y5 (客户结算)** 基于 `Y2` 和 `Y3` 自动计算**应收金额**。
    - **Y6 (产值管理)** 完全同步 `Y5`，用于内部核算。
    - **Y7 (分包商结算)** 基于 `Y4` 和 `Y3` 自动计算**应付金额**。
5.  **财务数据闭环**：
    - **Y8 (分包商支付)** 通过模板导入实际**已付成本**，与 `Y7` 的应付金额对比，形成成本闭环。
    - **Y9 (YPTT结算)** 通过模板导入实际**已收（已开发票）金额**，与 `Y5` 的应收金额对比，形成收入闭环。
6.  **监控与分析**：**告警系统**监控各模块关键字段的延迟和差异，**BI系统**则对所有模块数据进行汇总和可视化分析，为项目决策提供支持。

**简单来说**：**Y1/Y2/Y4** 是输入，**Y3** 是过程，**Y5/Y6/Y7** 是自动计算结果，**Y8/Y9** 是财务核销，**告警和BI** 是监控与决策支持。