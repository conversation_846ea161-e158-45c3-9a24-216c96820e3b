# YPTT导入数据修改处理逻辑详解

## 概述

YPTT系统提供了一套完整的导入数据修改机制，允许用户通过Excel文件批量修改已存在的数据。这个功能主要通过`AdjustExcelController`和`AdjustExcelService`实现，支持Y1、Y2、Y4等模块的数据批量修改。

## 1. 整体架构流程

### 1.1 修改流程概览

```mermaid
graph TD
    A[用户导出现有数据] --> B[修改Excel文件]
    B --> C[上传修改后的Excel]
    C --> D[数据验证和解析]
    D --> E[批量数据处理]
    E --> F[关联数据更新]
    F --> G[数据库事务提交]
    G --> H[返回处理结果]
```

### 1.2 核心组件

| 组件 | 作用 | 主要方法 |
|------|------|----------|
| AdjustExcelController | 控制器层，处理HTTP请求 | updateY1(), updateY2(), updateY4() |
| AdjustExcelService | 业务逻辑层，处理数据修改 | updateY1New(), updateY2(), updateY4() |
| AdjustMapper | 数据访问层，执行SQL更新 | updateSiteItemDataes(), updatePoItemDatas() |
| AdjustService | 关联数据处理服务 | adjustSiteItem(), adjustPoItem() |

## 2. Y1模块（站点条目）修改逻辑

### 2.1 接口入口

```java
// 控制器入口
@PostMapping("/import/update-y1")
public R<List<ImportResultVO>> updateY1(MultipartFile file, String key) {
    return R.ok(adjustService.updateY1New(file, key));
}
```

### 2.2 核心处理逻辑

#### 2.2.1 数据读取和验证

```java
public List<ImportResultVO> updateY1New(MultipartFile file, String key) {
    String redisKey = Y1_KEY + key;
    List<Map<String, Object>> mapList = read2Map(file);  // 读取Excel数据
    Integer maxSize = 10000; // 最大修改条数限制
    List<ImportResultVO> checkResult = new ArrayList<>();
    
    if (CollUtil.isEmpty(mapList)) {
        return Collections.emptyList();
    }
    
    if (mapList.size() > maxSize) {
        throw new RuntimeException("超过最大修改条数限制: " + maxSize);
    }
}
```

**大白话解释：**
- 首先读取用户上传的Excel文件
- 限制最大修改条数为10000条，防止系统过载
- 如果文件为空直接返回

#### 2.2.2 数据验证和处理

```java
// 进度跟踪初始化
ProgressY1VO vo = new ProgressY1VO(checkResult, 0.0);
redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);

List<Map<String, Object>> siteItemList = new ArrayList<>();
List<Map<String, Object>> poItemList = new ArrayList<>();

for (Map<String, Object> map : mapList) {
    try {
        // 1. 数据验证
        String uniField = (String) map.get("Identification field of uniqueness");
        if (StringUtils.isBlank(uniField)) {
            // 记录验证失败
            ImportResultVO failed = new ImportResultVO();
            failed.setStatus(ImportResultVO.STATUS_FAILED);
            failed.addWrongReason("唯一标识字段不能为空");
            checkResult.add(failed);
            continue;
        }
        
        // 2. 查询现有数据
        Long ufId = adjustMapper.selectUfIdByUniField(uniField);
        if (ufId == null) {
            // 数据不存在
            ImportResultVO failed = new ImportResultVO();
            failed.setStatus(ImportResultVO.STATUS_FAILED);
            failed.addWrongReason("找不到对应的数据记录");
            checkResult.add(failed);
            continue;
        }
        
        // 3. 构建更新数据
        Map<String, Object> siteItemData = buildSiteItemUpdateData(map, ufId);
        Map<String, Object> poItemData = buildPoItemUpdateData(map, ufId);
        
        siteItemList.add(siteItemData);
        poItemList.add(poItemData);
        
        // 4. 记录成功
        ImportResultVO success = new ImportResultVO();
        success.setStatus(ImportResultVO.STATUS_SUCCESS);
        checkResult.add(success);
        
    } catch (Exception e) {
        // 异常处理
        ImportResultVO failed = new ImportResultVO();
        failed.setStatus(ImportResultVO.STATUS_FAILED);
        failed.addWrongReason(e.getMessage());
        checkResult.add(failed);
    }
}
```

**大白话解释：**
- 遍历Excel中的每一行数据
- 验证唯一标识字段是否存在
- 根据唯一标识查询数据库中的现有记录
- 构建要更新的数据结构
- 记录每条数据的处理结果

#### 2.2.3 批量数据更新

```java
// 异步执行数据库更新
try {
    CompletableFuture.runAsync(() -> {
        doSql(siteItemList, poItemList);
    });
} catch (Exception e) {
    e.printStackTrace();
}

@Transactional(rollbackFor = Exception.class)
void doSql(List<Map<String, Object>> siteItemList, List<Map<String, Object>> poItemList) {
    try {
        // 批量更新站点条目数据
        adjustMapper.updateSiteItemDataes(siteItemList);
        // 批量更新PO条目数据
        adjustMapper.updateSitePoDataes(poItemList);
    } catch (Exception e) {
        e.printStackTrace();
        throw new RuntimeException("数据库更新失败: " + e.getMessage());
    }
}
```

**大白话解释：**
- 使用异步方式执行数据库更新，避免阻塞用户请求
- 在事务中批量更新数据，确保数据一致性
- 如果更新失败会回滚所有操作

### 2.3 SQL更新逻辑

```xml
<!-- 批量更新站点条目数据 -->
<update id="updateSiteItemDataes">
    <foreach collection="siteItemList" item="map" separator=";">
        update memm_e648652640b44b2092c93e1742e6171b
        set
        <foreach collection="map" index="key" item="value" separator=",">
            <choose>
                <when test="key == 'Site_item_status'">
                    Site_item_status = JSON_ARRAY(CONCAT(#{value}))
                </when>
                <otherwise>
                    `${key}` = #{value}
                </otherwise>
            </choose>
        </foreach>
        where id = #{map.id} and is_deleted = 0
    </foreach>
</update>
```

**大白话解释：**
- 使用MyBatis的foreach标签批量执行更新
- 动态构建SET子句，只更新有变化的字段
- 特殊处理JSON字段（如Site_item_status）
- 通过id定位要更新的记录

## 3. Y2模块（PO条目）修改逻辑

### 3.1 核心处理流程

```java
public List<ImportResultVO> updateY2(MultipartFile file, String key) {
    String redisKey = Y2_KEY + key;
    List<Map<String, Object>> mapList = read2Map(file);
    Integer maxSize = 2000; // Y2模块限制2000条
    
    // 数据容器
    List<Map<String, Object>> poItems = new ArrayList<>();
    List<Map<String, Object>> settlementDatas = new ArrayList<>();
    List<Map<String, Object>> productivityDatas = new ArrayList<>();
    List<Map<String, Object>> YPTTSettlements = new ArrayList<>();
    
    for (Map<String, Object> map : mapList) {
        try {
            // 1. 获取关键字段
            String projectCode = (String) map.get("YPTT Project code");
            String uniField = (String) map.get("Identification field of uniqueness");
            BigDecimal quantity = new BigDecimal(map.get("Quantity").toString());
            BigDecimal unitPrice = new BigDecimal(map.get("Unit price").toString());
            
            // 2. 计算数量变化
            BigDecimal originalQuantity = getOriginalQuantity(uniField);
            BigDecimal quantityReduce = originalQuantity.subtract(quantity);
            
            // 3. 查询关联数据
            List<Map<String, Object>> mapLists = adjustMapper.selectReport(projectCode);
            
            // 4. 异步处理关联数据更新
            asyncDoy2(projectCode, uniField, quantity, unitPrice, quantityReduce, 
                     mapLists, map, poItems, settlementDatas, productivityDatas, YPTTSettlements, mapCache);
                     
        } catch (Exception e) {
            // 错误处理
        }
    }
    
    // 5. 批量更新数据库
    updateInfo(poItems, settlementDatas, productivityDatas, YPTTSettlements);
}
```

**大白话解释：**
- Y2模块的修改比Y1复杂，因为涉及多个关联表
- 需要计算数量变化，影响后续的结算和产值计算
- 异步处理关联数据的更新逻辑
- 最后批量提交所有相关表的更新

### 3.2 关联数据处理

```java
void asyncDoy2(String projectCode, String uniField, BigDecimal quantity, BigDecimal unitPrice,
               BigDecimal quantityReduce, List<Map<String, Object>> mapLists, Map<String, Object> poItem,
               List<Map<String, Object>> poItems, List<Map<String, Object>> settlementDatas,
               List<Map<String, Object>> productivityDatas, List<Map<String, Object>> YPTTSettlements,
               Map<String, Map<String, Object>> mapCache) {
    try {
        // 调用AdjustService处理关联数据
        adjustService.adjustPoItem(projectCode, uniField, mapLists, quantity, unitPrice, quantityReduce,
                poItems, settlementDatas, productivityDatas, YPTTSettlements, mapCache);
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

**大白话解释：**
- 当PO条目的数量或单价发生变化时
- 需要重新计算相关的结算数据、产值数据、YPTT结算数据
- 这些计算逻辑在AdjustService中实现

### 3.3 批量更新事务

```java
@Transactional(rollbackFor = Exception.class)
void updateInfo(List<Map<String, Object>> poItems, List<Map<String, Object>> settlementDatas,
                List<Map<String, Object>> productivityDatas, List<Map<String, Object>> YPTTSettlements) {
    try {
        if (poItems.size() < 1) {
            throw new RuntimeException("poItems长度为0");
        }
        adjustMapper.updatePoItemDatas(poItems);
        
        if (settlementDatas.size() < 1) {
            throw new RuntimeException("settlementDatas长度为0");
        }
        adjustMapper.updateSettlementDatas(settlementDatas);
        
        if (productivityDatas.size() < 1) {
            throw new RuntimeException("productivityDatas长度为0");
        }
        adjustMapper.updateProductivityDatas(productivityDatas);
        
        if (YPTTSettlements.size() < 1) {
            throw new RuntimeException("YPTTSettlements长度为0");
        }
        adjustMapper.updateYPTTSettlements(YPTTSettlements);
        
    } catch (Exception e) {
        e.printStackTrace();
        throw new RuntimeException("批量更新失败: " + e.getMessage());
    }
}
```

**大白话解释：**
- 在一个事务中更新4个相关表
- 如果任何一个表更新失败，所有操作都会回滚
- 确保数据的一致性

## 4. Y4模块（分包商PO条目）修改逻辑

### 4.1 处理流程

Y4模块的处理逻辑与Y2类似，但关注点在分包商相关的数据：

```java
public List<ImportResultVO> updateY4(MultipartFile file, String key) {
    // 类似Y2的处理流程
    // 但重点处理分包商PO金额、里程碑、额外成本等字段
    
    for (Map<String, Object> map : mapList) {
        // 获取分包商相关字段
        BigDecimal subconPOAmount = new BigDecimal(map.get("Subcon PO amount").toString());
        BigDecimal additionalCost = new BigDecimal(map.get("additional cost").toString());
        
        // 处理分包商结算数据
        asyncDoy4(projectCode, uniField, quantity, unitPrice, mapLists, quantityReduce, 
                 SubconSettlement_1st, SubconSettlement_2nd, SubconSettlement_3rd, SubconSettlement_4th);
    }
}
```

## 5. 进度跟踪机制

### 5.1 Redis进度存储

```java
// 初始化进度
ProgressY1VO vo = new ProgressY1VO(checkResult, 0.0);
redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);

// 更新进度
if (++progress % 5 == 0 || progress == total) {
    vo.setProgress((double) progress / (double) total * 100);
    redisTemplate.setValueSerializer(RedisSerializer.java());
    redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
}
```

### 5.2 进度查询接口

```java
@GetMapping("/import/query-progress-y1")
public R<ProgressY1VO> queryProgressY1(String key) {
    return R.ok(adjustService.queryProgressY1(key));
}

public ProgressY1VO queryProgressY1(String key) {
    redisTemplate.setValueSerializer(RedisSerializer.java());
    Object o = redisTemplate.opsForValue().get(Y1_KEY + key);
    return o instanceof ProgressY1VO ? (ProgressY1VO) o : new ProgressY1VO(null, 100.0);
}
```

**大白话解释：**
- 使用Redis存储处理进度，设置5分钟过期时间
- 每处理5条记录或处理完成时更新一次进度
- 前端可以通过查询接口实时获取处理进度

## 6. 错误处理机制

### 6.1 数据验证错误

```java
try {
    // 数据处理逻辑
} catch (Exception e) {
    log.info("Y1批量更新失败: {}", e.getMessage());
    ImportResultVO failed = new ImportResultVO();
    failed.setStatus(ImportResultVO.STATUS_FAILED);
    failed.setIndex(mapList.indexOf(map));
    failed.setImportData(map);
    failed.addWrongReason(e.getMessage());
    result.add(failed);
}
```

### 6.2 数据库更新错误

```java
@Transactional(rollbackFor = Exception.class)
void updateInfo(...) {
    try {
        // 批量更新操作
    } catch (Exception e) {
        e.printStackTrace();
        throw new RuntimeException("更新异常");
    }
}
```

**大白话解释：**
- 每条记录的处理错误都会被捕获并记录
- 数据库更新使用事务，确保要么全部成功，要么全部回滚
- 错误信息会返回给用户，便于问题排查

## 7. 性能优化策略

### 7.1 批量处理

```java
// 使用批量更新而不是逐条更新
adjustMapper.updateSiteItemDataes(siteItemList);  // 批量更新

// 而不是：
// for (Map<String, Object> item : siteItemList) {
//     adjustMapper.updateSiteItemData(item);  // 逐条更新
// }
```

### 7.2 异步处理

```java
// 数据库更新使用异步，避免阻塞用户请求
CompletableFuture.runAsync(() -> {
    doSql(siteItemList, poItemList);
});
```

### 7.3 分批提交

```java
// 大量数据分批提交，避免内存溢出
private <T> void batchUpdate(Consumer<List<T>> updateFunction, List<T> dataList, int batchSize) {
    for (int i = 0; i < dataList.size(); i += batchSize) {
        int end = Math.min(i + batchSize, dataList.size());
        List<T> batch = dataList.subList(i, end);
        updateFunction.accept(batch);
    }
}
```

## 8. 使用场景和注意事项

### 8.1 适用场景
- 批量修改站点条目的数量、单价、状态等
- 批量调整PO条目的金额、里程碑等
- 批量更新分包商PO的成本信息

### 8.2 注意事项
- 修改前必须先导出现有数据作为模板
- 唯一标识字段不能修改，用于定位记录
- 数量和金额的修改会影响关联的结算和产值数据
- 大批量修改建议分批进行，避免系统压力过大

### 8.3 数据一致性保证
- 使用数据库事务确保更新的原子性
- 关联数据的更新通过AdjustService统一处理
- 修改后会触发相关的数据连接器重新计算
