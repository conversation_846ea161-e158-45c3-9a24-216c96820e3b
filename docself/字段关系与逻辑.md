# YPTT 工具模块与字段间详细关系分析

本文档旨在深入剖析YPTT项目管理工具中，各个模块与字段之间的精确联动与变化关系，揭示其内在的数据流与自动化逻辑。

## 核心基础：关键识别字段 (The Data Glue)

所有模块的数据能够准确关联，依赖于一套贯穿始终的关键识别字段。它们如同每条记录的“身份证”，确保从始至终讨论的都是同一个项目的同一项任务。

| 关键字段 | 来源模块 | 作用 | 在其他模块中的体现 (作为关联键) |
| :--- | :--- | :--- | :--- |
| **Y105** (项目编号) | Y1 | 关联项目级别的所有信息。 | Y205, Y301 |
| **Y109** (站点编号) | Y1 | 关联特定物理站点的所有信息。 | Y208, Y304, Y404 |
| **Y115** (条目代码) | Y1 | 关联特定工作任务（BOQ条目）。 | Y210, Y406 |
| **Y112** (阶段) | Y1 | 区分同一站点的不同批次任务。 | Y207 |

---

## 主数据流与变化关系

以下将按照项目的生命周期，详细拆解核心的数据流动和计算逻辑。

### 1. 收入流：从站点价值到发票结算 (Y1 -> Y2 -> Y3 -> Y5 -> Y9)

这条主线是项目收入管理的核心，展示了从初始估值到最终回款的全过程。

**Step 1: 价值定义与确认**
- **初始估算**: 在 `Y1` 板块，项目经理首先定义一项任务的理论价值。
  - **变化关系**: `Y118 (条目单价) * Y117 (条目数量) = Y119 (站点价值)`
  - *说明*: `Y119` 是项目的**内部预估价值**。

- **官方确认**: 客户的PO是价值的最终确认。
  - **变化关系**: `Y215 (PO 价值)` 是从客户PO导入的**官方合同价值**。
  - **核心监控点**: 系统会对比这两个价值，产生一个关键的健康指标。
    - **计算**: `Y119 (站点价值) - Y215 (PO 价值) = Y216 (PO 缺口)`
    - *说明*: 当 `Y216` 不为0时，**A-3 告警**会被触发，提示项目经理PO金额与预估不符，需要核实。

**Step 2: 交付进度驱动计算 (The Trigger)**

- **核心驱动**: `Y3 (站点交付信息)` 板块是整个自动化计算的“发动机”。
  - **变化关系**: 当项目团队在 `Y3` 中更新了任一关键交付日期 (如 `Y312` 施工完成, `Y315` 初验通过)，这个**日期**就成为一个触发信号。

**Step 3: 自动化结算金额计算**
- **联动触发**: `Y3` 的日期更新会**自动触发 `Y5` (结算管理信息) 板块**的计算。
  - **日期传递**: `Y3` 中定义的“可结算时间” (`Y319` 至 `Y322`) 会被系统自动复制到 `Y5` 对应的结算时间字段 (`Y504`, `Y507`, `Y510`, `Y513`)。
  - **金额计算**: 系统根据被触发的结算节点，查找 `Y2` 板块中预设的结算比例。
    - **计算**:
        - `Y215 (PO 价值) * Y218 (结算里程碑一比例) = Y506 (第一次可结算金额)`
        - `Y215 (PO 价值) * Y219 (结算里程碑二比例) = Y509 (第二次可结算金额)`
        - ...以此类推
  - **汇总计算**: 系统自动将各次可结算金额相加。
    - **计算**: `Y506 + Y509 + Y512 + Y515 = Y516 (可结算总金额)`

**Step 4: 产值同步**

- **内部核算**: `Y6 (产值管理)` 板块的数据与 `Y5` **完全同步**，是 `Y5` 的一个“镜像”。
  - **变化关系**:
    - `Y504 (第一次可结算时间)` 被自动复制到 `Y601 (第一次产值申报日期)`。
    - `Y506 (第一次可结算金额)` 被自动复制到 `Y602 (第一次产值申报金额)`。
    - ...以此类推
  - *说明*: 这确保了内部确认的产值与对客可结算的金额和时间点严格一致。

**Step 5: 财务结算闭环**

- **最终核销**: `Y9 (YPTT结算信息)` 板块通过模板导入财务开票的实际数据。
  - **变化关系**: 财务人员导入 `Y903, Y908, ...` 等发票金额后，系统会自动汇总。
    - **计算**: `Y903 + Y908 + Y913 + Y918 = Y922 (发票总金额)`
  - **核心监控点**: 系统将**自动计算的可结算总额**与**实际开票总额**进行对比。
    - **计算**: `Y516 (可结算总金额) - Y922 (发票总金额) = Y923 (发票总缺口)`
    - *说明*: `Y923` 代表了**已完工可开票但尚未开票的金额 (WIP)**，是衡量项目回款健康度的重要指标。当它为负数且超过30天，会触发 **A-8 发票delay告警**。

### 2. 成本流：从分包PO到分包支付 (Y4 -> Y3 -> Y7 -> Y8)

此流程与收入流高度相似，是项目成本管理的镜像。

**Step 1: 成本定义**

- **成本确认**: 在 `Y4` 板块，定义了需要支付给分包商的成本。
  - **变化关系**: `Y411 (分包商-PO 金额)` 是项目**锁定的外包成本**。

**Step 2: 交付进度驱动计算 (共享同一触发器)**

- **核心驱动**: 成本的计算同样由 `Y3` 板块的交付进度驱动。
  - **变化关系**: 当项目团队更新 `Y3` 中的**分包商可申请结算日期** (`Y323` 至 `Y326`) 时，触发信号发出。
  - *说明*: 这体现了工具设计的精妙之处——**同一个交付事件 (`Y3`)，同时驱动了收入 (`Y5`) 和成本 (`Y7`) 两条线的自动计算**。

**Step 3: 自动化应付金额计算**
- **联动触发**: `Y3` 的日期更新会**自动触发 `Y7` (分包商结算信息)** 的计算。
  - **日期传递**: `Y323` 等日期被自动复制到 `Y701` 等字段。
  - **金额计算**: 系统查找 `Y4` 中预设的分包结算比例。
    - **计算**:
        - `Y411 (分包PO金额) * Y412 (分包结算里程碑一比例) = Y703 (分包商第一次可结算金额)`
        - ...以此类推
  - **汇总计算**:
    - **计算**: `Y703 + Y706 + Y709 + Y712 = Y713 (分包商可结算总金额)`

**Step 4: 财务支付闭环**
- **最终核销**: `Y8 (分包商支付信息)` 板块通过模板导入对分包商的实际付款数据。
  - **变化关系**: 财务导入 `Y802, Y805, ...` 等支付金额后，系统自动汇总。
    - **计算**: `Y802 + Y805 + Y808 + Y811 = Y814 (分包商支付总金额)`
  - **核心监控点**: 系统将**自动计算的应付总额**与**实际支付总额**进行对比。
    - **计算**: `Y713 (分包商可结算总金额) - Y814 (分包商支付总金额) = Y815 (分包商未支付总金额)`
    - *说明*: `Y815` 代表了**应付但未付的分包款项**，是管理现金流和供应商关系的关键。当它为正数且超过30天，会触发 **A-7 分包支付delay告警**。

### 3. 核心状态联动关系：站点状态 (Y103)

`Y103` 字段不是一个流程的起点或终点，而是对整个项目收入与成本流最终状态的一个**综合判定**。

- **变化关系**: `Y103 (站点状态)` 的值是基于多个模块最终汇总字段的**逻辑判断**，是系统中最复杂的联动关系之一。
- **判定条件**: 站点状态变为 **“关闭”**，必须 **同时满足** 以下两个条件：
    1.  **收入线完全闭环**: `Y119 (站点价值) = Y215 (PO 价值) = Y516 (可结算总金额) = Y922 (发票总金额)`
        - *解读*: 预估价值、合同价值、可结算价值、已开票价值完全相等，代表收入无缺口。
    2.  **成本线完全闭环**: `Y411 (分包商-PO 金额) = Y713 (分包商可结算总金额) = Y814 (分包商支付总金额)`
        - *解读*: 合同成本、应付成本、已付成本完全相等，代表成本无缺口。

*说明*: 只有当一个任务的所有收入和成本都完全结清，没有任何遗留问题时，它的状态才能被系统判定为“关闭”。这是项目真正完结的最终标志。*