# YPTT系统接口文档

## 文档说明

本文档详细描述了YPTT（海外项目管理系统）的所有API接口，包括接口功能、参数说明、请求示例和响应格式。

## 接口通用说明

### 基础信息
- **基础URL**: `http://localhost:20001` (开发环境)
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

### 通用响应格式
```json
{
  "code": 0,           // 响应码，0表示成功
  "msg": "success",    // 响应消息
  "data": {},          // 响应数据
  "success": true      // 是否成功
}
```

### 权限说明
- 大部分接口需要登录认证
- 部分接口需要特定角色权限
- 内部接口需要`yptt-task-token`参数验证

---

## 1. 数据管理模块 (DataMangeController)

### 1.1 数据导入

#### 1.1.1 上传数据表
**接口名称**: 上传数据表进行导入  
**接口路径**: `POST /data-mange/import/upload-data-table`  
**接口作用**: 批量导入Excel数据到指定模块（Y1-Y9）

**请求参数**:
- `file` (MultipartFile): Excel文件，必填
- `moduleType` (String): 模块类型，必填，可选值：Y1,Y2,Y3,Y4,Y6,Y8,Y9
- `appId` (String): 应用ID，必填

**字段说明**:
- `file`: 上传的Excel文件，支持.xlsx格式
- `moduleType`: 数据模块类型，对应不同的业务模块
  - Y1: 站点条目管理
  - Y2: 采购订单条目管理
  - Y3: 生产力报告管理
  - Y4: 分包商PO条目管理
  - Y6: 决算管理
  - Y8: 分包商支付管理
  - Y9: 开票管理
- `appId`: 应用标识，用于权限控制

**请求示例**:
```bash
curl -X POST "http://localhost:20001/data-mange/import/upload-data-table" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/your/data.xlsx" \
  -F "moduleType=Y1" \
  -F "appId=your-app-id"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "导入任务已提交",
  "data": {
    "taskId": "import_task_123456",
    "status": "processing"
  },
  "success": true
}
```

#### 1.1.2 检查上传数据
**接口名称**: 检查上传数据格式  
**接口路径**: `POST /data-mange/import/check-upload-data`  
**接口作用**: 在正式导入前验证Excel数据格式和内容

**请求参数**: 同上传数据表接口

**请求示例**:
```bash
curl -X POST "http://localhost:20001/data-mange/import/check-upload-data" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/your/data.xlsx" \
  -F "moduleType=Y1" \
  -F "appId=your-app-id"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "数据检查完成",
  "data": {
    "totalRows": 100,
    "validRows": 95,
    "invalidRows": 5,
    "errors": [
      {
        "row": 10,
        "field": "Site_value",
        "message": "金额格式错误"
      }
    ]
  },
  "success": true
}
```

#### 1.1.3 下载导入模板
**接口名称**: 下载Excel导入模板  
**接口路径**: `GET /data-mange/import/download-template`  
**接口作用**: 下载指定模块的Excel导入模板

**请求参数**:
- `moduleType` (String): 模块类型，必填

**字段说明**:
- `moduleType`: 模块类型，决定下载哪个模板文件

**请求示例**:
```bash
curl -X GET "http://localhost:20001/data-mange/import/download-template?moduleType=Y1" \
  -H "Accept: application/octet-stream" \
  --output Y1_template.xlsx
```

### 1.2 分包商外部成本管理

#### 1.2.1 新增外部成本
**接口名称**: 新增分包商外部成本  
**接口路径**: `POST /data-mange/subcon/external-cost/add`  
**接口作用**: 为分包商PO添加额外的外部成本

**请求参数**:
```json
{
  "rootDataId": 123456,
  "dataId": 789012,
  "data": {
    "ExternalCost": "5000.00",
    "CostDescription": "额外运输费用",
    "CostDate": "2024-01-15"
  }
}
```

**字段说明**:
- `rootDataId`: 分包商PO的主记录ID
- `dataId`: 外部成本记录ID（新增时可为空）
- `ExternalCost`: 外部成本金额
- `CostDescription`: 成本描述
- `CostDate`: 成本发生日期

**请求示例**:
```bash
curl -X POST "http://localhost:20001/data-mange/subcon/external-cost/add" \
  -H "Content-Type: application/json" \
  -d '{
    "rootDataId": 123456,
    "data": {
      "ExternalCost": "5000.00",
      "CostDescription": "额外运输费用",
      "CostDate": "2024-01-15"
    }
  }'
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "外部成本添加成功",
  "data": true,
  "success": true
}
```

### 1.3 Y3模块数据处理

#### 1.3.1 Y3数据更新
**接口名称**: Y3模块数据更新  
**接口路径**: `POST /data-mange/import/update-y3`  
**接口作用**: 批量更新Y3模块（生产力报告）数据

**请求参数**:
- `file` (MultipartFile): Excel文件，必填
- `key` (String): 进度跟踪键，必填

**字段说明**:
- `file`: 包含Y3更新数据的Excel文件
- `key`: 用于Redis中跟踪处理进度的唯一键

**请求示例**:
```bash
curl -X POST "http://localhost:20001/data-mange/import/update-y3" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/y3_update.xlsx" \
  -F "key=y3_update_20240115_001"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "Y3数据更新完成",
  "data": [
    {
      "index": 0,
      "status": "SUCCESS",
      "importData": {...},
      "wrongReasons": []
    },
    {
      "index": 1,
      "status": "FAILED",
      "importData": {...},
      "wrongReasons": ["时间锁定期间不能修改"]
    }
  ],
  "success": true
}
```

#### 1.3.2 查询Y3导入进度
**接口名称**: 查询Y3导入进度  
**接口路径**: `GET /data-mange/import/query-progress-y3`  
**接口作用**: 查询Y3数据处理的实时进度

**请求参数**:
- `key` (String): 进度跟踪键，必填

**请求示例**:
```bash
curl -X GET "http://localhost:20001/data-mange/import/query-progress-y3?key=y3_update_20240115_001"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "progress": 75.5,
    "processedCount": 755,
    "totalCount": 1000,
    "status": "PROCESSING",
    "results": [...]
  },
  "success": true
}
```

### 1.4 批量删除功能

#### 1.4.1 批量删除数据
**接口名称**: 批量删除数据  
**接口路径**: `POST /data-mange/delete/batch`  
**接口作用**: 通过Excel文件批量删除指定的数据记录

**请求参数**:
- `file` (MultipartFile): 包含删除列表的Excel文件，必填
- `key` (String): 进度跟踪键，必填

**字段说明**:
- `file`: Excel文件，包含要删除的数据标识信息
- `key`: 用于跟踪删除进度的唯一键

**请求示例**:
```bash
curl -X POST "http://localhost:20001/data-mange/delete/batch" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/delete_list.xlsx" \
  -F "key=delete_batch_20240115_001"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "批量删除任务已提交",
  "data": {
    "taskId": "delete_task_123456",
    "totalCount": 50,
    "status": "PROCESSING"
  },
  "success": true
}
```

---

## 2. BI面板模块 (BiPanelController)

### 2.1 收支状态统计

#### 2.1.1 收支统计查询
**接口名称**: 收支状态统计查询  
**接口路径**: `GET /Bi-panel/income-expenditure-status`  
**接口作用**: 获取项目在指定时间范围内的收入和支出统计数据

**请求参数**:
- `startTime` (Date): 开始时间，格式：yyyy-MM-dd，可选
- `endTime` (Date): 结束时间，格式：yyyy-MM-dd，可选
- `projectId` (Long): 项目ID，必填
- `type` (String): 统计类型，必填
- `projectCycle` (Integer): 项目周期，必填，0=项目完整周期，1=自选时间范围

**字段说明**:
- `startTime/endTime`: 统计时间范围，当projectCycle=1时必填
- `projectId`: 要统计的项目ID
- `type`: 统计类型，影响统计的数据范围
- `projectCycle`: 周期类型，决定是统计完整项目周期还是指定时间段

**请求示例**:
```bash
curl -X GET "http://localhost:20001/Bi-panel/income-expenditure-status?projectId=123&type=all&projectCycle=1&startTime=2024-01-01&endTime=2024-12-31"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": [
    {
      "projectName": "海外项目A",
      "siteItemValueTotal": "1000000.00",
      "poItemValueTotal": "950000.00",
      "subconTotalAmountPoItem": "500000.00",
      "subconTotalAmountPaid": "450000.00",
      "totalInvoiceAmount": "800000.00",
      "productivityAmount": "750000.00"
    }
  ],
  "success": true
}
```

#### 2.1.2 收支统计导出
**接口名称**: 收支统计数据导出
**接口路径**: `POST /Bi-panel/income-expenditure-export`
**接口作用**: 导出收支统计数据为Excel文件

**请求参数**: 同收支统计查询接口

**请求示例**:
```bash
curl -X POST "http://localhost:20001/Bi-panel/income-expenditure-export" \
  -H "Content-Type: application/json" \
  -d '{
    "projectId": 123,
    "type": "all",
    "projectCycle": 1,
    "startTime": "2024-01-01",
    "endTime": "2024-12-31"
  }' \
  --output income_expenditure_report.xlsx
```

### 2.2 站点统计

#### 2.2.1 站点关闭率统计
**接口名称**: 站点条目统计
**接口路径**: `GET /Bi-panel/site-item-statistics`
**接口作用**: 统计各个项目下站点的关闭情况

**请求参数**:
- `appId` (String): 应用ID，可选
- `projectId` (String): 项目ID，可选

**字段说明**:
- `appId`: 应用标识，用于权限控制
- `projectId`: 项目ID，为空时统计所有有权限的项目

**请求示例**:
```bash
curl -X GET "http://localhost:20001/Bi-panel/site-item-statistics?projectId=123"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": [
    {
      "projectName": "海外项目A",
      "total": 100,
      "close": 75,
      "unclose": 20,
      "invalid": 5,
      "closeRate": 75.0
    }
  ],
  "success": true
}
```

### 2.3 金额统计

#### 2.3.1 总金额统计
**接口名称**: 总金额统计
**接口路径**: `GET /Bi-panel/total-amount`
**接口作用**: 统计指定时间范围内的各种金额（PO金额、产值决算金额、开票金额）

**请求参数**:
- `projectId` (String): 项目ID，可选
- `dateStrStart` (String): 开始日期，格式：yyyy-MM-dd，可选
- `dateStrEnd` (String): 结束日期，格式：yyyy-MM-dd，可选
- `dateType` (String): 日期类型，可选

**字段说明**:
- `projectId`: 项目ID，为空时统计所有项目
- `dateStrStart/dateStrEnd`: 统计时间范围
- `dateType`: 日期类型，可选值：
  - `PO_Received_date`: 按PO接收日期统计
  - `Productivity_report_date`: 按生产力报告日期统计
  - `KPI_Archive_date`: 按KPI归档日期统计
  - `Invoice_Date`: 按开票日期统计

**请求示例**:
```bash
curl -X GET "http://localhost:20001/Bi-panel/total-amount?projectId=123&dateStrStart=2024-01-01&dateStrEnd=2024-12-31&dateType=PO_Received_date"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "y2Amount": {
      "totalAmount": "1000000.00",
      "currency": "USD"
    },
    "y6Amount": {
      "totalAmount": "950000.00",
      "currency": "USD"
    },
    "y9Amount": {
      "totalAmount": "800000.00",
      "currency": "USD"
    }
  },
  "success": true
}
```

### 2.4 报表数据查询

#### 2.4.1 综合报表查询
**接口名称**: 综合报表数据查询
**接口路径**: `GET /Bi-panel/get-report-filed`
**接口作用**: 获取综合报表数据，关联多个模块的数据

**请求参数**:
- `page` (Integer): 页码，默认1
- `projectId` (String): 项目ID，可选
- `dateStrStart` (String): 开始日期，可选
- `dateStrEnd` (String): 结束日期，可选
- `dateType` (String): 日期类型，可选
- `area` (String): 地区，可选
- `projectIds` (String): 项目ID列表，逗号分隔，可选
- `moduleTypes` (String): 模块类型列表，逗号分隔，可选
- `nation` (String): 国家，可选
- `unId` (String): 唯一标识，可选

**请求示例**:
```bash
curl -X GET "http://localhost:20001/Bi-panel/get-report-filed?page=1&projectId=123&dateStrStart=2024-01-01&dateStrEnd=2024-12-31"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "projectCode": "PRJ001",
        "projectName": "海外项目A",
        "region": "Asia",
        "siteId": "SITE001",
        "siteName": "站点A",
        "siteValue": "10000.00",
        "poValue": "9500.00",
        "invoiceAmount": "8000.00"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  },
  "success": true
}
```

#### 2.4.2 报表数据导出
**接口名称**: 报表数据导出
**接口路径**: `POST /Bi-panel/report-export`
**接口作用**: 导出综合报表数据为Excel文件

**请求参数**: 同综合报表查询接口

**请求示例**:
```bash
curl -X POST "http://localhost:20001/Bi-panel/report-export" \
  -H "Content-Type: application/json" \
  -d '{
    "projectId": "123",
    "dateStrStart": "2024-01-01",
    "dateStrEnd": "2024-12-31"
  }' \
  --output comprehensive_report.xlsx
```

#### 2.4.3 切换查询方式
**接口名称**: 切换BI3查询方式
**接口路径**: `GET /Bi-panel/switchBi3Query`
**接口作用**: 在新旧两种查询方式之间切换

**请求示例**:
```bash
curl -X GET "http://localhost:20001/Bi-panel/switchBi3Query"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "切换成功",
  "data": null,
  "success": true
}
```

---

## 3. 数据调整模块 (AdjustExcelController)

### 3.1 Y1模块数据调整

#### 3.1.1 Y1数据导出
**接口名称**: Y1数据导出
**接口路径**: `POST /adjust-excel/export/update-y1`
**接口作用**: 导出Y1模块数据供用户修改

**请求参数**:
```json
{
  "conditions": [
    {
      "name": "YPTT_Project_code",
      "value": "PRJ001",
      "symbol": "eq"
    },
    {
      "name": "Region",
      "value": "Asia",
      "symbol": "eq"
    }
  ],
  "page": 1,
  "size": 1000
}
```

**字段说明**:
- `conditions`: 查询条件数组
  - `name`: 字段名
  - `value`: 字段值
  - `symbol`: 比较符号（eq=等于, gt=大于, lt=小于, range=范围）
- `page/size`: 分页参数

**请求示例**:
```bash
curl -X POST "http://localhost:20001/adjust-excel/export/update-y1" \
  -H "Content-Type: application/json" \
  -d '{
    "conditions": [
      {
        "name": "YPTT_Project_code",
        "value": "PRJ001",
        "symbol": "eq"
      }
    ],
    "page": 1,
    "size": 1000
  }' \
  --output y1_data_for_update.xlsx
```

#### 3.1.2 Y1数据更新
**接口名称**: Y1数据批量更新
**接口路径**: `POST /adjust-excel/import/update-y1`
**接口作用**: 批量更新Y1模块数据

**请求参数**:
- `file` (MultipartFile): 包含修改数据的Excel文件，必填
- `key` (String): 进度跟踪键，必填

**请求示例**:
```bash
curl -X POST "http://localhost:20001/adjust-excel/import/update-y1" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/y1_updated_data.xlsx" \
  -F "key=y1_update_20240115_001"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "Y1数据更新完成",
  "data": [
    {
      "index": 0,
      "status": "SUCCESS",
      "importData": {
        "id": "123456",
        "quantity": "100",
        "Unit_price": "1000.00",
        "Site_value": "100000.00"
      },
      "wrongReasons": []
    }
  ],
  "success": true
}
```

#### 3.1.3 查询Y1更新进度
**接口名称**: 查询Y1更新进度
**接口路径**: `GET /adjust-excel/import/query-progress-y1`
**接口作用**: 查询Y1数据更新的实时进度

**请求参数**:
- `key` (String): 进度跟踪键，必填

**请求示例**:
```bash
curl -X GET "http://localhost:20001/adjust-excel/import/query-progress-y1?key=y1_update_20240115_001"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "progress": 100.0,
    "processedCount": 1000,
    "totalCount": 1000,
    "status": "COMPLETED",
    "results": [...]
  },
  "success": true
}
```

#### 3.1.4 下载Y1调整模板
**接口名称**: 下载Y1调整模板
**接口路径**: `GET /adjust-excel/download-template-y1`
**接口作用**: 下载Y1数据调整的Excel模板

**请求示例**:
```bash
curl -X GET "http://localhost:20001/adjust-excel/download-template-y1" \
  --output y1_adjust_template.xlsx
```

### 3.2 Y2模块数据调整

#### 3.2.1 Y2数据导出
**接口名称**: Y2数据导出
**接口路径**: `POST /adjust-excel/export/update-y2`
**接口作用**: 导出Y2模块（PO条目）数据供用户修改

**请求参数**: 同Y1数据导出接口

**请求示例**:
```bash
curl -X POST "http://localhost:20001/adjust-excel/export/update-y2" \
  -H "Content-Type: application/json" \
  -d '{
    "conditions": [
      {
        "name": "YPTT_Project_code",
        "value": "PRJ001",
        "symbol": "eq"
      }
    ],
    "page": 1,
    "size": 2000
  }' \
  --output y2_data_for_update.xlsx
```

#### 3.2.2 Y2数据更新
**接口名称**: Y2数据批量更新
**接口路径**: `POST /adjust-excel/import/update-y2`
**接口作用**: 批量更新Y2模块数据，涉及多表联动

**请求参数**:
- `file` (MultipartFile): 包含修改数据的Excel文件，必填
- `key` (String): 进度跟踪键，必填

**请求示例**:
```bash
curl -X POST "http://localhost:20001/adjust-excel/import/update-y2" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/y2_updated_data.xlsx" \
  -F "key=y2_update_20240115_001"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "Y2数据更新完成",
  "data": [
    {
      "index": 0,
      "status": "SUCCESS",
      "importData": {
        "id": "123456",
        "quantity": "100",
        "Unit_price": "1000.00",
        "Milestone_1st": "25000.00"
      },
      "wrongReasons": []
    }
  ],
  "success": true
}
```

### 3.3 Y4模块数据调整

#### 3.3.1 Y4数据导出
**接口名称**: Y4数据导出
**接口路径**: `POST /adjust-excel/export/update-y4`
**接口作用**: 导出Y4模块（分包商PO条目）数据供用户修改

#### 3.3.2 Y4数据更新
**接口名称**: Y4数据批量更新
**接口路径**: `POST /adjust-excel/import/update-y4`
**接口作用**: 批量更新Y4模块数据

**请求参数**: 同Y1/Y2数据更新接口

---

## 4. 工作流模块

### 4.1 工作流提交拦截 (WorkFlowSubmitInterceptController)

#### 4.1.1 综合报销提交拦截
**接口名称**: 综合报销申请提交拦截
**接口路径**: `POST /workflow/fs/submit-intercept/other-reimbursement`
**接口作用**: 在综合报销申请提交前进行业务规则检查

**请求参数**:
```json
{
  "requestId": "REQ20240115001",
  "tableInfo": {
    "bxhj": "10000.00",
    "zfhj": "8000.00",
    "cxhj": "2000.00",
    "sub": [
      {
        "modelName": "com_expense_dt1",
        "fphm": "INV20240115001",
        "je": "5000.00"
      },
      {
        "modelName": "com_expense_dt2",
        "zfje": "8000.00"
      }
    ]
  }
}
```

**字段说明**:
- `requestId`: 工作流请求ID
- `tableInfo`: 表单数据
  - `bxhj`: 报销合计金额
  - `zfhj`: 支付合计金额
  - `cxhj`: 冲销合计金额
  - `sub`: 子表数据数组
    - `com_expense_dt1`: 费用明细子表
    - `com_expense_dt2`: 支付明细子表
    - `com_expense_dt3`: 冲销明细子表

**请求示例**:
```bash
curl -X POST "http://localhost:20001/workflow/fs/submit-intercept/other-reimbursement" \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "REQ20240115001",
    "tableInfo": {
      "bxhj": "10000.00",
      "zfhj": "8000.00",
      "cxhj": "2000.00",
      "sub": [
        {
          "modelName": "com_expense_dt1",
          "fphm": "INV20240115001",
          "je": "5000.00"
        }
      ]
    }
  }'
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "验证通过",
  "data": null,
  "success": true
}
```

**错误响应示例**:
```json
{
  "code": 1,
  "msg": "支付金额+冲销金额必须等于报销金额",
  "data": null,
  "success": false
}
```

#### 4.1.2 差旅报销提交拦截
**接口名称**: 差旅报销申请提交拦截
**接口路径**: `POST /workflow/fs/submit-intercept/travel-Reimbursement`
**接口作用**: 在差旅报销申请提交前进行业务规则检查

**请求参数**: 类似综合报销，但字段名略有不同
```json
{
  "requestId": "REQ20240115002",
  "tableInfo": {
    "bxhj": "5000.00",
    "zfhjje": "4000.00",
    "cxhjje": "1000.00"
  }
}
```

#### 4.1.3 财务付款申请提交拦截
**接口名称**: 财务付款申请提交拦截
**接口路径**: `POST /workflow/fs/submit-intercept/financial-payments`
**接口作用**: 在财务付款申请提交前进行检查

**请求示例**:
```bash
curl -X POST "http://localhost:20001/workflow/fs/submit-intercept/financial-payments" \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "REQ20240115003",
    "tableInfo": {
      "zfje": "50000.00",
      "skr": "供应商A"
    }
  }'
```

### 4.2 工作流结束拦截 (WorkFlowEndInterceptController)

#### 4.2.1 综合报销结束拦截
**接口名称**: 综合报销审批结束处理
**接口路径**: `POST /workflow/end-intercept/other-reimbursement`
**接口作用**: 综合报销审批通过后的后续业务处理

**请求参数**: 同提交拦截接口

**请求示例**:
```bash
curl -X POST "http://localhost:20001/workflow/end-intercept/other-reimbursement" \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "REQ20240115001",
    "tableInfo": {
      "bxhj": "10000.00",
      "zfhj": "8000.00",
      "cxhj": "2000.00",
      "sub": [
        {
          "modelName": "com_expense_dt1",
          "fphm": "INV20240115001",
          "je": "5000.00"
        },
        {
          "modelName": "com_expense_dt2",
          "zfje": "8000.00"
        }
      ]
    }
  }'
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "处理完成",
  "data": {
    "pendingPaymentBills": 1,
    "reimburseInvoices": 2,
    "writeOffAmount": "2000.00"
  },
  "success": true
}
```

#### 4.2.2 预借付款申请结束拦截
**接口名称**: 预借付款申请审批结束处理
**接口路径**: `POST /workflow/end-intercept/advance-payments-and-personal-loans`
**接口作用**: 借款申请审批通过后创建付款申请单

**请求参数**:
```json
{
  "requestId": "REQ20240115004",
  "tableInfo": {
    "jksqsy": "出差借款",
    "money_type": "CNY",
    "sub": [
      {
        "modelName": "person_loan_dt2",
        "zfje": "10000.00",
        "skr": "张三"
      }
    ]
  }
}
```

### 4.3 用户入职工作流 (WorkFlowUserEntryController)

#### 4.3.1 新增用户
**接口名称**: 新增系统用户
**接口路径**: `POST /workflow-user-entry/add`
**接口作用**: 通过工作流审批后自动创建系统用户账号

**请求参数**:
```json
{
  "requestId": "REQ20240115005",
  "tableInfo": {
    "phone": "13800138000",
    "username": "zhangsan",
    "name": "张三",
    "email": "<EMAIL>",
    "dept": "[\"1001\", \"1002\"]",
    "role": "[\"2001\", \"2002\"]",
    "post": "[\"3001\"]"
  }
}
```

**字段说明**:
- `phone`: 手机号，必填，用作登录账号
- `username`: 用户名，必填
- `name`: 真实姓名，必填
- `email`: 邮箱地址，可选
- `dept`: 部门ID数组，JSON字符串格式
- `role`: 角色ID数组，JSON字符串格式
- `post`: 岗位ID数组，JSON字符串格式

**请求示例**:
```bash
curl -X POST "http://localhost:20001/workflow-user-entry/add" \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "REQ20240115005",
    "tableInfo": {
      "phone": "13800138000",
      "username": "zhangsan",
      "name": "张三",
      "email": "<EMAIL>",
      "dept": "[\"1001\"]",
      "role": "[\"2001\"]",
      "post": "[\"3001\"]"
    }
  }'
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "用户创建成功",
  "data": {
    "userId": 123456,
    "username": "zhangsan",
    "created": true
  },
  "success": true
}
```

---

## 5. 警告信息模块 (WarningInfoController)

### 5.1 警告信息查询

#### 5.1.1 分页查询警告
**接口名称**: 分页查询警告信息
**接口路径**: `GET /warning/page`
**接口作用**: 分页查询系统中的警告信息

**请求参数**:
- `size` (Integer): 每页大小，默认10
- `cur` (Integer): 当前页码，默认1
- `projectCode` (String): 项目代码，可选
- `projectName` (String): 项目名称，可选
- `uniquenessField` (String): 唯一标识字段，可选
- `warnType` (List<String>): 警告类型列表，可选
- `startTime` (Date): 开始时间，可选
- `endTime` (Date): 结束时间，可选

**字段说明**:
- `warnType`: 警告类型，可选值：
  - `Site_Delay`: 站点延期警告
  - `Amount_Error`: 金额错误警告
  - `PO_Delay`: PO延期警告
  - `Payment_Overdue`: 付款超期警告

**请求示例**:
```bash
curl -X GET "http://localhost:20001/warning/page?size=20&cur=1&projectCode=PRJ001&warnType=Site_Delay,Amount_Error"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "warningType": "Site_Delay",
        "warningMsg": "Tips: Sites that have been assigned need to be delivered in time!",
        "projectName": "海外项目A",
        "uniquenessField": "PRJ001_Asia_SITE001_Phase1_ITEM001",
        "warningDataId": 123456,
        "createTime": "2024-01-15 10:30:00"
      }
    ],
    "total": 100,
    "size": 20,
    "current": 1,
    "pages": 5
  },
  "success": true
}
```

#### 5.1.2 警告统计
**接口名称**: 警告信息统计
**接口路径**: `GET /warning/statistics`
**接口作用**: 统计各种类型警告的数量

**请求参数**:
- `projectCode` (String): 项目代码，可选

**请求示例**:
```bash
curl -X GET "http://localhost:20001/warning/statistics?projectCode=PRJ001"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "统计成功",
  "data": {
    "totalWarnings": 150,
    "siteDelayCount": 50,
    "amountErrorCount": 30,
    "poDelayCount": 40,
    "paymentOverdueCount": 30
  },
  "success": true
}
```

#### 5.1.3 警告信息导出
**接口名称**: 警告信息导出
**接口路径**: `GET /warning/export`
**接口作用**: 导出警告信息为Excel文件

**请求参数**: 同分页查询接口

**请求示例**:
```bash
curl -X GET "http://localhost:20001/warning/export?projectCode=PRJ001&warnType=Site_Delay" \
  --output warning_report.xlsx
```

### 5.2 警告信息更新

#### 5.2.1 更新警告状态
**接口名称**: 更新警告状态
**接口路径**: `GET /warning/update`
**接口作用**: 定期检查业务数据并更新警告状态（内部接口）

**请求参数**:
- `listCode` (List<String>): 项目代码列表，可选
- `yptt-task-token` (String): 任务令牌，必填

**字段说明**:
- `listCode`: 要检查的项目代码列表，为空时检查所有项目
- `yptt-task-token`: 内部任务调用的验证令牌

**请求示例**:
```bash
curl -X GET "http://localhost:20001/warning/update?listCode=PRJ001,PRJ002&yptt-task-token=your-task-token"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "警告更新任务已提交",
  "data": true,
  "success": true
}
```

---

## 6. 任务管理模块 (TaskController)

### 6.1 台账更新任务

#### 6.1.1 更新项目台账
**接口名称**: 更新项目台账
**接口路径**: `GET /task/update-project-standing-book`
**接口作用**: 定期更新各种台账数据（内部接口）

**请求参数**:
- `yptt-task-token` (String): 任务令牌，必填

**请求示例**:
```bash
curl -X GET "http://localhost:20001/task/update-project-standing-book?yptt-task-token=your-task-token"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "台账更新完成",
  "data": {
    "allSuccessful": true,
    "projectStandingBookUpdated": 150,
    "siteStandingBookUpdated": 500,
    "poStandingBookUpdated": 300,
    "subconStandingBookUpdated": 200
  },
  "success": true
}
```

#### 6.1.2 检查导入任务
**接口名称**: 检查导入任务状态
**接口路径**: `GET /task/check-import-task`
**接口作用**: 检查和清理超时的导入任务（内部接口）

**请求参数**:
- `yptt-task-token` (String): 任务令牌，必填

**请求示例**:
```bash
curl -X GET "http://localhost:20001/task/check-import-task?yptt-task-token=your-task-token"
```

#### 6.1.3 收入修正任务
**接口名称**: 收入修正
**接口路径**: `GET /task/income-correction-task`
**接口作用**: 修正Y6模块中的收入数据（内部接口）

**请求示例**:
```bash
curl -X GET "http://localhost:20001/task/income-correction-task?yptt-task-token=your-task-token"
```

#### 6.1.4 更新站点状态
**接口名称**: 更新站点状态
**接口路径**: `GET /task/update-site-state`
**接口作用**: 根据业务规则自动更新站点状态（内部接口）

**请求示例**:
```bash
curl -X GET "http://localhost:20001/task/update-site-state?yptt-task-token=your-task-token"
```

---

## 7. 财务支付模块 (PaymentApplicationController)

### 7.1 付款状态管理

#### 7.1.1 新增后完成付款
**接口名称**: 新增付款后更新状态
**接口路径**: `POST /fs/payment-application/payment-end-by-after-add`
**接口作用**: 新增付款明细后自动检查是否付清（内部接口）

**请求参数**:
```json
{
  "mainDataId": 123456,
  "dataId": [789012],
  "data": {
    "payment_money": "50000.00",
    "payment_date": "2024-01-15",
    "payment_method": "银行转账"
  }
}
```

**字段说明**:
- `mainDataId`: 付款申请单主记录ID
- `dataId`: 付款明细记录ID数组
- `data`: 付款数据
  - `payment_money`: 付款金额
  - `payment_date`: 付款日期
  - `payment_method`: 付款方式

**请求示例**:
```bash
curl -X POST "http://localhost:20001/fs/payment-application/payment-end-by-after-add?yptt-task-token=your-task-token" \
  -H "Content-Type: application/json" \
  -d '{
    "mainDataId": 123456,
    "dataId": [789012],
    "data": {
      "payment_money": "50000.00",
      "payment_date": "2024-01-15"
    }
  }'
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "付款状态更新成功",
  "data": {
    "paymentCompleted": true,
    "remainingAmount": "0.00"
  },
  "success": true
}
```

#### 7.1.2 删除后完成付款
**接口名称**: 删除付款后更新状态
**接口路径**: `POST /fs/payment-application/payment-end-by-after-del`
**接口作用**: 删除付款明细后重新计算付款状态（内部接口）

**请求参数**: 同新增后完成付款接口

#### 7.1.3 更新后完成付款
**接口名称**: 更新付款后更新状态
**接口路径**: `POST /fs/payment-application/payment-end-by-after-update`
**接口作用**: 修改付款明细后重新计算付款状态（内部接口）

**请求参数**: 同新增后完成付款接口

---

## 8. 数据连接器模块 (Connector2codeController)

### 8.1 数据变化触发器

#### 8.1.1 Y1数据连接器
**接口名称**: Y1数据变化触发器
**接口路径**: `POST /connector2code/y1`
**接口作用**: Y1数据变化时自动触发相关计算（内部接口）

**请求参数**:
```json
{
  "dataId": 123456,
  "data": {
    "quantity": "100",
    "Unit_price": "1000.00",
    "Currency": "USD"
  },
  "operation": "UPDATE"
}
```

**字段说明**:
- `dataId`: 数据记录ID
- `data`: 变化的数据
- `operation`: 操作类型（INSERT/UPDATE/DELETE）

**请求示例**:
```bash
curl -X POST "http://localhost:20001/connector2code/y1?yptt-task-token=your-task-token" \
  -H "Content-Type: application/json" \
  -d '{
    "dataId": 123456,
    "data": {
      "quantity": "100",
      "Unit_price": "1000.00",
      "Currency": "USD"
    },
    "operation": "UPDATE"
  }'
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "Y1数据连接器处理完成",
  "data": true,
  "success": true
}
```

#### 8.1.2 Y2数据连接器
**接口名称**: Y2数据变化触发器
**接口路径**: `POST /connector2code/y2`
**接口作用**: Y2数据变化时自动触发相关计算（内部接口）

#### 8.1.3 Y3数据连接器
**接口名称**: Y3数据变化触发器
**接口路径**: `POST /connector2code/y3`
**接口作用**: Y3数据变化时自动触发相关计算（内部接口）

#### 8.1.4 Y4数据连接器
**接口名称**: Y4数据变化触发器
**接口路径**: `POST /connector2code/y4`
**接口作用**: Y4数据变化时自动触发相关计算（内部接口）

---

## 9. 货币兑换模块 (MoneyExchangeController)

### 9.1 货币转换

#### 9.1.1 货币兑换
**接口名称**: 货币汇率转换
**接口路径**: `GET /money-exchange/transfer`
**接口作用**: 提供货币之间的汇率转换计算

**请求参数**:
- `moneyInput` (String): 源货币代码，必填
- `moneyOutput` (String): 目标货币代码，必填
- `Amount` (BigDecimal): 转换金额，必填

**字段说明**:
- `moneyInput`: 源货币，如：USD、CNY、EUR等
- `moneyOutput`: 目标货币，如：USD、CNY、EUR等
- `Amount`: 要转换的金额，必须为正数

**请求示例**:
```bash
curl -X GET "http://localhost:20001/money-exchange/transfer?moneyInput=USD&moneyOutput=CNY&Amount=1000"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "转换成功",
  "data": {
    "sourceAmount": "1000.00",
    "sourceCurrency": "USD",
    "targetAmount": "7200.00",
    "targetCurrency": "CNY",
    "exchangeRate": "7.2000",
    "convertTime": "2024-01-15 14:30:00"
  },
  "success": true
}
```

**错误响应示例**:
```json
{
  "code": 1,
  "msg": "找不到该货币对的汇率信息",
  "data": null,
  "success": false
}
```

---

## 10. 数据锁定模块 (LockDataTimeController)

### 10.1 数据锁定管理

#### 10.1.1 锁定数据
**接口名称**: 锁定时间段数据
**接口路径**: `POST /lock-data-time/lock`
**接口作用**: 锁定特定时间段的数据，防止修改

**请求参数**:
```json
{
  "projectCode": "PRJ001",
  "lockStartDate": "2024-01-01",
  "lockEndDate": "2024-01-31",
  "lockReason": "月度数据已确认，禁止修改",
  "moduleTypes": ["Y1", "Y2", "Y3"]
}
```

**字段说明**:
- `projectCode`: 项目代码
- `lockStartDate`: 锁定开始日期
- `lockEndDate`: 锁定结束日期
- `lockReason`: 锁定原因
- `moduleTypes`: 要锁定的模块类型数组

**请求示例**:
```bash
curl -X POST "http://localhost:20001/lock-data-time/lock" \
  -H "Content-Type: application/json" \
  -d '{
    "projectCode": "PRJ001",
    "lockStartDate": "2024-01-01",
    "lockEndDate": "2024-01-31",
    "lockReason": "月度数据已确认，禁止修改",
    "moduleTypes": ["Y1", "Y2", "Y3"]
  }'
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "数据锁定成功",
  "data": {
    "lockId": 789012,
    "lockedRecords": 1500
  },
  "success": true
}
```

#### 10.1.2 解锁数据
**接口名称**: 解锁时间段数据
**接口路径**: `POST /lock-data-time/unlock`
**接口作用**: 解除数据锁定，允许重新修改

**请求参数**:
```json
{
  "lockId": 789012,
  "unlockReason": "需要修正数据错误"
}
```

#### 10.1.3 获取锁定列表
**接口名称**: 获取数据锁定列表
**接口路径**: `GET /lock-data-time/list`
**接口作用**: 查看当前的数据锁定情况

**请求参数**:
- `projectCode` (String): 项目代码，可选
- `page` (Integer): 页码，默认1
- `size` (Integer): 每页大小，默认10

**请求示例**:
```bash
curl -X GET "http://localhost:20001/lock-data-time/list?projectCode=PRJ001&page=1&size=10"
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "lockId": 789012,
        "projectCode": "PRJ001",
        "lockStartDate": "2024-01-01",
        "lockEndDate": "2024-01-31",
        "lockReason": "月度数据已确认，禁止修改",
        "moduleTypes": ["Y1", "Y2", "Y3"],
        "lockTime": "2024-02-01 09:00:00",
        "lockUser": "admin",
        "status": "LOCKED"
      }
    ],
    "total": 5,
    "size": 10,
    "current": 1,
    "pages": 1
  },
  "success": true
}
```

#### 10.1.4 设置操作时间段
**接口名称**: 设置允许操作的时间段
**接口路径**: `POST /lock-data-time/abilityOperateDate`
**接口作用**: 设置系统允许操作数据的时间范围

**请求参数**:
```json
{
  "projectCode": "PRJ001",
  "operateStartDate": "2024-01-01",
  "operateEndDate": "2024-12-31",
  "description": "2024年度数据操作期"
}
```

---

## 11. 金额调整模块 (AdjustController)

### 11.1 数据调整触发

#### 11.1.1 Y1金额调整
**接口名称**: Y1站点条目金额调整
**接口路径**: `POST /adjust/amount-siteItem`
**接口作用**: Y1数据变化时触发相关业务逻辑（内部接口）

**请求参数**:
```json
{
  "dataId": 123456,
  "oldData": {
    "quantity": "80",
    "Unit_price": "1000.00",
    "Site_value": "80000.00"
  },
  "newData": {
    "quantity": "100",
    "Unit_price": "1000.00",
    "Site_value": "100000.00"
  }
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:20001/adjust/amount-siteItem?yptt-task-token=your-task-token" \
  -H "Content-Type: application/json" \
  -d '{
    "dataId": 123456,
    "oldData": {
      "quantity": "80",
      "Unit_price": "1000.00"
    },
    "newData": {
      "quantity": "100",
      "Unit_price": "1000.00"
    }
  }'
```

#### 11.1.2 Y2金额调整
**接口名称**: Y2 PO条目金额调整
**接口路径**: `POST /adjust/amount-poItem`
**接口作用**: Y2数据变化时触发相关业务逻辑（内部接口）

#### 11.1.3 Y4金额调整
**接口名称**: Y4分包商PO条目金额调整
**接口路径**: `POST /adjust/amount-subPoItem`
**接口作用**: Y4数据变化时触发相关业务逻辑（内部接口）

---

## 接口使用注意事项

### 1. 权限要求
- 大部分接口需要登录认证
- 部分接口需要特定角色权限（管理员、PD、PM等）
- 内部接口需要`yptt-task-token`参数验证

### 2. 数据格式
- 所有日期格式统一为：`yyyy-MM-dd`
- 金额字段统一使用字符串格式，保留2位小数
- JSON数组字段需要使用字符串格式传递

### 3. 错误处理
- 接口返回统一的错误格式
- 错误码0表示成功，非0表示失败
- 详细错误信息在`msg`字段中

### 4. 性能考虑
- 大批量数据操作建议分批处理
- 使用进度查询接口跟踪长时间运行的任务
- 导出功能会直接返回文件流

### 5. 安全考虑
- 敏感操作需要二次确认
- 数据锁定期间的数据不能修改
- 内部接口不对外暴露，需要特殊令牌验证
