# YPTT系统数据使用情况分析

## 概述

本文档详细分析YPTT系统中各个模块导入的数据在系统中的具体使用场景，帮助您快速理解数据流向和业务价值。

## 1. 数据模块概览

### 1.1 核心数据模块

| 模块 | 中文名称 | 数据表 | 主要用途 |
|------|----------|--------|----------|
| Y1 | 站点条目管理 | memm_e648652640b44b2092c93e1742e6171b | 项目站点基础信息和价值统计 |
| Y2 | 采购订单条目管理 | memm_f37920ed96f942fb8f4b1bf16f79e39c | PO订单信息和采购金额统计 |
| Y3 | 生产力报告管理 | memm_5c8c376451894fdfb7e751c91da66f16 | 项目进度和完成情况跟踪 |
| Y4 | 分包商PO条目管理 | memm_157ac31323c34d46920918117cb577ad | 分包商订单和成本管理 |
| Y6 | 决算管理 | - | 项目产值和决算金额统计 |
| Y8 | 分包商支付管理 | memm_f562b5dbd2be42d99c4992dd2668ed74 | 分包商付款记录和状态 |
| Y9 | 开票管理 | - | 发票信息和开票金额统计 |

## 2. Y1模块（站点条目管理）数据使用分析

### 2.1 数据字段定义
```java
// Y1模块字段列表
List<String> Y1_FIELDS = Arrays.asList(
    "Department",           // 部门
    "YPTT_Project_code",   // 项目代码
    "YPTT_Project_name",   // 项目名称
    "Region",              // 区域
    "Area",                // 地区
    "Site_ID",             // 站点ID
    "Site_Name",           // 站点名称
    "site_allocation_date", // 站点分配日期
    "Phase",               // 阶段
    "Type_of_service",     // 服务类型
    "Site_Model",          // 站点模型
    "Item_code",           // 项目代码
    "BOQ_item",            // BOQ项目
    "quantity",            // 数量
    "Unit_price",          // 单价
    "Site_value",          // 站点价值
    "Remark",              // 备注
    "re_record",           // 重新记录
    "uniqueness_field"     // 唯一标识字段
);
```

### 2.2 Y1数据的使用场景

#### 2.2.1 BI面板统计
**使用位置**: `BiPanelController.incomeExpenditureStatus()`

```java
// 收支状态统计中使用Y1数据
// 统计站点价值总计 (siteItemValueTotal)
private BigDecimal siteItemValueTotal; // 来源于Y1的Site_value字段汇总
```

**具体查询**: `BasicMapper.incomeExpenditureStatus()`
- 按项目汇总所有站点的价值
- 按时间范围筛选站点分配日期
- 用于计算项目总收入

#### 2.2.2 站点统计分析
**使用位置**: `BiPanelController.siteItemStatistics()`
```java
// 统计站点关闭率
public List<SiteItemStatisticsVO> siteItemStatistics(String appId, String projectId) {
    // 统计各种状态的站点数量：
    // - close: 已关闭站点
    // - unclose: 未关闭站点  
    // - invalid: 无效站点
}
```

#### 2.2.3 综合报表数据
**使用位置**: `BiPanelController.getReportFiled()`
```java
// Y1数据在综合报表中的作用：
// 1. 提供站点基础信息（站点名称、区域、阶段等）
// 2. 提供站点价值数据用于财务统计
// 3. 作为其他模块数据关联的基础
```

#### 2.2.4 警告系统
**使用位置**: `WarningInfoService.updateSiteItemStatus()`
```java
// Y1数据用于生成站点相关警告：
// 1. 站点交付延期警告
// 2. 站点状态异常警告
// 3. 站点价值异常警告
```

#### 2.2.5 数据连接器
**使用位置**: `Connector2codeController.connector2codeY1()`
```java
// 当Y1数据变化时自动触发：
// 1. 重新计算站点价值 (quantity * Unit_price = Site_value)
// 2. 汇率转换（如果涉及外币）
// 3. 更新相关汇总数据
// 4. 触发台账更新
```

## 3. Y2模块（采购订单条目管理）数据使用分析

### 3.1 数据字段定义
```java
// Y2模块字段列表
List<String> Y2_FIELDS = Arrays.asList(
    "PO_Received_date",    // PO接收日期
    "PO_Number",           // PO号码
    "Contract_number",     // 合同号
    "Custom_project_name", // 客户项目名称
    "YPTT_Project_code",   // YPTT项目代码
    "Region",              // 区域
    "Phase",               // 阶段
    "Site_ID",             // 站点ID
    "Site_Name",           // 站点名称
    "Item_code",           // 项目代码
    "BOQ_item",            // BOQ项目
    "quantity",            // 数量
    "Unit_price",          // 单价
    "PO_Value",            // PO价值
    "PO_GAP",              // PO差额
    "Pre_payment",         // 预付款
    "Milestone_1st",       // 第一里程碑
    "Milestone_2nd",       // 第二里程碑
    "Milestone_3rd",       // 第三里程碑
    "Milestone_4th",       // 第四里程碑
    "Remark",              // 备注
    "re_record",           // 重新记录
    "uniqueness_field"     // 唯一标识字段
);
```

### 3.2 Y2数据的使用场景

#### 3.2.1 BI面板金额统计
**使用位置**: `BiPanelController.totalAmount()`
```java
// Y2数据用于PO金额统计
CompletableFuture<AmountY2VO> futureY2 = CompletableFuture.supplyAsync(() -> {
    if (StringUtils.isBlank(dateType) || "PO_Received_date".equals(dateType)) {
        return basicMapper.totalY2(projectId, dateStrStart, dateStrEnd, dateType);
    }
    return new AmountY2VO();
});
```

**具体统计内容**:
- 按PO接收日期统计总PO价值
- 按项目汇总PO金额
- 用于收支分析和财务报表

#### 3.2.2 收支状态统计
**使用位置**: `IncomeAndExpenditureVO.poItemValueTotal`
```java
// Y2数据贡献PO订单价值合计
private BigDecimal poItemValueTotal; // 来源于Y2的PO_Value字段汇总
```

#### 3.2.3 警告系统
**使用位置**: `WarningInfoService.updatePoStatus()`
```java
// Y2数据用于生成PO相关警告：
// 1. PO延期警告（基于PO_Received_date）
// 2. PO金额异常警告（PO_Value与Site_value不匹配）
// 3. 里程碑延期警告
```

#### 3.2.4 综合报表
**使用位置**: `OptimizeBiPanelService.getPODataForReport()`
```java
// Y2数据在报表中提供：
// 1. PO基础信息（PO号、合同号、客户项目名称）
// 2. PO金额数据用于财务分析
// 3. 里程碑信息用于进度跟踪
```

## 4. Y3模块（生产力报告管理）数据使用分析

### 4.1 数据字段定义
```java
// Y3模块字段列表
List<String> Y3_FIELDS = Arrays.asList(
    "YPTT_Project_code",      // YPTT项目代码
    "Region",                 // 区域
    "Site_ID",                // 站点ID
    "Phase",                  // 阶段
    "Item_code",              // 项目代码
    "uniqueness_field",       // 唯一标识字段
    "Site_belong_to",         // 站点归属
    "Team_Leader_DT",         // 团队负责人
    "engineer_DTA_SPV",       // 工程师监督
    "PLO_PC_Others",          // PLO PC其他
    "PIC_PC_PM",              // PIC PC PM
    "Start_Working_date",     // 开工日期
    "Completed_work_date",    // 完工日期
    "air_CI_Report_submit",   // 空中CI报告提交
    "Site_manager_Report",    // 站点经理报告
    "E_ATP_Pass",             // E ATP通过
    "F_PAC_Pass",             // F PAC通过
    "G_FAC",                  // G FAC
    "Remark",                 // 备注
    "re_record"               // 重新记录
);
```

### 4.2 Y3数据的使用场景

#### 4.2.1 项目进度跟踪
**使用位置**: `DataMangeController.updateY3()`
```java
// Y3数据用于跟踪项目执行进度：
// 1. 开工日期 vs 计划开工日期
// 2. 完工日期 vs 计划完工日期
// 3. 各个里程碑的完成情况
```

#### 4.2.2 警告系统
**使用位置**: `WarningInfoService.updateSiteDeliveryStatus()`
```java
// Y3数据用于生成进度相关警告：
// 1. 开工延期警告（Start_Working_date超期）
// 2. 完工延期警告（Completed_work_date超期）
// 3. 报告提交延期警告
```

#### 4.2.3 生产力分析
**使用位置**: 综合报表系统
```java
// Y3数据用于分析：
// 1. 团队生产效率
// 2. 项目执行质量
// 3. 资源配置合理性
```

#### 4.2.4 数据连接器
**使用位置**: `Connector2codeController.connector2codeY3()`
```java
// Y3数据变化时触发：
// 1. 项目进度重新计算
// 2. 完成百分比更新
// 3. 相关台账数据同步
```

## 5. Y4模块（分包商PO条目管理）数据使用分析

### 5.1 数据字段定义
```java
// Y4模块字段列表
List<String> Y4_FIELDS = Arrays.asList(
    "YPTT_Project_code",   // YPTT项目代码
    "Region",              // 区域
    "Site_ID",             // 站点ID
    "Phase",               // 阶段
    "Item_code",           // 项目代码
    "uniqueness_field",    // 唯一标识字段
    "Site_name",           // 站点名称
    "BOQ_item",            // BOQ项目
    "Quantity",            // 数量
    "Unit_price",          // 单价
    "Subcon_PO_amount",    // 分包商PO金额
    "Subcon_name",         // 分包商名称
    "Subcon_PO_number",    // 分包商PO号
    "release_date",        // 发布日期
    "Milestone_1st",       // 第一里程碑
    "Milestone_2nd",       // 第二里程碑
    "Milestone_3rd",       // 第三里程碑
    "Milestone_4th",       // 第四里程碑
    "additional_cost",     // 额外成本
    "Remark",              // 备注
    "re_record"            // 重新记录
);
```

### 5.2 Y4数据的使用场景

#### 5.2.1 分包商成本统计
**使用位置**: `IncomeAndExpenditureVO.subconTotalAmountPoItem`
```java
// Y4数据用于统计分包商PO总金额
private BigDecimal subconTotalAmountPoItem; // 来源于Y4的Subcon_PO_amount字段汇总
```

#### 5.2.2 外部成本管理
**使用位置**: `DataMangeController.addExternalCost()`
```java
// Y4数据关联外部成本：
// 1. 记录分包商的额外成本
// 2. 重新计算总的外部成本
// 3. 更新分包商PO的成本汇总
```

#### 5.2.3 分包商支付管理
**使用位置**: 与Y8模块关联
```java
// Y4数据为Y8提供基础：
// 1. 分包商信息
// 2. PO金额作为支付依据
// 3. 里程碑作为支付节点
```

#### 5.2.4 警告系统
**使用位置**: `WarningInfoService.updateSubsconStatus()`
```java
// Y4数据用于生成分包商相关警告：
// 1. 分包商PO延期警告
// 2. 分包商成本异常警告
// 3. 里程碑延期警告
```

## 6. Y8模块（分包商支付管理）数据使用分析

### 6.1 数据字段定义
```java
// Y8模块字段列表
List<String> Y8_FIELDS = Arrays.asList(
    "Subcon_name",         // 分包商名称
    "Subcon_PO_number",    // 分包商PO号
    "Site_ID",             // 站点ID
    "Item_code",           // 项目代码
    "uniqueness_field",    // 唯一标识字段
    "Payment_time_1st",    // 第一次付款时间
    "payment_amount_1st",  // 第一次付款金额
    "Payment_time_2st",    // 第二次付款时间
    "payment_amount_2st",  // 第二次付款金额
    "Payment_time_3st",    // 第三次付款时间
    "payment_amount_3st",  // 第三次付款金额
    "Payment_time_4st",    // 第四次付款时间
    "payment_amount_4st",  // 第四次付款金额
    "Remark",              // 备注
    "Totally_payment",     // 总付款
    "re_record",           // 重新记录
    "payment_number_1st",  // 第一次付款编号
    "payment_number_2st",  // 第二次付款编号
    "payment_number_3st",  // 第三次付款编号
    "payment_number_4st"   // 第四次付款编号
);
```

### 6.2 Y8数据的使用场景

#### 6.2.1 分包商支付统计
**使用位置**: `IncomeAndExpenditureVO.subconTotalAmountPaid`
```java
// Y8数据用于统计分包商支付总金额
private BigDecimal subconTotalAmountPaid; // 来源于Y8的各次付款金额汇总
```

#### 6.2.2 支付进度跟踪
**使用位置**: 财务管理系统
```java
// Y8数据用于跟踪：
// 1. 分包商付款进度
// 2. 付款时间节点
// 3. 剩余应付金额
```

#### 6.2.3 警告系统
**使用位置**: `WarningInfoService.updateSubsconStatus()`
```java
// Y8数据用于生成支付相关警告：
// 1. 付款超期警告
// 2. 付款金额异常警告
// 3. 分包商结算延期警告
```

## 7. Y9模块（开票管理）数据使用分析

### 7.1 数据字段定义
```java
// Y9模块字段列表
List<String> Y9_FIELDS = Arrays.asList(
    "PO_number",           // PO号码
    "Contract_number",     // 合同号
    "Phase",               // 阶段
    "Site_ID",             // 站点ID
    "Item_code",           // 项目代码
    "uniqueness_field",    // 唯一标识字段
    "Invoice_date_1st",    // 第一次开票日期
    "Invoice_number_1st",  // 第一次发票号
    "Invoice_Amount_1st",  // 第一次开票金额
    "Invoice_date_2nd",    // 第二次开票日期
    "Invoice_number_2nd",  // 第二次发票号
    "Invoice_Amount_2nd",  // 第二次开票金额
    "Invoice_date_3rd",    // 第三次开票日期
    "Invoice_number_3rd",  // 第三次发票号
    "Invoice_Amount_3rd",  // 第三次开票金额
    "Invoice_date_4st",    // 第四次开票日期
    "Invoice_number_4st",  // 第四次发票号
    "Invoice_Amount_4st",  // 第四次开票金额
    "Remark",              // 备注
    "Invoice_amount",      // 发票总金额
    "re_record",           // 重新记录
    "Invoice_remark_1st",  // 第一次发票备注
    "Invoice_remark_2nd",  // 第二次发票备注
    "Invoice_remark_3rd",  // 第三次发票备注
    "Invoice_remark_4th"   // 第四次发票备注
);
```

### 7.2 Y9数据的使用场景

#### 7.2.1 开票金额统计
**使用位置**: `BiPanelController.totalAmount()`
```java
// Y9数据用于开票金额统计
CompletableFuture<AmountY9VO> futureY9 = CompletableFuture.supplyAsync(() -> {
    if (StringUtils.isBlank(dateType) || "Invoice_Date".equals(dateType)) {
        return basicMapper.totalY9(projectId, dateStrStart, dateStrEnd, dateType);
    }
    return new AmountY9VO();
});
```

#### 7.2.2 收支状态统计
**使用位置**: `IncomeAndExpenditureVO.totalInvoiceAmount`
```java
// Y9数据贡献发票总金额
private BigDecimal totalInvoiceAmount; // 来源于Y9的各次开票金额汇总
```

#### 7.2.3 发票重复检查
**使用位置**: 工作流系统
```java
// Y9数据用于防止发票重复报销：
// 1. 记录已开具的发票号
// 2. 在报销流程中检查发票是否已使用
// 3. 防止同一张发票被多次报销
```

## 8. 数据关联关系图

```mermaid
graph TD
    A[Y1-站点条目] --> E[BI面板统计]
    B[Y2-PO条目] --> E
    C[Y3-生产力报告] --> F[进度跟踪]
    D[Y4-分包商PO] --> G[成本管理]
    H[Y8-分包商支付] --> G
    I[Y9-开票管理] --> E
    
    E --> J[收支分析报表]
    F --> K[警告系统]
    G --> K
    E --> K
    
    A --> L[台账更新]
    B --> L
    C --> L
    D --> L
    
    L --> M[定时任务]
    K --> M
```

## 9. 数据使用优先级分析

### 9.1 高频使用数据
1. **Y1数据**: 几乎所有统计报表都需要
2. **Y2数据**: 财务分析的核心数据
3. **Y9数据**: 收入统计的重要来源

### 9.2 中频使用数据
1. **Y4数据**: 成本控制的重要数据
2. **Y8数据**: 支付管理的核心数据

### 9.3 低频使用数据
1. **Y3数据**: 主要用于进度跟踪和警告

## 10. 数据质量要求

### 10.1 必填字段
- 所有模块的`uniqueness_field`（唯一标识）
- Y1的`Site_value`（站点价值）
- Y2的`PO_Value`（PO价值）
- Y9的`Invoice_amount`（发票金额）

### 10.2 数据一致性要求
- Y1和Y2的站点信息必须一致
- Y4和Y8的分包商信息必须匹配
- Y2和Y9的PO号必须对应

### 10.3 时间字段要求
- 所有日期字段必须符合业务逻辑顺序
- 支持时间锁定机制，防止修改历史数据
