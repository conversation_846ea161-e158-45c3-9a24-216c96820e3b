---
name: build
kind: pipeline
type: kubernetes

environment:
  DOCKER_PLUGIN_MIRROR: https://oci-hub.cqcyit.com:20305
  # DEP_TAG_LOWCODE_CLOUD: v1.1.7
  # PLUGIN_STORAGE_PATH: /drone/src/dockerlib

trigger:
  ref:
    - refs/heads/master
    - refs/tags/**
    # 测试drone的分支
    - refs/heads/ci/**
  event:
    - push
    - tag

steps:
  - name: build:jar
    image: maven:3-jdk-8-alpine
    pull: if-not-exists
    commands:
      - mvn clean package -DskipTests=true
    volumes:
      - name: cache
        path: /root/.m2
  - name: build:docker
    image: kit101z/dockerbuildkit:1.1.7
    # image: plugins/docker
    pull: if-not-exists
    environment:
      BUILDX_NO_DEFAULT_ATTESTATIONS: 1
    privileged: true
    settings:
      mirror: https://oci-hub.cqcyit.com:20305
      context: .
      dockerfile: ./Dockerfile
      registry: swr.cn-south-1.myhuaweicloud.com
      repo: swr.cn-south-1.myhuaweicloud.com/cloud-cqcyit/yptt/yptt-personalized-api
      pull_image: false
      auto_tag: true
      # tags: latest
      username:
        from_secret: huawei_docker_username
      password:
        from_secret: huawei_docker_password

volumes:
  - name: cache # The name use in this pipeline,
    claim:
      name: yptt-maven-repository
      read_only: false

---
#
# 更新CI构建环境中的lowcode-cloud 的版本，see: http://git.cyitce.com:30089/yptt/yptt-personalized-api/790
#

name: upgrade-deps
kind: pipeline
type: kubernetes

clone:
  disable: true

environment:
#  DEP_TAG_LOWCODE_CLOUD: v1.0.11
  DEP_TAG_LOWCODE_CLOUD: v3.0.19-alpha

trigger:
  event:
  - promote
  target:
  - upgrade-deps

steps:
  - name: build:load-dependencies
    image: bitnami/git
    pull: if-not-exists
    commands:
    - git clone http://git.cyitce.com:30088/lowcode/lowcode-cloud.git -b $DEP_TAG_LOWCODE_CLOUD --depth 1 lowcode-cloud
  - name: build:install-dependencies
    image: maven:3-jdk-8-alpine
    pull: if-not-exists
    commands:
    - cd lowcode-cloud
      #   - mvn install -DskipTests=true
    - |
      mvn clean install -pl pig-common/pig-common-core,pig-common/pig-common-feign,pig-common/pig-common-security,pig-common/pig-common-swagger,pig-common/pig-common-mybatis,pig-common/pig-common-test,pig-common/pig-common-me-app,pig-common/pig-common-xss -am -DskipTests=true
    volumes:
    - name: cache
      path: /root/.m2
volumes:
  - name: cache # The name use in this pipeline,
    claim:
      name: yptt-maven-repository
      read_only: false

---
name: deploy
kind: pipeline
type: kubernetes

trigger:
  ref:
    - refs/heads/master
  #  - refs/heads/_drone-dev
  event:
    - push

depends_on:
  - build

steps:
  - name: deploy:dev
    image: zc2638/drone-k8s-plugin:0.0.4
    environment:
      IMAGE_PULL_SECRET: huawei-secret
    settings:
      k8s_server:
        from_secret: kube_apiserver_20304
      k8s_token:
        from_secret: kube_token_20304
      k8s_ca_crt:
        from_secret: kube_base64ed_crt_20304
      k8s_skip_tls: false
      namespace: yptt-development
      debug: true
      templates:
        - deploy/kubernetes.tpl.yaml

---
#
# 手动执行部署，see: http://git.cyitce.com:30089/yptt/yptt-personalized-api/791
#
name: deploy:promote
kind: pipeline
type: kubernetes

trigger:
  ref:
    - refs/heads/master
  #  - refs/heads/_drone-dev
  event:
    - promote
  target:
    - deploy

steps:
  - name: deploy:dev
    image: zc2638/drone-k8s-plugin:0.0.4
    environment:
      IMAGE_PULL_SECRET: huawei-secret
    settings:
      k8s_server:
        from_secret: kube_apiserver_20304
      k8s_token:
        from_secret: kube_token_20304
      k8s_ca_crt:
        from_secret: kube_base64ed_crt_20304
      k8s_skip_tls: false
      namespace: yptt-development
      debug: true
      templates:
        - deploy/kubernetes.tpl.yaml
