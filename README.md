<p align="center">
    <a href="http://git.cyitce.com:30089/yptt/yptt-personalized-api">
        <img src="http://git.cyitce.com:30089/api/badges/yptt/yptt-personalized-api/status.svg" alt="Build Status" />
    </a>
</p>

## 快速开始

### 分支说明

- master: java8 + springboot 2.7 + springcloud 2021
- jdk17: java17 + springboot 3.0 + springcloud 2022

| 分支              | 说明                                                               |
|-----------------|------------------------------------------------------------------|
| master          | java8 + springboot 2.7 + springcloud 2021                        |
| sca-springboot3 | java17 + springboot 3.0 + springcloud 2022 |
| sct-springboot3 | java17 + springboot 3.0 + springcloud 2022 |


### 核心依赖

| 依赖                   | 版本         |
| ---------------------- |------------|
| Spring Boot            | 2.7.10      |
| Spring Cloud           | 2021.0.6   |
| Spring Cloud Alibaba   | 2021.0.5.0 |
| Spring Authorization Server | 0.4.1      |
| Mybatis Plus           | *******    |
| hutool                 | 5.8.15     |

### 模块说明

```shell
yptt-personalized-api       -- 海外项目自定义开发模块    http://git.cyitce.com:30088/yptt/yptt-personalized-api
```

### 本地开发 运行
1. 拉取指定lowcode-cloud代码
```shell
git clone http://git.cyitce.com:30088/lowcode/lowcode-cloud.git -b v1.0.7 --depth 1
```
2. 安装jar到本地maven仓库
```shell
mvn clean source:jar install -DskipTests=true 
```
3. 启动lowcode-cloud项目里nacos

运行`PigNacosApplication`

4. 启动业务服务

分别启动lowcode-admin以下业务服务，其他服务按需启动
- PigAdminApplication
- PigAuthApplication
- PigGatewayApplication
- BaseService
- ModelEngineApplication
- WorkflowEngineApplication
- YPTTPersonalizedApplication(海外自定义开发项目)


