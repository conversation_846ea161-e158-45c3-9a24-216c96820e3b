<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pig4cloud</groupId>
        <artifactId>pig</artifactId>
        <version>3.6.7</version>
    </parent>

    <groupId>org.example</groupId>
    <artifactId>yptt-personalized-api</artifactId>

    <properties>
        <easyexcel.version>3.0.5</easyexcel.version>
        <pig.version>3.6.7</pig.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
        <mysql.version>8.0.31</mysql.version>
    </properties>
    <dependencies>
        <!-- easyexcel依赖 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <!-- 重试机制       -->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <!-- spring jdbc -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-common-core</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!--feign 调用-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-common-feign</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-common-security</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!--日志处理-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-common-log</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!--接口文档-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-common-swagger</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!-- orm 模块相关 begin -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-common-mybatis</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!-- orm 模块相关 end -->
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>2021.0.5.0</version>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2021.0.5.0</version>
        </dependency>
        <!--xss 过滤-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-common-xss</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
            <version>2.7.10</version>
        </dependency>
        <!--单元测试-->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-common-test</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!-- seata client -->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-common-seata</artifactId>
            <version>${pig.version}</version>
        </dependency>

        <!-- common me-app -->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-common-me-app</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!-- upms-api -->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>base-service-api</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!-- upms-api -->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pig-upms-api</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!-- model-engine-api -->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>model-engine-api</artifactId>
            <version>${pig.version}</version>
        </dependency>
        <!-- workflow-engine-api -->
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>workflow-engine-api</artifactId>
            <version>${pig.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>**/*.xlsx</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xlsx</include>
                </includes>
                <filtering>false</filtering>
            </resource>

        </resources>
    </build>

    <repositories>
        <repository>
            <id>aliyun</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>aliyun-plugin</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>