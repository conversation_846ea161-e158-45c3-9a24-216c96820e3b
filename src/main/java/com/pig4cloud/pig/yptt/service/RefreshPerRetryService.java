package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.collection.CollUtil;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.me.api.consts.SkipParams;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataDTO;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataValueDTO;
import com.pig4cloud.pig.me.api.dto.operation.OperationBatchUpdateDTO;
import com.pig4cloud.pig.me.api.feign.RemoteOperationService;
import com.pig4cloud.pig.yptt.bizcode.RefreshPerCode;
import com.pig4cloud.pig.yptt.config.RuntimeVisibleThreadPoolExecutor;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import com.pig4cloud.pig.yptt.utils.MetaDataUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/01/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefreshPerRetryService {

	private final ViewConfProperties viewConfProperties;

	private final RemoteOperationService remoteOperationService;

	private final BasicMapper basicMapper;

	private final RuntimeVisibleThreadPoolExecutor refreshPerExecutor;

	private final RedissonClient redissonClient;

	private static final int BATCH_UPDATE_SIZE = 200;

	public static final List<String> SKIP_PARAMS = Arrays.asList(SkipParams.SKIP_DELETE_POST,
			SkipParams.SKIP_DELETE_PRE, SkipParams.SKIP_INSERT_POST, SkipParams.SKIP_INSERT_PRE,
			SkipParams.SKIP_UPDATE_POST, SkipParams.SKIP_UPDATE_PRE);

	@Retryable(value = { BizException.class }, backoff = @Backoff(delay = 5000))
	public void refreshPer(Long projectId) {
		long l = System.currentTimeMillis();
		RLock redissonClientLock = redissonClient.getLock("refreshPer:" + projectId.toString());
		boolean lock = false;
		try {
			// 尝试获取redis锁
			lock = redissonClientLock.tryLock(120, 600, TimeUnit.SECONDS);
			if (!lock) {
				throw new BizException(RefreshPerCode.LOCK_TIMED_OUT);
			}
			log.info("========异步更新项目权限表数据========");
			// 查询最新项目权限表
			MetaDataDTOWrapper project = new MetaDataDTOWrapper(
					Collections.singletonList(basicMapper.findProjectPerById(projectId)));
			log.debug("project: {}", project);
			if (Objects.nonNull(project.getDataId()) && Objects.nonNull(project.getData())) {
				SecurityContext context = SecurityContextHolder.getContext();
				ArrayList<CompletableFuture<Void>> completableFutures = new ArrayList<>();
				// 1. site
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshSite(project);
				}, refreshPerExecutor));
				// 2. site item
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshSiteItem(project);
				}, refreshPerExecutor));
				// 3. po
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshPo(project);
				}, refreshPerExecutor));
				// 4. po item
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshPoItem(project);
				}, refreshPerExecutor));
				// 5. site delivery info
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshSiteDeliveryInfo(project);
				}, refreshPerExecutor));
				// 6. subcon po
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshSubconPO(project);
				}, refreshPerExecutor));
				// 7. subcon po item
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshSubconPOItem(project);
				}, refreshPerExecutor));
				// 8. ready settlement
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshReadySettlement(project);
				}, refreshPerExecutor));
				// 9. productivity report
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshProductivityReport(project);
				}, refreshPerExecutor));
				// 10.subcon payment
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshSubconPayment(project);
				}, refreshPerExecutor));
				// 11.subcon settlement
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshSubconSettlement(project);
				}, refreshPerExecutor));
				// 12.yptt settlement
				completableFutures.add(CompletableFuture.runAsync(() -> {
					SecurityContextHolder.setContext(context);
					refreshYpttSettlement(project);
				}, refreshPerExecutor));
				// 等待所有任务执行完毕
				CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
			}
			log.info("========异步更新项目权限表数据========耗时: {}ms", (System.currentTimeMillis() - l));
		}
		catch (RejectedExecutionException e) {
			log.warn("任务队列已满!");
			throw new BizException(RefreshPerCode.REFRESH_PER_REJECT_ERROR, e);
		}
		catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			throw new BizException(RefreshPerCode.INTERRUPTED_ERROR, e);
		}
		catch (Exception e) {
			throw new BizException(RefreshPerCode.REFRESH_PER_ERROR, e);
		}
		finally {
			if (redissonClientLock.isLocked()) {
				redissonClientLock.unlock();
			}
		}
	}

	private void refreshProductivityReport(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> ids = basicMapper.getIdsByProjectId(projectId, GlobalConstants.Y6.NAME);
		MetaDataDTOWrapper info = new MetaDataDTOWrapper();
		info.setValue("query", project.getValue("y6_query"));
		info.setValue("update", project.getValue("y6_update"));
		info.setValue("del", project.getValue("y6_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(ids, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getProductivityReport().getViewGroupId(),
					viewConfProperties.getProductivityReport().getViewId(), batchIds, info.getData());
			completed += batchIds.size();
			logProgress("ProductivityReport", ids.size(), completed, startMills);
		}
	}

	private void refreshReadySettlement(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> ids = basicMapper.getIdsByProjectId(projectId, GlobalConstants.Y5.NAME);
		MetaDataDTOWrapper info = new MetaDataDTOWrapper();
		info.setValue("query", project.getValue("y5_query"));
		info.setValue("update", project.getValue("y5_update"));
		info.setValue("del", project.getValue("y5_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(ids, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getReadyForSettlement().getViewGroupId(),
					viewConfProperties.getReadyForSettlement().getViewId(), batchIds, info.getData());
			completed += batchIds.size();
			logProgress("ReadyForSettlement", ids.size(), completed, startMills);
		}
	}

	private void refreshSubconPayment(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> ids = basicMapper.getIdsByProjectId(projectId, GlobalConstants.Y8.NAME);
		MetaDataDTOWrapper info = new MetaDataDTOWrapper();
		info.setValue("query", project.getValue("y8_query"));
		info.setValue("update", project.getValue("y8_update"));
		info.setValue("del", project.getValue("y8_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(ids, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getSubconPayment().getViewGroupId(),
					viewConfProperties.getSubconPayment().getViewId(), batchIds, info.getData());
			completed += batchIds.size();
			logProgress("SubconPayment", ids.size(), completed, startMills);
		}
	}

	private void refreshSubconSettlement(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> ids = basicMapper.getIdsByProjectId(projectId, GlobalConstants.Y7.NAME);
		MetaDataDTOWrapper info = new MetaDataDTOWrapper();
		info.setValue("query", project.getValue("y7_query"));
		info.setValue("update", project.getValue("y7_update"));
		info.setValue("del", project.getValue("y7_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(ids, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getSubconSettlement().getViewGroupId(),
					viewConfProperties.getSubconSettlement().getViewId(), batchIds, info.getData());
			completed += batchIds.size();
			logProgress("SubconSettlement", ids.size(), completed, startMills);
		}
	}

	private void refreshYpttSettlement(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> ids = basicMapper.getIdsByProjectId(projectId, GlobalConstants.Y9.NAME);
		MetaDataDTOWrapper info = new MetaDataDTOWrapper();
		info.setValue("query", project.getValue("y9_query"));
		info.setValue("update", project.getValue("y9_update"));
		info.setValue("del", project.getValue("y9_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(ids, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getYpttSettlement().getViewGroupId(),
					viewConfProperties.getYpttSettlement().getViewId(), batchIds, info.getData());
			completed += batchIds.size();
			logProgress("YpttSettlement", ids.size(), completed, startMills);
		}
	}

	private void refreshSubconPOItem(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> ids = basicMapper.getIdsByProjectId(projectId, GlobalConstants.Y4.NAME);
		if (CollUtil.isEmpty(ids)) {
			return;
		}
		MetaDataDTOWrapper info = new MetaDataDTOWrapper();
		info.setValue("query", project.getValue("y4_query"));
		info.setValue("update", project.getValue("y4_update"));
		info.setValue("del", project.getValue("y4_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(ids, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getSubconPOItem().getViewGroupId(),
					viewConfProperties.getSubconPOItem().getViewId(), batchIds, info.getData());
			completed += batchIds.size();
			logProgress("SubconPOItem", ids.size(), completed, startMills);
		}

	}

	private void refreshSubconPO(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> ids = basicMapper.getSubconPoIdByProjectId(projectId);
		if (CollUtil.isEmpty(ids)) {
			return;
		}
		MetaDataDTOWrapper info = new MetaDataDTOWrapper();
		info.setValue("query", project.getValue("y4_query"));
		info.setValue("update", project.getValue("y4_update"));
		info.setValue("del", project.getValue("y4_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(ids, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getSubconPO().getViewGroupId(),
					viewConfProperties.getSubconPO().getViewId(), batchIds, info.getData());
			completed += batchIds.size();
			logProgress("SubconPO", ids.size(), completed, startMills);
		}

	}

	private void refreshSiteDeliveryInfo(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> ids = basicMapper.getIdsByProjectId(projectId, GlobalConstants.Y3.NAME);
		if (CollUtil.isEmpty(ids)) {
			return;
		}
		MetaDataDTOWrapper info = new MetaDataDTOWrapper();
		info.setValue("query", project.getValue("y3_query"));
		info.setValue("update", project.getValue("y3_update"));
		info.setValue("del", project.getValue("y3_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(ids, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getSiteDelivery().getViewGroupId(),
					viewConfProperties.getSiteDelivery().getViewId(), batchIds, info.getData());
			completed += batchIds.size();
			logProgress("SiteDeliveryInfo", ids.size(), completed, startMills);
		}
	}

	private void refreshPoItem(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> ids = basicMapper.getIdsByProjectId(projectId, GlobalConstants.Y2.NAME);
		if (CollUtil.isEmpty(ids)) {
			return;
		}
		MetaDataDTOWrapper poItem = new MetaDataDTOWrapper();
		poItem.setViewId(viewConfProperties.getPoItem().getViewId());
		poItem.setValue("query", project.getValue("y2_query"));
		poItem.setValue("update", project.getValue("y2_update"));
		poItem.setValue("del", project.getValue("y2_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(ids, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getPoItem().getViewGroupId(),
					viewConfProperties.getPoItem().getViewId(), batchIds, poItem.getData());
			completed += batchIds.size();
			logProgress("poItem", ids.size(), completed, startMills);
		}
	}

	private void refreshPo(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> ids = basicMapper.getPoIdByProjectId(projectId);
		if (CollUtil.isEmpty(ids)) {
			return;
		}
		MetaDataDTOWrapper po = new MetaDataDTOWrapper();
		po.setViewId(viewConfProperties.getPo().getViewId());
		po.setValue("query", project.getValue("y2_query"));
		po.setValue("update", project.getValue("y2_update"));
		po.setValue("del", project.getValue("y2_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(ids, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getPo().getViewGroupId(),
					viewConfProperties.getPo().getViewId(), batchIds, po.getData());
			completed += batchIds.size();
			logProgress("po", ids.size(), completed, startMills);
		}
	}

	private void refreshSiteItem(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> siteItemIds = basicMapper.getSiteItemIdByProjectId(projectId);
		if (CollUtil.isEmpty(siteItemIds)) {
			return;
		}
		MetaDataDTOWrapper siteItem = new MetaDataDTOWrapper();
		siteItem.setViewId(viewConfProperties.getSiteItem().getViewId());
		siteItem.setValue("query", project.getValue("y1_query"));
		siteItem.setValue("update", project.getValue("y1_update"));
		siteItem.setValue("del", project.getValue("y1_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(siteItemIds, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getSiteItem().getViewGroupId(),
					viewConfProperties.getSiteItem().getViewId(), batchIds, siteItem.getData());
			completed += batchIds.size();
			logProgress("siteItem", siteItemIds.size(), completed, startMills);
		}

	}

	private void refreshSite(MetaDataDTOWrapper project) {
		if (Objects.isNull(project.getValue("project"))) {
			return;
		}
		Long projectId = MetaDataUtil.handleDataIdJson2Long(project.getValue("project").toString());
		List<Long> siteIds = basicMapper.getSiteIdByProjectId(projectId);
		if (CollUtil.isEmpty(siteIds)) {
			return;
		}
		MetaDataDTOWrapper site = new MetaDataDTOWrapper();
		site.setValue("query", project.getValue("y1_query"));
		site.setValue("update", project.getValue("y1_update"));
		site.setValue("del", project.getValue("y1_del"));
		int completed = 0;
		long startMills = System.currentTimeMillis();
		List<List<Long>> batchList = CollUtil.split(siteIds, BATCH_UPDATE_SIZE);
		for (List<Long> batchIds : batchList) {
			boolean success = doBatchUpdate(viewConfProperties.getSite().getViewGroupId(),
					viewConfProperties.getSite().getViewId(), batchIds, site.getData());
			completed += batchIds.size();
			logProgress("site", siteIds.size(), completed, startMills);
		}

	}

	private boolean doBatchUpdate(Long viewGroupId, Long viewId, List<Long> ids, List<MetaDataValueDTO> mdvs) {
		List<MetaDataDTO> metaDataList = ids.stream().map(id -> {
			MetaDataDTO metaData = new MetaDataDTO();
			metaData.setDataId(id);
			metaData.setData(mdvs);
			metaData.setViewId(viewId);
			return metaData;
		}).collect(Collectors.toList());

		OperationBatchUpdateDTO batchUpdateDTO = new OperationBatchUpdateDTO();
		batchUpdateDTO.setViewGroupId(viewGroupId);
		batchUpdateDTO.setViewId(viewId);
		batchUpdateDTO.setMetaDataList(metaDataList);
		batchUpdateDTO.setSkipParams(SKIP_PARAMS);
		return remoteOperationService.nativeBatchUpdate(batchUpdateDTO);
	}

	private final DecimalFormat df = new DecimalFormat("0.00");

	private void logProgress(String name, int total, int completed, long startMills) {
		if (log.isDebugEnabled()) {
			int step = total / 100;
			step = step == 0 ? 5 : step;
			if (completed % step == 0) {
				double percentage = (double) completed / (double) total * 100;
				String percentageStr = df.format(percentage);
				long duration = System.currentTimeMillis() - startMills;
				log.debug("permission refresh[{}] progress: {}/{}, percentage: {}%, duration: {}ms", name, completed,
						total, percentageStr, duration);
			}
		}
	}

}
