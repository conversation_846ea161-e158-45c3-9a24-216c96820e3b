package com.pig4cloud.pig.yptt.service.transform;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.pig4cloud.pig.common.core.constant.SecurityConstants;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.consts.SymbolEnum;
import com.pig4cloud.pig.me.api.dto.operation.*;
import com.pig4cloud.pig.me.api.entity.MetaAttr;
import com.pig4cloud.pig.me.api.entity.ViewAttr;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.me.api.vo.ViewConfVO;
import com.pig4cloud.pig.me.api.vo.ViewGroupConfVO;
import com.pig4cloud.pig.yptt.bizcode.ExpectedOneButGotMany;
import com.pig4cloud.pig.yptt.bizcode.ImportForbidden;
import com.pig4cloud.pig.yptt.bizcode.RefreshPerCode;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.entity.dto.ProjectRolePermissionMapDTO;
import com.pig4cloud.pig.yptt.entity.dto.YPTTBatchImportDTO;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.DataPermissionsService;
import com.pig4cloud.pig.yptt.service.LockDataTimeService;
import com.pig4cloud.pig.yptt.service.Transformer;
import com.pig4cloud.pig.yptt.utils.ApplicationContextUtil;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import com.pig4cloud.pig.yptt.utils.MetaDataUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.pig4cloud.pig.yptt.service.DataMangeService.percentage_fields;

/**
 * <AUTHOR>
 * @date 2023/10/07
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractTransformer implements Transformer {

	protected final RemoteAppService remoteAppService;

	protected final ViewConfProperties viewConfProperties;

	protected final DataPermissionsService dataPermissionsService;

	protected final BasicMapper basicMapper;

	/* 项目编码 */
	protected final static String YPTT_PROJECT_META_ATTR_YPTT_PROJECT_CODE = "YPTT_Project_code";

	/** 属性名称：唯一标识 */
	protected final static String UNI_META_ATTR_UNIQUENESS_FIELD = "uniqueness_field";

	protected final static String UNI_META_ATTR_PROJECT_CODE = "Project_code";

	/** 属性名称：站点-站点编码 */
	protected final static String SITE_META_ATTR_SITE_SERIAL_NUMBER = "Site_Serial_number";

	/** 属性名称：客户项目-合同编码 */
	protected final static String CUSTOMER_PROJECT_META_ATTR_CONTRACT_NUMBER = "Contract_number";

	/** 属性名称：订单-订单号码 */
	protected final static String PO_META_ATTR_PO_NUMBER = "PO_number";

	/** 属性名称：分包商-分包商名称 */
	protected final static String SUBCON_META_ATTR_SUBCON_NAME = "Subcon_name";

	/** 属性名称：分包商PO-分包商PO号码 */
	protected final static String SUBCON_PO_META_ATTR_SUBCON_PO_NUMBER = "Subcon_PO_number";

	/** 站点状态：已关闭 */
	private static final String SITE_STATUE_CLOSED = "close";

	/** 站点状态：未关闭 */
	protected static final String SITE_STATUE_UNCLOSE = "unclose";

	/** 警告信息：正常 */
	protected static final String WARNING_NORMAL = "Normal";

	protected static final String PHASE_REGEX = "^Phase\\d{1,3}$";
	protected static final String PHASE_REGEX_NEW = "^Phase\\d{1,3}(?:[-+]\\d{1,3})?$";
	protected static final int CHAR_LENGTH = 300;

	private static final String[] FILED_NAME_LIST = new String[] { "Item_code", "Site_name", "Site_Name", "BOQ_item",
			"Subcon_name", "Site_belong_to", "Team_Leader_DT", "engineer_DTA_SPV", "PLO PC_Others", "PIC_PC_PM",
			"Custom_project_name", "Region", "PO_Number", "Contract_number", "YPTT_Project_code" };

	protected final RedissonClient redissonClient;

	protected final DataMangeService dataMangeService;

	private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	@Override
	public ImportResultVO validate(TransformContext context, int index, Map<String, Object> raw) {
		ImportResultVO valid = new ImportResultVO();
		valid.setIndex(index);
		valid.setImportData(raw);

		final YPTTBatchImportDTO param = context.getImportParam();
		final String moduleType = context.getModuleName();
		final String appId = context.getAppid();

		commonValidate(valid, moduleType);
		// 有错误就直接返回
		if (Objects.equals(valid.getStatus(), ImportResultVO.STATUS_FAILED)) {
			return valid;
		}
		ImportResultVO valid0 = this.doValidate(context, index, raw, valid);

		if (!Objects.equals(valid0.getStatus(), ImportResultVO.STATUS_FAILED)) {
			valid0.setStatus(ImportResultVO.STATUS_SUCCEED);
		}
		return valid0;
	}

	protected void commonValidate(ImportResultVO valid, String moduleType) {
		Map<String, Object> dateTmp = new HashMap<>(); //存放时间映射表

		valid.getImportData().forEach((k, v) -> {
			// 日期格式校验
			if (ArrayUtil.contains(DataMangeService.datetime_fields, k) && Objects.nonNull(v)) {
				try {
					String dateStr = v instanceof LocalDateTime ? ((LocalDateTime) v).toLocalDate().toString()
							: v.toString();
					MetaDataUtil.dateStr2LocalDateTime(dateStr);
				}
				catch (Exception e) {
					valid.addWrongReason("This field 【" + k + "】Incorrect date format Value 【" + v + "】;");
					valid.setStatus(ImportResultVO.STATUS_FAILED);
				}
				dateTmp.put(k, v);
			}
			// 百分比校验
			if (ArrayUtil.contains(DataMangeService.percentage_fields, k) && Objects.nonNull(v)) {
				try {
					MetaDataUtil.percentageStr2BigDecimal(v.toString(), 4, false);
				}
				catch (Exception e) {
					valid.addWrongReason("This field 【" + k + "】Illegal Percentage Format Value 【" + v + "】;");
					valid.setStatus(ImportResultVO.STATUS_FAILED);
				}
			}
			// 金额校验
			if (ArrayUtil.contains(DataMangeService.amount_fields, k) && Objects.nonNull(v)) {
				try {
					MetaDataUtil.numberStr2BigDecimal(v.toString());
				}
				catch (Exception e) {
					valid.addWrongReason("This field 【" + k + "】Illegal Number Format Value 【" + v + "】;");
					valid.setStatus(ImportResultVO.STATUS_FAILED);
				}
			}
			// 阶段格式校验
			if (Objects.equals("Phase", k) && Objects.nonNull(v) && !v.toString().matches(PHASE_REGEX_NEW)) {
				valid.addWrongReason("This field 【" + k + "】Incorrect format or length;");
				valid.setStatus(ImportResultVO.STATUS_FAILED);
			}
			// 长度校验
			if (ArrayUtil.contains(FILED_NAME_LIST, k) && Objects.nonNull(v) && v.toString().length() > CHAR_LENGTH) {
				valid.addWrongReason("This field 【" + k + "】Length limit exceeded;");
				valid.setStatus(ImportResultVO.STATUS_FAILED);
			}
			// Remark长度校验
			if (Objects.equals("Remark", k) && Objects.nonNull(v) && v.toString().length() > 1000) {
				valid.addWrongReason("This field 【" + k + "】Length limit exceeded;");
				valid.setStatus(ImportResultVO.STATUS_FAILED);
			}
			// re_record长度校验
			if (Objects.equals("re_record", k) && Objects.nonNull(v) && v.toString().trim().length() > 1) {
				valid.addWrongReason("This field 【" + k + "】can only be 1 or 0;");
				valid.setStatus(ImportResultVO.STATUS_FAILED);
			}
		});
	}

	protected void addRequire(Map<String, Object> raw, TransformContext context, ImportResultVO importCheckStatus) {
		YPTTBatchImportDTO param = context.getImportParam();
		ViewGroupConfVO viewGroupConfVO = getViewGroupConfVO(context, param.getViewGroupId(), param.getViewId());
		// 获取属性字段
		List<MetaAttr> metaAttrs = viewGroupConfVO.getAttrList()
			.stream()
			.map(ViewConfVO.MetaViewAttrVO::getMetaAttr)
			.collect(Collectors.toList());
		List<ViewAttr> viewAttrs = viewGroupConfVO.getAttrList()
			.stream()
			.map(ViewConfVO.MetaViewAttrVO::getViewAttr)
			.collect(Collectors.toList());
		// 校验字段是否填写完整
		raw.forEach((k, v) -> metaAttrs.stream()
			.filter(o -> Objects.equals(o.getPhysicalName(), k))
			.findFirst()
			.flatMap(metaAttr -> viewAttrs.stream()
				.filter(o -> Objects.equals(o.getMetaAttrId(), metaAttr.getId()))
				.findFirst())
			.ifPresent(viewAttr -> {
				if (Objects.equals(viewAttr.getIsRequired(), 1)
						&& (Objects.isNull(v) || StrUtil.isBlank(v.toString()))) {
					importCheckStatus.setStatus(ImportResultVO.STATUS_FAILED);
					importCheckStatus
						.addWrongReason("This field【" + viewAttr.getDisplayName() + "】Required fields，Cannot be empty");
				}
			}));
	}

	protected void updateSupport(Map<String, Object> raw, TransformContext context, ImportResultVO importCheckStatus) {
		YPTTBatchImportDTO param = context.getImportParam();
		ViewGroupConfVO viewGroupConfVO = getViewGroupConfVO(context, param.getViewGroupId(), param.getViewId());
		// 获取属性字段
		List<MetaAttr> metaAttrs = viewGroupConfVO.getAttrList()
			.stream()
			.map(ViewConfVO.MetaViewAttrVO::getMetaAttr)
			.collect(Collectors.toList());
		List<ViewAttr> viewAttrs = viewGroupConfVO.getAttrList()
			.stream()
			.map(ViewConfVO.MetaViewAttrVO::getViewAttr)
			.collect(Collectors.toList());
		raw.forEach((k, v) -> metaAttrs.stream()
			.filter(o -> Objects.equals(o.getPhysicalName(), k))
			.findFirst()
			.flatMap(metaAttr -> viewAttrs.stream()
				.filter(o -> Objects.equals(o.getMetaAttrId(), metaAttr.getId()))
				.findFirst())
			.ifPresent(viewAttr -> {
				if (Objects.equals(viewAttr.getIsEdit(), 0)
						&& (Objects.nonNull(v) && StrUtil.isNotBlank(v.toString()))) {
					importCheckStatus.setStatus(ImportResultVO.STATUS_FAILED);
					importCheckStatus.addWrongReason("This field【" + viewAttr.getDisplayName() + "】Not updatable");
				}
			}));
	}

	protected void validatePerm(TransformContext context, String ypttProjectCode) {
		Long userId = context.getUserId();
		List<ProjectRolePermissionMapDTO> permMapList = this.dataPermissionsService
			.queryProjectRolePermMapList(ypttProjectCode, userId);
		Set<ProjectRolePermissionMapDTO> addPermMapList = permMapList.stream()
			.filter(pm -> Objects.equals(pm.getPermAdd(), 1)
					&& CollUtil.contains(pm.getPermModel(), context.getModuleName()))
			.collect(Collectors.toSet());
		if (CollUtil.isEmpty(addPermMapList)) {
			throw new BizException(ImportForbidden.IMPORT_FORBIDDEN, context.getModuleName());
		}
	}

	protected void validatePerm(TransformContext context, Long ypttProjectDataId) {
		Long userId = context.getUserId();
		List<ProjectRolePermissionMapDTO> permMapList = this.dataPermissionsService
			.queryProjectRolePermMapList(ypttProjectDataId, userId);
		Set<ProjectRolePermissionMapDTO> addPermMapList = permMapList.stream()
			.filter(pm -> Objects.equals(pm.getPermAdd(), 1)
					&& CollUtil.contains(pm.getPermModel(), context.getModuleName()))
			.collect(Collectors.toSet());
		if (CollUtil.isEmpty(addPermMapList)) {
			throw new BizException(ImportForbidden.IMPORT_FORBIDDEN, context.getModuleName());
		}
	}

	protected abstract ImportResultVO doValidate(TransformContext context, int index, Map<String, Object> raw,
			ImportResultVO valid);

	@Override
	public ImportResultVO transform(TransformContext context, int index, Map<String, Object> raw) {
		return this.doTransform(context, index, raw);
	}

	protected abstract ImportResultVO doTransform(TransformContext context, int index, Map<String, Object> raw);

	// ====== helpers ======

	protected ViewGroupConfVO getViewGroupConfVO(TransformContext context, Long viewGroupId, Long viewId) {
		Assert.notNull(viewGroupId, "ViewGroupId must not be null");
		Assert.notNull(viewId, "ViewId must not be null");
		Map<String, ViewGroupConfVO> viewGroupConfCache = context.getViewGroupConfCache();
		String cacheKey = viewGroupId + "-" + viewId;
		ViewGroupConfVO hit = viewGroupConfCache.get(cacheKey);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		ViewGroupConfVO viewGroupConfVO = remoteAppService.getViewGroupConfVO(viewGroupId, viewId,
				SecurityConstants.FROM_IN);
		Assert.notNull(viewGroupConfVO, "Query ViewGroupConf failed.");
		viewGroupConfCache.put(cacheKey, viewGroupConfVO);
		return viewGroupConfVO;
	}

	protected MetaDataDTOWrapper remoteFindOne(String appid, Long viewGroupId, Long viewId, String name, Object value) {
		OperationPageDTO operationPageDTO = new OperationPageDTO();
		operationPageDTO.setViewGroupId(viewGroupId);
		operationPageDTO.setViewId(viewId);
		QueryDTO queryDTO = new QueryDTO();
		queryDTO.setName(name);
		queryDTO.setValue(value);
		queryDTO.setSymbol(SymbolEnum.EQUAL.getCode());
		operationPageDTO.setConditions(Collections.singletonList(queryDTO));

		PageResponseDTO pageResponseDTO = SpringUtil.getBean(RemoteAppService.class)
			.operationPage(operationPageDTO, SecurityConstants.FROM_IN, appid);
		if (CollUtil.isEmpty(pageResponseDTO.getRecords())) {
			log.trace("not findOne from remote: viewId: {}, name: {}, value: {}", viewId, name, value);
			return null;
		}
		if (pageResponseDTO.getTotal() > 1) {
			log.trace("expected one, but actual got many. Conditions: {}, response: {}",
					MetaDataUtil.conditionsToPrettyString(operationPageDTO.getConditions()), pageResponseDTO);
			throw new BizException(ExpectedOneButGotMany.EXPECT_ONE_BUT_GOT_MANY, value);
		}
		MetaDataDTOWrapper hit = new MetaDataDTOWrapper(CollUtil.getFirst(pageResponseDTO.getRecords()));
		log.trace("findOne from remote: name: {}, value: {}, {}", name, value, hit);
		return hit;
	}

	protected static MetaDataDTOWrapper localFindOne(List<MetaDataDTOWrapper> localStore, String name, Object value) {
		MetaDataDTOWrapper hit = CollUtil.findOne(localStore, o -> {
			Object targetValue = o.getValue(name);
			return Objects.equals(targetValue, value);
		});
		log.trace("findOne in LocalStore: name: {}, value: {}, hit: {}", name, value, hit);
		return hit;
	}

	/**
	 * 站点编号 查询 站点信息
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param SiteID 站点编号
	 * @return 站点信息
	 */
	protected MetaDataDTOWrapper findSiteBySiteID(String appid, List<MetaDataDTOWrapper> localStore, String SiteID) {
		MetaDataDTOWrapper hit = localFindOne(localStore, SITE_META_ATTR_SITE_SERIAL_NUMBER, SiteID);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> siteBySiteID = basicMapper.findSiteBySiteID(SiteID);
		return CollUtil.isEmpty(siteBySiteID) ? null : new MetaDataDTOWrapper(siteBySiteID);
	}

	/**
	 * 唯一标识串 查询 站点条目
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param uniquenessId 唯一标识串
	 * @return 站点条目
	 */
	protected MetaDataDTOWrapper findSiteItemByUniquenessField(String appid, List<MetaDataDTOWrapper> localStore,
			Long uniquenessId) {
		String uniIdJsonString = MetaDataUtil.handleDataId2Json(uniquenessId);
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniIdJsonString);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> siteItemByUniquenessField = basicMapper.findSiteItemByUniquenessField(uniquenessId);
		return CollUtil.isEmpty(siteItemByUniquenessField) ? null : new MetaDataDTOWrapper(siteItemByUniquenessField);
	}

	/**
	 * 项目编码 查询 项目
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param YPTTProjectCode 项目编码
	 * @return 项目信息
	 */
	protected MetaDataDTOWrapper findYPTTProjectByYPTTProjectCode(String appid, List<MetaDataDTOWrapper> localStore,
			String YPTTProjectCode) {
		MetaDataDTOWrapper hit = localFindOne(localStore, YPTT_PROJECT_META_ATTR_YPTT_PROJECT_CODE, YPTTProjectCode);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> ypttProjectByCode = basicMapper.findYpttProjectByCode(YPTTProjectCode);
		return CollUtil.isEmpty(ypttProjectByCode) ? null : new MetaDataDTOWrapper(ypttProjectByCode);
	}

	/**
	 * 唯一标识串 查询 完整唯一标识信息
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param uniquenessField 唯一标识串
	 * @return 唯一标识完整信息
	 */
	protected MetaDataDTOWrapper findUniquenessByUniquenessField(String appid, List<MetaDataDTOWrapper> localStore,
			String uniquenessField) {
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniquenessField);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> uniquenessByUniquenessField = basicMapper
			.findUniquenessByUniquenessField(uniquenessField);
		return CollUtil.isEmpty(uniquenessByUniquenessField) ? null
				: new MetaDataDTOWrapper(uniquenessByUniquenessField);
	}

	/**
	 * 唯一标识串 查询 完整唯一标识信息
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param id 唯一标识id
	 * @return 唯一标识完整信息
	 */
	protected MetaDataDTOWrapper findUniquenessById(String appid, List<MetaDataDTOWrapper> localStore, Long id) {
		MetaDataDTOWrapper hit = localFindOne(localStore, "id", id);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> uniquenessByUniquenessField = basicMapper.findUniquenessById(id);
		return CollUtil.isEmpty(uniquenessByUniquenessField) ? null
				: new MetaDataDTOWrapper(uniquenessByUniquenessField);
	}

	/**
	 * 唯一标识 查询 收支统计
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param uniquenessField 项目编码
	 * @return 收支统计
	 */
	protected MetaDataDTOWrapper findIncomeExpenditureByUniquenessField(String appid,
			List<MetaDataDTOWrapper> localStore, Long uniquenessField) {
		String uniIdJsonString = MetaDataUtil.handleDataId2Json(uniquenessField);
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniIdJsonString);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> incomeExpenditureByUniquenessField = basicMapper
			.findIncomeExpenditureByUniquenessField(uniquenessField);
		return CollUtil.isEmpty(incomeExpenditureByUniquenessField) ? null
				: new MetaDataDTOWrapper(incomeExpenditureByUniquenessField);
	}

	/**
	 * 保存 收支统计
	 * @param incomeExpenditure 收支
	 */
	protected void saveIncomeExpenditure(MetaDataDTOWrapper incomeExpenditure) {
		Map<String, Object> incomeExpenditureMap = incomeExpenditure.toMap();
		incomeExpenditure.clearNullValue();
		if (Objects.isNull(incomeExpenditure.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			incomeExpenditureMap.put("id", dataId);
			basicMapper.saveItemData(incomeExpenditureMap, viewConfProperties.getIncomeExpenditure().getTableName());
			incomeExpenditure.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(incomeExpenditureMap, viewConfProperties.getIncomeExpenditure().getTableName());
		}
	}

	/**
	 * 站点条目 是否已关闭
	 * @param siteItem 站点条目
	 * @return {@code true} 已关闭
	 */
	protected boolean isClosedSiteItem(MetaDataDTOWrapper siteItem) {
		System.out.println("siteItem.getValue(\"Site_item_status\")"+siteItem.getValue("Site_item_status"));
		return !Objects.equals("[\"" + SITE_STATUE_UNCLOSE + "\"]", siteItem.getValue("Site_item_status"));
	}

	/**
	 * 唯一标识数据ID 查询 PO条目
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param uniquenessId 唯一标识数据ID
	 * @return PO条目
	 */
	protected MetaDataDTOWrapper findPOItemByUniquenessId(String appid, List<MetaDataDTOWrapper> localStore,
			Long uniquenessId) {
		String uniIdJsonString = MetaDataUtil.handleDataId2Json(uniquenessId);
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniIdJsonString);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> poItemByUniquenessId = basicMapper.findPOItemByUniquenessId(uniquenessId);
		return CollUtil.isEmpty(poItemByUniquenessId) ? null : new MetaDataDTOWrapper(poItemByUniquenessId);
	}

	private final Object insertLock = new Object();

	/**
	 * 初始化警告阈值
	 * @param appid 应用id
	 * @param projectId 项目id
	 * @param warningThreshold 缓存
	 */
	protected void initWarningThreshold(String appid, Long projectId, MetaDataDTOWrapper warningThreshold) {
		// 只新增
		if (Objects.isNull(getWarningThreshold(projectId))) {
			RLock redissonClientLock = redissonClient.getLock("initWarningThreshold:" + projectId);
			boolean lock = false;
			try {
				lock = redissonClientLock.tryLock(15, 30, TimeUnit.SECONDS);
				if (!lock) {
					throw new BizException(RefreshPerCode.LOCK_TIMED_OUT);
				}
				if (Objects.isNull(getWarningThreshold(projectId))) {
					OperationInsertDTO insertDTO = new OperationInsertDTO();
					warningThreshold.setValue("Site_Delay_Warning", new BigDecimal("3"));
					warningThreshold.setValue("SitePO_Delay_Warning", new BigDecimal("7"));
					warningThreshold.setValue("Amount_Error_Warning", new BigDecimal("1.00"));
					warningThreshold.setValue("Work_Delay_Warning", new BigDecimal("7"));
					warningThreshold.setValue("Acceptance_Warning", new BigDecimal("30"));
					warningThreshold.setValue("Subcon_PO_Warning", new BigDecimal("7"));
					warningThreshold.setValue("SubconPaymentWarning", new BigDecimal("30"));
					warningThreshold.setValue("InvoiceDelayWarning", new BigDecimal("30"));
					Map<String, Object> warningThresholdMap = warningThreshold.toMap();
					Long dataId = IdUtil.getSnowflakeNextId();
					warningThresholdMap.put("id", dataId);
					basicMapper.saveItemData(warningThresholdMap,
							viewConfProperties.getWarningThreshold().getTableName());
					basicMapper.saveRelProject(IdUtil.getSnowflakeNextId(), dataId, projectId);
				}
			}
			catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				throw new BizException(RefreshPerCode.INTERRUPTED_ERROR, e);
			}
			finally {
				if (redissonClientLock.isLocked()) {
					redissonClientLock.unlock();
				}
			}
		}
	}

	/**
	 * 获取阈值
	 * @param projectId projectId
	 * @return 数据
	 */
	private Object getWarningThreshold(Long projectId) {
		return basicMapper.findWarningThreshold(projectId);
	}

	/**
	 * 保存 PO条目
	 * @param appid appid
	 * @param poDataId po数据id
	 * @param poItem PO条目
	 */
	protected void savePOItem(String appid, Long poDataId, MetaDataDTOWrapper poItem) {
		Map<String, Object> poItemMap = poItem.toMap();
		poItem.clearNullValue();
		if (Objects.isNull(poItem.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			poItemMap.put("id", dataId);
			basicMapper.saveItemData(poItemMap, viewConfProperties.getPoItem().getTableName());
			// 保存关系
			if (Objects.nonNull(poDataId)) {
				System.out.println("============================新增  id:"+IdUtil.getSnowflakeNextId());
				System.out.println("============================新增  poDataId:"+poDataId);
				System.out.println("============================新增  dataId"+ dataId);
				basicMapper.saveRelPo(IdUtil.getSnowflakeNextId(), poDataId, dataId);
			}
			poItem.setDataId(dataId);
		}
		else {
			System.out.println("===================================修改"+poItemMap);
			basicMapper.updateItemData(poItemMap, viewConfProperties.getPoItem().getTableName());
		}
	}

	/**
	 * 唯一标识数据ID 查询 分包商PO条目
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param uniquenessId 唯一标识数据ID
	 * @return 分包商PO条目
	 */
	protected MetaDataDTOWrapper findSubconPOItemByUniquenessId(String appid, List<MetaDataDTOWrapper> localStore,
			Long uniquenessId) {
		String uniIdJsonString = MetaDataUtil.handleDataId2Json(uniquenessId);
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniIdJsonString);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> subconPOItemByUniquenessId = basicMapper.findSubconPOItemByUniquenessId(uniquenessId);
		return CollUtil.isEmpty(subconPOItemByUniquenessId) ? null : new MetaDataDTOWrapper(subconPOItemByUniquenessId);
	}

	/**
	 * 保存 分包商PO条目
	 * @param subconPODataId 分包商PO数据id
	 * @param subconPOItem 分包商PO条目
	 */
	protected void saveSubconPOItem(Long subconPODataId, MetaDataDTOWrapper subconPOItem) {
		Map<String, Object> subconPOItemMap = subconPOItem.toMap();
		subconPOItem.clearNullValue();
		if (Objects.isNull(subconPOItem.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			subconPOItemMap.put("id", dataId);
			basicMapper.saveItemData(subconPOItemMap, viewConfProperties.getSubconPOItem().getTableName());
			subconPOItem.setDataId(dataId);
			log.info("subconpoItem 执行 插入"+subconPOItemMap);
		}
		else {
			basicMapper.updateItemData(subconPOItemMap, viewConfProperties.getSubconPOItem().getTableName());
			log.info("subconpoItem 执行 更新"+subconPOItemMap);
		}
	}

	/**
	 * 唯一标识数据ID 查询 站点交付信息
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param uniquenessId 唯一标识数据ID
	 * @return 站点交付信息
	 */
	protected MetaDataDTOWrapper findSiteDeliveryInfoByUniquenessId(String appid, List<MetaDataDTOWrapper> localStore,
			Long uniquenessId) {
		String uniIdJsonString = MetaDataUtil.handleDataId2Json(uniquenessId);
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniIdJsonString);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> siteDeliveryInfoByUniquenessId = basicMapper
			.findSiteDeliveryInfoByUniquenessId(uniquenessId);
		return CollUtil.isEmpty(siteDeliveryInfoByUniquenessId) ? null
				: new MetaDataDTOWrapper(siteDeliveryInfoByUniquenessId);
	}

	/**
	 * 保存 站点交付信息
	 * @param siteDeliveryInfo 站点交付信息
	 */
	protected void saveSiteDeliveryInfo(MetaDataDTOWrapper siteDeliveryInfo) {
		siteDeliveryInfo.clearNullValue();
		Map<String, Object> siteDeliveryInfoMap = siteDeliveryInfo.toMap();
		if (Objects.isNull(siteDeliveryInfo.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			siteDeliveryInfoMap.put("id", dataId);
			basicMapper.saveItemData(siteDeliveryInfoMap, viewConfProperties.getSiteDelivery().getTableName());
			siteDeliveryInfo.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(siteDeliveryInfoMap, viewConfProperties.getSiteDelivery().getTableName());
		}
	}

	/**
	 * 唯一标识数据ID 查询 供应商支付
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param uniquenessIdJson 唯一标识数据ID JsonString格式; "[\"12331231231\"]"
	 * @return 供应商支付
	 */
	protected MetaDataDTOWrapper findSubconPaymentByUniquenessIdJson(String appid, List<MetaDataDTOWrapper> localStore,
			String uniquenessIdJson) {
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniquenessIdJson);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> payment = basicMapper
			.findSubconPaymentByUniquenessIdJson(MetaDataUtil.handleDataIdJson2Long(uniquenessIdJson));
		return CollUtil.isEmpty(payment) ? null : new MetaDataDTOWrapper(payment);
	}

	/**
	 * 保存 供应商支付
	 * @param subconPayment 供应商支付
	 */
	protected void saveSubconPayment(MetaDataDTOWrapper subconPayment) {
		subconPayment.clearNullValue();
		Map<String, Object> subconPaymentMap = subconPayment.toMap();
		if (Objects.isNull(subconPayment.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			subconPaymentMap.put("id", dataId);
			basicMapper.saveItemData(subconPaymentMap, viewConfProperties.getSubconPayment().getTableName());
			subconPayment.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(subconPaymentMap, viewConfProperties.getSubconPayment().getTableName());
		}
	}

	/**
	 * 唯一标识数据ID 查询 YPTT结算
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param uniquenessIdJson 唯一标识数据ID JsonString格式; "[\"12331231231\"]"
	 * @return YPTT结算
	 */
	protected MetaDataDTOWrapper findYpttSettlementByUniquenessIdJson(String appid, List<MetaDataDTOWrapper> localStore,
			String uniquenessIdJson) {
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniquenessIdJson);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> settlement = basicMapper
			.findYpttSettlementByUniquenessIdJson(MetaDataUtil.handleDataIdJson2Long(uniquenessIdJson));
		return CollUtil.isEmpty(settlement) ? null : new MetaDataDTOWrapper(settlement);
	}

	/**
	 * 保存 YPTT结算
	 * @param ypttSettlement YPTT结算
	 */
	protected void saveYpttSettlement(MetaDataDTOWrapper ypttSettlement) {
		ypttSettlement.clearNullValue();
		Map<String, Object> ypttSettlementMap = ypttSettlement.toMap();
		ypttSettlement.clearNullValue();
		if (Objects.isNull(ypttSettlement.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			ypttSettlementMap.put("id", dataId);
			basicMapper.saveItemData(ypttSettlementMap, viewConfProperties.getYpttSettlement().getTableName());
			ypttSettlement.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(ypttSettlementMap, viewConfProperties.getYpttSettlement().getTableName());
		}
	}

	public void updateY78(String appid, String uniquenessIdJsonString, TransformContext context,
			MetaDataDTOWrapper existingPoItem, MetaDataDTOWrapper existingSubcon, MetaDataDTOWrapper existingSubconPo) {
		List<MetaDataDTOWrapper> projectCache = context.getProjectCache();
		// 新增或更新 分包商结算
		final List<MetaDataDTOWrapper> subconSettlementCache = context.getSubconSettlementCache();
		MetaDataDTOWrapper existingSubconSettlement = findSubconSettlementByUniquenessId(subconSettlementCache,
				uniquenessIdJsonString);
		if (Objects.isNull(existingSubconSettlement)) {
			existingSubconSettlement = new MetaDataDTOWrapper();
			existingSubconSettlement.setValue("uniqueness_field", uniquenessIdJsonString);
		}

		// 新增或更新 分包商支付
		final List<MetaDataDTOWrapper> subconPaymentCache = context.getSubconPayment();
		MetaDataDTOWrapper existingSubconPayment = findSubconPaymentByUniquenessIdJson(appid, subconPaymentCache,
				uniquenessIdJsonString);
		if (Objects.isNull(existingSubconPayment)) {
			existingSubconPayment = new MetaDataDTOWrapper();
			existingSubconPayment.setValue("uniqueness_field", uniquenessIdJsonString);
		}

		if (Objects.nonNull(existingPoItem)) {
			existingSubconSettlement.setValue("Site_ID", existingPoItem.getValue("Site_ID"));
			existingSubconSettlement.setValue("BOQ_item", existingPoItem.getValue("BOQ_item"));

			existingSubconPayment.setValue("Site_ID", existingPoItem.getValue("Site_ID"));
			existingSubconPayment.setValue("BOQ_item", existingPoItem.getValue("BOQ_item"));
			existingSubconPayment.setValue("Item_code", existingPoItem.getValue("Item_code"));
		}
		if (Objects.nonNull(existingSubcon)) {
			existingSubconSettlement.setValue("Subcon_name", existingSubcon.getValue("Subcon_name"));

			existingSubconPayment.setValue("Subcon_name", existingSubcon.getValue("Subcon_name"));
		}
		if (Objects.nonNull(existingSubconPo)) {
			existingSubconSettlement.setValue("Subcon_PO_number", existingSubconPo.getValue("Subcon_PO_number"));

			existingSubconPayment.setValue("Subcon_PO_number", existingSubconPo.getValue("Subcon_PO_number"));
		}
		// 更新
		saveSubconSettlement(existingSubconSettlement);
		saveSubconPayment(existingSubconPayment);
	}

	/**
	 * 新增y3,y4,y6,y7,y8,y9默认值
	 * @param uniquenessIdJsonString 唯一标识
	 * @param project 项目权限
	 * @param context cxt
	 * @param appid 应用id
	 */
	protected void addY3456789Module(String uniquenessIdJsonString, MetaDataDTOWrapper project,
			TransformContext context, String appid) {
		final List<MetaDataDTOWrapper> projectCache = context.getProjectCache();
		final List<MetaDataDTOWrapper> uniquenessCache = context.getUniquenessCache();
		// 查询 唯一标识
		Long uniquenessId = MetaDataUtil.handleDataIdJson2Long(uniquenessIdJsonString);
		MetaDataDTOWrapper existingUniqueness = findUniquenessById(appid, uniquenessCache, uniquenessId);
		// 查询 项目
		MetaDataDTOWrapper existingYPTTProject = findYPTTProjectByYPTTProjectCode(appid, projectCache,
				existingUniqueness.getValue("Project_code").toString());
		RLock redissonClientLock = redissonClient.getLock("addY3456789Module:" + uniquenessId);
		boolean lock = false;
		try {
			lock = redissonClientLock.tryLock(60, 90, TimeUnit.SECONDS);
			if (!lock) {
				throw new BizException(RefreshPerCode.LOCK_TIMED_OUT);
			}
			// 新增 站点条目交付信息
			final List<MetaDataDTOWrapper> siteDeliveryInfoCache = context.getSiteDeliveryInfoCache();
			MetaDataDTOWrapper existingSiteDeliveryInfo = findSiteDeliveryInfoByUniquenessId(appid,
					siteDeliveryInfoCache, uniquenessId);
			if (Objects.isNull(existingSiteDeliveryInfo)) {
				existingSiteDeliveryInfo = new MetaDataDTOWrapper();
				existingSiteDeliveryInfo.setValue("Project_code", existingUniqueness.getValue("Project_code"));
				existingSiteDeliveryInfo.setValue("uniqueness_field", uniquenessIdJsonString);
				existingSiteDeliveryInfo.setValue("Start_Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
				existingSiteDeliveryInfo.setValue("Check_Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
				existingSiteDeliveryInfo.setValue("SubconPo_Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
				// 基础信息
				Long userId = SecurityUtils.getUser().getId();
				existingSiteDeliveryInfo.setValue("create_by", userId);
				existingSiteDeliveryInfo.setValue("create_time", LocalDateTime.now());
				existingSiteDeliveryInfo.setValue("update_by", userId);
				existingSiteDeliveryInfo.setValue("update_time", LocalDateTime.now());
				saveSiteDeliveryInfo(existingSiteDeliveryInfo);
			}

			// 新增Subcon PO Item
			final List<MetaDataDTOWrapper> subconPoItemCache = context.getSubconPoItemCache();
			MetaDataDTOWrapper existingSubconPOItem = findSubconPOItemByUniquenessId(appid, subconPoItemCache,
					uniquenessId);
			if (Objects.isNull(existingSubconPOItem)) {
				existingSubconPOItem = new MetaDataDTOWrapper();
				existingSubconPOItem.setValue("Project_code", existingUniqueness.getValue("Project_code"));
				existingSubconPOItem.setValue("uniqueness_field", uniquenessIdJsonString);
				// 基础信息
				Long userId = SecurityUtils.getUser().getId();
				existingSubconPOItem.setValue("create_by", userId);
				existingSubconPOItem.setValue("create_time", LocalDateTime.now());
				existingSubconPOItem.setValue("update_by", userId);
				existingSubconPOItem.setValue("update_time", LocalDateTime.now());
				saveSubconPOItem(null, existingSubconPOItem);
			}

			// 新增或更新 可结算参考
			final List<MetaDataDTOWrapper> readySettlementCache = context.getReadySettlementCache();
			MetaDataDTOWrapper existingReadySettlement = findReadySettlementByUniquenessId(readySettlementCache,
					uniquenessIdJsonString);
			if (Objects.isNull(existingReadySettlement)) {
				existingReadySettlement = new MetaDataDTOWrapper();
				existingReadySettlement.setValue("uniqueness_field", uniquenessIdJsonString);
				existingReadySettlement.setValue("Project_code", existingUniqueness.getValue("Project_code"));
				existingReadySettlement.setValue("Project_name", existingYPTTProject.getValue("YPTT_Project_name"));
				existingReadySettlement.setValue("Phase", existingUniqueness.getValue("Phase"));
				existingReadySettlement.setValue("Item_code", existingUniqueness.getValue("Item_code"));
				existingReadySettlement.setValue("Site_ID", existingUniqueness.getValue("Site_ID"));
				existingReadySettlement.setValue("Department", existingYPTTProject.getValue("Department"));// 基础信息
				Long userId = SecurityUtils.getUser().getId();
				existingReadySettlement.setValue("create_by", userId);
				existingReadySettlement.setValue("create_time", LocalDateTime.now());
				existingReadySettlement.setValue("update_by", userId);
				existingReadySettlement.setValue("update_time", LocalDateTime.now());
				saveReadySettlement(existingReadySettlement);
			}

			// 新增或更新 产值报告
			final List<MetaDataDTOWrapper> productivityReportCache = context.getProductivityReportCache();
			MetaDataDTOWrapper existingProductivityReport = findProductivityReportByUniquenessId(
					productivityReportCache, uniquenessIdJsonString);
			if (Objects.isNull(existingProductivityReport)) {
				existingProductivityReport = new MetaDataDTOWrapper();
				existingProductivityReport.setValue("uniqueness_field", uniquenessIdJsonString);
				existingProductivityReport.setValue("Project_code", existingUniqueness.getValue("Project_code"));
				existingProductivityReport.setValue("Project_name", existingYPTTProject.getValue("YPTT_Project_name"));
				existingProductivityReport.setValue("Region", existingUniqueness.getValue("Region"));
				existingProductivityReport.setValue("Phase", existingUniqueness.getValue("Phase"));
				existingProductivityReport.setValue("Item_code", existingUniqueness.getValue("Item_code"));
				existingProductivityReport.setValue("Site_ID", existingUniqueness.getValue("Site_ID"));// 基础信息
				Long userId = SecurityUtils.getUser().getId();
				existingProductivityReport.setValue("create_by", userId);
				existingProductivityReport.setValue("create_time", LocalDateTime.now());
				existingProductivityReport.setValue("update_by", userId);
				existingProductivityReport.setValue("update_time", LocalDateTime.now());
				saveProductivityReport(existingProductivityReport);
			}

			// 新增或更新 分包商结算
			final List<MetaDataDTOWrapper> subconSettlementCache = context.getSubconSettlementCache();
			MetaDataDTOWrapper existingSubconSettlement = findSubconSettlementByUniquenessId(subconSettlementCache,
					uniquenessIdJsonString);
			if (Objects.isNull(existingSubconSettlement)) {
				existingSubconSettlement = new MetaDataDTOWrapper();
				existingSubconSettlement.setValue("uniqueness_field", uniquenessIdJsonString);
				existingSubconSettlement.setValue("Project_code", existingUniqueness.getValue("Project_code"));
				existingSubconSettlement.setValue("Site_ID", existingUniqueness.getValue("Site_ID"));// 基础信息
				Long userId = SecurityUtils.getUser().getId();
				existingSubconSettlement.setValue("create_by", userId);
				existingSubconSettlement.setValue("create_time", LocalDateTime.now());
				existingSubconSettlement.setValue("update_by", userId);
				existingSubconSettlement.setValue("update_time", LocalDateTime.now());
				saveSubconSettlement(existingSubconSettlement);
			}

			// 新增 分包商支付
			final List<MetaDataDTOWrapper> subconPaymentCache = context.getSubconPayment();
			MetaDataDTOWrapper existingSubconPayment = findSubconPaymentByUniquenessIdJson(appid, subconPaymentCache,
					uniquenessIdJsonString);
			if (Objects.isNull(existingSubconPayment)) {
				existingSubconPayment = new MetaDataDTOWrapper();
				existingSubconPayment.setValue("Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
				existingSubconPayment.setValue("Project_code", existingUniqueness.getValue("Project_code"));
				existingSubconPayment.setValue("uniqueness_field", uniquenessIdJsonString);// 基础信息
				Long userId = SecurityUtils.getUser().getId();
				existingSubconPayment.setValue("create_by", userId);
				existingSubconPayment.setValue("create_time", LocalDateTime.now());
				existingSubconPayment.setValue("update_by", userId);
				existingSubconPayment.setValue("update_time", LocalDateTime.now());
				saveSubconPayment(existingSubconPayment);
			}

			// 新增 YPTT结算
			final List<MetaDataDTOWrapper> ypttSettlementCache = context.getYpttSettlementCache();
			MetaDataDTOWrapper existingYPTTSettlement = findYpttSettlementByUniquenessIdJson(appid, ypttSettlementCache,
					uniquenessIdJsonString);
			if (Objects.isNull(existingYPTTSettlement)) {
				existingYPTTSettlement = new MetaDataDTOWrapper();
				existingYPTTSettlement.setValue("Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
				existingYPTTSettlement.setValue("uniqueness_field", uniquenessIdJsonString);
				existingYPTTSettlement.setValue("Project_code", existingUniqueness.getValue("Project_code"));
				existingYPTTSettlement.setValue("Project_name", existingYPTTProject.getValue("YPTT_Project_name"));
				existingYPTTSettlement.setValue("Phase", existingUniqueness.getValue("Phase"));
				existingYPTTSettlement.setValue("Item_code", existingUniqueness.getValue("Item_code"));
				existingYPTTSettlement.setValue("Site_ID", existingUniqueness.getValue("Site_ID"));// 基础信息
				Long userId = SecurityUtils.getUser().getId();
				existingYPTTSettlement.setValue("create_by", userId);
				existingYPTTSettlement.setValue("create_time", LocalDateTime.now());
				existingYPTTSettlement.setValue("update_by", userId);
				existingYPTTSettlement.setValue("update_time", LocalDateTime.now());
				saveYpttSettlement(existingYPTTSettlement);
			}
		}
		catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			throw new BizException(RefreshPerCode.INTERRUPTED_ERROR, e);
		}
		finally {
			if (redissonClientLock.isLocked()) {
				redissonClientLock.unlock();
			}
		}
	}

	/**
	 * 唯一标识数据ID 查询 可结算参考
	 * @param localStore 缓存
	 * @param uniquenessIdJson 唯一标识
	 * @return MetaDataDTOWrapper
	 */
	protected MetaDataDTOWrapper findReadySettlementByUniquenessId(List<MetaDataDTOWrapper> localStore,
			String uniquenessIdJson) {
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniquenessIdJson);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> settlement = basicMapper
			.findReadySettlementByUniquenessId(MetaDataUtil.handleDataIdJson2Long(uniquenessIdJson));
		return CollUtil.isEmpty(settlement) ? null : new MetaDataDTOWrapper(settlement);
	}

	/**
	 * 保存 可结算参考
	 * @param readySettlement 缓存
	 */
	protected void saveReadySettlement(MetaDataDTOWrapper readySettlement) {
		Map<String, Object> readySettlementMap = readySettlement.toMap();
		readySettlement.clearNullValue();
		if (Objects.isNull(readySettlement.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			readySettlementMap.put("id", dataId);
			basicMapper.saveItemData(readySettlementMap, viewConfProperties.getReadyForSettlement().getTableName());
			readySettlement.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(readySettlementMap, viewConfProperties.getReadyForSettlement().getTableName());
		}
	}

	/**
	 * 唯一标识数据ID 查询 产值报告
	 * @param localStore 缓存
	 * @param uniquenessIdJson 唯一标识
	 * @return MetaDataDTOWrapper
	 */
	protected MetaDataDTOWrapper findProductivityReportByUniquenessId(List<MetaDataDTOWrapper> localStore,
			String uniquenessIdJson) {
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniquenessIdJson);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> reportByUniquenessId = basicMapper
			.findProductivityReportByUniquenessId(MetaDataUtil.handleDataIdJson2Long(uniquenessIdJson));
		return CollUtil.isEmpty(reportByUniquenessId) ? null : new MetaDataDTOWrapper(reportByUniquenessId);
	}

	/**
	 * 保存 产值报告
	 * @param productivityReport 缓存
	 */
	protected void saveProductivityReport(MetaDataDTOWrapper productivityReport) {
		Map<String, Object> productivityReportMap = productivityReport.toMap();
		productivityReport.clearNullValue();
		if (Objects.isNull(productivityReport.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			productivityReportMap.put("id", dataId);
			basicMapper.saveItemData(productivityReportMap, viewConfProperties.getProductivityReport().getTableName());
			productivityReport.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(productivityReportMap,
					viewConfProperties.getProductivityReport().getTableName());
		}
	}

	/**
	 * 唯一标识数据ID 查询 分包商结算
	 * @param localStore 缓存
	 * @param uniquenessIdJson 唯一标识
	 * @return MetaDataDTOWrapper
	 */
	protected MetaDataDTOWrapper findSubconSettlementByUniquenessId(List<MetaDataDTOWrapper> localStore,
			String uniquenessIdJson) {
		MetaDataDTOWrapper hit = localFindOne(localStore, UNI_META_ATTR_UNIQUENESS_FIELD, uniquenessIdJson);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> settlementByUniquenessId = basicMapper
			.findSubconSettlementByUniquenessId(MetaDataUtil.handleDataIdJson2Long(uniquenessIdJson));
		return CollUtil.isEmpty(settlementByUniquenessId) ? null : new MetaDataDTOWrapper(settlementByUniquenessId);
	}

	/**
	 * 保存 分包商结算
	 * @param subconSettlement 缓存
	 */
	protected void saveSubconSettlement(MetaDataDTOWrapper subconSettlement) {
		Map<String, Object> subconSettlementMap = subconSettlement.toMap();
		subconSettlement.clearNullValue();
		if (Objects.isNull(subconSettlement.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			subconSettlementMap.put("id", dataId);
			basicMapper.saveItemData(subconSettlementMap, viewConfProperties.getSubconSettlement().getTableName());
			subconSettlement.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(subconSettlementMap, viewConfProperties.getSubconSettlement().getTableName());
		}
	}

	/**
	 * 保存 站点条目
	 * @param siteDataId 站点数据id
	 * @param siteItem 站点条目
	 */
	protected void saveSiteItem(Long siteDataId, MetaDataDTOWrapper siteItem) {
		Map<String, Object> siteItemMap = siteItem.toMap();
		siteItem.clearNullValue();
		if (Objects.isNull(siteItem.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			siteItemMap.put("id", dataId);
			basicMapper.saveItemData(siteItemMap, viewConfProperties.getSiteItem().getTableName());
			// 保存关系
			if (Objects.nonNull(siteDataId)) {
				System.out.println("======================================执行新增" + IdUtil.getSnowflakeNextId() + "----"+siteDataId + "----"+dataId);
				basicMapper.saveRelSite(IdUtil.getSnowflakeNextId(), siteDataId, dataId);
			}
			siteItem.setDataId(dataId);
		}
		else {
			System.out.println("==========================================执行修改" + siteItemMap);
			basicMapper.updateItemData(siteItemMap, viewConfProperties.getSiteItem().getTableName());
		}
	}

	public void y9connector(MetaDataDTOWrapper map) {
		Object uniqueness_field = map.getValue("uniqueness_field");
		BigDecimal invoiceAmount = MetaDataUtil.handleObject2BigDecimal(map.getValue("Invoice_amount"), true);
		BigDecimal invoiceAmount1st = MetaDataUtil.handleObject2BigDecimal(map.getValue("Invoice_Amount_1st"), false);
		BigDecimal invoiceAmount2st = MetaDataUtil.handleObject2BigDecimal(map.getValue("Invoice_Amount_2st"), false);
		BigDecimal invoiceAmount3st = MetaDataUtil.handleObject2BigDecimal(map.getValue("Invoice_Amount_3st"), false);
		BigDecimal invoiceAmount4st = MetaDataUtil.handleObject2BigDecimal(map.getValue("Invoice_Amount_4st"), false);
		invoiceAmount = invoiceAmount.add(Objects.isNull(invoiceAmount1st) ? BigDecimal.ZERO : invoiceAmount1st)
			.add(Objects.isNull(invoiceAmount2st) ? BigDecimal.ZERO : invoiceAmount2st)
			.add(Objects.isNull(invoiceAmount3st) ? BigDecimal.ZERO : invoiceAmount3st)
			.add(Objects.isNull(invoiceAmount4st) ? BigDecimal.ZERO : invoiceAmount4st);
		map.setValue("Invoice_amount", invoiceAmount);
		if (Objects.isNull(uniqueness_field)) {
			return;
		}
		Long unId = MetaDataUtil.handleDataIdJson2Long(uniqueness_field.toString());
		List<Map<String, Object>> siteItem = basicMapper.findSiteItemByUniquenessField(unId);
		List<Map<String, Object>> incomeExpenditure = basicMapper.findIncomeExpenditureByUniquenessField(unId);
		List<Map<String, Object>> readySettlement = basicMapper.findReadySettlementByUniquenessId(unId);
		MetaDataDTOWrapper incomeExpenditureWrapper = new MetaDataDTOWrapper(incomeExpenditure);
		if (CollUtil.isNotEmpty(readySettlement)) {
			MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(readySettlement);
			BigDecimal amount1st = MetaDataUtil.handleObject2BigDecimal(wrapper.getValue("amount_1st"), true);
			BigDecimal amount2nd = MetaDataUtil.handleObject2BigDecimal(wrapper.getValue("amount_2nd"), true);
			BigDecimal amount3rd = MetaDataUtil.handleObject2BigDecimal(wrapper.getValue("amount_3rd"), true);
			BigDecimal amount4th = MetaDataUtil.handleObject2BigDecimal(wrapper.getValue("amount_4th"), true);
			BigDecimal readySettlementAmount = MetaDataUtil
				.handleObject2BigDecimal(wrapper.getValue("settlement_Amount"), true);
			if (Objects.isNull(readySettlementAmount)) {
				map.setValue("Invoice_amount_gap", null);
			}
			else {
				map.setValue("Invoice_amount_gap", readySettlementAmount.subtract(invoiceAmount));
			}
			if (Objects.nonNull(invoiceAmount1st)) {
				map.setValue("Invoice_Amount_diff_1st", amount1st.subtract(invoiceAmount1st));
				incomeExpenditureWrapper.setValue("Invoice_amount_date",
						MetaDataUtil.handleObject2String(map.getValue("Invoice_date_1st")));
				incomeExpenditureWrapper.setValue("Invoice_amount", invoiceAmount1st);
			}
			if (Objects.nonNull(invoiceAmount2st)) {
				incomeExpenditureWrapper.setValue("Invoice_amount_date2",
						MetaDataUtil.handleObject2String(map.getValue("Invoice_date_2st")));
				map.setValue("Invoice_Amount_diff_2st", amount2nd.subtract(invoiceAmount2st));
				incomeExpenditureWrapper.setValue("Invoice_amount_2", invoiceAmount2st);
			}
			if (Objects.nonNull(invoiceAmount3st)) {
				incomeExpenditureWrapper.setValue("Invoice_amount_date3",
						MetaDataUtil.handleObject2String(map.getValue("Invoice_date_3st")));
				map.setValue("Invoice_Amount_diff_3st", amount3rd.subtract(invoiceAmount3st));
				incomeExpenditureWrapper.setValue("Invoice_amount_3", invoiceAmount3st);
			}
			if (Objects.nonNull(invoiceAmount4st)) {
				incomeExpenditureWrapper.setValue("Invoice_amount_date4",
						MetaDataUtil.handleObject2String(map.getValue("Invoice_date_4st")));
				map.setValue("Invoice_Amount_diff_4st", amount4th.subtract(invoiceAmount4st));
				incomeExpenditureWrapper.setValue("Invoice_amount_4", invoiceAmount4st);
			}
		}
		saveIncomeExpenditure(incomeExpenditureWrapper);
		// 站点条目状态
		if (CollUtil.isNotEmpty(siteItem)) {
			MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(siteItem);
			y1connector(wrapper);
			saveSiteItem(null, wrapper);
		}
	}

	public void y8connector(MetaDataDTOWrapper map) {
		Object uniqueness_field = map.getValue("uniqueness_field");
		BigDecimal paymentAmount1st = MetaDataUtil.handleObject2BigDecimal(map.getValue("payment_amount_1st"), true);
		BigDecimal paymentAmount2st = MetaDataUtil.handleObject2BigDecimal(map.getValue("payment_amount_2st"), true);
		BigDecimal paymentAmount3st = MetaDataUtil.handleObject2BigDecimal(map.getValue("payment_amount_3st"), true);
		BigDecimal paymentAmount4st = MetaDataUtil.handleObject2BigDecimal(map.getValue("payment_amount_4st"), true);
		BigDecimal paymentAmount = paymentAmount1st.add(paymentAmount2st.add(paymentAmount3st.add(paymentAmount4st)));
		if (Objects.isNull(uniqueness_field)) {
			return;
		}
		Long unId = MetaDataUtil.handleDataIdJson2Long(uniqueness_field.toString());
		List<Map<String, Object>> subconSettlement = basicMapper.findSubconSettlementByUniquenessId(unId);
		List<Map<String, Object>> siteItem = basicMapper.findSiteItemByUniquenessField(unId);
		List<Map<String, Object>> incomeExpenditure = basicMapper.findIncomeExpenditureByUniquenessField(unId);
		MetaDataDTOWrapper incomeExpenditureWrapper = new MetaDataDTOWrapper(incomeExpenditure);
		String paymentTime1st = MetaDataUtil.handleObject2String(map.getValue("Payment_time_1st"));
		String paymentTime2st = MetaDataUtil.handleObject2String(map.getValue("Payment_time_2st"));
		String paymentTime3st = MetaDataUtil.handleObject2String(map.getValue("Payment_time_3st"));
		String paymentTime4st = MetaDataUtil.handleObject2String(map.getValue("Payment_time_4st"));
		map.setValue("Totally_payment", paymentAmount);
		if (StrUtil.isNotBlank(paymentTime1st)) {
			incomeExpenditureWrapper.setValue("Subcon_payment", paymentAmount1st);
			incomeExpenditureWrapper.setValue("Subcon_payment_date", paymentTime1st);
		}
		if (StrUtil.isNotBlank(paymentTime2st)) {
			incomeExpenditureWrapper.setValue("Subcon_payment_2", paymentAmount2st);
			incomeExpenditureWrapper.setValue("Subcon_payment_date2", paymentTime2st);
		}
		if (StrUtil.isNotBlank(paymentTime3st)) {
			incomeExpenditureWrapper.setValue("Subcon_payment_3", paymentAmount3st);
			incomeExpenditureWrapper.setValue("Subcon_payment_date3", paymentTime3st);
		}
		if (StrUtil.isNotBlank(paymentTime4st)) {
			incomeExpenditureWrapper.setValue("Subcon_payment_4", paymentAmount4st);
			incomeExpenditureWrapper.setValue("Subcon_payment_date4", paymentTime4st);
		}
		if (CollUtil.isNotEmpty(subconSettlement)) {
			MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(subconSettlement);
			BigDecimal totallyAmount = MetaDataUtil.handleObject2BigDecimal(wrapper.getValue("Totally_Amount"), true);
			if (Objects.isNull(totallyAmount)) {
				map.setValue("Totally_payment_gap", null);
			}
			else {
				map.setValue("Totally_payment_gap", totallyAmount.subtract(paymentAmount));
			}
		}
		saveIncomeExpenditure(incomeExpenditureWrapper);
		// 站点条目状态
		if (CollUtil.isNotEmpty(siteItem)) {
			MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(siteItem);
			y1connector(wrapper);
			saveSiteItem(null, wrapper);
		}
	}

	public void y4connector(MetaDataDTOWrapper map) {
		Object uniqueness_field = map.getValue("uniqueness_field");
		if (Objects.isNull(uniqueness_field)) {
			return;
		}
		BigDecimal milestone1st = MetaDataUtil.handleObject2BigDecimal(map.getValue("Milestone_1st"), true);
		BigDecimal milestone2nd = MetaDataUtil.handleObject2BigDecimal(map.getValue("Milestone_2nd"), true);
		BigDecimal milestone3rd = MetaDataUtil.handleObject2BigDecimal(map.getValue("Milestone_3rd"), true);
		BigDecimal milestone4th = MetaDataUtil.handleObject2BigDecimal(map.getValue("Milestone_4th"), true);

		BigDecimal quantityReduce = MetaDataUtil.handleObject2BigDecimal(map.getValue("quantity_reduce"), true);
		BigDecimal quantity = MetaDataUtil.handleObject2BigDecimal(map.getValue("Quantity"), true);
		BigDecimal unitPrice = MetaDataUtil.handleObject2BigDecimal(map.getValue("Unit_price"), true);
		BigDecimal subconPoAmount = (quantity.subtract(quantityReduce)).multiply(unitPrice);
		map.setValue("Subcon_PO_amount", subconPoAmount);
		Long unId = MetaDataUtil.handleDataIdJson2Long(uniqueness_field.toString());
		List<Map<String, Object>> settlement = basicMapper.findSubconSettlementByUniquenessId(unId);
		List<Map<String, Object>> incomeExpenditure = basicMapper.findIncomeExpenditureByUniquenessField(unId);
		List<Map<String, Object>> subconPayment = basicMapper.findSubconPaymentByUniquenessIdJson(unId);
		List<Map<String, Object>> siteItem = basicMapper.findSiteItemByUniquenessField(unId);
		MetaDataDTOWrapper incomeExpenditureWrapper = new MetaDataDTOWrapper(incomeExpenditure);
		incomeExpenditureWrapper.setValue("Subcon_PO_amount", subconPoAmount);
		// 分包商结算
		if (CollUtil.isNotEmpty(settlement)) {
			MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(settlement);
			String settlementTime1st = MetaDataUtil.handleObject2String(wrapper.getValue("settlement_time_1st"));
			String settlementTime2nd = MetaDataUtil.handleObject2String(wrapper.getValue("settlement_time_2nd"));
			String settlementTime3rd = MetaDataUtil.handleObject2String(wrapper.getValue("settlement_time_3rd"));
			String settlementTime4th = MetaDataUtil.handleObject2String(wrapper.getValue("settlement_time_4th"));
			BigDecimal totallyAmount = BigDecimal.ZERO;
			if (StrUtil.isNotBlank(settlementTime1st)) {
				BigDecimal decimal = subconPoAmount.multiply(milestone1st).setScale(6, RoundingMode.HALF_UP);
				totallyAmount = totallyAmount.add(decimal);
				wrapper.setValue("settlement_ratio_1st", milestone1st);
				wrapper.setValue("settlementAmount_1st", decimal);
				incomeExpenditureWrapper.setValue("Subcon_settlement_d", settlementTime1st);
			}
			if (StrUtil.isNotBlank(settlementTime2nd)) {
				BigDecimal decimal = subconPoAmount.multiply(milestone2nd).setScale(6, RoundingMode.HALF_UP);
				totallyAmount = totallyAmount.add(decimal);
				wrapper.setValue("settlement_ratio_2nd", milestone2nd);
				wrapper.setValue("settlementAmount_2nd", decimal);
				incomeExpenditureWrapper.setValue("Subcon_settlement_d", settlementTime2nd);
			}
			if (StrUtil.isNotBlank(settlementTime3rd)) {
				BigDecimal decimal = subconPoAmount.multiply(milestone3rd).setScale(6, RoundingMode.HALF_UP);
				totallyAmount = totallyAmount.add(decimal);
				wrapper.setValue("settlement_ratio_3rd", milestone3rd);
				wrapper.setValue("settlementAmount_3rd", decimal);
				incomeExpenditureWrapper.setValue("Subcon_settlement_d", settlementTime3rd);
			}
			if (StrUtil.isNotBlank(settlementTime4th)) {
				BigDecimal decimal = subconPoAmount.multiply(milestone4th).setScale(6, RoundingMode.HALF_UP);
				totallyAmount = totallyAmount.add(decimal);
				wrapper.setValue("settlement_ratio_4th", milestone4th);
				wrapper.setValue("settlementAmount_4th", decimal);
				incomeExpenditureWrapper.setValue("Subcon_settlement_d", settlementTime4th);
			}
			wrapper.setValue("Totally_Amount", totallyAmount);
			if (BigDecimal.ZERO.compareTo(subconPoAmount) == 0 || BigDecimal.ZERO.compareTo(totallyAmount) == 0) {
				wrapper.setValue("Totally_amount_Gap", null);
			}
			else {
				wrapper.setValue("Totally_amount_Gap", subconPoAmount.subtract(totallyAmount));
			}
			incomeExpenditureWrapper.setValue("Subcon_settlement", totallyAmount);
			saveSubconSettlement(wrapper);
		}
		// 分包商支付
		if (CollUtil.isNotEmpty(subconPayment)) {
			MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(subconPayment);
			BigDecimal totallyPayment = MetaDataUtil.handleObject2BigDecimal(wrapper.getValue("Totally_payment"), true);
			if (BigDecimal.ZERO.compareTo(subconPoAmount) == 0 || BigDecimal.ZERO.compareTo(totallyPayment) == 0) {
				wrapper.setValue("Totally_payment_gap", null);
			}
			else {
				wrapper.setValue("Totally_payment_gap", subconPoAmount.subtract(totallyPayment));
			}
			saveSubconPayment(wrapper);
		}
		// 收支统计
		saveIncomeExpenditure(incomeExpenditureWrapper);
		// 站点条目状态
		if (CollUtil.isNotEmpty(siteItem)) {
			MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(siteItem);
			y1connector(wrapper);
			saveSiteItem(null, wrapper);
		}

	}

	public void y3connector(MetaDataDTOWrapper map) {
		Object uniqueness_field = map.getValue("uniqueness_field");
		Object id = map.getValue("id");
		if (Objects.isNull(id) || Objects.isNull(uniqueness_field)) {
			return;
		}
		Long dataId = Long.parseLong(id.toString());
		Long unId = MetaDataUtil.handleDataIdJson2Long(uniqueness_field.toString());
		dataMangeService.toY3Connector(map.toMap(), unId, dataId);
	}

	public void y2connector(MetaDataDTOWrapper poItemMap) {
		Object Contract_number = poItemMap.getValue("Contract_number");
		BigDecimal quantity = MetaDataUtil.handleObject2BigDecimal(poItemMap.getValue("Quantity"), true);
		BigDecimal unitPrice = MetaDataUtil.handleObject2BigDecimal(poItemMap.getValue("Unit_price"), true);
		BigDecimal quantityReduce = MetaDataUtil.handleObject2BigDecimal(poItemMap.getValue("quantity_reduce"), true);
		BigDecimal poValue = (quantity.subtract(quantityReduce)).multiply(unitPrice).setScale(6, RoundingMode.HALF_UP);
		poItemMap.setValue("PO_value", poValue);
		BigDecimal milestone1st = Objects.isNull(poItemMap.getValue("Milestone_1st")) ? null
				: new BigDecimal(poItemMap.getValue("Milestone_1st").toString());
		BigDecimal milestone2nd = Objects.isNull(poItemMap.getValue("Milestone_2nd")) ? null
				: new BigDecimal(poItemMap.getValue("Milestone_2nd").toString());
		BigDecimal milestone3rd = Objects.isNull(poItemMap.getValue("Milestone_3rd")) ? null
				: new BigDecimal(poItemMap.getValue("Milestone_3rd").toString());
		BigDecimal milestone4th = Objects.isNull(poItemMap.getValue("Milestone_4th")) ? null
				: new BigDecimal(poItemMap.getValue("Milestone_4th").toString());
		Object uniquenessField = poItemMap.getValue("uniqueness_field");
		if (Objects.nonNull(uniquenessField)) {
			Long unId = MetaDataUtil.handleDataIdJson2Long(uniquenessField.toString());
			// 查询站点交付信息
			List<Map<String, Object>> siteDeliveryInfoByUniquenessId = basicMapper
				.findSiteDeliveryInfoByUniquenessId(unId);
			if (CollUtil.isEmpty(siteDeliveryInfoByUniquenessId)) {
				return;
			}
			Map<String, Object> siteDeliveryInfoMap = siteDeliveryInfoByUniquenessId.get(0);
			Object settlement1st = siteDeliveryInfoMap.get("settlement_1st");
			Object settlement2nd = siteDeliveryInfoMap.get("settlement_2nd");
			Object settlement3rd = siteDeliveryInfoMap.get("settlement_3rd");
			Object settlement4th = siteDeliveryInfoMap.get("settlement_4th");
			// 查询可结算数据
			List<Map<String, Object>> readySettlementByUniquenessId = basicMapper
				.findReadySettlementByUniquenessId(unId);
			// 查询 产值报告
			List<Map<String, Object>> reportByUniquenessId = basicMapper.findProductivityReportByUniquenessId(unId);
			// 查询 YPTT结算
			List<Map<String, Object>> ypttSettlementByUniquenessId = basicMapper
				.findYpttSettlementByUniquenessIdJson(unId);
			// 查询站点条目
			List<Map<String, Object>> siteItemByUniquenessField = basicMapper.findSiteItemByUniquenessField(unId);
			// 查询 收支项目
			List<Map<String, Object>> incomeExpenditureByUniquenessField = basicMapper
				.findIncomeExpenditureByUniquenessField(unId);
			Map<String, Object> incomeExpenditureMap = new HashMap<>();
			if (CollUtil.isNotEmpty(incomeExpenditureByUniquenessField)) {
				incomeExpenditureMap = incomeExpenditureByUniquenessField.get(0);
			}
			BigDecimal readySettlementAmount = BigDecimal.ZERO;
			BigDecimal prePay = BigDecimal.ZERO;
//			List<Long> roles = SecurityUtils.getRoles();
			if (CollUtil.isNotEmpty(readySettlementByUniquenessId)) {
				BigDecimal amount1 = BigDecimal.ZERO;
				BigDecimal amount2 = BigDecimal.ZERO;
				BigDecimal amount3 = BigDecimal.ZERO;
				BigDecimal amount4 = BigDecimal.ZERO;
				Map<String, Object> readySettlementMap = readySettlementByUniquenessId.get(0);
				MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(Collections.singletonList(readySettlementMap));
				wrapper.setValue("Pre_Settlement_date", poItemMap.getValue("PO_Received_date"));
				wrapper.setValue("Pre_payment_ratio", poItemMap.getValue("Pre_payment"));
				BigDecimal prePayment = new BigDecimal(poItemMap.getValue("Pre_payment").toString());
				prePay = prePayment.multiply(poValue).setScale(6, RoundingMode.HALF_UP);
				wrapper.setValue("Pre_payment_amount", prePay);


				if (Objects.nonNull(settlement1st)) {
					incomeExpenditureMap.put("Ready_settlement_d", settlement1st);
					amount1 = poValue.multiply(milestone1st).setScale(6, RoundingMode.HALF_UP);
					wrapper.setValue("amount_1st", amount1);
					wrapper.setValue("settlement_1st", settlement1st);
					wrapper.setValue("Settlement_ratio_1st", milestone1st);
				}
				if (Objects.nonNull(settlement2nd)) {
					incomeExpenditureMap.put("Ready_settlement_d", settlement2nd);
					amount2 = poValue.multiply(milestone2nd).setScale(6, RoundingMode.HALF_UP);
					wrapper.setValue("amount_2nd", amount2);
					wrapper.setValue("settlement_2nd", settlement2nd);
					wrapper.setValue("Settlement_ratio_2nd", milestone2nd);
				}
				if (Objects.nonNull(settlement3rd)) {
					incomeExpenditureMap.put("Ready_settlement_d", settlement3rd);
					amount3 = poValue.multiply(milestone3rd).setScale(6, RoundingMode.HALF_UP);
					wrapper.setValue("amount_3rd", amount3);
					wrapper.setValue("settlement_3rd", settlement3rd);
					wrapper.setValue("Settlement_ratio_3rd", milestone3rd);
				}
				if (Objects.nonNull(settlement4th)) {
					incomeExpenditureMap.put("Ready_settlement_d", settlement4th);
					amount4 = poValue.multiply(milestone4th).setScale(6, RoundingMode.HALF_UP);
					wrapper.setValue("amount_4th", amount1);
					wrapper.setValue("settlement_4th", settlement4th);
					wrapper.setValue("Settlement_ratio_4th", milestone4th);
				}
				readySettlementAmount = amount1.add(amount2.add(amount3.add(amount4.add(prePay))));
				incomeExpenditureMap.put("Ready_settlement", readySettlementAmount);

				wrapper.setValue("settlement_Amount", readySettlementAmount);
				wrapper.setValue("PO_value", poValue);
				wrapper.setValue("BOQ_item", poItemMap.getValue("BOQ_item"));
				wrapper.setValue("settlement_amountGap", poValue.subtract(readySettlementAmount));
				saveReadySettlement(wrapper);
			}
			if (CollUtil.isNotEmpty(reportByUniquenessId)) {
				Map<String, Object> reportMap = reportByUniquenessId.get(0);
				BigDecimal amount1 = BigDecimal.ZERO;
				BigDecimal amount2 = BigDecimal.ZERO;
				BigDecimal amount3 = BigDecimal.ZERO;
				BigDecimal amount4 = BigDecimal.ZERO;
				MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(Collections.singletonList(reportMap));
				if (Objects.nonNull(settlement1st)) {
					incomeExpenditureMap.put("Productivity_AmountD", settlement1st);
					amount1 = poValue.multiply(milestone1st).add(prePay).setScale(6, RoundingMode.HALF_UP);
					wrapper.setValue("report_amount_1st", amount1);
					wrapper.setValue("report_date_1st", settlement1st);
				}
				if (Objects.nonNull(settlement2nd)) {
					incomeExpenditureMap.put("Productivity_AmountD", settlement2nd);
					amount2 = poValue.multiply(milestone2nd).setScale(6, RoundingMode.HALF_UP);
					wrapper.setValue("report_amount_2nd", amount2);
					wrapper.setValue("report_date_2nd", settlement2nd);
				}
				if (Objects.nonNull(settlement3rd)) {
					incomeExpenditureMap.put("Productivity_AmountD", settlement3rd);
					amount3 = poValue.multiply(milestone3rd).setScale(6, RoundingMode.HALF_UP);
					wrapper.setValue("report_amount_3rd", amount3);
					wrapper.setValue("report_date_3rd", settlement3rd);
				}
				if (Objects.nonNull(settlement4th)) {
					incomeExpenditureMap.put("Productivity_AmountD", settlement4th);
					amount4 = poValue.multiply(milestone4th).setScale(6, RoundingMode.HALF_UP);
					wrapper.setValue("report_amount_4th", amount4);
					wrapper.setValue("report_date_4th", settlement4th);
				}
				BigDecimal decimal = amount1.add(amount2.add(amount3.add(amount4)));
				wrapper.setValue("Productivity_Amount", decimal);
				wrapper.setValue("declaration_ratio", poValue.compareTo(BigDecimal.ZERO) > 0
						? decimal.divide(poValue, 4, RoundingMode.HALF_UP) : null);
				incomeExpenditureMap.put("Productivity_Amount", decimal);

				wrapper.setValue("PO_value", poValue);
				wrapper.setValue("BOQ_item", poItemMap.getValue("BOQ_item"));
				saveProductivityReport(wrapper);
			}
			if (CollUtil.isNotEmpty(ypttSettlementByUniquenessId)) {
				Map<String, Object> ypttSettlementMap = ypttSettlementByUniquenessId.get(0);
				MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(Collections.singletonList(ypttSettlementMap));
				BigDecimal invoiceAmount = Objects.isNull(wrapper.getValue("Invoice_amount")) ? BigDecimal.ZERO
						: new BigDecimal(wrapper.getValue("Invoice_amount").toString());
				wrapper.setValue("Invoice_amount_gap", readySettlementAmount.subtract(invoiceAmount));
				wrapper.setValue("PO_value", poValue);
				wrapper.setValue("Contract_number", Contract_number);
				saveYpttSettlement(wrapper);
			}
			if (CollUtil.isNotEmpty(siteItemByUniquenessField)) {
				MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(siteItemByUniquenessField);
				y1connector(wrapper);
				BigDecimal siteValue = MetaDataUtil.handleObject2BigDecimal(wrapper.getValue("Site_value"), true);
				poItemMap.setValue("PO_gap", siteValue.subtract(poValue));
				saveSiteItem(null, wrapper);
			}
			// 保存 收支项目统计
			saveIncomeExpenditure(new MetaDataDTOWrapper(Collections.singletonList(incomeExpenditureMap)));
		}

	}

	public void y1connector(MetaDataDTOWrapper siteItemMap) {
		BigDecimal unitPrice = MetaDataUtil.handleObject2BigDecimal(siteItemMap.getValue("Unit_price"), true);
		BigDecimal quantity = MetaDataUtil.handleObject2BigDecimal(siteItemMap.getValue("Quantity"), true);
		BigDecimal siteValue = unitPrice.multiply(quantity).setScale(6, RoundingMode.HALF_UP);
		Object uniquenessField = siteItemMap.getValue("uniqueness_field");
		if (Objects.nonNull(uniquenessField)) {
			Long unId = MetaDataUtil.handleDataIdJson2Long(uniquenessField.toString());
			List<Map<String, Object>> poItemByUniquenessId = basicMapper.findPOItemByUniquenessId(unId);
			List<Map<String, Object>> readySettlement = basicMapper.findReadySettlementByUniquenessId(unId);
			List<Map<String, Object>> siteDeliveryInfo = basicMapper.findSiteDeliveryInfoByUniquenessId(unId);
			List<Map<String, Object>> ypttSettlementByUniquenessId = basicMapper
				.findYpttSettlementByUniquenessIdJson(unId);
			List<Map<String, Object>> subconPaymentByUniquenessId = basicMapper
				.findSubconPaymentByUniquenessIdJson(unId);
			List<Map<String, Object>> subconPOItemd = basicMapper.findSubconPOItemByUniquenessId(unId);
			if (CollUtil.isEmpty(siteDeliveryInfo)) {
				return;
			}
			Map<String, Object> deliveryInfo = siteDeliveryInfo.get(0);
			String siteBelongTo = MetaDataUtil.handleObject2String(deliveryInfo.get("Site_belong_to"));
			if (CollUtil.isEmpty(poItemByUniquenessId) || CollUtil.isEmpty(ypttSettlementByUniquenessId)
					|| ((CollUtil.isEmpty(subconPaymentByUniquenessId) || CollUtil.isEmpty(subconPOItemd))
							&& !Objects.equals(siteBelongTo, "YPTT"))
					|| CollUtil.isEmpty(readySettlement)) {
				siteItemMap.setValue("Site_item_status", MetaDataUtil.handleValues2Json(SITE_STATUE_UNCLOSE));
				return;
			}
			Map<String, Object> poitemMap = poItemByUniquenessId.get(0);
			Map<String, Object> ypttMap = ypttSettlementByUniquenessId.get(0);
			Map<String, Object> readySettlementMap = readySettlement.get(0);
			Map<String, Object> subconPOItemdMap = Objects.equals(siteBelongTo, "YPTT") ? new HashMap<>()
					: subconPOItemd.get(0);
			Map<String, Object> subconPaymentMap = Objects.equals(siteBelongTo, "YPTT") ? new HashMap<>()
					: subconPaymentByUniquenessId.get(0);
			Object po_value = poitemMap.get("PO_value");
			Object yptt_amount = ypttMap.get("Invoice_amount");
			Object payment = subconPaymentMap.get("Totally_payment");
			Object subconPoAmount = subconPOItemdMap.get("Subcon_PO_amount");
			Object readyAmount = readySettlementMap.get("settlement_Amount");
			if (Objects.isNull(po_value) || Objects.isNull(yptt_amount) || Objects.isNull(readyAmount)
					|| ((Objects.isNull(payment) || Objects.isNull(subconPoAmount))
							&& !Objects.equals(siteBelongTo, "YPTT"))) {
				siteItemMap.setValue("Site_item_status", MetaDataUtil.handleValues2Json(SITE_STATUE_UNCLOSE));
				return;
			}
			BigDecimal poValue = new BigDecimal(po_value.toString());
			BigDecimal yptt = new BigDecimal(yptt_amount.toString());
			BigDecimal ready = new BigDecimal(readyAmount.toString());
			BigDecimal subconPo = Objects.isNull(subconPoAmount) ? BigDecimal.ZERO
					: new BigDecimal(subconPoAmount.toString());
			BigDecimal pay = Objects.isNull(payment) ? new BigDecimal("-1") : new BigDecimal(payment.toString());

			if (poValue.compareTo(BigDecimal.ZERO) == 0 || yptt.compareTo(BigDecimal.ZERO) == 0
					|| ready.compareTo(BigDecimal.ZERO) == 0 || subconPo.compareTo(BigDecimal.ZERO) == 0
					|| pay.compareTo(BigDecimal.ZERO) == 0 || siteValue.compareTo(BigDecimal.ZERO) == 0) {
				return;
			}
			if (poValue.compareTo(siteValue) == 0 && yptt.compareTo(ready) == 0 && poValue.compareTo(yptt) == 0
					&& (pay.compareTo(subconPo) == 0 || Objects.equals(siteBelongTo, "YPTT"))) {
				siteItemMap.setValue("Site_item_status", MetaDataUtil.handleValues2Json(SITE_STATUE_CLOSED));
			}
		}

	}

}
