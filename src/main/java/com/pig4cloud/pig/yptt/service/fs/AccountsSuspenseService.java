package com.pig4cloud.pig.yptt.service.fs;

import com.pig4cloud.pig.yptt.mapper.AccountsSuspenseMpper;
import com.pig4cloud.pig.yptt.service.ViewModelRelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * @ClassName: AccountsSuspenseService
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-08-11  14:14
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountsSuspenseService {
    private final ViewModelRelService viewModelRelService;
    private final AccountsSuspenseMpper accountsSuspenseMpper;

    private final String modelName = "f_ca_loan";
    /**
     * 财务挂账支付扣减已剩余支付金额
     * @param dataId 财务挂账id
     * @param paymentMoney 扣除金额
     * @return 扣除成功
     **/
    public Boolean reducePaymentMoney(Long dataId,BigDecimal paymentMoney){
        // 获取物理表
        String modelTableName = viewModelRelService.getModelTableNameByModelName(modelName);
        // 是否超出剩余金额
        BigDecimal unpaidMoney = accountsSuspenseMpper.selectUnpaidMoney(modelTableName, dataId);

        if(unpaidMoney.compareTo(paymentMoney)<0){
            log.error("扣除金额（{}）超出剩余金额（{}）,挂账数据ID:{}",paymentMoney,unpaidMoney,dataId);
            return false;
        }
        // 扣除金额
        boolean flag = accountsSuspenseMpper.updateUnpaidMoney(modelTableName, dataId, paymentMoney);

        if (!flag){
            log.error("扣除金额（{}）失败,挂账数据ID:{}",paymentMoney,dataId);
            return false;
        }

        return true;
    }
}