package com.pig4cloud.pig.yptt.service.transform;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.DataPermissionsService;
import com.pig4cloud.pig.yptt.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Y8 供应商支付 转为 供应商支付
 *
 * <AUTHOR>
 * @date 2023/10/7
 */
@Component
@Slf4j
public class Y8Transformer extends AbstractTransformer {

    private final static String MODULE_NAME = GlobalConstants.Y8.NAME;

    public Y8Transformer(RemoteAppService remoteAppService, ViewConfProperties viewConfProperties,
                         DataPermissionsService dataPermissionsService, BasicMapper basicMapper, RedissonClient redissonClient,
                         DataMangeService dataMangeService) {
        super(remoteAppService, viewConfProperties, dataPermissionsService, basicMapper, redissonClient,
                dataMangeService);
    }

    @Override
    public ImportResultVO doValidate(TransformContext context, int index, Map<String, Object> raw,
                                     ImportResultVO valid) {
        final String appid = context.getAppid();
        final List<MetaDataDTOWrapper> subconCache = context.getSubconCache();
        final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
        final List<MetaDataDTOWrapper> siteItemCache = context.getSiteItemCache();
        final List<MetaDataDTOWrapper> subconPoCache = context.getSubconPoCache();
        final List<MetaDataDTOWrapper> subconPoItemCache = context.getSubconPoItemCache();
        final List<MetaDataDTOWrapper> subconPaymentCache = context.getSubconPayment();

        Dict dict = new Dict(raw);
        String Subcon_name = dict.getStr("Subcon_name");
        String Subcon_PO_number = dict.getStr("Subcon_PO_number");
        String Site_ID = dict.getStr("Site_ID");
        String Item_code = dict.getStr("Item_code");
        String Phase = dict.getStr("Phase");
        String uniqueness_field = MetaDataUtil.getUniquenessField(dict, GlobalConstants.Y8.NAME);

        // 查询 供应商PO
        MetaDataDTOWrapper existingSubconPO = findSubconPOBySubconPONumber(appid, subconPoCache, Subcon_PO_number);
        if (Objects.isNull(existingSubconPO)) {
            return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED, String
                    .format("Subcon PO [%s] has not been added yet. Please add the Subcon PO first.", Subcon_PO_number));
        }
        subconPoCache.add(existingSubconPO);

        // 查询 分包商
        MetaDataDTOWrapper existingSubcon = findSubconBySubconName(appid, subconCache, Subcon_name);
        if (Objects.isNull(existingSubcon)) {
            return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED,
                    String.format("Subcon [%s] has not been added yet. Please add the subcon first.", Subcon_name));
        }
        subconCache.add(existingSubcon);

        // 查询 站点
        MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
        if (Objects.isNull(existingSite)) {
            return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED,
                    String.format("Site [%s] has not been added yet. Please add the site first", Site_ID));
        }
        String projectCode = ""; //existingSite.getValue("Project_code").toString();
        siteCache.add(existingSite);

        // 查询 分包商PO条目
        List<MetaDataDTOWrapper> existingSubconPOItemList = findSubconPOItem(appid, subconPoItemCache,
                existingSubconPO.getDataId(), existingSubcon.getDataId(), existingSite.getDataId(), Item_code, Phase);
        if (CollUtil.size(existingSubconPOItemList) < 1) {
            return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED, String
                    .format("Subcon PO item [%s] has not been added yet. Please add the PO item first.", uniqueness_field));
        }
        if (CollUtil.size(existingSubconPOItemList) > 1) {
            return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED,
                    String.format("The number of Subcon PO item [%s] should be 1, but get 2", uniqueness_field));
        }
        MetaDataDTOWrapper existingSubconPoItem = CollUtil.getFirst(existingSubconPOItemList);
        subconPoItemCache.add(existingSubconPoItem);

        // 查询 站点条目, 验证站点条目是否关闭
        final String uniquenessIdJsonString = (String) existingSubconPoItem.getValue(UNI_META_ATTR_UNIQUENESS_FIELD);
        final Long uniquenessId = MetaDataUtil.handleDataIdJson2Long(uniquenessIdJsonString);
        if (Objects.nonNull(uniquenessId)) {
            MetaDataDTOWrapper existingSiteItem = findSiteItemByUniquenessField(appid, siteItemCache, uniquenessId);
            if (Objects.isNull(existingSiteItem) || Objects.isNull(existingSiteItem.getValue("site"))) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Site item [%s] not found.", uniqueness_field));
                return valid;
            }
            System.out.println("existingSiteItem"+existingSiteItem);
            boolean isClosedSiteItem = isClosedSiteItem(existingSiteItem);
            if (isClosedSiteItem) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Site item [%s] is closed.", uniqueness_field));
                return valid;
            }
            siteItemCache.add(existingSiteItem);
            // 项目权限验证
            Long ypttProjectDataId = MetaDataUtil
                    .handleDataIdJson2Long((String) existingSiteItem.getValue("TPTT_Project"));
            if (Objects.isNull(ypttProjectDataId)) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(
                        String.format("Please improve site item[%s] project information.", uniqueness_field));
                return valid;
            }
            this.validatePerm(context, ypttProjectDataId);
            projectCode = (String) existingSiteItem.getValue("Project_code");
        }
        MetaDataDTOWrapper siteDelivery = findSiteDeliveryInfoByUniquenessId(appid, Collections.emptyList(),
                uniquenessId);
        if (Objects.nonNull(siteDelivery)) {
            String siteBelongTo = MetaDataUtil.handleObject2String(siteDelivery.getValue("Site_belong_to"));
            if (Objects.equals(siteBelongTo, "YPTT")) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason("Site_belong_to YPTT and upload are not allowed");
                return valid;
            }
        }

        // 查询 分包商支付
        MetaDataDTOWrapper existingSubconPayment = findSubconPaymentByUniquenessIdJson(appid, subconPaymentCache,
                uniquenessIdJsonString);
        if (Objects.isNull(existingSubconPayment) || (Objects.nonNull(existingSubconPayment.getDataId())
                && Objects.isNull(existingSubconPayment.getValue("Payment_time_1st")))) {
            valid.addWrongReason(String.format("Subcon payment [%s] will be added.", uniqueness_field));
            addRequire(raw, context, valid);
        } else {
//            checkY8SubconPayDate(valid, dict, projectCode, existingSubconPayment); // y8校验日期锁

            valid.addWrongReason(String.format("Subcon payment [%s] will be updated.", uniqueness_field));
            subconPaymentCache.add(existingSubconPayment);
            updateSupport(raw, context, valid);
        }
        return valid;
    }

    private void checkY8SubconPayDate(ImportResultVO valid, Dict dict, String projectCode, MetaDataDTOWrapper existingSubconPayment) {
        //当前日期是否在锁定日期范围内
        LockTimeV3Util lockTimeV3Util = new LockTimeV3Util();
        if (lockTimeV3Util.checkTimeLock(valid, projectCode, null)) {
            valid.setStatus(ImportResultVO.STATUS_FAILED);
            valid.addWrongReason("The current time is locked");
        }

        // 新的开票日期（字符串）
        String Payment_time_1st = dict.getStr("Payment_time_1st");
        String Payment_time_2st = dict.getStr("Payment_time_2st");
        String Payment_time_3st = dict.getStr("Payment_time_3st");
        String Payment_time_4st = dict.getStr("Payment_time_4st");

        // 旧的开票日期（LocalDate）
        LocalDate oldDate1 = LockTimeV3Util.toLocalDate(existingSubconPayment.getValue("Payment_time_1st"));
        LocalDate oldDate2 = LockTimeV3Util.toLocalDate(existingSubconPayment.getValue("Payment_time_2st"));
        LocalDate oldDate3 = LockTimeV3Util.toLocalDate(existingSubconPayment.getValue("Payment_time_3st"));
        LocalDate oldDate4 = LockTimeV3Util.toLocalDate(existingSubconPayment.getValue("Payment_time_4st"));

        // 新日期（LocalDate）
        LocalDate newDate1 = LockTimeV3Util.toLocalDate(Payment_time_1st);
        if (lockTimeV3Util.checkTimeLock(valid, projectCode, oldDate1)) {
            // 比较（可替换为你自己的处理逻辑）
            if (!Objects.equals(newDate1, oldDate1)) {
                // 第一次日期不同，处理逻辑
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason("The db Payment_time_1st time is locked and cannot be modified");
            }
        } else {
            if (!Objects.equals(newDate1, oldDate1)) {
                // 第一次日期不同，处理逻辑
                if (lockTimeV3Util.checkTimeLock(valid, projectCode, newDate1)) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The db Payment_time_1st time is locked and cannot be modified");
                }
            }
        }

        LocalDate newDate2 = LockTimeV3Util.toLocalDate(Payment_time_2st);
        if (lockTimeV3Util.checkTimeLock(valid, projectCode, oldDate2)) {
            if (!Objects.equals(newDate2, oldDate2)) {
                // 第二次日期不同，处理逻辑
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason("The db Payment_time_2st time is locked and cannot be modified");
            }
        } else {
            if (!Objects.equals(newDate2, oldDate2)) {
                // 第二次日期不同，处理逻辑
                if (lockTimeV3Util.checkTimeLock(valid, projectCode, newDate2)) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The db Payment_time_2st time is locked and cannot be modified");
                }
            }
        }
        LocalDate newDate3 = LockTimeV3Util.toLocalDate(Payment_time_3st);
        if (lockTimeV3Util.checkTimeLock(valid, projectCode, oldDate3)) {
            if (!Objects.equals(newDate3, oldDate3)) {
                // 第三次日期不同，处理逻辑
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason("The db Payment_time_3st time is locked and cannot be modified");
            }
        } else {
            if (!Objects.equals(newDate3, oldDate3)) {
                // 第三次日期不同，处理逻辑
                if (lockTimeV3Util.checkTimeLock(valid, projectCode, newDate3)) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The db Payment_time_3st time is locked and cannot be modified");
                }
            }
        }
        LocalDate newDate4 = LockTimeV3Util.toLocalDate(Payment_time_4st);
        if (lockTimeV3Util.checkTimeLock(valid, projectCode, oldDate4)) {
            if (!Objects.equals(newDate4, oldDate4)) {
                // 第四次日期不同，处理逻辑
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason("The db Payment_time_4st time is locked and cannot be modified");
            }
        } else {
            if (!Objects.equals(newDate4, oldDate4)) {
                // 第四次日期不同，处理逻辑
                if (lockTimeV3Util.checkTimeLock(valid, projectCode, newDate4)) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The db Payment_time_4st time is locked and cannot be modified");
                }
            }
        }
    }

    @Override
    public ImportResultVO doTransform(TransformContext context, int index, Map<String, Object> raw) {
        final String appid = context.getAppid();
        final List<MetaDataDTOWrapper> subconCache = context.getSubconCache();
        final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
        final List<MetaDataDTOWrapper> subconPoCache = context.getSubconPoCache();
        final List<MetaDataDTOWrapper> subconPoItemCache = context.getSubconPoItemCache();
        final List<MetaDataDTOWrapper> subconPaymentCache = context.getSubconPayment();

        Dict dict = new Dict(raw);
        String Subcon_name = dict.getStr("Subcon_name");
        String Subcon_PO_number = dict.getStr("Subcon_PO_number");
        String Site_ID = dict.getStr("Site_ID");
        String Item_code = dict.getStr("Item_code");
        String Payment_time_1st = dict.getStr("Payment_time_1st");
        String payment_amount_1st = dict.getStr("payment_amount_1st");
        String Payment_time_2st = dict.getStr("Payment_time_2st");
        String payment_amount_2st = dict.getStr("payment_amount_2st");
        String Payment_time_3st = dict.getStr("Payment_time_3st");
        String payment_amount_3st = dict.getStr("payment_amount_3st");
        String Payment_time_4st = dict.getStr("Payment_time_4st");
        String payment_amount_4st = dict.getStr("payment_amount_4st");
        String payment_number_1st = dict.getStr("payment_number_1st");
        String payment_number_2st = dict.getStr("payment_number_2st");
        String payment_number_3st = dict.getStr("payment_number_3st");
        String payment_number_4st = dict.getStr("payment_number_4st");
        String Remark = dict.getStr("Remark");
        String record = dict.getStr("re_record");
        String cnAmount_1st = dict.getStr("CN_amount_1st");
        String cnRemark_1st = dict.getStr("CN_remark_1st");
        String cnAmount_2nd = dict.getStr("CN_amount_2nd");
        String cnRemark_2nd = dict.getStr("CN_remark_2nd");
        String cnAmount_3rd = dict.getStr("CN_amount_3rd");
        String cnRemark_3rd = dict.getStr("CN_remark_3rd");
        String cnAmount_4st = dict.getStr("CN_amount_4st");
        String cnRemark_4st = dict.getStr("CN_remark_4st");
        String TotallyCNAmount = dict.getStr("Totally_CN_amount");
        String CN_date_1st = dict.getStr("CN_date_1st");
        String CN_number_1st = dict.getStr("CN_number_1st");
        String CN_date_2nd = dict.getStr("CN_date_2nd");
        String CN_number_2nd = dict.getStr("CN_number_2nd");
        String CN_date_3rd = dict.getStr("CN_date_3rd");
        String CN_number_3rd = dict.getStr("CN_number_3rd");
        String CN_date_4st = dict.getStr("CN_date_4st");
        String CN_number_4st = dict.getStr("CN_number_4st");
        String Phase = dict.getStr("Phase");

        // 调用过 validate，供应商PO、分包商、站点、分包商PO条目 均不会为null
        // 查询 供应商PO
        MetaDataDTOWrapper existingSubconPO = findSubconPOBySubconPONumber(appid, subconPoCache, Subcon_PO_number);
        Assert.notNull(existingSubconPO);
        // 查询 分包商
        MetaDataDTOWrapper existingSubcon = findSubconBySubconName(appid, subconCache, Subcon_name);
        Assert.notNull(existingSubcon);
        // 查询 站点
        MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
        Assert.notNull(existingSite);
        // 查询 分包商PO条目
        List<MetaDataDTOWrapper> existingSubconPOItemList = findSubconPOItem(appid, subconPoItemCache,
                existingSubconPO.getDataId(), existingSubcon.getDataId(), existingSite.getDataId(), Item_code, Phase);
        MetaDataDTOWrapper existingSubconPoItem = CollUtil.getFirst(existingSubconPOItemList);

        // 查询 分包商支付
        final String uniquenessIdJsonString = (String) existingSubconPoItem.getValue(UNI_META_ATTR_UNIQUENESS_FIELD);
        MetaDataDTOWrapper existingSubconPayment = findSubconPaymentByUniquenessIdJson(appid, subconPaymentCache,
                uniquenessIdJsonString);
        if (Objects.isNull(existingSubconPayment)) {
            existingSubconPayment = new MetaDataDTOWrapper();
            existingSubconPayment.setValue("uniqueness_field", uniquenessIdJsonString);
            existingSubconPayment.setValue("Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
            // 基础信息
            Long userId = SecurityUtils.getUser().getId();
            existingSubconPayment.setValue("create_by", userId);
            existingSubconPayment.setValue("create_time", LocalDateTime.now());
            existingSubconPayment.setValue("update_by", userId);
            existingSubconPayment.setValue("update_time", LocalDateTime.now());
        }
        existingSubconPayment.setValue("Project_code", existingSubconPoItem.getValue("Project_code"));
        if (!StringUtils.isBlank(Payment_time_1st) && !StringUtils.isBlank(payment_number_1st) && !StringUtils.isBlank(payment_amount_1st)) {
            existingSubconPayment.setValue("Payment_time_1st", DateUtil.parse(Payment_time_1st));
            existingSubconPayment.setValue("payment_number_1st", payment_number_1st);
            existingSubconPayment.setValue("payment_amount_1st", MetaDataUtil.numberStr2BigDecimal(payment_amount_1st));
        }

        if (!StringUtils.isBlank(Payment_time_2st) && !StringUtils.isBlank(payment_number_2st) && !StringUtils.isBlank(payment_amount_2st)) {
            existingSubconPayment.setValue("Payment_time_2st", DateUtil.parse(Payment_time_2st));
            existingSubconPayment.setValue("payment_number_2st", payment_number_2st);
            existingSubconPayment.setValue("payment_amount_2st", MetaDataUtil.numberStr2BigDecimal(payment_amount_2st));
        }
        if (!StringUtils.isBlank(Payment_time_3st) && !StringUtils.isBlank(payment_number_3st) && !StringUtils.isBlank(payment_amount_3st)) {
            existingSubconPayment.setValue("Payment_time_3st", DateUtil.parse(Payment_time_3st));
            existingSubconPayment.setValue("payment_number_3st", payment_number_3st);
            existingSubconPayment.setValue("payment_amount_3st", MetaDataUtil.numberStr2BigDecimal(payment_amount_3st));
        }
        if (!StringUtils.isBlank(Payment_time_4st) && !StringUtils.isBlank(payment_number_4st) && !StringUtils.isBlank(payment_amount_4st)) {
            existingSubconPayment.setValue("Payment_time_4st", DateUtil.parse(Payment_time_4st));
            existingSubconPayment.setValue("payment_number_4st", payment_number_4st);
            existingSubconPayment.setValue("payment_amount_4st", MetaDataUtil.numberStr2BigDecimal(payment_amount_4st));
        }
        existingSubconPayment.setValue("re_record", record);
        existingSubconPayment.setValue("CN_amount_1st", cnAmount_1st);
        existingSubconPayment.setValue("CN_remark_1st", cnRemark_1st);
        existingSubconPayment.setValue("CN_amount_2nd", cnAmount_2nd);
        existingSubconPayment.setValue("CN_remark_2nd", cnRemark_2nd);
        existingSubconPayment.setValue("CN_amount_3rd", cnAmount_3rd);
        existingSubconPayment.setValue("CN_remark_3rd", cnRemark_3rd);
        existingSubconPayment.setValue("CN_amount_4st", cnAmount_4st);
        existingSubconPayment.setValue("CN_remark_4st", cnRemark_4st);

        existingSubconPayment.setValue("CN_date_1st", CN_date_1st);
        existingSubconPayment.setValue("CN_number_1st", CN_number_1st);
        existingSubconPayment.setValue("CN_date_2nd", CN_date_2nd);
        existingSubconPayment.setValue("CN_number_2nd", CN_number_2nd);
        existingSubconPayment.setValue("CN_date_3rd", CN_date_3rd);
        existingSubconPayment.setValue("CN_number_3rd", CN_number_3rd);
        existingSubconPayment.setValue("CN_date_4st", CN_date_4st);
        existingSubconPayment.setValue("CN_number_4st", CN_number_4st);
        BigDecimal total = new BigDecimal("0");
        if (StringUtils.isNotBlank(cnAmount_1st)) {
            total = total.add(new BigDecimal(cnAmount_1st));
        }
        if (StringUtils.isNotBlank(cnAmount_2nd)) {
            total = total.add(new BigDecimal(cnAmount_2nd));
        }
        if (StringUtils.isNotBlank(cnAmount_3rd)) {
            total = total.add(new BigDecimal(cnAmount_3rd));
        }
        if (StringUtils.isNotBlank(cnAmount_4st)) {
            total = total.add(new BigDecimal(cnAmount_4st));
        }
        existingSubconPayment.setValue("Totally_CN_amount", total.toString());
        if (StrUtil.isNotBlank(Remark)) {
            String newRemark = MetaDataUtil.appendStr(existingSubconPayment.getValue("remark"), Remark, "\n\n");
            existingSubconPayment.setValue("remark", newRemark);
        }
        // 连接器执行
        y8connector(existingSubconPayment);

        saveSubconPayment(existingSubconPayment);
        subconPaymentCache.add(existingSubconPayment);
        return new ImportResultVO(index, raw, ImportResultVO.STATUS_SUCCEED, "");
    }

    @Override
    public boolean support(TransformContext context) {
        return Objects.equals(MODULE_NAME, context.getModuleName());
    }

    /**
     * 供应商名称 查询 供应商
     *
     * @param appid      appid
     * @param localStore 本地缓存
     * @param subconName 供应商名称
     * @return 供应商
     */
    private MetaDataDTOWrapper findSubconBySubconName(String appid, List<MetaDataDTOWrapper> localStore,
                                                      String subconName) {
        MetaDataDTOWrapper hit = localFindOne(localStore, SUBCON_META_ATTR_SUBCON_NAME, subconName);
        if (Objects.nonNull(hit)) {
            return hit;
        }
        List<Map<String, Object>> subconBySubconName = basicMapper.findSubconBySubconName(subconName);
        return CollUtil.isEmpty(subconBySubconName) ? null : new MetaDataDTOWrapper(subconBySubconName);
    }

    /**
     * 分包商PO编号 查询 分包商PO
     *
     * @param appid          appid
     * @param localStore     本地缓存
     * @param subconPoNumber 分包商PO编号
     * @return 分包商PO
     */
    private MetaDataDTOWrapper findSubconPOBySubconPONumber(String appid, List<MetaDataDTOWrapper> localStore,
                                                            String subconPoNumber) {
        MetaDataDTOWrapper hit = localFindOne(localStore, SUBCON_PO_META_ATTR_SUBCON_PO_NUMBER, subconPoNumber);
        if (Objects.nonNull(hit)) {
            return hit;
        }
        List<Map<String, Object>> subconPONumber = basicMapper.findSubconPOBySubconPONumber(subconPoNumber);
        return CollUtil.isEmpty(subconPONumber) ? null : new MetaDataDTOWrapper(subconPONumber);
    }

    /**
     * 供应商PO数据ID、供应商数据ID、站点ID、业务条目代码 查询 供应商订单条目
     *
     * @param appid          appid
     * @param localCache     本地缓存
     * @param subconPoDataId 供应商PO 数据ID
     * @param subconDataId   供应商 数据ID
     * @param siteDataId     站点 数据ID
     * @param itemCode       业务条目代码
     * @return 供应商订单条目
     */
    private List<MetaDataDTOWrapper> findSubconPOItem(String appid, List<MetaDataDTOWrapper> localCache,
                                                      Long subconPoDataId, Long subconDataId, Long siteDataId, String itemCode, String phase) {
        String subconPoDataIdJson = MetaDataUtil.handleDataId2Json(subconPoDataId);
        String subconDataIdJson = MetaDataUtil.handleDataId2Json(subconDataId);
        String siteDataIdJson = MetaDataUtil.handleDataId2Json(siteDataId);
        for (MetaDataDTOWrapper subconPoItem : localCache) {
            Object existingSubconPo = subconPoItem.getValue("Subcon_PO");
            Object existingSubcon = subconPoItem.getValue("Subcon");
            Object existingSite = subconPoItem.getValue("Site");
            Object existingItemCode = subconPoItem.getValue("Item_code");
            Object Phase = subconPoItem.getValue("Phase");
            // @formatter:off
            if (
                    Objects.equals(existingSubconPo, subconPoDataIdJson) &&
                            Objects.equals(existingSubcon, subconDataIdJson) &&
                            Objects.equals(existingSite, siteDataIdJson) &&
                            Objects.equals(existingItemCode, itemCode) &&
                            Objects.equals(Phase, phase)
            ) {
                // @formatter:on
                log.trace("findOne in PoItemCache: keys: {}-{}-{}-{}, value: {}-{}-{}-{}, hit: {}", "SubconPO",
                        "Subcon", "Site", "Item_code", subconPoDataId, subconDataId, siteDataId, itemCode,
                        subconPoItem);
                return Collections.singletonList(subconPoItem);
            }
        }
        // 查询
        List<Map<String, Object>> customerProjectByContractNumber = basicMapper.findSubconPOItem(subconPoDataId,
                subconDataId, siteDataId, itemCode, phase);
        return CollUtil.isEmpty(customerProjectByContractNumber) ? null
                : Collections.singletonList(new MetaDataDTOWrapper(customerProjectByContractNumber));
    }

}
