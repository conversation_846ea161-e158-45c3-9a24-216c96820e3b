package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.dto.operation.OperationUpdateDTO;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.controller.ApiRes;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.mapper.ConnectorMapper;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import com.pig4cloud.pig.yptt.utils.MetaDataUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Connector2codeService {

	private final ConnectorMapper connectorMapper;

	private final BasicMapper basicMapper;

	private final ViewConfProperties viewConfProperties;

	/**
	 * y3更新->y567
	 * @param o dto
	 * @return boolean
	 */
	@Transactional(rollbackFor = Exception.class)
	public ApiRes y3Connector2code(OperationUpdateDTO o) {
		MetaDataDTOWrapper y3Wrapper = new MetaDataDTOWrapper(o);
		Object uniquenessField = y3Wrapper.getValue("uniqueness_field");
		String siteBelongTo = MetaDataUtil.handleObject2String(y3Wrapper.getValue("Site_belong_to"));
		if (Objects.nonNull(uniquenessField)) {
			Long unField = MetaDataUtil.handleDataIdJson2Long(uniquenessField.toString());
			// 判断站点是否关闭
			if (Objects.equals(1, connectorMapper.siteItemClosed(unField))) {
				return ApiRes.failed("The Site is closed and not Editable！");
			}
			HashMap<String, Object> incomeExpenditure = new HashMap<>();
			// 查询PO Item
			Map<String, Object> poItem = connectorMapper.getPoItemByUniquenessId(unField);

			// 查询Subcon PO Item
			Map<String, Object> subconPOItem = connectorMapper.getSubconPoItemByUniquenessId(unField);
			BigDecimal yptt = null;
			BigDecimal subconPay = null;
			BigDecimal poAmount = null;
			System.out.println("-------------poItem" +poItem );
			if (Objects.nonNull(poItem) && poItem.size() > 0) {
				System.out.println("进来了");
				poAmount = MetaDataUtil.handleObject2BigDecimal(poItem.get("PO_value"), true);
				// 查询项目
				List<Map<String, Object>> project = basicMapper
					.findYpttProjectByCode(MetaDataUtil.handleObject2String(poItem.get("Project_code")));
				// 查询站点
				List<Map<String, Object>> site = basicMapper.findSiteBySiteID(poItem.get("Site_ID").toString());
				// 查询po
				Map<String, Object> po = basicMapper
					.findPoById(MetaDataUtil.handleDataIdJson2Long(poItem.get("PO").toString()));

				// 可结算、yptt结算更新
				yptt = updateReadySettlement(y3Wrapper, poItem, incomeExpenditure, unField, project.get(0), po);

				// 产值报告更新
				updateProductivityReport(y3Wrapper, poItem, incomeExpenditure, unField, project.get(0), po,
						site.get(0));
			}

			if (Objects.nonNull(subconPOItem) && subconPOItem.size() > 0 && !Objects.equals("YPTT", siteBelongTo)) {
				// 分包商结算、支付更新
				subconPay = updateSubconSettlement(y3Wrapper, subconPOItem, incomeExpenditure, unField);
			}
			// 站点自动关闭
			closeSiteStatus(y3Wrapper, yptt, subconPay, poAmount, unField);

			// 收支统计更新
			if (!incomeExpenditure.isEmpty()) {
				connectorMapper.updateIncomeExpenditure(incomeExpenditure, unField);
			}
		}
		return ApiRes.ok(Boolean.TRUE);
	}

	/**
	 * 关闭站点
	 * @param yptt
	 * @param subconPay
	 * @param poAmount
	 * @param unField
	 */
	private void closeSiteStatus(MetaDataDTOWrapper y3Wrapper, BigDecimal yptt, BigDecimal subconPay,
			BigDecimal poAmount, Long unField) {
		List<Map<String, Object>> siteItem = basicMapper.findSiteItemByUniquenessField(unField);
		List<Map<String, Object>> subconPOItem = basicMapper.findSubconPOItemByUniquenessId(unField);
		List<Map<String, Object>> readySettlement = basicMapper.findReadySettlementByUniquenessId(unField);
		if (CollUtil.isEmpty(siteItem) || CollUtil.isEmpty(subconPOItem) || CollUtil.isEmpty(readySettlement)) {
			return;
		}
		BigDecimal site = MetaDataUtil.handleObject2BigDecimal(siteItem.get(0).get("Site_value"), true);
		BigDecimal subconPO = MetaDataUtil.handleObject2BigDecimal(subconPOItem.get(0).get("Subcon_PO_amount"), true);
		BigDecimal ready = MetaDataUtil.handleObject2BigDecimal(readySettlement.get(0).get("settlement_Amount"), true);
		subconPay = Objects.isNull(subconPay) ? new BigDecimal("-1") : subconPay;
		String siteBelongTo = MetaDataUtil.handleObject2String(y3Wrapper.getValue("Site_belong_to"));
		if (site.compareTo(BigDecimal.ZERO) == 0 || subconPO.compareTo(BigDecimal.ZERO) == 0
				|| ready.compareTo(BigDecimal.ZERO) == 0 || subconPay.compareTo(BigDecimal.ZERO) == 0) {
			return;
		}
		if (Objects.nonNull(yptt) && Objects.nonNull(poAmount)) {
			if (yptt.compareTo(ready) == 0 && poAmount.compareTo(site) == 0 && yptt.compareTo(poAmount) == 0
					&& (subconPay.compareTo(subconPO) == 0 || Objects.equals(siteBelongTo, "YPTT"))) {
				connectorMapper.closeSite(unField);
			}
		}
	}

	/**
	 * 分包商结算更新
	 * @param y3Wrapper y3
	 * @param subconPOItem subconPOItem
	 * @param unField unField
	 * @return BigDecimal 分包商支付gap
	 */
	public BigDecimal updateSubconSettlement(MetaDataDTOWrapper y3Wrapper, Map<String, Object> subconPOItem,
			Map<String, Object> incomeExpenditure, Long unField) {
		Object siteBelongTo = MetaDataUtil.handleObject2String(y3Wrapper.getValue("Site_belong_to"));
		if (Objects.equals(siteBelongTo, "YPTT")) {
			y3Wrapper.setValue("SubconSettlement_1st", null);
			y3Wrapper.setValue("SubconSettlement_2nd", null);
			y3Wrapper.setValue("SubconSettlement_3rd", null);
			y3Wrapper.setValue("SubconSettlement_4th", null);
			return new BigDecimal("-1");
		}
		Object subconSettlement1st = y3Wrapper.getValue("SubconSettlement_1st");
		Object subconSettlement2nd = y3Wrapper.getValue("SubconSettlement_2nd");
		Object subconSettlement3rd = y3Wrapper.getValue("SubconSettlement_3rd");
		Object subconSettlement4th = y3Wrapper.getValue("SubconSettlement_4th");
		BigDecimal subconPOQuantity = Objects.isNull(subconPOItem.get("Quantity")) ? BigDecimal.ZERO
				: new BigDecimal(subconPOItem.get("Quantity").toString());
		BigDecimal unitPrice = Objects.isNull(subconPOItem.get("Unit_price")) ? BigDecimal.ZERO
				: new BigDecimal(subconPOItem.get("Unit_price").toString());
		BigDecimal quantityReduce = Objects.isNull(subconPOItem.get("quantity_reduce")) ? BigDecimal.ZERO
				: new BigDecimal(subconPOItem.get("quantity_reduce").toString());
		BigDecimal subconPOAmount = (subconPOQuantity.add(quantityReduce)).multiply(unitPrice);
		BigDecimal subconPOMilestone1st = Objects.isNull(subconPOItem.get("Milestone_1st")) ? BigDecimal.ZERO
				: new BigDecimal(subconPOItem.get("Milestone_1st").toString());
		BigDecimal subconPOMilestone2nd = Objects.isNull(subconPOItem.get("Milestone_2nd")) ? BigDecimal.ZERO
				: new BigDecimal(subconPOItem.get("Milestone_2nd").toString());
		BigDecimal subconPOMilestone3rd = Objects.isNull(subconPOItem.get("Milestone_3rd")) ? BigDecimal.ZERO
				: new BigDecimal(subconPOItem.get("Milestone_3rd").toString());
		BigDecimal subconPOMilestone4th = Objects.isNull(subconPOItem.get("Milestone_4th")) ? BigDecimal.ZERO
				: new BigDecimal(subconPOItem.get("Milestone_4th").toString());

		// 更新subconSettlement
		HashMap<String, Object> subconSettlement = new HashMap<>();
		BigDecimal totalAmount = BigDecimal.ZERO;
		if (Objects.nonNull(subconSettlement1st) && subconPOMilestone1st.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal amount1st = subconPOAmount.multiply(subconPOMilestone1st);
			subconSettlement.put("settlement_time_1st", subconSettlement1st);
			subconSettlement.put("settlementAmount_1st", amount1st.compareTo(BigDecimal.ZERO) == 0 ? null : amount1st);
			subconSettlement.put("settlement_ratio_1st", subconPOMilestone1st);
			totalAmount = totalAmount.add(amount1st);
			incomeExpenditure.put("Subcon_settlement", amount1st);
			incomeExpenditure.put("Subcon_settlement_d", subconSettlement1st);
		}
//		else {
//			subconSettlement.put("settlementAmount_1st", null);
//			subconSettlement.put("settlement_time_1st", null);
//			subconSettlement.put("settlement_ratio_1st", null);
//			incomeExpenditure.put("Subcon_settlement", null);
//			incomeExpenditure.put("Subcon_settlement_d", null);
//		}
		if (Objects.nonNull(subconSettlement2nd) && subconPOMilestone2nd.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal amount2nd = subconPOAmount.multiply(subconPOMilestone2nd);
			subconSettlement.put("settlement_time_2nd", subconSettlement2nd);
			subconSettlement.put("settlementAmount_2nd", amount2nd.compareTo(BigDecimal.ZERO) == 0 ? null : amount2nd);
			subconSettlement.put("settlement_ratio_2nd", subconPOMilestone2nd);
			totalAmount = totalAmount.add(amount2nd);
			incomeExpenditure.put("Subcon_settlement_2", amount2nd);
			incomeExpenditure.put("Subcon_settlement_d2", subconSettlement2nd);
		}
//		else {
//			subconSettlement.put("settlement_time_2nd", null);
//			subconSettlement.put("settlementAmount_2nd", null);
//			subconSettlement.put("settlement_ratio_2nd", null);
//			incomeExpenditure.put("Subcon_settlement_2", null);
//			incomeExpenditure.put("Subcon_settlement_d2", null);
//		}
		if (Objects.nonNull(subconSettlement3rd) && subconPOMilestone3rd.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal amount3rd = subconPOAmount.multiply(subconPOMilestone3rd);
			subconSettlement.put("settlement_time_3rd", subconSettlement3rd);
			subconSettlement.put("settlementAmount_3rd", amount3rd.compareTo(BigDecimal.ZERO) == 0 ? null : amount3rd);
			subconSettlement.put("settlement_ratio_3rd", subconPOMilestone3rd);
			incomeExpenditure.put("Subcon_settlement_3", amount3rd);
			incomeExpenditure.put("Subcon_settlement_d3", subconSettlement3rd);
			totalAmount = totalAmount.add(amount3rd);
		}
//		else {
//			subconSettlement.put("settlement_time_3rd", null);
//			subconSettlement.put("settlementAmount_3rd", null);
//			subconSettlement.put("settlement_ratio_3rd", null);
//			incomeExpenditure.put("Subcon_settlement_3", null);
//			incomeExpenditure.put("Subcon_settlement_d3", null);
//		}
		if (Objects.nonNull(subconSettlement4th) && subconPOMilestone4th.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal amount4th = subconPOAmount.multiply(subconPOMilestone4th);
			subconSettlement.put("settlement_time_4th", subconSettlement4th);
			subconSettlement.put("settlementAmount_4th", amount4th.compareTo(BigDecimal.ZERO) == 0 ? null : amount4th);
			subconSettlement.put("settlement_ratio_4th", subconPOMilestone4th);
			totalAmount = totalAmount.add(amount4th);
			incomeExpenditure.put("Subcon_settlement_4", amount4th);
			incomeExpenditure.put("Subcon_settlement_d4", subconSettlement4th);
		}
//		else {
//			subconSettlement.put("settlement_time_4th", null);
//			subconSettlement.put("settlementAmount_4th", null);
//			subconSettlement.put("settlement_ratio_4th", null);
//			incomeExpenditure.put("Subcon_settlement_4", null);
//			incomeExpenditure.put("Subcon_settlement_d4", null);
//		}
		subconSettlement.put("Totally_Amount", totalAmount.compareTo(BigDecimal.ZERO) == 0 ? null : totalAmount);
		subconSettlement.put("Totally_amount_Gap", subconPOAmount.subtract(totalAmount));
		if (Objects.isNull(basicMapper.findSubconSettlementByUniquenessId(unField))) {
			subconSettlement.put("uniqueness_field", MetaDataUtil.handleDataId2Json(unField));
			subconSettlement.put("id", IdUtil.getSnowflakeNextId());
			subconSettlement.put("Project_code", y3Wrapper.getValue("Project_code"));
			subconSettlement.put("Site_ID", y3Wrapper.getValue("Site_ID"));
			// 基础信息
			Long userId = SecurityUtils.getUser().getId();
			subconSettlement.put("create_by", userId);
			subconSettlement.put("create_time", LocalDateTime.now());
			subconSettlement.put("update_by", userId);
			subconSettlement.put("update_time", LocalDateTime.now());
			basicMapper.saveItemData(subconSettlement, viewConfProperties.getSubconSettlement().getTableName());
		}
		else {
			connectorMapper.updateSubconSettlement(subconSettlement, unField);
		}

		// 分包商支付更新收支统计
		HashMap<String, Object> subconPaymentUpdate = new HashMap<>();
		Map<String, Object> subconPayment = connectorMapper.getSubconPaymentByUniquenessId(unField);
		if (CollUtil.isEmpty(subconPayment)) {
			return null;
		}
		BigDecimal subconPaymentAmount = Objects.nonNull(subconPayment.get("Totally_payment"))
				? new BigDecimal(subconPayment.get("Totally_payment").toString()) : BigDecimal.ZERO;

		// 更新SubconPayment gap金额
		BigDecimal gap = totalAmount.subtract(subconPaymentAmount).setScale(6, RoundingMode.HALF_UP);
		subconPaymentUpdate.put("Totally_payment_gap", gap);
		connectorMapper.updateSubconPayment(subconPaymentUpdate, unField);
		return subconPaymentAmount;
	}

	/**
	 * 产值报告更新
	 * @param y3Wrapper y3
	 * @param poItem poItem
	 * @param unField unField
	 */
	public void updateProductivityReport(MetaDataDTOWrapper y3Wrapper, Map<String, Object> poItem,
			Map<String, Object> incomeExpenditure, Long unField, Map<String, Object> project, Map<String, Object> po,
			Map<String, Object> site) {
		Long userId = SecurityUtils.getUser().getId();
		Object reportDate1st = y3Wrapper.getValue("settlement_1st");
		Object reportDate2nd = y3Wrapper.getValue("settlement_2nd");
		Object reportDate3rd = y3Wrapper.getValue("settlement_3rd");
		Object reportDate4th = y3Wrapper.getValue("settlement_4th");
		BigDecimal milestone1st = Objects.nonNull(poItem.get("Milestone_1st"))
				? new BigDecimal(poItem.get("Milestone_1st").toString()) : BigDecimal.ZERO;
		BigDecimal milestone2nd = Objects.nonNull(poItem.get("Milestone_2nd"))
				? new BigDecimal(poItem.get("Milestone_2nd").toString()) : BigDecimal.ZERO;
		BigDecimal milestone3rd = Objects.nonNull(poItem.get("Milestone_3rd"))
				? new BigDecimal(poItem.get("Milestone_3rd").toString()) : BigDecimal.ZERO;
		BigDecimal milestone4th = Objects.nonNull(poItem.get("Milestone_4th"))
				? new BigDecimal(poItem.get("Milestone_4th").toString()) : BigDecimal.ZERO;
		BigDecimal unitPrice = Objects.nonNull(poItem.get("Unit_price"))
				? new BigDecimal(poItem.get("Unit_price").toString()) : BigDecimal.ZERO;
//		BigDecimal quantityReduce = Objects.nonNull(poItem.get("amount_reduce"))
//				? new BigDecimal(poItem.get("quantity_reduce").toString()) : BigDecimal.ZERO;
		BigDecimal quantityReduce = Objects.nonNull(poItem.get("quantity_reduce"))
				? new BigDecimal(poItem.get("quantity_reduce").toString()) : BigDecimal.ZERO;
		BigDecimal quantity = Objects.nonNull(poItem.get("Quantity"))
				? new BigDecimal(poItem.get("Quantity").toString()) : BigDecimal.ZERO;
		System.out.println("-----------unitPrice"+unitPrice + "----quantity"+quantity+"---quantityReduce-"+quantityReduce);
		BigDecimal poValue = unitPrice.multiply(quantity.subtract(quantityReduce));
		BigDecimal pre_payment = MetaDataUtil.handleObject2BigDecimal(poItem.get("Pre_payment"), true);
		BigDecimal prePay = poValue.multiply(pre_payment);
		// 更新productivityReport
		HashMap<String, Object> productivityReport = new HashMap<>();
		BigDecimal settlement_Amount = BigDecimal.ZERO;
		if (Objects.nonNull(reportDate1st) && milestone1st.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal amount = milestone1st.multiply(poValue).add(prePay);
			System.out.println("---------------"+milestone1st+"---"+poValue+"-----"+prePay);
			productivityReport.put("report_date_1st", reportDate1st);
			productivityReport.put("report_amount_1st", amount);
			settlement_Amount = settlement_Amount.add(amount);
			incomeExpenditure.put("Productivity_AmountD", reportDate1st);
			incomeExpenditure.put("Productivity_Amount", amount);
		}
//		else {
//			productivityReport.put("report_date_1st", null);
//			productivityReport.put("report_amount_1st", null);
//			incomeExpenditure.put("Productivity_AmountD", null);
//			incomeExpenditure.put("Productivity_Amount", null);
//		}
		if (Objects.nonNull(reportDate2nd) && milestone2nd.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal amount = milestone2nd.multiply(poValue);
			productivityReport.put("report_date_2nd", reportDate2nd);
			productivityReport.put("report_amount_2nd", amount);
			settlement_Amount = settlement_Amount.add(amount);
			incomeExpenditure.put("Productivity_AmountD2", reportDate2nd);
			incomeExpenditure.put("Productivity_Amount2", amount);
		}
//		else {
//			productivityReport.put("report_date_2nd", null);
//			productivityReport.put("report_amount_2nd", null);
//			incomeExpenditure.put("Productivity_AmountD2", null);
//			incomeExpenditure.put("Productivity_Amount2", null);
//		}
		if (Objects.nonNull(reportDate3rd) && milestone3rd.compareTo(BigDecimal.ZERO) > 0) {
			productivityReport.put("report_date_3rd", reportDate3rd);
			BigDecimal amount = milestone3rd.multiply(poValue);
			productivityReport.put("report_amount_3rd", amount);
			settlement_Amount = settlement_Amount.add(amount);
			incomeExpenditure.put("ProductivityAmountD3", reportDate3rd);
			incomeExpenditure.put("Productivity_Amount3", amount);
		}
//		else {
//			productivityReport.put("report_date_3rd", null);
//			productivityReport.put("report_amount_3rd", null);
//			incomeExpenditure.put("ProductivityAmountD3", null);
//			incomeExpenditure.put("Productivity_Amount3", null);
//		}
		if (Objects.nonNull(reportDate4th) && milestone4th.compareTo(BigDecimal.ZERO) > 0) {
			productivityReport.put("report_date_4th", reportDate4th);
			BigDecimal amount = milestone4th.multiply(poValue);
			productivityReport.put("report_amount_4th", amount);
			settlement_Amount = settlement_Amount.add(amount);
			incomeExpenditure.put("ProductivityAmountD4", reportDate4th);
			incomeExpenditure.put("Productivity_Amount4", amount);
		}
//		else {
//			productivityReport.put("report_date_4th", null);
//			productivityReport.put("report_amount_4th", null);
//			incomeExpenditure.put("ProductivityAmountD4", null);
//			incomeExpenditure.put("Productivity_Amount4", null);
//		}
		productivityReport.put("Productivity_Amount", settlement_Amount);
		productivityReport.put("declaration_ratio", poValue.compareTo(BigDecimal.ZERO) > 0
				? settlement_Amount.divide(poValue, RoundingMode.CEILING) : null);
		if (CollUtil.isEmpty(basicMapper.findProductivityReportByUniquenessId(unField))) {
			productivityReport.put("id", IdUtil.getSnowflakeNextId());
			productivityReport.put("PO_number", po.get("PO_number"));
			productivityReport.put("BOQ_item", poItem.get("BOQ_item"));
			productivityReport.put("PO_value", poValue);
			productivityReport.put("site_name", site.get("site_name"));
			productivityReport.put("uniqueness_field", MetaDataUtil.handleDataId2Json(unField));
			productivityReport.put("Project_code", y3Wrapper.getValue("Project_code"));
			productivityReport.put("Project_name", project.get("YPTT_Project_name"));
			productivityReport.put("Region", poItem.get("Region"));
			productivityReport.put("Phase", y3Wrapper.getValue("Phase"));
			productivityReport.put("Item_code", y3Wrapper.getValue("Item_code"));
			productivityReport.put("Site_ID", y3Wrapper.getValue("Site_ID"));
			// 基础信息
			productivityReport.put("create_by", userId);
			productivityReport.put("create_time", LocalDateTime.now());
			productivityReport.put("update_by", userId);
			productivityReport.put("update_time", LocalDateTime.now());
			basicMapper.saveItemData(productivityReport, viewConfProperties.getProductivityReport().getTableName());
			System.out.println("---------------------------productivityReport save");
		}
		else {
			connectorMapper.updateProductivityReport(productivityReport, unField);
			System.out.println("-----------------productivityReport"+productivityReport);
		}

	}

	/**
	 * 可结算更新及yptt结算更新
	 * @param y3Wrapper y3
	 * @param poItem poItem
	 * @param unField unField
	 * @return yptt结算gap
	 */
	public BigDecimal updateReadySettlement(MetaDataDTOWrapper y3Wrapper, Map<String, Object> poItem,
			Map<String, Object> incomeExpenditure, Long unField, Map<String, Object> project, Map<String, Object> po) {
		Long userId = SecurityUtils.getUser().getId();
		Object settlement4th = y3Wrapper.getValue("settlement_4th");
		Object settlement3rd = y3Wrapper.getValue("settlement_3rd");
		Object settlement2nd = y3Wrapper.getValue("settlement_2nd");
		Object settlement1st = y3Wrapper.getValue("settlement_1st");
		BigDecimal milestone1st = Objects.nonNull(poItem.get("Milestone_1st"))
				? new BigDecimal(poItem.get("Milestone_1st").toString()) : BigDecimal.ZERO;
		BigDecimal milestone2nd = Objects.nonNull(poItem.get("Milestone_2nd"))
				? new BigDecimal(poItem.get("Milestone_2nd").toString()) : BigDecimal.ZERO;
		BigDecimal milestone3rd = Objects.nonNull(poItem.get("Milestone_3rd"))
				? new BigDecimal(poItem.get("Milestone_3rd").toString()) : BigDecimal.ZERO;
		BigDecimal milestone4th = Objects.nonNull(poItem.get("Milestone_4th"))
				? new BigDecimal(poItem.get("Milestone_4th").toString()) : BigDecimal.ZERO;
		BigDecimal unitPrice = Objects.nonNull(poItem.get("Unit_price"))
				? new BigDecimal(poItem.get("Unit_price").toString()) : BigDecimal.ZERO;
		BigDecimal quantityReduce = Objects.nonNull(poItem.get("quantity_reduce"))
				? new BigDecimal(poItem.get("quantity_reduce").toString()) : BigDecimal.ZERO;
		BigDecimal q = Objects.nonNull(poItem.get("Quantity")) ? new BigDecimal(poItem.get("Quantity").toString())
				: BigDecimal.ZERO;
		BigDecimal prePaymentRatio = Objects.nonNull(poItem.get("Pre_payment"))
				? new BigDecimal(poItem.get("Pre_payment").toString()) : BigDecimal.ZERO;
		Object poReceivedDate = poItem.get("PO_Received_date");
		BigDecimal poValue = Objects.nonNull(poItem.get("PO_value")) ? new BigDecimal(poItem.get("PO_value").toString())
				: BigDecimal.ZERO;
		// 计算调价
		System.out.println("++++++++++poItem"+poItem);
		System.out.println("++++++++++++++++++q.add(quantityReduce)"+q+"+++"+quantityReduce);
		BigDecimal quantity = q.subtract(quantityReduce);
		BigDecimal prePay = poValue.multiply(prePaymentRatio).setScale(6, RoundingMode.HALF_UP);
		// 更新readySettlement
		HashMap<String, Object> readySettlement = new HashMap<>();
		System.out.println("+++++++++++++++unitPrice"+unitPrice + "++++++++++quantity"+quantity);
		System.out.println("+++++++++++settlement1st"+settlement1st);
		if (Objects.nonNull(poReceivedDate) && prePaymentRatio.compareTo(BigDecimal.ZERO) > 0) {
			readySettlement.put("Pre_Settlement_date", poReceivedDate);
			readySettlement.put("Pre_payment_amount", prePay);
			readySettlement.put("Pre_payment_ratio", prePaymentRatio);

			incomeExpenditure.put("Pre_Settlement_date", poReceivedDate);
			incomeExpenditure.put("Pre_payment_amount", prePay);
		}
		BigDecimal settlement_Amount = BigDecimal.ZERO;
		if (Objects.nonNull(settlement1st) && milestone1st.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal multiply = milestone1st.multiply(unitPrice).multiply(quantity);
			readySettlement.put("settlement_1st", settlement1st);
			readySettlement.put("Settlement_ratio_1st", milestone1st);
			readySettlement.put("amount_1st", multiply);
			incomeExpenditure.put("Ready_settlement", multiply);
			incomeExpenditure.put("Ready_settlement_d", settlement1st);
			settlement_Amount = settlement_Amount.add(multiply);
		}
//		else {
//			readySettlement.put("settlement_1st", null);
//			readySettlement.put("Settlement_ratio_1st", null);
//			readySettlement.put("amount_1st", null);
//			incomeExpenditure.put("Ready_settlement", null);
//			incomeExpenditure.put("Ready_settlement_d", null);
//		}
		if (Objects.nonNull(settlement2nd) && milestone2nd.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal multiply = milestone2nd.multiply(unitPrice).multiply(quantity);
			readySettlement.put("settlement_2nd", settlement2nd);
			readySettlement.put("Settlement_ratio_2nd", milestone2nd);
			readySettlement.put("amount_2nd", multiply);
			settlement_Amount = settlement_Amount.add(multiply);
			incomeExpenditure.put("Ready_settlement_2", multiply);
			incomeExpenditure.put("Ready_settlement_d2", settlement2nd);
		}
//		else {
//			readySettlement.put("Settlement_ratio_2nd", null);
//			readySettlement.put("settlement_2nd", null);
//			readySettlement.put("amount_2nd", null);
//			incomeExpenditure.put("Ready_settlement_2", null);
//			incomeExpenditure.put("Ready_settlement_d2", null);
//		}
		if (Objects.nonNull(settlement3rd) && milestone3rd.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal multiply = milestone3rd.multiply(unitPrice).multiply(quantity);
			readySettlement.put("settlement_3rd", settlement3rd);
			readySettlement.put("Settlement_ratio_3rd", milestone3rd);
			readySettlement.put("amount_3rd", multiply);
			settlement_Amount = settlement_Amount.add(multiply);
			incomeExpenditure.put("Ready_settlement_3", multiply);
			incomeExpenditure.put("Ready_settlement_d3", settlement3rd);
		}
//		else {
//			readySettlement.put("Settlement_ratio_3rd", null);
//			readySettlement.put("settlement_3rd", null);
//			readySettlement.put("amount_3rd", null);
//			incomeExpenditure.put("Ready_settlement_3", null);
//			incomeExpenditure.put("Ready_settlement_d3", null);
//		}
		if (Objects.nonNull(settlement4th) && milestone4th.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal multiply = milestone4th.multiply(unitPrice).multiply(quantity);
			readySettlement.put("Settlement_ratio_4th", milestone4th);
			readySettlement.put("settlement_4th", settlement4th);
			readySettlement.put("amount_4th", multiply);
			settlement_Amount = settlement_Amount.add(multiply);
			incomeExpenditure.put("Ready_settlement_4", multiply);
			incomeExpenditure.put("Ready_settlement_d4", settlement4th);
		}
//		else {
//			readySettlement.put("Settlement_ratio_4th", null);
//			readySettlement.put("settlement_4th", null);
//			readySettlement.put("amount_4th", null);
//			incomeExpenditure.put("Ready_settlement_4", null);
//			incomeExpenditure.put("Ready_settlement_d4", null);
//		}
		settlement_Amount = settlement_Amount.add(prePay);
		readySettlement.put("settlement_Amount", settlement_Amount);
		readySettlement.put("settlement_amountGap",
				(unitPrice.multiply(quantity)).subtract(settlement_Amount).setScale(6, RoundingMode.HALF_UP));
		if (CollUtil.isEmpty(basicMapper.findReadySettlementByUniquenessId(unField))) {
			readySettlement.put("id", IdUtil.getSnowflakeNextId());
			readySettlement.put("uniqueness_field", MetaDataUtil.handleDataId2Json(unField));
			readySettlement.put("Project_code", y3Wrapper.getValue("Project_code"));
			readySettlement.put("Project_name", project.get("YPTT_Project_name"));
			readySettlement.put("Phase", y3Wrapper.getValue("Phase"));
			readySettlement.put("Item_code", y3Wrapper.getValue("Item_code"));
			readySettlement.put("Site_ID", y3Wrapper.getValue("Site_ID"));
			readySettlement.put("PO_number", po.get("PO_number"));
			readySettlement.put("BOQ_item", poItem.get("BOQ_item"));
			readySettlement.put("PO_value", poValue);
			readySettlement.put("Department", project.get("Department"));
			// 基础信息
			readySettlement.put("create_by", userId);
			readySettlement.put("create_time", LocalDateTime.now());
			readySettlement.put("update_by", userId);
			readySettlement.put("update_time", LocalDateTime.now());
			basicMapper.saveItemData(readySettlement, viewConfProperties.getReadyForSettlement().getTableName());
		}
		else {
			connectorMapper.updateReadySettlement(readySettlement, unField);
			System.out.println("--------------readySettlement" + readySettlement);
		}

		// yptt结算更新收支统计
		HashMap<String, Object> ypttUpdate = new HashMap<>();
		Map<String, Object> ypttSettlement = connectorMapper.getYpttSettlementByUniquenessId(unField);
		if (CollUtil.isEmpty(ypttSettlement)) {
			return null;
		}
		BigDecimal ypttAmount = Objects.nonNull(ypttSettlement.get("Invoice_amount"))
				? new BigDecimal(ypttSettlement.get("Invoice_amount").toString()) : BigDecimal.ZERO;
		// 更新yptt金额差异
		updateYpttInvoiceAmountDiff(ypttSettlement, readySettlement, ypttUpdate);
		BigDecimal gap = settlement_Amount.subtract(ypttAmount).setScale(6, RoundingMode.HALF_UP);
		// 更新yptt gap金额
		ypttUpdate.put("Invoice_amount_gap", gap);
		connectorMapper.updateYpttSettlement(ypttUpdate, unField);
		return ypttAmount;
	}

	public void updateYpttInvoiceAmountDiff(Map<String, Object> ypttSettlement, Map<String, Object> readySettlement,
			Map<String, Object> ypttUpdate) {
		Object invoiceAmount1st = ypttSettlement.get("Invoice_Amount_1st");
		Object invoiceAmount2st = ypttSettlement.get("Invoice_Amount_2st");
		Object invoiceAmount3st = ypttSettlement.get("Invoice_Amount_3st");
		Object invoiceAmount4st = ypttSettlement.get("Invoice_Amount_4st");
		Object amount1st = readySettlement.get("amount_1st");
		Object amount2nd = readySettlement.get("amount_2nd");
		Object amount3rd = readySettlement.get("amount_3rd");
		Object amount4th = readySettlement.get("amount_4th");
		if (Objects.nonNull(invoiceAmount1st)) {
			BigDecimal diff1st = Objects.isNull(amount1st)
					? new BigDecimal("0").subtract(new BigDecimal(invoiceAmount1st.toString()))
					: new BigDecimal(amount1st.toString()).subtract(new BigDecimal(invoiceAmount1st.toString()));
			ypttUpdate.put("Invoice_Amount_diff_1st", diff1st);
		}
		if (Objects.nonNull(invoiceAmount2st)) {
			BigDecimal diff2st = Objects.isNull(amount2nd)
					? new BigDecimal("0").subtract(new BigDecimal(invoiceAmount2st.toString()))
					: new BigDecimal(amount2nd.toString()).subtract(new BigDecimal(invoiceAmount2st.toString()));
			ypttUpdate.put("Invoice_Amount_diff_2st", diff2st);
		}
		if (Objects.nonNull(invoiceAmount3st)) {
			BigDecimal diff3st = Objects.isNull(amount3rd)
					? new BigDecimal("0").subtract(new BigDecimal(invoiceAmount3st.toString()))
					: new BigDecimal(amount3rd.toString()).subtract(new BigDecimal(invoiceAmount3st.toString()));
			ypttUpdate.put("Invoice_Amount_diff_3st", diff3st);
		}
		if (Objects.nonNull(invoiceAmount4st)) {
			BigDecimal diff4st = Objects.isNull(amount4th)
					? new BigDecimal("0").subtract(new BigDecimal(invoiceAmount4st.toString()))
					: new BigDecimal(amount4th.toString()).subtract(new BigDecimal(invoiceAmount4st.toString()));
			ypttUpdate.put("Invoice_Amount_diff_4st", diff4st);
		}
	}

}
