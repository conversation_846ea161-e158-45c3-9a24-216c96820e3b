package com.pig4cloud.pig.yptt.service.fs;

import com.pig4cloud.pig.yptt.mapper.CustomerMapper;
import com.pig4cloud.pig.yptt.service.ViewModelRelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName: CustomerService
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-08-26  15:57
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerService {
    private final CustomerMapper customerMapper;
    private final ViewModelRelService viewModelRelService;

    private static final String MODEL_TABLE = "customer";
    private static final String MODEL_TABLE_2 = "cus_pro_codes";

    /**
     * 判断客户/供应商是否登记
     **/
    public Boolean isExistsCustomer(String name) {
        String tableName = viewModelRelService.getModelTableNameByModelName(MODEL_TABLE);
        return customerMapper.isExistsCustomer(tableName,name) > 0;
    }

    /**
     * 修改项目客户编码的状态为已使用
     **/
    public void updateProjectCustomerCodeStatus(String id, String companyName) {
        String tableName = viewModelRelService.getModelTableNameByModelName(MODEL_TABLE_2);
        if(customerMapper.updateProjectCustomerCodeStatus(tableName,id,companyName)>0){
            log.info("修改项目客户编码的状态为已使用");
        }else {
            log.error("修改项目客户编码的状态为已使用失败");
        }
    }
}