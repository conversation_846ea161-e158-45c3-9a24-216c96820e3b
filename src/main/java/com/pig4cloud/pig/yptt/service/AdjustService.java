package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataValueDTO;
import com.pig4cloud.pig.me.api.dto.operation.OperationUpdateDTO;
import com.pig4cloud.pig.yptt.controller.ApiRes;
import com.pig4cloud.pig.yptt.mapper.AdjustMapper;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AdjustService
 * @Description 调整
 * @date 2025/1/11 16:45
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdjustService {
    private final AdjustMapper adjustMapper;


    @Transactional(rollbackFor = Exception.class)
    public ApiRes adjustAmountSite(OperationUpdateDTO obj) {
        System.out.println("站点信息更改");
        //查询当前项目的y1-y9的所有信息
        String projectCode = null;
        String itemCode = null;
        String keyMeta = null;
        BigDecimal quantity = new BigDecimal(0);
        BigDecimal unitPrice = new BigDecimal(0);
        List<MetaDataValueDTO> dataArray = obj.getData();
        for (MetaDataValueDTO data : dataArray) {
            if ("Project_code".equals(data.getName())) {
                projectCode = (String) data.getValue();
            }
            if ("Item_code".equals(data.getName())) {
                itemCode = (String) data.getValue();
            }
            if ("uniqueness_field".equals(data.getName())) {
                keyMeta = data.getExtValue().replace("[[\"", "").replace("\"]]", "");
            }
            if ("Quantity".equals(data.getName())) {
                quantity = new BigDecimal(data.getValue().toString());
            }
            if ("Unit_price".equals(data.getName())) {
                unitPrice = new BigDecimal(data.getValue().toString());
            }
        }

        if (StringUtils.isBlank(projectCode)) {
            return ApiRes.ok("project_code is not exist!");
        }

        List<Map<String, Object>> mapLists = adjustMapper.selectReport(projectCode);
        //1、调整y1的金额或者数量（Quantity*Unit_price = Site_value）
        String finalProjectCode = projectCode;
        String finalKeyMeta = keyMeta;
        BigDecimal finalQuantity = quantity;
        BigDecimal finalUnitPrice = unitPrice;
        CompletableFuture.runAsync(() -> {
            adjustSiteItem(finalProjectCode, finalKeyMeta, mapLists, finalQuantity, finalUnitPrice);
        });

        return ApiRes.ok(Boolean.TRUE);
    }


    //调整y1的金额或者数量（Quantity * Unit_price = Site_value）
    @Transactional(rollbackFor = Exception.class)
    boolean adjustSiteItem(String projectCode, String keyMeta, List<Map<String, Object>> mapLists, BigDecimal quantity, BigDecimal unitPrice) {
        Long siteItemId = translateY1(keyMeta, mapLists);
        if (siteItemId == null) {
            return false;
        }

        //获取金额和数量
        BigDecimal siteValue;
        siteValue = quantity.multiply(unitPrice);
        Map<String, Object> siteItem = new HashMap<>();
        siteItem.put("Quantity", quantity);
        siteItem.put("Unit_price", unitPrice);
        siteItem.put("Site_value", siteValue);
        siteItem.put("id", siteItemId);
        try {
            adjustMapper.updateSiteItemData(siteItem);
        } catch (Exception e) {
            e.printStackTrace();
        }


        //更新poItem中的pogap
        BigDecimal poValue = new BigDecimal(0); //  PO_Item-采购订单条目 - 条目单价
        for (Map<String, Object> map : mapLists) {
            String key = (String) map.get("poItemUn");
            if (key.equals(keyMeta)) {
                poValue = new BigDecimal(map.get("PO_value") == null ? "0" : map.get("PO_value").toString());
            }
        }
        BigDecimal poGAP = siteValue.subtract(poValue);
        Long poItemId = translateY2(keyMeta, mapLists, null);
        Map<String, Object> poItem = new HashMap<>();
        poItem.put("id", poItemId);
        poItem.put("PO_gap", poGAP);
        try {
            adjustMapper.updatePoItemData(poItem);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    //获取y1的相关数据的id
    private Long translateY1(String keyMeta, List<Map<String, Object>> mapLists) {

        String key;
        for (Map<String, Object> map : mapLists) {
            key = (String) map.get("poItemUn");
            if (key.equals(keyMeta)) {
                return (Long) map.get("siteItemId");
            }
        }
        return null;
    }

    //    @Transactional(rollbackFor = Exception.class)
    public ApiRes adjustAmountPo(OperationUpdateDTO obj) {
        System.out.println("po信息更改");
        //查询当前项目的y1-y9的所有信息
        String projectCode = null;
        String keyMeta = null;
        BigDecimal quantity = new BigDecimal(0); //poItem 数量
        BigDecimal unitPrice = new BigDecimal(0); //poItem 单价
        BigDecimal quantityReduce = new BigDecimal(0); //poItem 减少值
        List<MetaDataValueDTO> dataArray = obj.getData();
        for (MetaDataValueDTO data : dataArray) {
            if ("Project_code".equals(data.getName())) {
                projectCode = (String) data.getValue();
            }
            if ("uniqueness_field".equals(data.getName())) {
                keyMeta = data.getExtValue().toString().replace("[[\"", "").replace("\"]]", "");
            }

            if ("Quantity".equals(data.getName())) {
                quantity = new BigDecimal(data.getValue() == null ? "0" : data.getValue().toString());
            }
            if ("Unit_price".equals(data.getName())) {
                unitPrice = new BigDecimal(data.getValue() == null ? "0" : data.getValue().toString());
            }
            if ("quantity_reduce".equals(data.getName())) {
                quantityReduce = new BigDecimal(data.getValue() == null ? "0" : data.getValue().toString());
            }
        }

        if (StringUtils.isBlank(projectCode)) {
            return ApiRes.failed("project_code is not exist!");
        }

        Map<String, String> projectMap = new HashMap<>(); //存放projectCode
        List<Map<String, Object>> poItems = new ArrayList<>();  //调整y2的金额或者数量
        List<Map<String, Object>> settlementDatas = new ArrayList<>(); //更新y5关联金额
        List<Map<String, Object>> productivityDatas = new ArrayList<>(); //更新y6关联金额
        List<Map<String, Object>> YPTTSettlements = new ArrayList<>(); //更新关联的y9的数据
        Map<String, Map<String, Object>> mapCache = null; //缓存maplist key-value

        List<Map<String, Object>> mapLists = null;
        try {
            mapLists = adjustMapper.selectReport(projectCode);
            mapCache = mapLists.stream()
                    .collect(Collectors.toMap(
                            m -> (String) m.get("poItemUn"),
                            Function.identity()
                    ));
        } catch (Exception e) {
            e.printStackTrace();
        }
        //1、调整y1的金额或者数量（Quantity*Unit_price = Site_value）
        String finalProjectCode = projectCode;
        List<Map<String, Object>> finalMapLists = mapLists;
        String finalKeyMeta = keyMeta;
        BigDecimal finalQuantity = quantity;
        BigDecimal finalUnitPrice = unitPrice;
        BigDecimal finalQuantityReduce = quantityReduce;

        try {
            adjustPoItem(finalProjectCode, finalKeyMeta, finalMapLists, finalQuantity, finalUnitPrice, finalQuantityReduce,
                    poItems, settlementDatas, productivityDatas, YPTTSettlements, mapCache);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            CompletableFuture.runAsync(() -> {
                exceSql(poItems, settlementDatas, productivityDatas, YPTTSettlements);
            });
        } catch (Exception e) {
            throw new RuntimeException("调整poItem 失败" + e.getMessage());
        }

        return ApiRes.ok(Boolean.TRUE);
    }

    @Transactional(rollbackFor = Exception.class)
    void exceSql(List<Map<String, Object>> poItems, List<Map<String, Object>> settlementDatas, List<Map<String, Object>> productivityDatas, List<Map<String, Object>> YPTTSettlements) {
        try {
//            adjustMapper.updatePoItemDatas(poItems);
//            adjustMapper.updateSettlementDatas(settlementDatas);
//            adjustMapper.updateProductivityDatas(productivityDatas);
//            adjustMapper.updateYPTTSettlements(YPTTSettlements);
            log.info("开始执行数据库更改操作");
            batchUpdate(adjustMapper::updatePoItemDatas, poItems, 100); // 每100条提交一次
            batchUpdate(adjustMapper::updateSettlementDatas, settlementDatas, 100);
            batchUpdate(adjustMapper::updateProductivityDatas, productivityDatas, 100);
            batchUpdate(adjustMapper::updateYPTTSettlements, YPTTSettlements, 100);
            log.info("执行完成");
        } catch (Exception e) {
            throw new RuntimeException("数据或者相关联数据更新异常" + e.getMessage());
        }
    }

    // 辅助方法：分批次处理
    private <T> void batchUpdate(Consumer<List<T>> updater, List<T> data, int batchSize) {
        for (int i = 0; i < data.size(); i += batchSize) {
            List<T> batch = data.subList(i, Math.min(i + batchSize, data.size()));
            updater.accept(batch);
        }
    }

    //调整y2的金额或者数量（(Quantity - Quantity Adjust ) * Unit_price = PO_value）
//    @Transactional(rollbackFor = Exception.class)
    boolean adjustPoItem(String projectCode, String keyMeta, List<Map<String, Object>> mapLists,
                         BigDecimal quantity, BigDecimal unitPrice, BigDecimal quantityReduce,
                         List<Map<String, Object>> poItems, List<Map<String, Object>> settlementDatas,
                         List<Map<String, Object>> productivityDatas, List<Map<String, Object>> YPTTSettlements,
                         Map<String, Map<String, Object>> mapCache) {
        Long poItemId = translateY2(keyMeta, mapLists, mapCache);
        //获取站点价值
        BigDecimal siteValue = translateY2SiteValue(keyMeta, mapLists, mapCache);
        //获取金额和数量
        BigDecimal poValue; // poItem 总金额

        poValue = (quantity.subtract(quantityReduce)).multiply(unitPrice);
        BigDecimal poGAP = siteValue.subtract(poValue);
        Map<String, Object> poItem = new HashMap<>();
        poItem.put("Quantity", quantity);
        poItem.put("Unit_price", unitPrice);
        poItem.put("PO_value", poValue);
        poItem.put("id", poItemId);
        poItem.put("quantity_reduce", quantityReduce);
        poItem.put("PO_gap", poGAP);
        //更新PO_Item-采购订单条目金额
        try {
            System.out.println("poitem更新id" + poItemId);
//            adjustMapper.updatePoItemData(poItem);
            poItems.add(poItem);
        } catch (Exception e) {
            e.printStackTrace();
        }


        BigDecimal prePaymentRatio = new BigDecimal(0); //  可结算参考 Pre_payment_ratio - 预付款比例
        BigDecimal SettlementRatio_1st = new BigDecimal(0); //  可结算参考 Settlement_ratio_1st - 第一次可结算比例
        BigDecimal SettlementRatio_2nd = new BigDecimal(0); //  可结算参考 Settlement_ratio_2nd - 第二次可结算比例
        BigDecimal SettlementRatio_3rd = new BigDecimal(0); //  可结算参考 Settlement_ratio_3rd - 第三次可结算比例
        BigDecimal SettlementRatio_4th = new BigDecimal(0); //  可结算参考 Settlement_ratio_4th - 第四次可结算比例
        BigDecimal invoiceAmount = new BigDecimal(0); //  YPTT结算 Invoice_amount - 发票总金额

        Map<String, Object> map = mapCache.get(keyMeta);
        prePaymentRatio = new BigDecimal(map.get("Pre_payment_ratio") == null ? "0" : map.get("Pre_payment_ratio").toString());
        SettlementRatio_1st = new BigDecimal(map.get("settlement%-1st") == null ? "0" : map.get("settlement%-1st").toString());
        SettlementRatio_2nd = new BigDecimal(map.get("settlement%-2nd") == null ? "0" : map.get("settlement%-2nd").toString());
        SettlementRatio_3rd = new BigDecimal(map.get("settlement%-3rd") == null ? "0" : map.get("settlement%-3rd").toString());
        SettlementRatio_4th = new BigDecimal(map.get("settlement%-4th") == null ? "0" : map.get("settlement%-4th").toString());
        invoiceAmount = new BigDecimal(map.get("TotallyInvoiceAmount") == null ? "0" : map.get("TotallyInvoiceAmount").toString());
//        for (Map<String, Object> map : mapLists) {
//            String key = (String) map.get("poItemUn");
//            if (key.equals(keyMeta)) {
//                prePaymentRatio = new BigDecimal(map.get("Pre_payment_ratio") == null ? "0" : map.get("Pre_payment_ratio").toString());
//                SettlementRatio_1st = new BigDecimal(map.get("settlement%-1st") == null ? "0" : map.get("settlement%-1st").toString());
//                SettlementRatio_2nd = new BigDecimal(map.get("settlement%-2nd") == null ? "0" : map.get("settlement%-2nd").toString());
//                SettlementRatio_3rd = new BigDecimal(map.get("settlement%-3rd") == null ? "0" : map.get("settlement%-3rd").toString());
//                SettlementRatio_4th = new BigDecimal(map.get("settlement%-4th") == null ? "0" : map.get("settlement%-4th").toString());
//                invoiceAmount = new BigDecimal(map.get("TotallyInvoiceAmount") == null ? "0" : map.get("TotallyInvoiceAmount").toString());
//            }
//        }
        //校验 预付款 + 结算里程碑一 +结算里程碑二 + 结算里程碑三 + 结算里程碑四 = 100%
        BigDecimal add = prePaymentRatio.add(SettlementRatio_1st).add(SettlementRatio_2nd).add(SettlementRatio_3rd).add(SettlementRatio_4th);
        if (add.doubleValue() > 1.0) {
            throw new RuntimeException("结算里程碑+预付款比例错误");
        }


        //更新y5关联金额 settlementData
        BigDecimal prePaymentAmount; //  可结算参考 Pre_payment_amount - 预付款金额
        prePaymentAmount = poValue.multiply(prePaymentRatio);
        BigDecimal amount_1st; //  可结算参考 amount_1st - 第一次可结算金额
        amount_1st = poValue.multiply(SettlementRatio_1st);
        BigDecimal amount_2nd; //  可结算参考 amount_2nd - 第二次可结算金额
        amount_2nd = poValue.multiply(SettlementRatio_2nd);
        BigDecimal amount_3rd; //  可结算参考 amount_3rd - 第三次可结算金额
        amount_3rd = poValue.multiply(SettlementRatio_3rd);
        BigDecimal amount_4th; //  可结算参考 amount_4th - 第四次可结算金额
        amount_4th = poValue.multiply(SettlementRatio_4th);

        BigDecimal settlementAmount; //  可结算参考 settlement_Amount - 可结算总金额
        settlementAmount = amount_1st.add(amount_2nd).add(amount_3rd).add(amount_4th);

        BigDecimal settlementAmountGap; //  可结算参考 settlement_amountGap - 不可结算总金额
        settlementAmountGap = poValue.subtract(settlementAmount);

        Map<String, Object> settlementData = new HashMap<>();
        settlementData.put("Pre_payment_amount", prePaymentAmount);
        settlementData.put("amount_1st", amount_1st);
        settlementData.put("amount_2nd", amount_2nd);
        settlementData.put("amount_3rd", amount_3rd);
        settlementData.put("amount_4th", amount_4th);
        settlementData.put("settlement_Amount", settlementAmount);
        settlementData.put("settlement_amountGap", settlementAmountGap);
        Long readySettlementId = translateY2ReadySettlementId(keyMeta, mapLists, mapCache);
        settlementData.put("id", readySettlementId);

        try {
//            adjustMapper.updateSettlementData(settlementData);
            settlementDatas.add(settlementData);
        } catch (Exception e) {
            e.printStackTrace();
        }


        //查询出y6的信息
        //更新y6关联金额 （产值申报总金额/po_value）  productivityData

        BigDecimal reportAmount_1st = amount_1st; //  产值管理 report_amount_1st - 第一次产值申报金额
        BigDecimal reportAmount_2nd = amount_2nd; //  产值管理 report_amount_2nd - 第二次产值申报金额
        BigDecimal reportAmount_3rd = amount_3rd; //  产值管理 report_amount_3rd - 第三次产值申报金额
        BigDecimal reportAmount_4th = amount_4th; //  产值管理 report_amount_4th - 第四次产值申报金额
        BigDecimal productivityAmount = reportAmount_1st.add(reportAmount_2nd).add(reportAmount_3rd).add(reportAmount_4th); //  产值管理 Productivity_Amount - 产值申报总金额
        BigDecimal declarationRatio; //  产值管理 declaration_ratio - 产值申报总比例
        if (poValue.doubleValue() == 0 || productivityAmount.doubleValue() == 0) {
            declarationRatio = new BigDecimal("0");
        } else {
            declarationRatio = productivityAmount.divide(poValue);
        }
        Map<String, Object> productivityData = new HashMap<>();
        productivityData.put("report_amount_1st", reportAmount_1st.add(prePaymentAmount));
        productivityData.put("report_amount_2nd", reportAmount_2nd);
        productivityData.put("report_amount_3rd", reportAmount_3rd);
        productivityData.put("report_amount_4th", reportAmount_4th);
        productivityData.put("Productivity_Amount", productivityAmount);
        productivityData.put("declaration_ratio", declarationRatio);
        Long productivityId = translateY2ProductivityId(keyMeta, mapLists, mapCache);
        productivityData.put("id", productivityId);

        try {
//            adjustMapper.updateProductivityData(productivityData);
            productivityDatas.add(productivityData);
        } catch (Exception e) {
            e.printStackTrace();
        }

        //更新关联的y9的数据
        //todo y9 发票结算金额
        BigDecimal Invoice_Amount_1st = new BigDecimal(map.get("Invoice_Amount_1st") == null ? "0" : map.get("Invoice_Amount_1st").toString());
        BigDecimal Invoice_Amount_2st = new BigDecimal(map.get("Invoice_Amount_2st") == null ? "0" : map.get("Invoice_Amount_2st").toString());
        BigDecimal Invoice_Amount_3st = new BigDecimal(map.get("Invoice_Amount_3st") == null ? "0" : map.get("Invoice_Amount_3st").toString());
        BigDecimal Invoice_Amount_4st = new BigDecimal(map.get("Invoice_Amount_4st") == null ? "0" : map.get("Invoice_Amount_4st").toString());

        //计算结算发票差值
        BigDecimal invoiceDiff_1st = amount_1st.subtract(Invoice_Amount_1st);
        BigDecimal invoiceDiff_2st = amount_2nd.subtract(Invoice_Amount_2st);
        BigDecimal invoiceDiff_3st = amount_3rd.subtract(Invoice_Amount_3st);
        BigDecimal invoiceDiff_4st = amount_4th.subtract(Invoice_Amount_4st);

        Map<String, Object> YPTTSettlement = new HashMap<>();
        BigDecimal InvoiceAmountGap; //  YPTT结算 Invoice_amount_gap - 发票总缺口
        InvoiceAmountGap = settlementAmount.subtract(invoiceAmount);
        YPTTSettlement.put("Invoice_Amount_diff_1st", invoiceDiff_1st);
        YPTTSettlement.put("Invoice_Amount_diff_2st", invoiceDiff_2st);
        YPTTSettlement.put("Invoice_Amount_diff_3st", invoiceDiff_3st);
        YPTTSettlement.put("Invoice_Amount_diff_4st", invoiceDiff_4st);

        YPTTSettlement.put("Invoice_amount_gap", InvoiceAmountGap);
        Long YPTTSettlementId = translateY2YPTTSettlementId(keyMeta, mapLists, mapCache);
        YPTTSettlement.put("id", YPTTSettlementId);
        try {
//            adjustMapper.updateYPTTSettlement(YPTTSettlement);
            YPTTSettlements.add(YPTTSettlement);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    //获取y9的相关数据 poItem的id
    private Long translateY2YPTTSettlementId(String keyMeta, List<Map<String, Object>> mapLists, Map<String, Map<String, Object>> mapCache) {
        if (ObjectUtils.isNotEmpty(mapCache)) {
            Map<String, Object> data = mapCache.get(keyMeta);
            return data != null ? (Long) data.get("YPTTSettlementId") : null;
        }
        String key;
        for (Map<String, Object> map : mapLists) {
            key = (String) map.get("poItemUn");
            if (key.equals(keyMeta)) {
                return (Long) map.get("YPTTSettlementId");
            }
        }
        return null;
    }

    //获取y1的相关数据 siteItem的site value
    private BigDecimal translateY2SiteValue(String keyMeta, List<Map<String, Object>> mapLists, Map<String, Map<String, Object>> mapCache) {
        if (ObjectUtils.isNotEmpty(mapCache)) {
            Map<String, Object> data = mapCache.get(keyMeta);
            return data != null ? (BigDecimal) data.get("Site_value") : BigDecimal.ZERO;
        }

        String key;
        for (Map<String, Object> map : mapLists) {
            key = (String) map.get("poItemUn");
            if (key.equals(keyMeta)) {
                return (BigDecimal) map.get("Site_value");
            }
        }
        return new BigDecimal("0");
    }

    //获取y5的相关数据 poItem的id
    private Long translateY2(String keyMeta, List<Map<String, Object>> mapLists, Map<String, Map<String, Object>> mapCache) {
        if (ObjectUtils.isNotEmpty(mapCache)) {
            Map<String, Object> data = mapCache.get(keyMeta);
            return data != null ? (Long) data.get("poItemId") : null;
        }
        String key;
        for (Map<String, Object> map : mapLists) {
            key = (String) map.get("poItemUn");
            if (key.equals(keyMeta)) {
                return (Long) map.get("poItemId");
            }
        }
        return null;
    }

    //获取y6的相关数据Productivity_Report的id的id
    private Long translateY2ProductivityId(String keyMeta, List<Map<String, Object>> mapLists, Map<String, Map<String, Object>> mapCache) {
        if (ObjectUtils.isNotEmpty(mapCache)) {
            Map<String, Object> data = mapCache.get(keyMeta);
            return data != null ? (Long) data.get("ProductivityReportId") : null;
        }

        String key;
        for (Map<String, Object> map : mapLists) {
            key = (String) map.get("poItemUn");
            if (key.equals(keyMeta)) {
                return (Long) map.get("ProductivityReportId");
            }
        }
        return null;
    }

    //获取y2的相关数据Ready_For_Settlement的id的id
    private Long translateY2ReadySettlementId(String keyMeta, List<Map<String, Object>> mapLists, Map<String, Map<String, Object>> mapCache) {
        if (ObjectUtils.isNotEmpty(mapCache)) {
            Map<String, Object> data = mapCache.get(keyMeta);
            return data != null ? (Long) data.get("ReadySettlementId") : null;
        }
        String key;
        for (Map<String, Object> map : mapLists) {
            key = (String) map.get("poItemUn");
            if (key.equals(keyMeta)) {
                return (Long) map.get("ReadySettlementId");
            }
        }
        return null;
    }

    public synchronized Boolean updateAmountSiteItme() {

        List<Map<String, Object>> mapLists = adjustMapper.selectReportNoCondition();
        List<Map<String, Object>> siteItems = adjustMapper.selectSiteItem();
        for (Map<String, Object> siteItem : siteItems) {
            String finalProjectCode = (String) siteItem.get("Project_code");
            String finalKeyMeta = (String) siteItem.get("uniField");
            BigDecimal finalQuantity = (BigDecimal) siteItem.get("Quantity");
            BigDecimal finalUnitPrice = (BigDecimal) siteItem.get("Unit_price");
            CompletableFuture.runAsync(() -> {
                adjustSiteItem(finalProjectCode, finalKeyMeta, mapLists, finalQuantity, finalUnitPrice);
            });
        }

        return true;
    }

    public synchronized Boolean updateAmountPOItem() {

        List<Map<String, Object>> mapLists = adjustMapper.selectReportNoCondition();
        List<Map<String, Object>> poItems = adjustMapper.selectPOItem();
        for (Map<String, Object> poItem : poItems) {
            String finalProjectCode = (String) poItem.get("Project_code");
            String finalKeyMeta = (String) poItem.get("uniField");
            if (StringUtils.isBlank(finalKeyMeta)) {
                continue;
            }
            BigDecimal finalQuantity = (BigDecimal) poItem.get("Quantity");
            BigDecimal finalUnitPrice = (BigDecimal) poItem.get("Unit_price");
            BigDecimal finalQuantityReduce = (BigDecimal) poItem.get("quantity_reduce");
//            CompletableFuture.runAsync(() -> {
//                adjustPoItem(finalProjectCode, finalKeyMeta, mapLists, finalQuantity, finalUnitPrice, finalQuantityReduce);
//            });
        }

        return true;
    }

    public ApiRes adjustAmountSubPo(OperationUpdateDTO obj) {
        System.out.println("subPo信息更改");
        //查询当前项目的y1-y9的所有信息
        String projectCode = null;
        String keyMeta = null;
        BigDecimal quantity = new BigDecimal(0); //poItem 数量
        BigDecimal unitPrice = new BigDecimal(0); //poItem 单价
        BigDecimal quantityReduce = new BigDecimal(0); //poItem 减少值
        BigDecimal SubconSettlement_1st = new BigDecimal(0); //分包商第一次可结算比例
        BigDecimal SubconSettlement_2nd = new BigDecimal(0); //分包商第二次可结算比例
        BigDecimal SubconSettlement_3rd = new BigDecimal(0); //分包商第三次可结算比例
        BigDecimal SubconSettlement_4th = new BigDecimal(0); //分包商第四次可结算比例
        List<MetaDataValueDTO> dataArray = obj.getData();
        for (MetaDataValueDTO data : dataArray) {
            if ("Project_code".equals(data.getName())) {
                projectCode = (String) data.getValue();
            }
            if ("uniqueness_field".equals(data.getName())) {
                keyMeta = data.getExtValue().replace("[[\"", "").replace("\"]]", "");
            }

            if ("Quantity".equals(data.getName())) {
                quantity = new BigDecimal(data.getValue().toString());
            }
            if ("Unit_price".equals(data.getName())) {
                unitPrice = new BigDecimal(data.getValue().toString());
            }
            if ("quantity_reduce".equals(data.getName())) {
                quantityReduce = new BigDecimal(data.getValue().toString());
            }

            if ("Milestone_1st".equals(data.getName())) {
                SubconSettlement_1st = new BigDecimal(data.getValue().toString());
            }
            if ("Milestone_2nd".equals(data.getName())) {
                SubconSettlement_2nd = new BigDecimal(data.getValue().toString());
            }
            if ("Milestone_3rd".equals(data.getName())) {
                SubconSettlement_3rd = new BigDecimal(data.getValue().toString());
            }
            if ("Milestone_4th".equals(data.getName())) {
                SubconSettlement_4th = new BigDecimal(data.getValue().toString());
            }

        }

        if (StringUtils.isBlank(projectCode)) {
            return ApiRes.failed("project_code is not exist!");
        }


        List<Map<String, Object>> mapLists = null;
        try {
            mapLists = adjustMapper.selectReport(projectCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //1、调整y1的金额或者数量（Quantity*Unit_price = Site_value）
        String finalProjectCode = projectCode;
        List<Map<String, Object>> finalMapLists = mapLists;
        String finalKeyMeta = keyMeta;
        BigDecimal finalQuantity = quantity;
        BigDecimal finalUnitPrice = unitPrice;
        BigDecimal finalQuantityReduce = quantityReduce;
        BigDecimal finalSubconSettlement_1st = SubconSettlement_1st;
        BigDecimal finalSubconSettlement_2nd = SubconSettlement_2nd;
        BigDecimal finalSubconSettlement_3rd = SubconSettlement_3rd;
        BigDecimal finalSubconSettlement_4th = SubconSettlement_4th;
        CompletableFuture.runAsync(() -> {
            adjustSubPoItem(finalProjectCode, finalKeyMeta, finalMapLists, finalQuantity, finalUnitPrice, finalQuantityReduce,
                    finalSubconSettlement_1st, finalSubconSettlement_2nd, finalSubconSettlement_3rd, finalSubconSettlement_4th);
        });

        return ApiRes.ok(Boolean.TRUE);
    }

    void adjustSubPoItem(String finalProjectCode, String keyMeta, List<Map<String, Object>> mapLists, BigDecimal quantity, BigDecimal unitPrice, BigDecimal quantityReduce,
                         BigDecimal SubconSettlement_1st, BigDecimal SubconSettlement_2nd, BigDecimal SubconSettlement_3rd, BigDecimal SubconSettlement_4th) {
        Long subPoItemId = translateY4(keyMeta, mapLists);
        BigDecimal subPoValue = (quantity.subtract(quantityReduce)).multiply(unitPrice);

        Map<String, Object> subPoItem = new HashMap<>();
        subPoItem.put("id", subPoItemId);
        subPoItem.put("Subcon_PO_amount", subPoValue);
        if (SubconSettlement_1st != null) {
            subPoItem.put("Milestone_1st", SubconSettlement_1st);
        }
        if (SubconSettlement_2nd != null) {
            subPoItem.put("Milestone_2nd", SubconSettlement_2nd);
        }
        if (SubconSettlement_3rd != null) {
            subPoItem.put("Milestone_3rd", SubconSettlement_3rd);
        }
        if (SubconSettlement_4th != null) {
            subPoItem.put("Milestone_4th", SubconSettlement_4th);
        }
        subPoItem.put("Unit_price", unitPrice);

        BigDecimal afterQuantity = quantity.subtract(quantityReduce);
        subPoItem.put("quantity_reduce", quantityReduce);
        BigDecimal subConValue = afterQuantity.multiply(unitPrice);
        subPoItem.put("Subcon_PO_amount", subConValue);
        //更新Subcon_PO_Item-分包商PO条目
        try {
            adjustMapper.updateSubPoItemData(subPoItem);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //更新关联y7 分包商结算
        Long subSettlementId = translateY7(keyMeta, mapLists);
        Map<String, Object> subSettlement = new HashMap<>();
        subSettlement.put("id", subSettlementId);
        BigDecimal totallyAmount = translateY7GetTotallyAmount(keyMeta, mapLists); //分包商可结算总金额
        BigDecimal settlementAmount_1st;
        if (SubconSettlement_1st != null) {
            subSettlement.put("settlement_ratio_1st", SubconSettlement_1st); //分包商第一次可结算比例
            settlementAmount_1st = subPoValue.multiply(SubconSettlement_1st);
            subSettlement.put("settlementAmount_1st", settlementAmount_1st);
            totallyAmount = settlementAmount_1st;
        }

        BigDecimal settlementAmount_2nd;
        if (SubconSettlement_2nd != null) {
            subSettlement.put("settlement_ratio_2nd", SubconSettlement_2nd); //分包商第二次可结算比例
            settlementAmount_2nd = subPoValue.multiply(SubconSettlement_2nd);
            subSettlement.put("settlementAmount_2nd", settlementAmount_2nd);
            totallyAmount = totallyAmount.add(settlementAmount_2nd);
        }

        BigDecimal settlementAmount_3rd;
        if (SubconSettlement_3rd != null) {
            subSettlement.put("settlement_ratio_3rd", SubconSettlement_3rd); //分包商第三次可结算比例
            settlementAmount_3rd = subPoValue.multiply(SubconSettlement_3rd);
            subSettlement.put("settlementAmount_3rd", settlementAmount_3rd);
            totallyAmount = totallyAmount.add(settlementAmount_3rd);
        }

        BigDecimal settlementAmount_4th;
        if (SubconSettlement_4th != null) {
            subSettlement.put("settlement_ratio_4th", SubconSettlement_4th); //分包商第四次可结算比例
            settlementAmount_4th = subPoValue.multiply(SubconSettlement_4th);
            subSettlement.put("settlementAmount_4th", settlementAmount_4th);
            totallyAmount = totallyAmount.add(settlementAmount_4th);
        }

        if (totallyAmount.doubleValue() != 0.0) {
            subSettlement.put("Totally_Amount", totallyAmount);
        }

        subSettlement.put("Totally_amount_Gap", subPoValue.subtract(totallyAmount));

        //更新Subcon_Settlement分包商结算
        try {
            adjustMapper.updateSubSettlementData(subSettlement);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    //获取y5的相关数据 poItem的id
    private Long translateY4(String keyMeta, List<Map<String, Object>> mapLists) {

        String key;
        for (Map<String, Object> map : mapLists) {
            key = (String) map.get("poItemUn");
            if (key.equals(keyMeta)) {
                return (Long) map.get("subItemId");
            }
        }
        return null;
    }

    //获取y5的相关数据 Subcon_Settlement 的id
    private Long translateY7(String keyMeta, List<Map<String, Object>> mapLists) {

        String key;
        for (Map<String, Object> map : mapLists) {
            key = (String) map.get("poItemUn");
            if (key.equals(keyMeta)) {
                return (Long) map.get("SubconSettlementId");
            }
        }
        return null;
    }

    //获取y5的相关数据 Subcon_Settlement 的总金额
    private BigDecimal translateY7GetTotallyAmount(String keyMeta, List<Map<String, Object>> mapLists) {

        String key;
        for (Map<String, Object> map : mapLists) {
            key = (String) map.get("poItemUn");
            if (key.equals(keyMeta)) {
                return (BigDecimal) map.get("Totally_Amount");
            }
        }
        return new BigDecimal("0");
    }

    public Boolean updateY6KPI() {
        log.info("开始执行定时任务");
        // 查询数据
        List<Map<String, Object>> maps = null;
        try {
            maps = adjustMapper.selectKPIY2AndY3();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (maps == null) {
            log.warn("查询结果为空");
            return false;
        }
        log.info("-----------查询数据大小" + maps.size());
        // 过滤数据
        maps.removeIf(x -> x == null || x.get("KPI_Archive_date") == null);
        // 检查数据并更新
        if (!maps.isEmpty()) {
            try {
                log.info("开始执行任务，共 {} 条数据需要更新", maps.size());
//                adjustMapper.updateProductivityDatas(maps);
                batchResult(maps);
                log.info("执行完成");
                return true;
            } catch (Exception e) {
                e.printStackTrace();
                log.error("执行失败");
                return false;
            }
        } else {
            log.warn("无有效数据需要更新");
            return false;
        }
    }

    public void batchResult(List<Map<String, Object>> result) {
        // 每批最多处理 5000 条数据
        int batchSize = 5000;
        int totalSize = result.size();
        int fromIndex = 0;

        // 创建线程池
        int threadPoolSize = 1; // 线程池大小
        ExecutorService executor = Executors.newFixedThreadPool(threadPoolSize);

        // 使用 CountDownLatch 等待所有任务完成
        CountDownLatch latch = new CountDownLatch((totalSize + batchSize - 1) / batchSize);

        // 分批处理数据
        while (fromIndex < totalSize) {
            int toIndex = Math.min(fromIndex + batchSize, totalSize);
            List<Map<String, Object>> batchList = result.subList(fromIndex, toIndex);

            // 提交任务到线程池
            executor.submit(() -> {
                try {
                    // 执行数据删除
                    adjustMapper.updateProductivityDatas(batchList);
                    System.out.println("成功处理一批数据，共 " + batchList.size() + " 条");
                } catch (Exception e) {
                    System.out.println("一批数据处理失败: " + e.getMessage());
                } finally {
                    // 任务完成，计数器减一
                    latch.countDown();
                }
            });

            // 更新起始索引
            fromIndex = toIndex;
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        // 等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            System.out.println("主线程被中断: " + e.getMessage());
        }

        // 关闭线程池
        executor.shutdown();
    }
}