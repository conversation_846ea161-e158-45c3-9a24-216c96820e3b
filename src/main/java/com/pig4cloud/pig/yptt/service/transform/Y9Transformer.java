package com.pig4cloud.pig.yptt.service.transform;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.DataPermissionsService;
import com.pig4cloud.pig.yptt.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Y9 YPTT结算 转为 YPTT结算
 *
 * <AUTHOR>
 * @date 2023/10/7
 */
@Component
@Slf4j
public class Y9Transformer extends AbstractTransformer {

    private final static String MODULE_NAME = GlobalConstants.Y9.NAME;

    public Y9Transformer(RemoteAppService remoteAppService, ViewConfProperties viewConfProperties,
                         DataPermissionsService dataPermissionsService, BasicMapper basicMapper, RedissonClient redissonClient,
                         DataMangeService dataMangeService) {
        super(remoteAppService, viewConfProperties, dataPermissionsService, basicMapper, redissonClient,
                dataMangeService);
    }

    @Override
    public ImportResultVO doValidate(TransformContext context, int index, Map<String, Object> raw,
                                     ImportResultVO valid) {
        final String appid = context.getAppid();
        final List<MetaDataDTOWrapper> poCache = context.getPoCache();
        final List<MetaDataDTOWrapper> customerProjectCache = context.getCustomerProjectCache();
        final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
        final List<MetaDataDTOWrapper> siteItemCache = context.getSiteItemCache();
        final List<MetaDataDTOWrapper> poItemCache = context.getPoItemCache();
        final List<MetaDataDTOWrapper> ypttSettlementCache = context.getYpttSettlementCache();

        Dict dict = new Dict(raw);
        String PO_number = dict.getStr("PO_number");
        String Contract_number = dict.getStr("Contract_number");
        String Phase = dict.getStr("Phase");
        String Site_ID = dict.getStr("Site_ID");
        String Item_code = dict.getStr("Item_code");
        String uniqueness_field = MetaDataUtil.getUniquenessField(dict, GlobalConstants.Y9.NAME);

        // 查询 订单
        MetaDataDTOWrapper existingPO = findPoByPoNumber(appid, poCache, PO_number);
        if (Objects.isNull(existingPO)) {
            return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED,
                    String.format("PO [%s] has not been added yet. Please add the PO first.", PO_number));
        }
        poCache.add(existingPO);

        // 查询 客户项目
        MetaDataDTOWrapper existingCustomerProject = findCustomerProjectByContractNumber(appid, customerProjectCache,
                Contract_number);
        if (Objects.isNull(existingCustomerProject)) {
            return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED,
                    String.format(
                            "Customer Project [%s] has not been added yet. Please add the customer project first.",
                            Contract_number));
        }
        customerProjectCache.add(existingCustomerProject);

        // 查询 站点
        MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
        if (Objects.isNull(existingSite)) {
            return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED,
                    String.format("Site [%s] has not been added yet. Please add the site first", Site_ID));
        }
        siteCache.add(existingSite);

        // 查询 订单条目
        List<MetaDataDTOWrapper> existingPOItemList = findPOItem(appid, poItemCache, existingPO.getDataId(), Phase,
                existingSite.getDataId(), Item_code);
        if (CollUtil.size(existingPOItemList) < 1) {
            return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED, String
                    .format("PO item [%s] has not been added yet. Please add the PO item first.", uniqueness_field));
        }
        if (CollUtil.size(existingPOItemList) > 1) {
            return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED,
                    String.format("The number of PO item [%s] should be 1, but get 2", uniqueness_field));
        }
        MetaDataDTOWrapper existingPOItem = CollUtil.getFirst(existingPOItemList);
        poItemCache.add(existingPOItem);

        // 查询 站点条目, 验证站点条目是否关闭
        final String uniquenessIdJsonString = (String) existingPOItem.getValue(UNI_META_ATTR_UNIQUENESS_FIELD);
        final Long uniquenessId = MetaDataUtil.handleDataIdJson2Long(uniquenessIdJsonString);
        String projectCode = "";
        if (Objects.nonNull(uniquenessId)) {
            MetaDataDTOWrapper existingSiteItem = findSiteItemByUniquenessField(appid, siteItemCache, uniquenessId);
            if (Objects.isNull(existingSiteItem) || Objects.isNull(existingSiteItem.getValue("site"))) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Site item [%s] not found.", uniqueness_field));
                return valid;
            }
            boolean isClosedSiteItem = isClosedSiteItem(existingSiteItem);
            if (isClosedSiteItem) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Site item [%s] is closed.", uniqueness_field));
                return valid;
            }
            siteItemCache.add(existingSiteItem);
            // 项目权限验证
            Long ypttProjectDataId = MetaDataUtil
                    .handleDataIdJson2Long((String) existingSiteItem.getValue("TPTT_Project"));
            if (Objects.isNull(ypttProjectDataId)) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(
                        String.format("Please improve site item[%s] project information.", uniqueness_field));
                return valid;
            }
            this.validatePerm(context, ypttProjectDataId);
            projectCode = (String) existingSiteItem.getValue("Project_code");
        }

        // 查询 YPTT结算信息
        MetaDataDTOWrapper existingYPTTSettlement = findYpttSettlementByUniquenessIdJson(appid, ypttSettlementCache,
                uniquenessIdJsonString);
        if (Objects.isNull(existingYPTTSettlement) || (Objects.nonNull(existingYPTTSettlement.getDataId())
                && Objects.isNull(existingYPTTSettlement.getValue("Invoice_date_1st")))) {
            valid.addWrongReason(String.format("Yptt settlement [%s] will be added.", uniqueness_field));
            addRequire(raw, context, valid);
        } else {
            //校验结算日期
            checkInvoiceDateAndAmount(valid, dict, projectCode, existingYPTTSettlement);
            valid.addWrongReason(String.format("Yptt settlement [%s] will be updated.", uniqueness_field));
            ypttSettlementCache.add(existingYPTTSettlement);
            updateSupport(raw, context, valid);
        }

        return valid;
    }

    /**
     * 检查开票日期和金额是否允许修改
     * @param valid 校验结果对象
     * @param dict 上传的数据字典
     * @param projectCode 项目代码
     * @param existingSettlement 数据库中已有的结算数据
     */
    private void checkInvoiceDateAndAmount(
            ImportResultVO valid,
            Dict dict,
            String projectCode,
            MetaDataDTOWrapper existingSettlement) {

        LockTimeV3Util lockTimeUtil = new LockTimeV3Util();

        // 1. 检查当前时间是否被锁定
        if (lockTimeUtil.checkTimeLock(valid, projectCode, null)) {
            valid.setStatus(ImportResultVO.STATUS_FAILED);
            valid.addWrongReason("当前时间处于锁定期间，禁止操作");
            return;
        }

        // 2. 检查4组开票日期和金额
        checkSingleInvoice(valid, dict, projectCode, existingSettlement, lockTimeUtil,
                "1st", "Invoice_date_1st", "Invoice_Amount_1st", "Invoice_number_1st");
        checkSingleInvoice(valid, dict, projectCode, existingSettlement, lockTimeUtil,
                "2nd", "Invoice_date_2st", "Invoice_Amount_2st", "Invoice_number_2st");
        checkSingleInvoice(valid, dict, projectCode, existingSettlement, lockTimeUtil,
                "3rd", "Invoice_date_3st", "Invoice_Amount_3st", "Invoice_number_3st");
        checkSingleInvoice(valid, dict, projectCode, existingSettlement, lockTimeUtil,
                "4th", "Invoice_date_4st", "Invoice_Amount_4st", "Invoice_number_4st");
        //3. 检查cnamount 和cndate
        checkSingleCN(valid, dict, projectCode, existingSettlement, lockTimeUtil,
                "1st", "CN_date_1st", "CN_amount_1st", "CN_number_1st");
        checkSingleCN(valid, dict, projectCode, existingSettlement, lockTimeUtil,
                "2nd", "CN_date_2nd", "CN_amount_2nd", "CN_number_2nd");
        checkSingleCN(valid, dict, projectCode, existingSettlement, lockTimeUtil,
                "3rd", "CN_date_3rd", "CN_amount_3rd", "CN_number_3rd");
        checkSingleCN(valid, dict, projectCode, existingSettlement, lockTimeUtil,
                "4th", "CN_date_4st", "CN_amount_4st", "CN_number_4st");

    }

    public String tranField(String fieldName, String suffix){
        if ("1st".equals(suffix)){
            return fieldName.replace("1st",suffix);
        }else if("2nd".equals(suffix)){
            return fieldName.replace("2st",suffix);
        }else if("3rd".equals(suffix)){
            return fieldName.replace("3st",suffix);
        }else if("4th".equals(suffix)){
            return fieldName.replace("4st",suffix);
        }
        return fieldName;
    }

    /**
     * 检查单个开票记录（日期+金额）
     */
    private void checkSingleInvoice(
            ImportResultVO valid,
            Dict dict,
            String projectCode,
            MetaDataDTOWrapper existingSettlement,
            LockTimeV3Util lockTimeUtil,
            String suffix,
            String dateField,
            String amountField,
            String numberField) {

        // 获取数据库中的旧值
        LocalDate oldDate = LockTimeV3Util.toLocalDate(existingSettlement.getValue(dateField));
        BigDecimal oldAmount = Convert.toBigDecimal(existingSettlement.getValue(amountField), BigDecimal.ZERO);
        String oldNumber = (String) existingSettlement.getValue(numberField);
        // 获取上传的新值
        LocalDate newDate = LockTimeV3Util.toLocalDate(dict.getStr(tranField(dateField,suffix)));
        BigDecimal newAmount = Convert.toBigDecimal(dict.get(tranField(amountField,suffix)), BigDecimal.ZERO);
        String newNumber = (String) dict.get(tranField(numberField,suffix));
        if (newDate == null) { //当前阶段未上传日期，不做处理
            if (lockTimeUtil.checkTimeLock(valid, projectCode, oldDate)) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason("禁止删除锁定日期的记录");
            }
            return;
        }
        // 情况1：数据库中日期被锁定
        if (lockTimeUtil.checkTimeLock(valid, projectCode, oldDate)) {
            // 如果日期或金额有修改
            if (!Objects.equals(newDate, oldDate) || newAmount.compareTo(oldAmount) != 0|| !newNumber.equals(oldNumber)) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("第%s次开票记录已被锁定，禁止修改日期或金额和票号", suffix));
            }
            return;
        }

        // 情况2：数据库中日期未锁定
        // 检查新日期是否被锁定
        boolean isNewDateLocked = lockTimeUtil.checkTimeLock(valid, projectCode, newDate);

        // 如果日期有变化且新日期被锁定
        if (!Objects.equals(newDate, oldDate) && isNewDateLocked) {
            valid.setStatus(ImportResultVO.STATUS_FAILED);
            valid.addWrongReason(String.format("第%s次开票的新日期%s处于锁定期间", suffix, newDate));
            return;
        }

        // 如果金额有变化且新日期被锁定（金额随日期锁定）
        if (!newAmount.equals(oldAmount) && isNewDateLocked) {
            valid.setStatus(ImportResultVO.STATUS_FAILED);
            valid.addWrongReason(String.format("第%s次开票金额因日期%s被锁定而禁止修改", suffix, newDate));
        }
        // 如果票号有变化且新日期被锁定（票号随日期锁定）
        if (!newNumber.equals(oldNumber) && isNewDateLocked) {
            valid.setStatus(ImportResultVO.STATUS_FAILED);
            valid.addWrongReason(String.format("第%s次发票号因日期%s被锁定而禁止修改", suffix, newDate));
        }
    }

    /**
     * 检查单个CN记录（日期+金额 + 票号）
     */
    private void checkSingleCN(
            ImportResultVO valid,
            Dict dict,
            String projectCode,
            MetaDataDTOWrapper existingSettlement,
            LockTimeV3Util lockTimeUtil,
            String suffix,
            String dateField,
            String amountField,
            String cnNumberField) {

        // 获取数据库中的旧值
        LocalDate oldDate = LockTimeV3Util.toLocalDate(existingSettlement.getValue(dateField));
        BigDecimal oldAmount = Convert.toBigDecimal(existingSettlement.getValue(amountField), BigDecimal.ZERO);
        String oldNumber = (String) existingSettlement.getValue(cnNumberField);
        // 获取上传的新值
        LocalDate newDate = LockTimeV3Util.toLocalDate(dict.getStr(dateField));
        BigDecimal newAmount = Convert.toBigDecimal(dict.get(amountField), BigDecimal.ZERO);
        String newNumber = (String) dict.get(cnNumberField);
        if (newDate == null) { //当前阶段未上传日期，不做处理
            if (lockTimeUtil.checkTimeLock(valid, projectCode, oldDate)) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason("禁止删除锁定日期的CN记录");
            }
            return;
        }
        // 情况1：数据库中日期被锁定
        if (lockTimeUtil.checkTimeLock(valid, projectCode, oldDate)) {
            // 如果日期或金额有修改
            if (!Objects.equals(newDate, oldDate) || newAmount.compareTo(oldAmount) != 0 || !newNumber.equals(oldNumber)) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("第%s次开票中CN记录已被锁定，禁止修改日期或金额、票号", suffix));
            }
            return;
        }

        // 情况2：数据库中日期未锁定
        // 检查新日期是否被锁定
        boolean isNewDateLocked = lockTimeUtil.checkTimeLock(valid, projectCode, newDate);

        // 如果日期有变化且新日期被锁定
        if (!Objects.equals(newDate, oldDate) && isNewDateLocked) {
            valid.setStatus(ImportResultVO.STATUS_FAILED);
            valid.addWrongReason(String.format("第%s次CN的新日期%s处于锁定期间", suffix, newDate));
            return;
        }

        // 如果金额有变化且新日期被锁定（金额随日期锁定）
        if (!newAmount.equals(oldAmount) && isNewDateLocked) {
            valid.setStatus(ImportResultVO.STATUS_FAILED);
            valid.addWrongReason(String.format("第%s次CN金额因日期%s被锁定而禁止修改", suffix, newDate));
        }

        // 如果票号有变化且新日期被锁定（票号随日期锁定）
        if (!newNumber.equals(oldNumber) && isNewDateLocked) {
            valid.setStatus(ImportResultVO.STATUS_FAILED);
            valid.addWrongReason(String.format("第%s次CN票号因日期%s被锁定而禁止修改", suffix, newDate));
        }
    }


    @Override
    public ImportResultVO doTransform(TransformContext context, int index, Map<String, Object> raw) {
        final String appid = context.getAppid();
        final List<MetaDataDTOWrapper> poCache = context.getPoCache();
        final List<MetaDataDTOWrapper> customerProjectCache = context.getCustomerProjectCache();
        final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
        final List<MetaDataDTOWrapper> poItemCache = context.getPoItemCache();
        final List<MetaDataDTOWrapper> ypttSettlementCache = context.getYpttSettlementCache();

        Dict dict = new Dict(raw);
        String PO_number = dict.getStr("PO_number");
        String Contract_number = dict.getStr("Contract_number");
        String Phase = dict.getStr("Phase");
        String Site_ID = dict.getStr("Site_ID");
        String Item_code = dict.getStr("Item_code");
        String Invoice_date_1st = dict.getStr("Invoice_date_1st");
        String Invoice_number_1st = dict.getStr("Invoice_number_1st");
        String Invoice_Amount_1st = dict.getStr("Invoice_Amount_1st");
        String Invoice_date_2nd = dict.getStr("Invoice_date_2nd");
        String Invoice_number_2nd = dict.getStr("Invoice_number_2nd");
        String Invoice_Amount_2nd = dict.getStr("Invoice_Amount_2nd");
        String Invoice_date_3rd = dict.getStr("Invoice_date_3rd");
        String Invoice_number_3rd = dict.getStr("Invoice_number_3rd");
        String Invoice_Amount_3rd = dict.getStr("Invoice_Amount_3rd");
        String Invoice_date_4st = dict.getStr("Invoice_date_4st");
        String Invoice_number_4st = dict.getStr("Invoice_number_4st");
        String Invoice_Amount_4st = dict.getStr("Invoice_Amount_4st");
        String Remark = dict.getStr("Remark");
        String Invoice_amount = dict.getStr("Invoice_amount");
        String Invoice_amount_gap = dict.getStr("Invoice_amount_gap");
        String record = dict.getStr("re_record");
        String remark_1st = dict.getStr("Invoice_remark_1st");
        String remark_2nd = dict.getStr("Invoice_remark_2nd");
        String remark_3rd = dict.getStr("Invoice_remark_3rd");
        String remark_4th = dict.getStr("Invoice_remark_4th");
        String cnAmount_1st = dict.getStr("CN_amount_1st");
        String cnRemark_1st = dict.getStr("CN_remark_1st");
        String cnAmount_2nd = dict.getStr("CN_amount_2nd");
        String cnRemark_2nd = dict.getStr("CN_remark_2nd");
        String cnAmount_3rd = dict.getStr("CN_amount_3rd");
        String cnRemark_3rd = dict.getStr("CN_remark_3rd");
        String cnAmount_4st = dict.getStr("CN_amount_4st");
        String cnRemark_4st = dict.getStr("CN_remark_4st");
        String TotallyCNAmount = dict.getStr("Totally_CN_amount");
        String CN_date_1st = dict.getStr("CN_date_1st");
        String CN_number_1st = dict.getStr("CN_number_1st");
        String CN_date_2nd = dict.getStr("CN_date_2nd");
        String CN_number_2nd = dict.getStr("CN_number_2nd");
        String CN_date_3rd = dict.getStr("CN_date_3rd");
        String CN_number_3rd = dict.getStr("CN_number_3rd");
        String CN_date_4st = dict.getStr("CN_date_4st");
        String CN_number_4st = dict.getStr("CN_number_4st");

        // 调用过 validate, 订单、客户项目、站点、订单条目 均不会为null
        // 查询 订单
        MetaDataDTOWrapper existingPO = findPoByPoNumber(appid, poCache, PO_number);
        // 查询 客户项目
        MetaDataDTOWrapper existingCustomerProject = findCustomerProjectByContractNumber(appid, customerProjectCache,
                Contract_number);
        // 查询 站点
        MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
        // 查询 订单条目
        List<MetaDataDTOWrapper> existingPOItemList = findPOItem(appid, poItemCache, existingPO.getDataId(), Phase,
                existingSite.getDataId(), Item_code);
        MetaDataDTOWrapper existingPOItem = CollUtil.getFirst(existingPOItemList);

        // 查询 YPTT结算信息
        final String uniquenessIdJsonString = (String) existingPOItem.getValue(UNI_META_ATTR_UNIQUENESS_FIELD);
        MetaDataDTOWrapper existingYPTTSettlement = findYpttSettlementByUniquenessIdJson(appid, ypttSettlementCache,
                uniquenessIdJsonString);
        List<Map<String, Object>> projectCode = basicMapper
                .findYpttProjectByCode(MetaDataUtil.handleObject2String(existingPOItem.getValue("Project_code")));
        if (Objects.isNull(existingYPTTSettlement)) {
            existingYPTTSettlement = new MetaDataDTOWrapper();
            existingYPTTSettlement.setValue("uniqueness_field", uniquenessIdJsonString);
            existingYPTTSettlement.setValue("Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
            // 基础信息
            Long userId = SecurityUtils.getUser().getId();
            existingYPTTSettlement.setValue("create_by", userId);
            existingYPTTSettlement.setValue("create_time", LocalDateTime.now());
            existingYPTTSettlement.setValue("update_by", userId);
            existingYPTTSettlement.setValue("update_time", LocalDateTime.now());
        }
        if (StrUtil.isNotBlank(Remark)) {
            String newRemark = MetaDataUtil.appendStr(existingYPTTSettlement.getValue("remark"), Remark, "\n\n");
            existingYPTTSettlement.setValue("remark", newRemark);
        }
        // 基础信息更新
        if (CollUtil.isNotEmpty(projectCode)) {
            existingYPTTSettlement.setValue("Project_code", projectCode.get(0).get("YPTT_Project_code"));
            existingYPTTSettlement.setValue("Project_name", projectCode.get(0).get("YPTT_Project_name"));
        }
        existingYPTTSettlement.setValue("Contract_number", Contract_number);
        existingYPTTSettlement.setValue("PO_number", PO_number);
        existingYPTTSettlement.setValue("BOQ_item", existingPOItem.getValue("BOQ_item"));
        existingYPTTSettlement.setValue("PO_value", existingPOItem.getValue("PO_value"));
        existingYPTTSettlement.setValue("Phase", existingPOItem.getValue("Phase"));
        existingYPTTSettlement.setValue("Item_code", existingPOItem.getValue("Item_code"));
        existingYPTTSettlement.setValue("Site_ID", existingPOItem.getValue("Site_ID"));

        if (!StringUtils.isBlank(Invoice_date_1st) && !StringUtils.isBlank(Invoice_number_1st) && !StringUtils.isBlank(Invoice_Amount_1st)) { //不存在开票日期的话，就不处理当前阶段的发票号码、发票金额、开票日期
            existingYPTTSettlement.setValue("Invoice_date_1st", DateUtil.parse(Invoice_date_1st));
            existingYPTTSettlement.setValue("Invoice_number_1st", Invoice_number_1st);
            existingYPTTSettlement.setValue("Invoice_Amount_1st", MetaDataUtil.numberStr2BigDecimal(Invoice_Amount_1st));
        }
        if (!StringUtils.isBlank(Invoice_date_2nd) && !StringUtils.isBlank(Invoice_number_2nd) && !StringUtils.isBlank(Invoice_Amount_2nd)) { //不存在开票日期的话，就不处理当前阶段的发票号码、发票金额、开票日期
            existingYPTTSettlement.setValue("Invoice_date_2st", DateUtil.parse(Invoice_date_2nd));
            existingYPTTSettlement.setValue("Invoice_number_2st", Invoice_number_2nd);
            existingYPTTSettlement.setValue("Invoice_Amount_2st", MetaDataUtil.numberStr2BigDecimal(Invoice_Amount_2nd));
        }
        if (!StringUtils.isBlank(Invoice_date_3rd) && !StringUtils.isBlank(Invoice_number_3rd) && !StringUtils.isBlank(Invoice_Amount_3rd)) { //不存在开票日期的话，就不处理当前阶段的发票号码、发票金额、开票日期
            existingYPTTSettlement.setValue("Invoice_date_3st", DateUtil.parse(Invoice_date_3rd));
            existingYPTTSettlement.setValue("Invoice_number_3st", Invoice_number_3rd);
            existingYPTTSettlement.setValue("Invoice_Amount_3st", MetaDataUtil.numberStr2BigDecimal(Invoice_Amount_3rd));
        }
        if (!StringUtils.isBlank(Invoice_date_4st) && !StringUtils.isBlank(Invoice_number_4st) && !StringUtils.isBlank(Invoice_Amount_4st)) { //不存在开票日期的话，就不处理当前阶段的发票号码、发票金额、开票日期
            existingYPTTSettlement.setValue("Invoice_date_4st", DateUtil.parse(Invoice_date_4st));
            existingYPTTSettlement.setValue("Invoice_number_4st", Invoice_number_4st);
            existingYPTTSettlement.setValue("Invoice_Amount_4st", MetaDataUtil.numberStr2BigDecimal(Invoice_Amount_4st));
        }
        existingYPTTSettlement.setValue("Invoice_amount", MetaDataUtil.numberStr2BigDecimal(Invoice_amount));
        existingYPTTSettlement.setValue("Invoice_amount_gap", MetaDataUtil.numberStr2BigDecimal(Invoice_amount_gap));

        if (!StringUtils.isBlank(CN_date_1st) && !StringUtils.isBlank(CN_number_1st) && !StringUtils.isBlank(cnAmount_1st)) { //不存在CN日期的话，就不处理当前阶段的发票号码、发票金额、开票日期
            existingYPTTSettlement.setValue("CN_date_1st", DateUtil.parse(CN_date_1st));
            existingYPTTSettlement.setValue("CN_number_1st", CN_number_1st);
            existingYPTTSettlement.setValue("CN_amount_1st", MetaDataUtil.numberStr2BigDecimal(cnAmount_1st));
            existingYPTTSettlement.setValue("CN_remark_1st", cnRemark_1st);
        }

        if (!StringUtils.isBlank(CN_date_2nd) && !StringUtils.isBlank(CN_number_2nd) && !StringUtils.isBlank(cnAmount_2nd)) { //不存在CN日期的话，就不处理当前阶段的发票号码、发票金额、开票日期
            existingYPTTSettlement.setValue("CN_date_2nd", DateUtil.parse(CN_date_1st));
            existingYPTTSettlement.setValue("CN_number_2nd", CN_number_2nd);
            existingYPTTSettlement.setValue("CN_amount_2nd", MetaDataUtil.numberStr2BigDecimal(cnAmount_2nd));
            existingYPTTSettlement.setValue("CN_remark_2nd", cnRemark_2nd);
        }

        if (!StringUtils.isBlank(CN_date_3rd) && !StringUtils.isBlank(CN_number_3rd) && !StringUtils.isBlank(cnAmount_3rd)) { //不存在CN日期的话，就不处理当前阶段的发票号码、发票金额、开票日期
            existingYPTTSettlement.setValue("CN_date_3rd", DateUtil.parse(CN_date_3rd));
            existingYPTTSettlement.setValue("CN_number_3rd", CN_number_3rd);
            existingYPTTSettlement.setValue("CN_amount_3rd", MetaDataUtil.numberStr2BigDecimal(cnAmount_3rd));
            existingYPTTSettlement.setValue("CN_remark_3rd", cnRemark_3rd);
        }

        if (!StringUtils.isBlank(CN_date_4st) && !StringUtils.isBlank(CN_number_4st) && !StringUtils.isBlank(cnAmount_4st)) { //不存在CN日期的话，就不处理当前阶段的发票号码、发票金额、开票日期
            existingYPTTSettlement.setValue("CN_date_4st", DateUtil.parse(CN_date_4st));
            existingYPTTSettlement.setValue("CN_number_4st", CN_number_4st);
            existingYPTTSettlement.setValue("CN_amount_4st", MetaDataUtil.numberStr2BigDecimal(cnAmount_4st));
            existingYPTTSettlement.setValue("CN_remark_4st", cnRemark_4st);
        }
        existingYPTTSettlement.setValue("re_record", record);
        existingYPTTSettlement.setValue("Invoice_remark_1st", remark_1st);
        existingYPTTSettlement.setValue("Invoice_remark_2st", remark_2nd);
        existingYPTTSettlement.setValue("Invoice_remark_3st", remark_3rd);
        existingYPTTSettlement.setValue("Invoice_remark_4st", remark_4th);
        existingYPTTSettlement.setValue("Contract_number", Contract_number);

        BigDecimal total = new BigDecimal("0");
        BigDecimal CN_amount_1st = MetaDataUtil.handleObject2BigDecimal(existingYPTTSettlement.getValue("CN_amount_1st"), false);
        BigDecimal CN_amount_2nd = MetaDataUtil.handleObject2BigDecimal(existingYPTTSettlement.getValue("CN_amount_2nd"), false);
        BigDecimal CN_amount_3rd = MetaDataUtil.handleObject2BigDecimal(existingYPTTSettlement.getValue("CN_amount_3rd"), false);
        BigDecimal CN_amount_4st = MetaDataUtil.handleObject2BigDecimal(existingYPTTSettlement.getValue("CN_amount_4st"), false);
        total = total.add(Objects.isNull(CN_amount_1st) ? BigDecimal.ZERO : CN_amount_1st)
                .add(Objects.isNull(CN_amount_2nd) ? BigDecimal.ZERO : CN_amount_2nd)
                .add(Objects.isNull(CN_amount_3rd) ? BigDecimal.ZERO : CN_amount_3rd)
                .add(Objects.isNull(CN_amount_4st) ? BigDecimal.ZERO : CN_amount_4st);
        existingYPTTSettlement.setValue("Totally_CN_amount", total);
        // 连接器执行
        y9connector(existingYPTTSettlement);
        saveYpttSettlement(existingYPTTSettlement);
        ypttSettlementCache.add(existingYPTTSettlement);
        return new ImportResultVO(index, raw, ImportResultVO.STATUS_SUCCEED, "");
    }

    @Override
    public boolean support(TransformContext context) {
        return Objects.equals(MODULE_NAME, context.getModuleName());
    }

    /**
     * PO编号 查询 PO完整信息
     *
     * @param appid      appid
     * @param localStore 本地缓存
     * @param PO_number  PO编号
     * @return PO完整信息
     */
    private MetaDataDTOWrapper findPoByPoNumber(String appid, List<MetaDataDTOWrapper> localStore, String PO_number) {
        MetaDataDTOWrapper hit = localFindOne(localStore, PO_META_ATTR_PO_NUMBER, PO_number);
        if (Objects.nonNull(hit)) {
            return hit;
        }
        List<Map<String, Object>> poByPoNumber = basicMapper.findPoByPoNumber(PO_number);
        return CollUtil.isEmpty(poByPoNumber) ? null : new MetaDataDTOWrapper(poByPoNumber);
    }

    /**
     * 客户合同编码 查询 客户项目
     *
     * @param appid          appid
     * @param localStore     本地缓存
     * @param customerNumber 客户合同编码
     * @return 客户项目
     */
    private MetaDataDTOWrapper findCustomerProjectByContractNumber(String appid, List<MetaDataDTOWrapper> localStore,
                                                                   String customerNumber) {
        MetaDataDTOWrapper hit = localFindOne(localStore, CUSTOMER_PROJECT_META_ATTR_CONTRACT_NUMBER, customerNumber);
        if (Objects.nonNull(hit)) {
            return hit;
        }
        List<Map<String, Object>> customerProjectByContractNumber = basicMapper
                .findCustomerProjectByContractNumber(customerNumber, null);
        return CollUtil.isEmpty(customerProjectByContractNumber) ? null
                : new MetaDataDTOWrapper(customerProjectByContractNumber);
    }

    /**
     * PO号码、客户合同编号、阶段、站点ID、业务条目代码 查询 订单条目
     *
     * @param appid      appid
     * @param localCache 本地缓存
     * @param poDataId   PO 数据ID
     * @param Phase      阶段
     * @param siteDataId 站点 数据ID
     * @param itemCode   业务条目代码
     * @return 订单条目
     */
    private List<MetaDataDTOWrapper> findPOItem(String appid, List<MetaDataDTOWrapper> localCache, Long poDataId,
                                                String Phase, Long siteDataId, String itemCode) {
        String poDataIdJson = MetaDataUtil.handleDataId2Json(poDataId);
        String siteDataIdJson = MetaDataUtil.handleDataId2Json(siteDataId);
        for (MetaDataDTOWrapper poItem : localCache) {
            Object existingPo = poItem.getValue("PO");
            Object existingPhase = poItem.getValue("Phase");
            Object existingSite = poItem.getValue("site");
            Object existingItemCode = poItem.getValue("Item_code");
            // @formatter:off
            if (
                    Objects.equals(existingPo, poDataIdJson) &&
                            Objects.equals(existingPhase, Phase) &&
                            Objects.equals(existingSite, siteDataIdJson) &&
                            Objects.equals(existingItemCode, itemCode)
            ) {
                // @formatter:on
                return Collections.singletonList(poItem);
            }
        }
        // 查询
        List<Map<String, Object>> customerProjectByContractNumber = basicMapper.findPOItem(poDataId, Phase, siteDataId,
                itemCode);
        return CollUtil.isEmpty(customerProjectByContractNumber) ? null
                : Collections.singletonList(new MetaDataDTOWrapper(customerProjectByContractNumber));
    }

}
