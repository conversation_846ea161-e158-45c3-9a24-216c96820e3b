package com.pig4cloud.pig.yptt.service.standingbook;

import com.pig4cloud.pig.yptt.entity.dto.SubconStandingBookDTO;
import com.pig4cloud.pig.yptt.mapper.SubconStandingBookMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubconStandingBookUpdater extends StandingBookUpdater<SubconStandingBookDTO> {

	private final SubconStandingBookMapper subconStandingBookMapper;

	@Override
	protected String getName() {
		return "SubconStandingBook";
	}

	@Override
	protected List<SubconStandingBookDTO> generate(int i, int size) {
		return subconStandingBookMapper.generateSubconStandingBookList(i, size);
	}

	@Override
	protected int save(SubconStandingBookDTO generateSubconStandingBookDTO) {
		return subconStandingBookMapper.update(generateSubconStandingBookDTO);
	}

}
