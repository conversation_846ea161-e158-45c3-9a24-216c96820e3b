package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.yptt.controller.ApiRes;
import com.pig4cloud.pig.yptt.entity.MetaModeInfo;
import com.pig4cloud.pig.yptt.mapper.ViewModelRel;
import com.pig4cloud.pig.yptt.utils.RedisUtil;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName: MetaModelRelService
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-08-05  11:11
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ViewModelRelService {
    private final ViewModelRel viewModelRel;
    private final RedisUtil redisUtil;

    /**
     * @description: 获取模型关系物理表
     * @param leftModel 左模型名称
     * @param rightModel 右模型名称
     * @return String 物理表
     **/
    public String getModelRelTableNameByModelName(String leftModel, String rightModel) {

        String modelRelTableName = (String) redisUtil.get("model:rel:tableName:"+leftModel+":"+rightModel);

        if (StrUtil.isNotBlank(modelRelTableName)){
            return modelRelTableName;
        }

        // 获取对应付款单
        MetaModeInfo leftModelInfo = viewModelRel.getModelTableNameByModelName(leftModel);
        if (ObjectUtil.isEmpty(leftModelInfo)){
            throw new RuntimeException("左模型不存在");
        }

        // 获取付款明细
        MetaModeInfo rightModelInfo = viewModelRel.getModelTableNameByModelName(rightModel);
        if (ObjectUtil.isEmpty(rightModelInfo)){
            throw new RuntimeException("右模型不存在");
        }

        modelRelTableName = viewModelRel.getModelRelTableNameByModelName(leftModelInfo.getId(), rightModelInfo.getId());
        if(StrUtil.isBlank(modelRelTableName)){
            throw new RuntimeException("不存在的模型关系");
        }
        redisUtil.set("model-rel:"+leftModel+":"+rightModel, modelRelTableName, 60*60*24);

        return modelRelTableName;
    }

    /**
     * @description: 获取模型物理表
     *
     **/
    public String getModelTableNameByModelName(String modelName) {
        String modelTableName = (String) redisUtil.get("model:tableName:"+modelName);

        if(!StrUtil.isBlank(modelTableName)){
            return modelTableName;
        }

        // 获取模型信息
        MetaModeInfo modelInfo = viewModelRel.getModelTableNameByModelName(modelName);
        if (ObjectUtil.isEmpty(modelInfo)){
            throw new RuntimeException("模型不存在");
        }
        modelTableName = modelInfo.getTableName();
        redisUtil.set("model:"+modelName, modelTableName, 60*60*24);

        return modelTableName;
    }
}