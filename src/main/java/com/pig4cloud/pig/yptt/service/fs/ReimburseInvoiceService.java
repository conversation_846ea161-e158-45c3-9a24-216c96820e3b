package com.pig4cloud.pig.yptt.service.fs;

import com.pig4cloud.pig.yptt.entity.ReimburseInvoiceEntity;
import com.pig4cloud.pig.yptt.mapper.ReimburseInvoiceMapper;
import com.pig4cloud.pig.yptt.service.ViewModelRelService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName: ReimburseInvoiceService
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-08-14  10:17
 * @Version: 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class ReimburseInvoiceService {
    private final ReimburseInvoiceMapper reimburseInvoiceMapper;
    private final ViewModelRelService viewModelRelService;

    private final String modelTableName = "reimburse_invoice";

    /**
     * 批量记录已报销发票
     * @param reimburseInvoices
     * @return 批量记录成功
     **/
    public Boolean addReimburseInvoice(List<ReimburseInvoiceEntity> reimburseInvoices) {
        String modelTable = viewModelRelService.getModelTableNameByModelName(modelTableName);

        if(reimburseInvoices.size()==0){
            return true;
        }

        int i = reimburseInvoiceMapper.addBatch(modelTable,reimburseInvoices);

        if(i>0){
            return true;
        }

        return false;
    }

    public Boolean isDuplicateInvoiceNums(List<String> invoiceNums) {
        if(invoiceNums.size()==0){
            return false;
        }

        String modelTable = viewModelRelService.getModelTableNameByModelName(modelTableName);

        return reimburseInvoiceMapper.isDuplicateInvoiceNums(modelTable,invoiceNums);
    }
}