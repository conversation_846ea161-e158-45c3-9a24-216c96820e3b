package com.pig4cloud.pig.yptt.service.workflow;

import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.admin.api.dto.user.AddTenantUserDTO;
import com.pig4cloud.pig.admin.api.entity.SysUser;
import com.pig4cloud.pig.admin.api.feign.RemoteUserServiceV2;
import com.pig4cloud.pig.yptt.controller.WorkFlowApiRes;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlow.request.RequestInfo;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlow.request.TableInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName: WorkFlowUserEntryService
 * @Description: 用户入职信息流程
 * @Author: lijianpan
 * @CreateTime: 2025-06-19  19:57
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkFlowUserEntryService {
    private final RemoteUserServiceV2 remoteUserServiceV2;

    /**
     * 新增用户
     **/
    public WorkFlowApiRes add(RequestInfo requestInfo) {
        TableInfo tableInfo = requestInfo.getTableInfo();
        log.info("新增用户");

        log.info("流程请求参数：{}", requestInfo);

        AddTenantUserDTO body = new AddTenantUserDTO();
        body.setTenantId(1694550407300681729L);

        try {
            if(!StrUtil.isNotBlank(tableInfo.getValue("phone"))){
                throw new IllegalArgumentException("手机号不能为空！");
            }

            if(!StrUtil.isNumeric(tableInfo.getValue("phone"))){
                throw new IllegalArgumentException("手机号格式不正确！");
            }

            //账户
//            body.setUsername(tableInfo.getValue("phone"));
            //密码
            body.setPassword("Abcd1234");
            //身份证
//            body.setIdentityCard(tableInfo.getValue("id_card"));
            //手机号
            body.setPhone(tableInfo.getValue("phone"));
            //邮箱
//            body.setEmail(tableInfo.getValue("email"));
            //昵称
            body.setFullname(tableInfo.getValue("nick"));
            //部门
            List<String> depts =  tableInfo.getJsonArrayValue("department");
            if (!depts.isEmpty()) {
                body.setDeptId(Long.valueOf(tableInfo.getJsonArrayValue("department").get(0)));
            }else {
                throw new IllegalArgumentException("部门不能为空！");
            }
        }catch (Exception e){
            e.printStackTrace();
            return WorkFlowApiRes.error(e.getMessage());
        }

        // 岗位
        Optional<List<Long>> postIdsOptional = Optional.of(tableInfo.getJsonArrayValue("post"))
                .filter(list -> !list.isEmpty())
                .map(
                        list -> list.stream()
                                .map(Long::parseLong)
                                .collect(Collectors.toList())
                );
        postIdsOptional.ifPresent(body::setPostIds);

        SysUser sysUser = null;

        try {
            sysUser = remoteUserServiceV2.getUserByPhone(body.getPhone(),"Y");
        }catch (Exception e){
            log.error("查询用户信息异常！",e.getMessage());
            return WorkFlowApiRes.error("查询用户信息异常！");
        }


        if (sysUser!=null){
            return WorkFlowApiRes.error("手机号已注册！");
        }

        boolean flag = false;

        try {
            flag = remoteUserServiceV2.registerUser(body,"Y");
        } catch (Exception e) {
            log.error("新增用户异常！",e.getMessage());
            return WorkFlowApiRes.error("新增用户异常！");
        }

        if(!flag){
            return WorkFlowApiRes.error("新增用户失败！");
        }

        return WorkFlowApiRes.ok();
    }
}