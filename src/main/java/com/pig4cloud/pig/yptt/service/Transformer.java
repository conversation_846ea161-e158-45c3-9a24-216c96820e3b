package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.lang.Assert;
import com.pig4cloud.pig.me.api.vo.ViewGroupConfVO;
import com.pig4cloud.pig.yptt.entity.dto.YPTTBatchImportDTO;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import lombok.Getter;
import org.apache.commons.collections4.list.SetUniqueList;
import org.apache.commons.collections4.list.UnmodifiableList;
import org.apache.commons.collections4.map.UnmodifiableMap;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/09/28
 */
public interface Transformer {

	ImportResultVO validate(TransformContext context, int index, Map<String, Object> raw);

	ImportResultVO transform(TransformContext context, int index, Map<String, Object> raw);

	boolean support(TransformContext context);

	class TransformContext {

		@Getter
		private final Long userId;

		@Getter
		private final YPTTBatchImportDTO importParam;

		@Getter
		private final List<Map<String, Object>> raws;

		@Getter
		private final String operationType;

		private final Map<String, List<MetaDataDTOWrapper>> cache = new HashMap<>(11);

		private final Map<String, Long> deptCache = new HashMap<>(0);

		private final Map<String, ViewGroupConfVO> viewGroupConfCache = new HashMap<>();

		private final Object cacheLockInstance = new Object();

		public TransformContext(Long userId, YPTTBatchImportDTO importParam, List<Map<String, Object>> raws,
				String operationType) {
			Assert.notNull(importParam, "import parameter is empty");
			this.userId = userId;
			this.operationType = operationType;
			this.importParam = importParam;
			this.raws = UnmodifiableList
				.unmodifiableList(raws.stream().map(UnmodifiableMap::unmodifiableMap).collect(Collectors.toList()));
		}

		public String getAppid() {
			return this.importParam.getAppId();
		}

		public String getModuleName() {
			return this.importParam.getModuleType();
		}

		public List<MetaDataDTOWrapper> getProjectCache() {
			return getCacheIfNonExistThenCreateAndReturn("ypttProject");
		}

		// TODO 删除代码段：BI-2 收支统计数据更新不应该在导入中处理
		// public List<MetaDataDTOWrapper> getIncomeExpenditure() {
		// return getCacheIfNonExistThenCreateAndReturn("incomeExpenditure");
		// }

		public List<MetaDataDTOWrapper> getCustomerProjectCache() {
			return getCacheIfNonExistThenCreateAndReturn("customerProject");
		}

		public List<MetaDataDTOWrapper> getUniquenessCache() {
			return getCacheIfNonExistThenCreateAndReturn("uniqueness");
		}

		public List<MetaDataDTOWrapper> getSiteCache() {
			return getCacheIfNonExistThenCreateAndReturn("site");
		}

		public List<MetaDataDTOWrapper> getSiteItemCache() {
			return getCacheIfNonExistThenCreateAndReturn("siteItem");
		}

		public List<MetaDataDTOWrapper> getPoCache() {
			return getCacheIfNonExistThenCreateAndReturn("po");
		}

		public List<MetaDataDTOWrapper> getPoItemCache() {
			return getCacheIfNonExistThenCreateAndReturn("poItem");
		}

		public List<MetaDataDTOWrapper> getSubconCache() {
			return getCacheIfNonExistThenCreateAndReturn("subcon");
		}

		public List<MetaDataDTOWrapper> getSiteDeliveryInfoCache() {
			return getCacheIfNonExistThenCreateAndReturn("siteDeliveryInfo");
		}

		public List<MetaDataDTOWrapper> getReadySettlementCache() {
			return getCacheIfNonExistThenCreateAndReturn("readySettlement");
		}

		public List<MetaDataDTOWrapper> getIncomeExpenditure() {
			return getCacheIfNonExistThenCreateAndReturn("incomeExpenditure");
		}

		public List<MetaDataDTOWrapper> getProductivityReportCache() {
			return getCacheIfNonExistThenCreateAndReturn("productivityReport");
		}

		public List<MetaDataDTOWrapper> getSubconSettlementCache() {
			return getCacheIfNonExistThenCreateAndReturn("subconSettlement");
		}

		public List<MetaDataDTOWrapper> getSubconPoCache() {
			return getCacheIfNonExistThenCreateAndReturn("subconPo");
		}

		public List<MetaDataDTOWrapper> getSubconPoItemCache() {
			return getCacheIfNonExistThenCreateAndReturn("subconPOItem");
		}

		public List<MetaDataDTOWrapper> getYpttSettlementCache() {
			return getCacheIfNonExistThenCreateAndReturn("ypttSettlement");
		}

		public List<MetaDataDTOWrapper> getSubconPayment() {
			return getCacheIfNonExistThenCreateAndReturn("subconPayment");
		}

		public Map<String, Long> getDepartmentCache() {
			return deptCache;
		}

		public Map<String, ViewGroupConfVO> getViewGroupConfCache() {
			return viewGroupConfCache;
		}

		/**
		 * 获取缓存，若缓存未尚未创建，则创建后返回
		 * <p>
		 * TODO 未测试并发场景
		 * @param cacheName 缓存名称
		 * @return 返回缓存
		 */
		private List<MetaDataDTOWrapper> getCacheIfNonExistThenCreateAndReturn(String cacheName) {
			if (!cache.containsKey(cacheName)) {
				synchronized (this.cacheLockInstance) {
					if (!cache.containsKey(cacheName)) {
						this.cache.put(cacheName, SetUniqueList.setUniqueList(new ArrayList<>()));
					}
				}
			}
			return cache.get(cacheName);
		}

	}

}
