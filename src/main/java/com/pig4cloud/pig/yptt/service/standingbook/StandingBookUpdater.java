package com.pig4cloud.pig.yptt.service.standingbook;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.yptt.bizcode.ExpectedOneButGotMany;
import com.pig4cloud.pig.yptt.entity.dto.ProjectStandingBookDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/01/17
 */
@Slf4j
public abstract class StandingBookUpdater<Payload> {

	protected abstract String getName();

	protected abstract List<Payload> generate(int i, int size);

	protected abstract int save(Payload payload);

	protected int size() {
		return 100;
	}

	public List<R<Payload>> update() {
		int i = 0;
		int size = size();
		boolean hasNext = true;
		List<R<Payload>> result = new ArrayList<>();
		// 每次生成并保存100条，直到生成的数据为空列表
		while (hasNext) {
			List<Payload> list = generate(i, size);
			for (Payload payload : list) {
				try {
					int row = save(payload);
					if (row <= 0) {
						result.add(R.failed(payload, "save failed"));
						log.error("{} failed, {}", getName(), payload);
					}
					else {
						result.add(R.ok(payload, "save success"));
					}
				}
				catch (Exception e) {
					log.error("Saving generated {} error, cause :", getName(), e);
					result.add(R.failed(payload, e.getMessage()));
				}
			}
			if (CollUtil.isEmpty(list)) {
				hasNext = false;
			}
			i += size;
		}
		return result;
	}

}
