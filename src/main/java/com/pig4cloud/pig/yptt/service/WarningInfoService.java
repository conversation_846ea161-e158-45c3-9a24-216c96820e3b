package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.entity.ProjectRole;
import com.pig4cloud.pig.yptt.entity.WarnTemp;
import com.pig4cloud.pig.yptt.entity.WarningMessage;
import com.pig4cloud.pig.yptt.entity.dto.WarningPageDTO;
import com.pig4cloud.pig.yptt.entity.vo.WarningStatisticsVO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.mapper.WarningMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2023/09/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WarningInfoService {

	private final ViewConfProperties viewConfProperties;

	private final WarningMapper warningMapper;

	private final TransactionTemplate transactionTemplate;

	private static final String SITE_PO_DELAY = "Site_PO_Delay";

	private static final String SITE_DELAY = "Site_Delay";

	private static final String AMOUNT_ERROR = "Amount_Error";

	private static final String START_WORKING_DELAY = "Start_Working_Delay";

	private static final String ACCEPTANCE_DELAY = "Acceptance_Delay";

	private static final String SUBCON_PO_DELAY = "Subcon_PO_Delay";

	private static final String INVOICE_DELAY = "Invoice_Delay";

	private static final String SUBCON_PAYMENT_DELAY = "Subcon_Payment_Delay";

	private final BasicMapper basicMapper;

	/**
	 * 定期清理警告信息
	 * @return
	 */
	public synchronized Boolean clearWarningInfo() {
		warningMapper.clearWarningInfo();
		return Boolean.TRUE;
	}

	public synchronized Boolean updateStatus() {
		List<String> codes = basicMapper.getProjectCodes();
		updateByProjectCode(codes);
		return Boolean.TRUE;
	}

	public void updateByProjectCode(List<String> projectCodes) {
		if (CollUtil.isEmpty(projectCodes)) {
			projectCodes = basicMapper.getProjectCodes();
		}
		if (CollUtil.isEmpty(projectCodes)) {
			return;
		}
		for (String projectCode : projectCodes) {
			// 更新站点条目警告状态
			updateSiteItemStatus(projectCode);

			// 更新站点交付警告状态
			updateSiteDeliveryStatus(projectCode);

			// 更新采购订单警告状态
			updatePoStatus(projectCode);

			// 更新分包商支付警告状态
			updateSubsconStatus(projectCode);

			// 更新YPTT结算警告状态
			updateYPTTSettlementStatus(projectCode);
		}
	}

	public void updateSiteItemStatus(String projectCode) {
		Integer pageSize = viewConfProperties.getPageSize();
		String sitePODelayWarning = warningMapper.getThresholdByCode(projectCode, "SitePO_Delay_Warning");
		List<Long> warnDataIdByCode = warningMapper.getWarnDataIdByCode(projectCode, SITE_PO_DELAY);
		int i = 0;
		boolean hasNext = true;
		List<WarningMessage> warningMessages = new ArrayList<>();
		while (hasNext) {
			List<WarnTemp> tempList = warningMapper.selectSiteWarnTemp(i, pageSize, projectCode, sitePODelayWarning);
			if (CollUtil.isNotEmpty(tempList)) {
				List<Long> idList = new ArrayList<>();
				for (WarnTemp temp : tempList) {
					if (CollUtil.isEmpty(warnDataIdByCode) || !warnDataIdByCode.contains(temp.getId())) {
						WarningMessage warningMessage = new WarningMessage();
						warningMessage.setWarningMsg("Tips: Track and update PO information in time!");
						warningMessage.setWarningDataId(temp.getId());
						warningMessage.setProjectCode(temp.getProjectCode());
						warningMessage.setUniquenessField(temp.getUniquenessField());
						warningMessage.setWarningType(SITE_PO_DELAY);
						warningMessage.setProjectName(temp.getProjectName());
						warningMessage.setViewId(viewConfProperties.getSiteItem().getWarnViewId());
						warningMessage.setViewGroupId(viewConfProperties.getSiteItem().getWarnViewGroupId());
						warningMessage.setMenuId(viewConfProperties.getSiteItem().getMenuId());
						warningMessage.setId(IdUtil.getSnowflakeNextId());
						warningMessages.add(warningMessage);
						idList.add(temp.getId());
					}
				}
				if (CollUtil.isNotEmpty(idList)) {
					warningMapper.updateSiteWarning(SITE_PO_DELAY, idList);
				}
			}
			hasNext = tempList.size() == pageSize;
			i += pageSize;
		}
		saveWarningMessage(warningMessages);

		// 移除警告
		List<Long> removeIdList = warningMapper.selectRemoveSiteId(projectCode, sitePODelayWarning);

		transactionTemplate.execute(status -> {
			List<WarningMessage> remove = new ArrayList<>();
			if (CollUtil.isNotEmpty(removeIdList)) {
				warningMapper.updateSiteWarning("Normal", removeIdList);
				for (Long aLong : removeIdList) {
					WarningMessage warningMessage = new WarningMessage();
					warningMessage.setWarningDataId(aLong);
					warningMessage.setWarningType(SITE_PO_DELAY);
					remove.add(warningMessage);
				}
			}
			removeWarningMessageByWarningType(remove);
			return Boolean.TRUE;
		});
	}

	public void updatePoStatus(String projectCode) {
		Integer pageSize = viewConfProperties.getPageSize();
		String siteDelayWarning = warningMapper.getThresholdByCode(projectCode, "Site_Delay_Warning");
		String amountErrorWarning = warningMapper.getThresholdByCode(projectCode, "Amount_Error_Warning");
		List<Long> siteDelayId = warningMapper.getWarnDataIdByCode(projectCode, SITE_DELAY);
		List<Long> amountDelayId = warningMapper.getWarnDataIdByCode(projectCode, AMOUNT_ERROR);
		List<WarningMessage> warningMessages = new ArrayList<>();
		BiConsumer<Integer, Boolean> siteDelay = (i, hasNext) -> {
			while (hasNext) {
				// 站点信息delay告警
				List<WarnTemp> tempList = warningMapper.selectPoWarnTemp(i, pageSize, projectCode, siteDelayWarning);
				if (CollUtil.isNotEmpty(tempList)) {
					List<Long> siteIdList = new ArrayList<>();
					for (WarnTemp temp : tempList) {
						if (CollUtil.isEmpty(siteDelayId) || !siteDelayId.contains(temp.getId())) {
							WarningMessage warningMessage = new WarningMessage();
							warningMessage.setWarningMsg("Tips: Update site status information in time!");
							warningMessage.setWarningDataId(temp.getId());
							warningMessage.setUniquenessField(temp.getUniquenessField());
							warningMessage.setWarningType(SITE_DELAY);
							warningMessage.setProjectName(temp.getProjectName());
							warningMessage.setProjectCode(temp.getProjectCode());
							warningMessage.setViewId(viewConfProperties.getPoItem().getWarnViewId());
							warningMessage.setViewGroupId(viewConfProperties.getPoItem().getWarnViewGroupId());
							warningMessage.setMenuId(viewConfProperties.getPoItem().getMenuId());
							warningMessage.setId(IdUtil.getSnowflakeNextId());
							warningMessages.add(warningMessage);
							siteIdList.add(temp.getId());
						}
					}
					if (CollUtil.isNotEmpty(siteIdList)) {
						warningMapper.updatePoWarning(SITE_DELAY, siteIdList);
					}
				}
				hasNext = tempList.size() == pageSize;
				i += pageSize;
			}
		};
		siteDelay.accept(0, Boolean.TRUE);
		// po金额错误
		BiConsumer<Integer, Boolean> amountError = (i, hasNext) -> {
			while (hasNext) {
				List<WarnTemp> poTempList = warningMapper.selectPoAmountWarnTemp(i, pageSize, projectCode,
						amountErrorWarning);
				if (CollUtil.isNotEmpty(poTempList)) {
					List<Long> poIdList = new ArrayList<>();
					for (WarnTemp temp : poTempList) {
						if (CollUtil.isEmpty(amountDelayId) || !amountDelayId.contains(temp.getId())) {
							WarningMessage warningMessage = new WarningMessage();
							warningMessage.setWarningMsg("Tips: Ensure that the site matches the PO entry and price");
							warningMessage.setWarningDataId(temp.getId());
							warningMessage.setUniquenessField(temp.getUniquenessField());
							warningMessage.setWarningType(AMOUNT_ERROR);
							warningMessage.setProjectCode(temp.getProjectCode());
							warningMessage.setProjectName(temp.getProjectName());
							warningMessage.setViewId(viewConfProperties.getPoItem().getWarnViewId());
							warningMessage.setViewGroupId(viewConfProperties.getPoItem().getWarnViewGroupId());
							warningMessage.setMenuId(viewConfProperties.getPoItem().getMenuId());
							warningMessage.setId(IdUtil.getSnowflakeNextId());
							warningMessages.add(warningMessage);
							poIdList.add(temp.getId());
						}
						if (CollUtil.isNotEmpty(poIdList)) {
							warningMapper.updatePoWarning(AMOUNT_ERROR, poIdList);
						}
					}
				}
				hasNext = poTempList.size() == pageSize;
				i += pageSize;
			}
		};
		amountError.accept(0, Boolean.TRUE);
		// 保存警告信息数据
		saveWarningMessage(warningMessages);

		transactionTemplate.execute(status -> {
			// 站点信息delay告警
			List<Long> removeSiteIdList = warningMapper.selectRemovePoId(projectCode, siteDelayWarning);
			List<WarningMessage> remove = new ArrayList<>();
			if (CollUtil.isNotEmpty(removeSiteIdList)) {
				warningMapper.updatePoWarning("Site_Delay_Normal", removeSiteIdList);
				for (Long aLong : removeSiteIdList) {
					WarningMessage warningMessage = new WarningMessage();
					warningMessage.setWarningDataId(aLong);
					warningMessage.setWarningType(SITE_DELAY);
					remove.add(warningMessage);
				}
			}
			// 移除警告
			removeWarningMessageByWarningType(remove);
			return Boolean.TRUE;
		});

		transactionTemplate.execute(status -> {
			List<WarningMessage> remove = new ArrayList<>();
			// po金额错误
			List<Long> removePoIdList = warningMapper.selectRemovePoAmountWarnTemp(projectCode, amountErrorWarning);
			if (CollUtil.isNotEmpty(removePoIdList)) {
				warningMapper.updatePoWarning("Amount_Error_Normal", removePoIdList);
				for (Long aLong : removePoIdList) {
					WarningMessage warningMessage = new WarningMessage();
					warningMessage.setWarningDataId(aLong);
					warningMessage.setWarningType(AMOUNT_ERROR);
					remove.add(warningMessage);
				}
			}
			// 移除警告
			removeWarningMessageByWarningType(remove);
			return Boolean.TRUE;
		});

	}

	public void updateSiteDeliveryStatus(String projectCode) {
		Integer pageSize = viewConfProperties.getPageSize();
		String warning = warningMapper.getThresholdByCode(projectCode, "Work_Delay_Warning");
		String acceptanceWarning = warningMapper.getThresholdByCode(projectCode, "Acceptance_Warning");
		String subconPoWarning = warningMapper.getThresholdByCode(projectCode, "Subcon_PO_Warning");
		List<Long> startDelayId = warningMapper.getWarnDataIdByCode(projectCode, START_WORKING_DELAY);
		List<Long> acceptanceDelayId = warningMapper.getWarnDataIdByCode(projectCode, ACCEPTANCE_DELAY);
		List<Long> subconPoDelayId = warningMapper.getWarnDataIdByCode(projectCode, SUBCON_PO_DELAY);
		List<WarningMessage> warningMessages = new ArrayList<>();
		BiConsumer<Integer, Boolean> start = (i, hasNext) -> {
			while (hasNext) {
				// A-4 开工delay告警
				List<WarnTemp> startTempList = warningMapper.selectStartWorkWarnTemp(i, pageSize, projectCode, warning);
				if (CollUtil.isNotEmpty(startTempList)) {
					List<Long> startIdList = new ArrayList<>();
					for (WarnTemp temp : startTempList) {
						if (CollUtil.isEmpty(startDelayId) || !startDelayId.contains(temp.getId())) {
							WarningMessage warningMessage = new WarningMessage();
							warningMessage.setWarningType(START_WORKING_DELAY);
							warningMessage
								.setWarningMsg("Tips: Sites that have been assigned need to start work in time!");
							warningMessage.setWarningDataId(temp.getId());
							warningMessage.setProjectName(temp.getProjectName());
							warningMessage.setUniquenessField(temp.getUniquenessField());
							warningMessage.setProjectCode(temp.getProjectCode());
							warningMessage.setMenuId(viewConfProperties.getSiteDelivery().getMenuId());
							warningMessage.setViewId(viewConfProperties.getSiteDelivery().getWarnViewId());
							warningMessage.setViewGroupId(viewConfProperties.getSiteDelivery().getWarnViewGroupId());
							warningMessage.setId(IdUtil.getSnowflakeNextId());
							warningMessages.add(warningMessage);
							startIdList.add(temp.getId());
						}
					}
					if (CollUtil.isNotEmpty(startIdList)) {
						warningMapper.updateSiteDeliveryWarning(START_WORKING_DELAY, startIdList);
					}
				}
				hasNext = startTempList.size() == pageSize;
				i += pageSize;
			}
		};
		start.accept(0, Boolean.TRUE);
		BiConsumer<Integer, Boolean> check = (i, hasNext) -> {
			while (hasNext) {
				// A-5 验收delay告警
				List<WarnTemp> checkTempList = warningMapper.selectCheckWorkWarnTemp(i, pageSize, projectCode,
						acceptanceWarning);
				if (CollUtil.isNotEmpty(checkTempList)) {
					List<Long> checkIdList = new ArrayList<>();
					for (WarnTemp temp : checkTempList) {
						if (CollUtil.isEmpty(acceptanceDelayId) || !acceptanceDelayId.contains(temp.getId())) {
							WarningMessage warningMessage = new WarningMessage();
							warningMessage.setWarningType(ACCEPTANCE_DELAY);
							warningMessage.setWarningMsg(
									"Tips: Sites that have already started need to be completed and accepted in time.");
							warningMessage.setWarningDataId(temp.getId());
							warningMessage.setUniquenessField(temp.getUniquenessField());
							warningMessage.setProjectName(temp.getProjectName());
							warningMessage.setMenuId(viewConfProperties.getSiteDelivery().getMenuId());
							warningMessage.setProjectCode(temp.getProjectCode());
							warningMessage.setViewId(viewConfProperties.getSiteDelivery().getWarnViewId());
							warningMessage.setViewGroupId(viewConfProperties.getSiteDelivery().getWarnViewGroupId());
							warningMessage.setId(IdUtil.getSnowflakeNextId());
							warningMessages.add(warningMessage);
							checkIdList.add(temp.getId());
						}
					}
					if (CollUtil.isNotEmpty(checkIdList)) {
						warningMapper.updateSiteDeliveryWarning(ACCEPTANCE_DELAY, checkIdList);
					}
				}
				hasNext = checkTempList.size() == pageSize;
				i += pageSize;
			}
		};
		check.accept(0, Boolean.TRUE);
		BiConsumer<Integer, Boolean> subcon = (i, hasNext) -> {
			while (hasNext) {
				// A-6 分包PO delay告警
				List<WarnTemp> subconTempList = warningMapper.selectSubconPoWarnTemp(i, pageSize, projectCode,
						subconPoWarning);
				if (CollUtil.isNotEmpty(subconTempList)) {
					List<Long> subconIdList = new ArrayList<>();
					for (WarnTemp temp : subconTempList) {
						if (CollUtil.isEmpty(subconPoDelayId) || !subconPoDelayId.contains(temp.getId())) {
							WarningMessage warningMessage = new WarningMessage();
							warningMessage.setWarningType(SUBCON_PO_DELAY);
							warningMessage.setWarningMsg(
									"Tips:All sites assigned to subcontractors need to send PO to subcontractors in a timely manner!");
							warningMessage.setWarningDataId(temp.getId());
							warningMessage.setUniquenessField(temp.getUniquenessField());
							warningMessage.setProjectName(temp.getProjectName());
							warningMessage.setProjectCode(temp.getProjectCode());
							warningMessage.setMenuId(viewConfProperties.getSiteDelivery().getMenuId());
							warningMessage.setViewId(viewConfProperties.getSiteDelivery().getWarnViewId());
							warningMessage.setId(IdUtil.getSnowflakeNextId());
							warningMessage.setViewGroupId(viewConfProperties.getSiteDelivery().getWarnViewGroupId());
							warningMessages.add(warningMessage);
							subconIdList.add(temp.getId());
						}
					}
					if (CollUtil.isNotEmpty(subconIdList)) {
						warningMapper.updateSiteDeliveryWarning(SUBCON_PO_DELAY, subconIdList);
					}
				}
				hasNext = subconTempList.size() == pageSize;
				i += pageSize;
			}
		};
		subcon.accept(0, Boolean.TRUE);
		// 保存警告信息
		saveWarningMessage(warningMessages);

		transactionTemplate.execute(status -> {
			// 移除警告信息及更新
			List<WarningMessage> removeWarningMsg = new ArrayList<>();
			// A-4 开工delay告警
			List<Long> removeStartIdList = warningMapper.selectRemoveStartWorkWarnTemp(projectCode, warning);
			if (CollUtil.isNotEmpty(removeStartIdList)) {
				for (Long aLong : removeStartIdList) {
					WarningMessage warningMessage = new WarningMessage();
					warningMessage.setWarningType(START_WORKING_DELAY);
					warningMessage.setWarningDataId(aLong);
					removeWarningMsg.add(warningMessage);
				}
				warningMapper.updateSiteDeliveryWarning("Start_Working_Delay_Normal", removeStartIdList);
			}
			removeWarningMessageByWarningType(removeWarningMsg);
			return Boolean.TRUE;
		});

		transactionTemplate.execute(status -> {
			List<WarningMessage> removeWarningMsg = new ArrayList<>();
			// A-5 验收delay告警
			List<Long> removeCheckIdList = warningMapper.selectRemoveCheckWorkWarnTemp(projectCode, acceptanceWarning);
			if (CollUtil.isNotEmpty(removeCheckIdList)) {
				for (Long aLong : removeCheckIdList) {
					WarningMessage warningMessage = new WarningMessage();
					warningMessage.setWarningType(ACCEPTANCE_DELAY);
					warningMessage.setWarningDataId(aLong);
					removeWarningMsg.add(warningMessage);
				}
				warningMapper.updateSiteDeliveryWarning("Acceptance_Delay_Normal", removeCheckIdList);
			}
			removeWarningMessageByWarningType(removeWarningMsg);
			return Boolean.TRUE;
		});

		transactionTemplate.execute(status -> {
			List<WarningMessage> removeWarningMsg = new ArrayList<>();
			// A-6 分包PO delay告警
			List<Long> removeSubconIdList = warningMapper.selectRemoveSubconPoWarnTemp(projectCode, subconPoWarning);
			System.out.println("===分包PO delay告警 移除" + removeSubconIdList.size());
			if (CollUtil.isNotEmpty(removeSubconIdList)) {
				for (Long aLong : removeSubconIdList) {
					WarningMessage warningMessage = new WarningMessage();
					warningMessage.setWarningType(SUBCON_PO_DELAY);
					warningMessage.setWarningDataId(aLong);
					removeWarningMsg.add(warningMessage);
				}
				warningMapper.updateSiteDeliveryWarning("Subcon_PO_Delay_Normal", removeSubconIdList);
			}
			removeWarningMessageByWarningType(removeWarningMsg);
			return Boolean.TRUE;
		});
	}

	public void updateYPTTSettlementStatus(String projectCode) {
		Integer pageSize = viewConfProperties.getPageSize();
		List<WarningMessage> warningMessages = new ArrayList<>();
		String invoiceDelayWarning = warningMapper.getThresholdByCode(projectCode, "InvoiceDelayWarning");
		List<Long> warnDataIdByCode = warningMapper.getWarnDataIdByCode(projectCode, INVOICE_DELAY);
		BiConsumer<Integer, Boolean> invoiceDelay = (i, hasNext) -> {
			while (hasNext) {
				// 获取更新id
				List<WarnTemp> tempList = warningMapper.selectYpttSettlementWarnTemp(i, pageSize, projectCode,
						invoiceDelayWarning);
				if (CollUtil.isNotEmpty(tempList)) {
					List<Long> idList = new ArrayList<>();
					for (WarnTemp temp : tempList) {
						if (CollUtil.isEmpty(warnDataIdByCode) || !warnDataIdByCode.contains(temp.getId())) {
							WarningMessage warningMessage = new WarningMessage();
							warningMessage.setWarningMsg(
									"Tips: All settable PO needs to be settled in time and invoiced to the customer!");
							warningMessage.setWarningDataId(temp.getId());
							warningMessage.setWarningType(INVOICE_DELAY);
							warningMessage.setUniquenessField(temp.getUniquenessField());
							warningMessage.setProjectName(temp.getProjectName());
							warningMessage.setProjectCode(temp.getProjectCode());
							warningMessage.setViewId(viewConfProperties.getYpttSettlement().getWarnViewId());
							warningMessage.setViewGroupId(viewConfProperties.getYpttSettlement().getWarnViewGroupId());
							warningMessage.setMenuId(viewConfProperties.getYpttSettlement().getMenuId());
							warningMessage.setId(IdUtil.getSnowflakeNextId());
							warningMessages.add(warningMessage);
							idList.add(temp.getId());
						}
					}
					if (CollUtil.isNotEmpty(idList)) {
						warningMapper.updateYpttSettlementWarning(INVOICE_DELAY, idList);
					}
				}
				hasNext = tempList.size() == pageSize;
				i += pageSize;
			}
		};
		invoiceDelay.accept(0, Boolean.TRUE);
		// 保存警告信息数据
		saveWarningMessage(warningMessages);

		transactionTemplate.execute(status -> {
			// 获取移除id
			List<Long> removeId = warningMapper.selectRemoveYpttSettlementWarnTemp(projectCode, invoiceDelayWarning);
			List<WarningMessage> remove = new ArrayList<>();
			if (CollUtil.isNotEmpty(removeId)) {
				warningMapper.updateYpttSettlementWarning("Invoice_Delay_Normal", removeId);
				for (Long aLong : removeId) {
					WarningMessage warningMessage = new WarningMessage();
					warningMessage.setWarningDataId(aLong);
					warningMessage.setWarningType(INVOICE_DELAY);
					remove.add(warningMessage);
				}
			}
			// 移除警告状态及警告信息
			removeWarningMessageByWarningType(remove);
			return Boolean.TRUE;
		});

	}

	public void updateSubsconStatus(String projectCode) {
		Integer pageSize = viewConfProperties.getPageSize();
		List<WarningMessage> warningMessages = new ArrayList<>();
		String subconPaymentWarning = warningMapper.getThresholdByCode(projectCode, "SubconPaymentWarning");
		List<Long> warnDataIdByCode = warningMapper.getWarnDataIdByCode(projectCode, SUBCON_PAYMENT_DELAY);
		BiConsumer<Integer, Boolean> subconPaymentDelay = (i, hasNext) -> {
			while (hasNext) {
				// 获取id集合
				List<WarnTemp> tempList = warningMapper.selectSubconPayWarnTemp(i, pageSize, projectCode,
						subconPaymentWarning);
				if (CollUtil.isNotEmpty(tempList)) {
					List<Long> idList = new ArrayList<>();
					for (WarnTemp temp : tempList) {
						if (CollUtil.isEmpty(warnDataIdByCode) || !warnDataIdByCode.contains(temp.getId())) {
							WarningMessage warningMessage = new WarningMessage();
							warningMessage.setWarningMsg(
									"Tips: The PO that can be settled by all subcontractors needs to push the subcontractors to settle in time!");
							warningMessage.setWarningDataId(temp.getId());
							warningMessage.setWarningType(SUBCON_PAYMENT_DELAY);
							warningMessage.setProjectName(temp.getProjectName());
							warningMessage.setProjectCode(temp.getProjectCode());
							warningMessage.setUniquenessField(temp.getUniquenessField());
							warningMessage.setViewId(viewConfProperties.getSubconPayment().getWarnViewId());
							warningMessage.setViewGroupId(viewConfProperties.getSubconPayment().getWarnViewGroupId());
							warningMessage.setMenuId(viewConfProperties.getSubconPayment().getMenuId());
							warningMessage.setId(IdUtil.getSnowflakeNextId());
							warningMessages.add(warningMessage);
							idList.add(temp.getId());
						}
					}
					if (CollUtil.isNotEmpty(idList)) {
						warningMapper.updateSubconPayWarning(SUBCON_PAYMENT_DELAY, idList);
					}
				}
				hasNext = tempList.size() == pageSize;
				i += pageSize;
			}
		};
		subconPaymentDelay.accept(0, Boolean.TRUE);
		// 保存警告信息数据
		saveWarningMessage(warningMessages);

		transactionTemplate.execute(status -> {
			// 获取移除id集合
			List<Long> removeIdList = warningMapper.selectRemoveSubconPayWarnTemp(projectCode, subconPaymentWarning);
			List<WarningMessage> remove = new ArrayList<>();
			if (CollUtil.isNotEmpty(removeIdList)) {
				warningMapper.updateSubconPayWarning("Subcon_Payment_Delay_Normal", removeIdList);
				for (Long aLong : removeIdList) {
					WarningMessage warningMessage = new WarningMessage();
					warningMessage.setWarningDataId(aLong);
					warningMessage.setWarningType(SUBCON_PAYMENT_DELAY);
					remove.add(warningMessage);
				}
			}
			// 移除警告信息
			removeWarningMessageByWarningType(remove);
			return Boolean.TRUE;
		});

	}

	public void saveWarningMessage(List<WarningMessage> warningMessageList) {
		Integer operationSize = viewConfProperties.getOperationSize();
		if (CollUtil.isNotEmpty(warningMessageList)) {
			int size = warningMessageList.size();
			for (int i = 0; i < size; i += operationSize) {
				List<WarningMessage> warningMessages = warningMessageList.subList(i, Math.min(size, i + operationSize));
				warningMapper.saveMsg(warningMessages);
			}
		}
	}

	public void removeWarningMessageByWarningType(List<WarningMessage> deleteWarningMessageList) {
		Integer operationSize = viewConfProperties.getOperationSize();
		if (CollUtil.isNotEmpty(deleteWarningMessageList)) {
			// 获取警告类型
			String warningType = deleteWarningMessageList.get(0).getWarningType();
			int size = deleteWarningMessageList.size();
			for (int i = 0; i < size; i += operationSize) {
				List<WarningMessage> warningMessages = deleteWarningMessageList.subList(i,
						Math.min(size, i + operationSize));
//				System.out.println("----------------移除的告警"+warningMessages);
				warningMapper.deleteMsgByType(warningMessages, warningType);
			}
		}
	}

	private String listToStr(List<Long> ids) {
		StringJoiner stringJoiner = new StringJoiner(StrUtil.COMMA);
		for (Long id : ids) {
			stringJoiner.add(String.valueOf(id));
		}
		return stringJoiner.toString();
	}

	/**
	 * 分页查询警告信息
	 * @param size 大小
	 * @param cur 当前页
	 * @param projectCode 项目id
	 * @param projectName 项目名称
	 * @param warnType 警告类型
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return IPage
	 */
	public IPage<WarningMessage> page(Integer size, Integer cur, String projectCode, String projectName,
			String uniquenessField, List<String> warnType, Date startTime, Date endTime) {
		int current = Objects.isNull(cur) || cur < 0 ? 1 : cur;
		int sizePage = Objects.isNull(size) || size < 0 ? 10 : size;
		List<WarningPageDTO> currentRole = getCurrentRole();
		if (CollUtil.isEmpty(currentRole)) {
			return new Page<>();
		}
		return warningMapper.warnPage(Page.of(current, sizePage), currentRole, projectCode, projectName,
				uniquenessField, warnType, startTime, endTime);

	}

	public List<WarningPageDTO> getCurrentRole() {
		Long userId = SecurityUtils.getUser().getId();
		List<ProjectRole> strings = warningMapper.getProjectRole(userId);
		List<String> roleList = new ArrayList<>();
		List<WarningPageDTO> dtoList = new ArrayList<>();
		if (CollUtil.isEmpty(strings)) {
			return Collections.emptyList();
		}
		for (ProjectRole string : strings) {
			Assert.isTrue(StrUtil.isNotBlank(string.getRole()));
			WarningPageDTO warningPageDTO = new WarningPageDTO();
			warningPageDTO.setProjectId(string.getProjectName());
			for (Object o : new JSONArray(string.getRole())) {
				roleList.add(o.toString());
			}
			Set<String> warnTypeList = new HashSet<>();
			for (String s : warningMapper.warnRoleList(roleList)) {
				for (Object o : new JSONArray(s)) {
					warnTypeList.add(o.toString());
				}
			}
			warningPageDTO.setWarnTypeList(warnTypeList);
			dtoList.add(warningPageDTO);
		}
		return dtoList;
	}

	public List<WarningStatisticsVO> statistics(String projectCode, String projectName, String uniquenessField,
			List<String> warnType, Date startTime, Date endTime) {
		List<WarningPageDTO> currentRole = getCurrentRole();
		if (CollUtil.isEmpty(currentRole)) {
			return Collections.emptyList();
		}
		return warningMapper.warningStatistics(currentRole, projectCode, projectName, uniquenessField, warnType,
				startTime, endTime);
	}

	public List<Map<String, Object>> listMap(String projectCode, String projectName, String uniquenessField,
			List<String> warnType, Date startTime, Date endTime) {
		List<WarningPageDTO> currentRole = getCurrentRole();
		if (CollUtil.isEmpty(currentRole)) {
			return Collections.emptyList();
		}
		return warningMapper.listMap(currentRole, projectCode, projectName, uniquenessField, warnType, startTime,
				endTime);
	}

}
