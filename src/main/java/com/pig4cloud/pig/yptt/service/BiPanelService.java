package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.admin.api.entity.SysRole;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.service.PigUser;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.yptt.bizcode.YpttBizCode;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.BatchExport;
import com.pig4cloud.pig.yptt.entity.ProjectRole;
import com.pig4cloud.pig.yptt.entity.SiteItem;
import com.pig4cloud.pig.yptt.entity.StatisticsUserOperate;
import com.pig4cloud.pig.yptt.entity.dto.ProjectStandingBookDTO;
import com.pig4cloud.pig.yptt.entity.vo.*;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.mapper.RoleMapper;
import com.pig4cloud.pig.yptt.mapper.WarningMapper;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/09/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BiPanelService {

    private final BasicMapper basicMapper;

    private final RemoteAppService remoteAppService;

    private final WarningMapper warningMapper;

    private final RoleMapper roleMapper;

    private final ViewConfProperties viewConfProperties;

    private static final Long SYSTEM_ADMIN_ID = 1694550407313264642L;

    /**
     * BI权限验证
     *
     * @param type      类型
     * @param projectId 项目id
     * @return Boolean
     */
    public Boolean authorityCheck(String type, Long projectId) {
        PigUser pigUser = SecurityUtils.getUser();
        // 系统管理员放行
        if (pigUser.getRoleList()
                .stream()
                .map(SysRole::getRoleId)
                .collect(Collectors.toList())
                .contains(SYSTEM_ADMIN_ID)) {
            return Boolean.TRUE;
        }
        Long userId = pigUser.getId();
        List<ProjectRole> projectRoles = warningMapper.getProjectRole(userId);
        List<String> roleList = new ArrayList<>();
        for (ProjectRole projectRole : projectRoles) {
            if (Objects.equals(projectId.toString(), projectRole.getProjectName())) {
                for (Object o : new JSONArray(projectRole.getRole())) {
                    roleList.add(o.toString());
                }
                List<String> biTypeList = roleMapper.getBiRole(roleList);
                for (String s : biTypeList) {
                    for (Object o : new JSONArray(s)) {
                        if (Objects.equals(type, o)) {
                            return Boolean.TRUE;
                        }
                    }
                }
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 收支统计
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param projectId    项目id
     * @param type         类型
     * @param projectCycle 项目周期,0:项目完整周期时间，1:自选周期时间
     * @return List<IncomeAndExpenditureVO>
     */
    public List<IncomeAndExpenditureVO> incomeExpenditureStatus(Date startTime, Date endTime, Long projectId,
                                                                String type, Integer projectCycle) {
        System.out.println("--------------startTime1" + startTime);
        System.out.println("--------------endTime1" + endTime);
        List<IncomeAndExpenditureVO> res = new ArrayList<>();
        if (Objects.equals(1, projectCycle)) {
//            if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
//                throw new BizException(YpttBizCode.DATE_IS_NULL_ERROR);
//            }
            // 获取BI收支统计数据
            int sYear = DateUtil.year(startTime);
            int sMonth = DateUtil.month(startTime) + 1;
            if (Objects.equals(type, GlobalConstants.BiPanel.YEAR)) {
                LocalDate sDate = LocalDate.of(sYear, 1, 1);
                startTime = Date.from(sDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            } else if (Objects.equals(type, GlobalConstants.BiPanel.MONTH)) {
                LocalDate endDate = LocalDate.of(sYear, sMonth, 1);
                startTime = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            int eYear = DateUtil.year(endTime);
            int eMonth = DateUtil.month(endTime) + 1;
            if (Objects.equals(type, GlobalConstants.BiPanel.YEAR)) {
                LocalDate endDate = LocalDate.of(eYear, 12, getDaysInMonth(eYear, 12 - 1));
                endTime = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            } else if (Objects.equals(type, GlobalConstants.BiPanel.MONTH)) {
                LocalDate endDate = LocalDate.of(eYear, eMonth, getDaysInMonth(eYear, eMonth - 1));
                endTime = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            }
        } else if (Objects.equals(0, projectCycle)) {
            startTime = null;
            endTime = null;
        } else {
            throw new BizException(YpttBizCode.PROJECT_CYCLE_TYPE_ERROR);
        }
        System.out.println("--------------startTime2" + startTime);
        System.out.println("--------------endTime2" + endTime);
        List<Map<String, Object>> maps = basicMapper.getIncomeExpenditure(projectId, startTime, endTime);
        List<Map<String, Object>> mapList = deduplicateByField(maps, "uniqueness_field");
        if (CollUtil.isNotEmpty(mapList)) {
            if (Objects.isNull(startTime) && Objects.equals(0, projectCycle)) {
                CycleDate cycleTime = getProjectCycleTime(mapList);
                startTime = cycleTime.getStart();
                endTime = cycleTime.getEnd();
            }
            if (Objects.equals(type, GlobalConstants.BiPanel.YEAR)) {
                int start = DateUtil.year(startTime);
                int end = DateUtil.year(endTime);
                LocalDate startDate = LocalDate.of(start, 1, 1);
                LocalDate endDate = LocalDate.of(end, 12, 31);
                LocalDate currentDate = startDate;
                while (currentDate.isBefore(endDate) || currentDate.equals(endDate)) {
                    Date s = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    Date e = Date.from(currentDate.plusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                    IncomeAndExpenditureVO incomeAndExpenditureVO = getDateLineData(mapList, s, e);
                    incomeAndExpenditureVO.setDateLine(String.valueOf(currentDate.getYear()));
                    res.add(incomeAndExpenditureVO);
                    currentDate = currentDate.plusYears(1);
                }
            } else if (Objects.equals(type, GlobalConstants.BiPanel.MONTH)) {
                int startMonth = DateUtil.month(startTime) + 1;
                int endMonth = DateUtil.month(endTime) + 1;
                int startYear = DateUtil.year(startTime);
                int endYear = DateUtil.year(endTime);
                LocalDate startDate = LocalDate.of(startYear, startMonth, 1);
                LocalDate endDate = LocalDate.of(endYear, endMonth, getDaysInMonth(endYear, endMonth - 1));
                LocalDate currentDate = startDate;
                while (currentDate.isBefore(endDate) || currentDate.equals(endDate)) {
                    Date s = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    Date e = Date.from(currentDate.plusMonths(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                    IncomeAndExpenditureVO incomeAndExpenditureVO = getDateLineData(mapList, s, e);
                    incomeAndExpenditureVO
                            .setDateLine(currentDate.getYear() + StrUtil.DASHED + currentDate.getMonth().getValue());
                    res.add(incomeAndExpenditureVO);
                    currentDate = currentDate.plusMonths(1);
                }
            } else if (Objects.equals(type, GlobalConstants.BiPanel.DAY)) {
                int startYear = DateUtil.year(startTime);
                int startMonth = DateUtil.month(startTime) + 1;
                int startDay = DateUtil.dayOfMonth(startTime);
                int endYear = DateUtil.year(endTime);
                int endMonth = DateUtil.month(endTime) + 1;
                int endDay = DateUtil.dayOfMonth(endTime);
                LocalDate startDate = LocalDate.of(startYear, startMonth, startDay);
                LocalDate endDate = LocalDate.of(endYear, endMonth, endDay);
                LocalDate currentDate = startDate;
                while (currentDate.isBefore(endDate) || currentDate.equals(endDate)) {
                    Date s = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    Date e = Date.from(currentDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                    IncomeAndExpenditureVO incomeAndExpenditureVO = getDateLineData(mapList, s, e);
                    incomeAndExpenditureVO.setDateLine(currentDate.getYear() + StrUtil.DASHED
                            + currentDate.getMonth().getValue() + StrUtil.DASHED + currentDate.getDayOfMonth());
                    res.add(incomeAndExpenditureVO);
                    currentDate = currentDate.plusDays(1);
                }
            }

        }
        return res;
    }
    /**
     * 去重
     * @param mapList 原始数据列表
     * @param fieldName 去重依据的字段名
     * @return 去重后的列表
     */
    public static List<Map<String, Object>> deduplicateByField(
            List<Map<String, Object>> mapList,
            String fieldName) {

        // 使用LinkedHashMap保持插入顺序
        Map<Object, Map<String, Object>> uniqueMap = new LinkedHashMap<>();

        for (Map<String, Object> map : mapList) {
            Object key = map.get(fieldName);

            // 处理null值情况
            if (key == null) {
                // 策略1: 跳过null值 - 不添加到结果中
                // continue;

                // 策略2: 保留第一个null值
                if (!uniqueMap.containsKey(null)) {
                    uniqueMap.put(null, map);
                }
                continue;
            }

            // 仅当key不存在时才放入，保留首次出现的数据
            if (!uniqueMap.containsKey(key)) {
                uniqueMap.put(key, map);
            }
        }

        return new ArrayList<>(uniqueMap.values());
    }

    @SneakyThrows
    private CycleDate getProjectCycleTime(List<Map<String, Object>> mapList) {
        CycleDate cycleDate = new CycleDate();
        Date start = null;
        Date end = null;
        for (Map<String, Object> map : mapList) {
            for (String key : Arrays.asList("Item_Value_date", "PO_Value_date", "Subcon_PO_amount_d",
                    "Ready_settlement_d", "Productivity_AmountD", "Subcon_settlement_d", "Subcon_payment_date",
                    "Invoice_amount_date", "Ready_settlement_d2", "Ready_settlement_d3", "Ready_settlement_d4",
                    "Productivity_AmountD2", "ProductivityAmountD3", "ProductivityAmountD4", "Subcon_settlement_d2",
                    "Subcon_settlement_d3", "Subcon_settlement_d4", "Subcon_payment_date2", "Subcon_payment_date3",
                    "Subcon_payment_date4", "Invoice_amount_date2", "Invoice_amount_date3", "Invoice_amount_date4",
                    "Pre_Settlement_date")) {
                Object mapDate = map.get(key);
                if (Objects.nonNull(mapDate)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yy-MM-dd");
                    Date date = sdf.parse(mapDate.toString());
                    if (Objects.isNull(start)) {
                        start = date;
                        end = date;
                    }
                    start = date.before(start) ? date : start;
                    end = date.after(end) ? date : end;
                }
            }
        }
        cycleDate.setStart(Objects.nonNull(start) ? start : new Date());
        cycleDate.setEnd(Objects.nonNull(end) ? end : new Date());
        return cycleDate;
    }

    @SneakyThrows
    private IncomeAndExpenditureVO getDateLineData(List<Map<String, Object>> records, Date start, Date end) {
        IncomeAndExpenditureVO incomeAndExpenditureVO = new IncomeAndExpenditureVO();
        BigDecimal siteItemValueTotal = new BigDecimal(0);
        BigDecimal poItemValueTotal = new BigDecimal(0);
        BigDecimal subconTotalAmountPoItem = new BigDecimal(0);
        BigDecimal totalSettableAmount = new BigDecimal(0);
        BigDecimal totalOutputValue = new BigDecimal(0);
        BigDecimal subconTotalAmountSettled = new BigDecimal(0);
        BigDecimal subconTotalAmountPaid = new BigDecimal(0);
        BigDecimal totalInvoiceAmount = new BigDecimal(0);
        BigDecimal CNAmount = new BigDecimal(0);
        for (Map<String, Object> record : records) {
            Object projectName = record.get("YPTT_Project_name");
            if (Objects.nonNull(projectName)) {
                incomeAndExpenditureVO.setProjectName(projectName.toString());
            }
            // 统计站点价值
            siteItemValueTotal = siteItemValueTotal
                    .add(statisticalAmount("Item_Value", "Item_Value_date", start, end, record));
            // po价值
            poItemValueTotal = poItemValueTotal.add(statisticalAmount("PO_Value", "PO_Value_date", start, end, record));
            // 分包商po价值
            subconTotalAmountPoItem = subconTotalAmountPoItem
                    .add(statisticalAmount("Subcon_PO_amount", "Subcon_PO_amount_d", start, end, record));
            // 可结算金额
            totalSettableAmount = totalSettableAmount
                    .add(statisticalAmount("Ready_settlement", "Ready_settlement_d", start, end, record));
            totalSettableAmount = totalSettableAmount
                    .add(statisticalAmount("Ready_settlement_2", "Ready_settlement_d2", start, end, record));
            totalSettableAmount = totalSettableAmount
                    .add(statisticalAmount("Ready_settlement_3", "Ready_settlement_d3", start, end, record));
            totalSettableAmount = totalSettableAmount
                    .add(statisticalAmount("Ready_settlement_4", "Ready_settlement_d4", start, end, record));
            totalSettableAmount = totalSettableAmount
                    .add(statisticalAmount("Pre_payment_amount", "Pre_Settlement_date", start, end, record));
            // 产值报告金额
            totalOutputValue = totalOutputValue
                    .add(statisticalAmount("Productivity_Amount", "Productivity_AmountD", start, end, record));
            totalOutputValue = totalOutputValue
                    .add(statisticalAmount("Productivity_Amount2", "Productivity_AmountD2", start, end, record));
            totalOutputValue = totalOutputValue
                    .add(statisticalAmount("Productivity_Amount3", "ProductivityAmountD3", start, end, record));
            totalOutputValue = totalOutputValue
                    .add(statisticalAmount("Productivity_Amount4", "ProductivityAmountD4", start, end, record));
            // 分包商结算金额
            subconTotalAmountSettled = subconTotalAmountSettled
                    .add(statisticalAmount("Subcon_settlement", "Subcon_settlement_d", start, end, record));
            subconTotalAmountSettled = subconTotalAmountSettled
                    .add(statisticalAmount("Subcon_settlement_2", "Subcon_settlement_d2", start, end, record));
            subconTotalAmountSettled = subconTotalAmountSettled
                    .add(statisticalAmount("Subcon_settlement_3", "Subcon_settlement_d3", start, end, record));
            subconTotalAmountSettled = subconTotalAmountSettled
                    .add(statisticalAmount("Subcon_settlement_4", "Subcon_settlement_d4", start, end, record));
            // 分包商支付金额
            subconTotalAmountPaid = subconTotalAmountPaid
                    .add(statisticalAmount("Subcon_payment", "Subcon_payment_date", start, end, record));
            subconTotalAmountPaid = subconTotalAmountPaid
                    .add(statisticalAmount("Subcon_payment_2", "Subcon_payment_date2", start, end, record));
            subconTotalAmountPaid = subconTotalAmountPaid
                    .add(statisticalAmount("Subcon_payment_3", "Subcon_payment_date3", start, end, record));
            subconTotalAmountPaid = subconTotalAmountPaid
                    .add(statisticalAmount("Subcon_payment_4", "Subcon_payment_date4", start, end, record));
            // 发票金额
            totalInvoiceAmount = totalInvoiceAmount
                    .add(statisticalAmount("Invoice_amount", "Invoice_amount_date", start, end, record));
            totalInvoiceAmount = totalInvoiceAmount
                    .add(statisticalAmount("Invoice_amount_2", "Invoice_amount_date2", start, end, record));
            totalInvoiceAmount = totalInvoiceAmount
                    .add(statisticalAmount("Invoice_amount_3", "Invoice_amount_date3", start, end, record));
            totalInvoiceAmount = totalInvoiceAmount
                    .add(statisticalAmount("Invoice_amount_4", "Invoice_amount_date4", start, end, record));
            // y9 cn金额

            CNAmount = CNAmount
                    .add(statisticalAmount("CN_amount_1st", "CN_date_1st", start, end, record));
            CNAmount = CNAmount
                    .add(statisticalAmount("CN_amount_2nd", "CN_date_2nd", start, end, record));
            CNAmount = CNAmount
                    .add(statisticalAmount("CN_amount_3rd", "CN_date_3rd", start, end, record));
            CNAmount = CNAmount
                    .add(statisticalAmount("CN_amount_4st", "CN_date_4st", start, end, record));
        }
        incomeAndExpenditureVO.setPoItemValueTotal(poItemValueTotal);
        incomeAndExpenditureVO.setSiteItemValueTotal(siteItemValueTotal);
        incomeAndExpenditureVO.setSubconTotalAmountPaid(subconTotalAmountPaid);
        incomeAndExpenditureVO.setSubconTotalAmountPoItem(subconTotalAmountPoItem);
        incomeAndExpenditureVO.setSubconTotalAmountSettled(subconTotalAmountSettled);
        incomeAndExpenditureVO.setTotalInvoiceAmount(totalInvoiceAmount);
        incomeAndExpenditureVO.setTotalOutputValue(totalOutputValue);
        incomeAndExpenditureVO.setTotalSettableAmount(totalSettableAmount);
        incomeAndExpenditureVO.setCNAmount(CNAmount);
        return incomeAndExpenditureVO;
    }

    @SneakyThrows
    private BigDecimal statisticalAmount(String fieldAmount, String filedDate, Date start, Date end,
                                         Map<String, Object> map) {
        BigDecimal bigDecimal = new BigDecimal("0");
        Object mapDate = map.get(filedDate);
        if (Objects.nonNull(mapDate)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yy-MM-dd");
            Date date = sdf.parse(mapDate.toString());
            if ((date.before(end) && date.after(start)) || date.equals(start)) {
                Object amount = map.get(fieldAmount);
                if (Objects.nonNull(amount) && StrUtil.isNotBlank(amount.toString())) {
                    bigDecimal = bigDecimal.add(new BigDecimal(amount.toString()));
                }
            }
        }
        return bigDecimal;
    }

    public int getDaysInMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month, 1);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 站点关闭率
     *
     * @param appId 应用id
     * @return List<SiteItemStatisticsVO>
     */
    public List<SiteItemStatisticsVO> siteItemStatistics(String appId, String projectId) {
        List<SiteItemStatisticsVO> res = new ArrayList<>();
        PigUser user = SecurityUtils.getUser();
        Assert.notNull(user);
        List<SiteItem> siteItems = new ArrayList<>();
        List<ProjectStandingBookDTO> projectIds = new ArrayList<>();
        List<Long> roleIdList = user.getRoleList().stream().map(SysRole::getRoleId).collect(Collectors.toList());
        // 系统管理员权限
        if (roleIdList.contains(SYSTEM_ADMIN_ID)) {
            siteItems = roleMapper.getAllSiteItems();
            projectIds = roleMapper.getAllProjectsById(projectId);
        } else {
            siteItems = roleMapper.getSiteItem(user.getId());
            projectIds = roleMapper.getProject(user.getId(), projectId);
            // 权限过滤
            projectIds = projectIds.stream()
                    .filter(o -> authorityCheck(GlobalConstants.BiPanel.B1, o.getProjectId()))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(projectIds)) {
            for (ProjectStandingBookDTO project : projectIds) {
                SiteItemStatisticsVO siteItemStatisticsVO = new SiteItemStatisticsVO();
                siteItemStatisticsVO.setProjectName(project.getProjectName());
                // 统计总数
                long count = siteItems.stream()
                        .filter(o -> Objects.equals(o.getProjectId(), project.getProjectId()))
                        .count();
                siteItemStatisticsVO.setTotal(count);
                // 统计关闭
                long close = siteItems.stream()
                        .filter(o -> Objects.equals(o.getProjectId(), project.getProjectId())
                                && Objects.equals("[\"close\"]", o.getStatus()))
                        .count();
                siteItemStatisticsVO.setClose(close);
                // 统计未关闭
                long unClose = siteItems.stream()
                        .filter(o -> Objects.equals(o.getProjectId(), project.getProjectId())
                                && Objects.equals("[\"unclose\"]", o.getStatus()))
                        .count();
                // 统计无效状态
                long invalid = siteItems.stream()
                        .filter(o -> Objects.equals(o.getProjectId(), project.getProjectId())
                                && Objects.equals("[\"invalid\"]", o.getStatus()))
                        .count();
                siteItemStatisticsVO.setUnclose(unClose);

                siteItemStatisticsVO.setInvalid(invalid);
                res.add(siteItemStatisticsVO);
            }
        }
        return res;
    }

    /**
     * 导出
     *
     * @param projectId id
     * @return BatchExport
     */
    public BatchExport batchExport(Long projectId) {
        BatchExport batchExportDTO = new BatchExport();
        List<Map<String, Object>> maps = reportForm(projectId.toString(), null, null, null, null, null, null, null);
        if (CollUtil.isEmpty(maps)) {
            return null;
        }
        List<String> headMap = new ArrayList<>(maps.get(0).keySet());
        List<List<Object>> dataList = new ArrayList<>();
        for (Map<String, Object> map : maps) {
            List<Object> list = new ArrayList<>();
            for (String s : headMap) {
                list.add(map.get(s));
            }
            dataList.add(list);
        }
        batchExportDTO.setSheetName("report-form");
        batchExportDTO.setList(dataList);
        batchExportDTO.setHeadMap(headMap);
        return batchExportDTO;
    }

    /**
     * 获取导出字段
     *
     * @param page 分页
     * @param
     * @return ReportDataFiledVO
     */
    public ReportDataFiledVO getReportFiled(Page<Map<String, Object>> page, String projectId, String dateStrStart, String dateStrEnd, String dateType, String area, String projectIds, String moduleTypes, String nation) {
        ReportDataFiledVO reportDataFiledVO = new ReportDataFiledVO();
        if (StringUtils.isEmpty(dateStrStart) || StringUtils.isEmpty(dateStrEnd)) {
            dateType = "";
        }
        try {
            reportDataFiledVO.setPage(basicMapper.reportFormPage(page, projectId, dateStrStart, dateStrEnd, dateType, area, toProjectIds(projectId), moduleTypes, nation));
        } catch (Exception e) {
            e.printStackTrace();
        }
        String heads = getHeard(moduleTypes);
        reportDataFiledVO.setHeaders(JSONUtil.toList(heads, ReportDataFiledVO.Header.class));
        return reportDataFiledVO;
    }

    private String getHeard(String moduleTypes) {
        String heads = null;
        if (StringUtils.isBlank(moduleTypes)) {
            heads = ResourceUtil.readUtf8Str("json/report-head.json");
        } else {
            com.alibaba.fastjson.JSONArray headsArray = new com.alibaba.fastjson.JSONArray();
            if (!moduleTypes.contains("Y1")) {
                heads = ResourceUtil.readUtf8Str("json/report-head-common.json");
                com.alibaba.fastjson.JSONArray headsArrayT = com.alibaba.fastjson.JSONArray.parseArray(heads);
                headsArray.add(headsArrayT.get(0));
            }
            String[] split = moduleTypes.split(",");
            for (int i = 0; i < split.length; i++) {
                heads = ResourceUtil.readUtf8Str("json/report-head-" + split[i] + ".json");
                com.alibaba.fastjson.JSONArray headsArrayT = com.alibaba.fastjson.JSONArray.parseArray(heads);
                headsArray.add(headsArrayT.get(0));
            }
            heads = headsArray.toJSONString();
        }
        return heads;
    }

    List<String> toProjectIds(String projectIds) {

        if (StringUtils.isEmpty(projectIds)) {
            return null;
        }
        String[] array = projectIds.split(",");
        // 将数组转换为List
        List<String> list = Arrays.asList(array);
        return list;
    }

    Map<String, String> opeDate(String dateStr, String dateType) {
        Map<String, String> dateMap = new HashMap<>();
        if (StringUtils.isEmpty(dateStr) || StringUtils.isEmpty(dateType)) {
            return dateMap;
        }
        dateMap.put(dateType, dateStr);
        return dateMap;
    }

    /**
     * 报表查询
     *
     * @param projectId projectId
     * @return Map<String, Object>
     */
    public List<Map<String, Object>> reportForm(String projectId, String dateStrStart, String dateStrEnd, String dateType, String area, String projectIds, String moduleTypes, String nation) {
        if (StringUtils.isEmpty(dateStrStart) || StringUtils.isEmpty(dateStrEnd)) {
            dateType = "";
        }
        return basicMapper.reportForm(projectId, dateStrStart, dateStrEnd, dateType, area, toProjectIds(projectId), moduleTypes, nation);
    }

    /**
     * 统计金额一段时间内的 产值及决算金额y6、开票y9、PO金额y2
     */
    public R<TotalAmountVO> totalAmount(String projectId, String dateStrStart, String dateStrEnd, String dateType) {
        return R.ok(getData(projectId, dateStrStart, dateStrEnd, dateType));
    }

    /**
     * 异步获取数据
     */
    private TotalAmountVO getData(String projectId, String dateStrStart, String dateStrEnd, String dateType) {
        TotalAmountVO totalAmountVO = new TotalAmountVO();
        // 使用自定义线程池(可根据需要调整大小)
        CompletableFuture<AmountY2VO> futureY2 = CompletableFuture.supplyAsync(() -> {
            if (StringUtils.isBlank(dateType) || "PO_Received_date".equals(dateType)) {
                return basicMapper.totalY2(projectId, dateStrStart, dateStrEnd, dateType);
            }
            return new AmountY2VO();
        });
        CompletableFuture<AmountY6VO> futureY6 = CompletableFuture.supplyAsync(() -> {
            if (StringUtils.isBlank(dateType) || "Productivity_report_date".equals(dateType)
                    || "KPI_Archive_date".equals(dateType)) {
                return basicMapper.totalY6(projectId, dateStrStart, dateStrEnd, dateType);
            }
            return new AmountY6VO();
        });
        CompletableFuture<AmountY9VO> futureY9 = CompletableFuture.supplyAsync(() -> {
            if (StringUtils.isBlank(dateType) || "Invoice_Date".equals(dateType)) {
                return basicMapper.totalY9(projectId, dateStrStart, dateStrEnd, dateType);
            }
            return new AmountY9VO();
        });
        try {
            AmountY2VO amountY2VO = futureY2.get();
            AmountY6VO amountY6VO = futureY6.get();
            AmountY9VO amountY9VO = futureY9.get();
            setAmount(totalAmountVO, amountY2VO, amountY6VO, amountY9VO);

        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return totalAmountVO;
    }

    private void setAmount(TotalAmountVO totalAmountVO, AmountY2VO amountY2VO, AmountY6VO amountY6VO, AmountY9VO amountY9VO) {
        if (amountY2VO != null) {
            totalAmountVO.setTotalPOValue(amountY2VO.getTotalPOValue());
        }

        if (amountY6VO != null) {
            totalAmountVO.setTotalKPIArchiveAmount(amountY6VO.getTotalKPIArchiveAmount());
            totalAmountVO.setTotal1stProductivityReportAmount(amountY6VO.getTotal1stProductivityReportAmount());
            totalAmountVO.setTotal2ndProductivityReportAmount(amountY6VO.getTotal2ndProductivityReportAmount());
            totalAmountVO.setTotal3rdProductivityReportAmount(amountY6VO.getTotal3rdProductivityReportAmount());
            totalAmountVO.setTotal4thProductivityReportAmount(amountY6VO.getTotal4thProductivityReportAmount());
            totalAmountVO.setTotalProductivityAmount(amountY6VO.getTotalProductivityAmount());
        }

        if (amountY9VO != null) {
            totalAmountVO.setTotalInvoiceAmount1st(amountY9VO.getTotalInvoiceAmount1st());
            totalAmountVO.setTotalInvoiceAmount2st(amountY9VO.getTotalInvoiceAmount2st());
            totalAmountVO.setTotalInvoiceAmount3st(amountY9VO.getTotalInvoiceAmount3st());
            totalAmountVO.setTotalInvoiceAmount4st(amountY9VO.getTotalInvoiceAmount4st());
            totalAmountVO.setTotalInvoiceAmount(amountY9VO.getTotalInvoiceAmount());
            totalAmountVO.setTotalCNAmount(amountY9VO.getTotalCNAmount());
        }

    }

    @Data
    static class CycleDate {

        private Date start;

        private Date end;

    }

}
