package com.pig4cloud.pig.yptt.service.standingbook;

import com.pig4cloud.pig.yptt.entity.dto.SiteStandingBookDTO;
import com.pig4cloud.pig.yptt.mapper.SiteStandingBookMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SiteStandingBookUpdater extends StandingBookUpdater<SiteStandingBookDTO> {

	private final SiteStandingBookMapper siteStandingBookMapper;

	@Override
	protected String getName() {
		return "SiteStandingBook";
	}

	@Override
	protected List<SiteStandingBookDTO> generate(int i, int size) {
		return siteStandingBookMapper.generateSiteStandingBookList(i, size);
	}

	@Override
	protected int save(SiteStandingBookDTO dto) {
		return siteStandingBookMapper.update(dto);
	}

}