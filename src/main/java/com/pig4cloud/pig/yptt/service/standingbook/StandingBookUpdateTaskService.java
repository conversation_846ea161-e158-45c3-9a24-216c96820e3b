package com.pig4cloud.pig.yptt.service.standingbook;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.pig4cloud.pig.common.core.constant.enums.CommonBizCode;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.core.util.StrUtil;
import com.pig4cloud.pig.yptt.entity.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.list.UnmodifiableList;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/01/17
 */
@Slf4j
@Service
public class StandingBookUpdateTaskService implements ApplicationContextAware, InitializingBean {

	private ApplicationContext applicationContext;

	private List<StandingBookUpdater<StandingBook>> standingBookUpdaters;

	private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 5, 60, TimeUnit.SECONDS,
			new LinkedBlockingQueue<>(10));

	public synchronized StandingBookUpdateTaskResultDTO update() {
		StandingBookUpdateTaskResultDTO standingBookUpdateTaskResultDTO = new StandingBookUpdateTaskResultDTO();
		for (StandingBookUpdater<StandingBook> standingBookUpdater : standingBookUpdaters) {
			final String name = standingBookUpdater.getName();
			Future<R<List<R<StandingBook>>>> future = asyncGenerate(standingBookUpdater::update, name);
			R<List<R<StandingBook>>> asyncResult = getAsyncResult(future);
			standingBookUpdateTaskResultDTO.put(name, asyncResult);
		}
		return standingBookUpdateTaskResultDTO;
	}

	private Future<R<List<R<StandingBook>>>> asyncGenerate(Supplier<List<R<StandingBook>>> generator,
			String messageName) {
		return threadPoolExecutor.submit(() -> generate(generator, messageName));
	}

	private R<List<R<StandingBook>>> getAsyncResult(Future<R<List<R<StandingBook>>>> future) {
		try {
			return future.get();
		}
		catch (InterruptedException | ExecutionException e) {
			log.error("get generated result failed", e);
			return R.failed(null, "generate failed, cause: " + e.getMessage());
		}
	}

	private R<List<R<StandingBook>>> generate(Supplier<List<R<StandingBook>>> generator, String messageName) {
		long startMills = System.currentTimeMillis();
		List<R<StandingBook>> results = generator.get();
		// 响应信息处理
		// 生成0条，warning
		if (results.isEmpty()) {
			String message = String.format("The number of updated %s is 0", messageName);
			log.warn(message);
			log.debug("[{}] takes {} ms.", messageName, System.currentTimeMillis() - startMills);
			return R.ok(Collections.emptyList(), message);
		}
		// 全部成功
		long successCount = results.stream()
			.filter(r -> Objects.equals(r.getCode(), CommonBizCode.SUCCESS.name()))
			.count();
		if (successCount == results.size()) {
			String message = String.format("The number of updated %s is %d", messageName, results.size());
			log.debug("[{}] takes {} ms. {}", messageName, System.currentTimeMillis() - startMills, message);
			return R.ok(null, message);
		}
		// 部分失败或全部失败
		List<R<StandingBook>> failureList = results.stream()
			.filter(r -> !Objects.equals(r.getCode(), CommonBizCode.SUCCESS.name()))
			.collect(Collectors.toList());
		long total = results.size();
		long failureCount = failureList.size();
		String msg = StrUtil.format("Partial {} updates failed. The total/success/failure number is {}/{}/{}.",
				messageName, total, successCount, failureCount);
		log.error(msg + ", and details: {}", JSON.toJSONString(failureList));
		log.debug("[{}] takes {} ms. {}", messageName, System.currentTimeMillis() - startMills, msg);
		return R.failed(failureList, msg + " The data is a failure list");
	}

	@Override
	public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}

	@SuppressWarnings("unchecked")
	@Override
	public void afterPropertiesSet() {
		List<StandingBookUpdater<StandingBook>> updaters = applicationContext.getBeansOfType(StandingBookUpdater.class)
			.values()
			.stream()
			.map(bean -> (StandingBookUpdater<StandingBook>) bean)
			.collect(Collectors.toList());
		if (CollUtil.isEmpty(updaters)) {
			throw new RuntimeException("Not found StandingBookUpdater components");
		}
		this.standingBookUpdaters = new UnmodifiableList<>(updaters);
	}

}
