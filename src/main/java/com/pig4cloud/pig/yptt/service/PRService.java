package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.service.PigUser;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.dto.operation.OperationPageDTO;
import com.pig4cloud.pig.me.api.dto.operation.QueryDTO;
import com.pig4cloud.pig.yptt.entity.PRDownloadEntity;
import com.pig4cloud.pig.yptt.entity.PRTotalEntity;
import com.pig4cloud.pig.yptt.entity.dto.PRDataDTO;
import com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo;
import com.pig4cloud.pig.yptt.mapper.AdjustExcelMapper;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.utils.MetaDataUtil;
import com.pig4cloud.pig.yptt.utils.PRUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName PRService
 * @Description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PRService {
    private final AdjustExcelMapper adjustExcelMapper;

    public List<Map<String, Object>> exportPRData(PRDataDTO pageDTO, String prNo) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        String projectCode = pageDTO.getProjectCode();
        if (StringUtils.isBlank(projectCode)){
            return null;
        }
        String siteId = pageDTO.getSiteId();
        List<String> listSiteIds = new ArrayList<>();
        if (siteId != null && !"".equals(siteId)) {
            String[] split = siteId.split(",");
            for (String s : split) {
                listSiteIds.add(s);
            }
        }
        List<String> ids = pageDTO.getIds();
        if (ObjectUtils.isNotEmpty(ids)) {
            listSiteIds = null;
        }
        List<Map<String, Object>> baseData = adjustExcelMapper.searchPRBaseData2(ids, pageDTO.getItemCode(),
                pageDTO.getPhase(), pageDTO.getProjectCode(), pageDTO.getRegion(), listSiteIds, pageDTO.getUnId());
        long endTime = System.currentTimeMillis();
        System.out.println("====查询数据消耗时间" +  (endTime - startTime));
        Map<String, Map<String, Object>> assembledReport = baseData.stream()
                .filter(row -> row.get("unId") != null) // 确保关联键存在
                .collect(Collectors.toMap(
                        row -> (String) row.get("unId"), // Map 的 Key
                        Function.identity(),               // Map 的 Value
                        (existing, replacement) -> {
                            System.err.println("警告: 发现重复的 unId Key: " + existing.get("unId") + "，数据可能不一致。");
                            return existing; // 保留第一个遇到的记录
                        }
                ));

        // 提取后续查询所需的关联键列表
        // uniqueFields: uf.uniqueness_field 列表，假设大部分模块通过此字段关联
//        List<String> uniqueFields = new ArrayList<>(assembledReport.keySet());
        List<String> ufIds = extractUfIds(baseData);
//        pageMap = adjustExcelMapper.searchPR(pageMap, projectCode, region, siteId, itemCode, phase, unId);
        List<Map<String, Object>> otherInfo = adjustExcelMapper.selectOtherInfoByUnIds(ufIds);
        mergeData(assembledReport, otherInfo, "unId");
        List<Map<String, Object>> maps =new ArrayList<>();
        for (String k : assembledReport.keySet()) {
            maps.add(assembledReport.get(k));
        }
        long endTimeother = System.currentTimeMillis();
        System.out.println("====处理数据其他消耗时间" +  (endTimeother - startTime));
        //查询部门信息
        List<Map<String, Object>> deptInfos = adjustExcelMapper.selectDeptInfo(SecurityUtils.getUserTenantId());
        Map<String, Object> deptInfo = new HashMap<>();
        for (Map<String, Object> di : deptInfos) {
            deptInfo.put(di.get("deptId").toString(),di.get("deptName"));
        }
        new Thread(() -> {
            // 异步执行的代码
            batchInsertPRDownload(projectCode, baseData, prNo);
        }).start();
        long endTime2 = System.currentTimeMillis();
        System.out.println("====插入数据" +  (endTime2 - startTime));
        return finalMap(maps, deptInfo);
    }

    @NotNull
    private List<PRDownloadEntity> excPrDownloadEntities(String projectCode, List<Map<String, Object>> baseData, String prNo) {
        Long userId = SecurityUtils.getUser().getId();
        Date date = new Date();

        long prId = IdWorker.getId();
        PRTotalEntity prTotalEntity = new PRTotalEntity();
        prTotalEntity.setProjectCode(projectCode);
        prTotalEntity.setDownloadUser(userId);
        prTotalEntity.setDownloadDate(date);
        prTotalEntity.setDownloadNo(prNo);
        List<PRDownloadEntity> pRDownLoadRecords = new ArrayList<>();
        for (Map<String, Object> record : baseData) {
            long id = IdWorker.getId();
            PRDownloadEntity prDownloadEntity = new PRDownloadEntity();
            prDownloadEntity.setId(id);
            prDownloadEntity.setDownloadUser(userId);
            prDownloadEntity.setDownloadDate(date);
            prDownloadEntity.setUfId(Long.valueOf(record.get("id").toString()));
            prDownloadEntity.setPrTotalId(prId);
            pRDownLoadRecords.add(prDownloadEntity);
        }
        //创建文件下载记录
        prTotalEntity.setDownloadNum(pRDownLoadRecords.size() + 1);
        prTotalEntity.setId(prId);
        adjustExcelMapper.insertPRTotal(prTotalEntity);
        return pRDownLoadRecords;
    }

    public R<Page<Map<String, Object>>> search(String projectCode, String region, String siteId, String itemCode, String phase,Integer page, Integer size, String unId) {
        if (StringUtils.isBlank(projectCode)) {
            return null;
        }
        Page<Map<String, Object>> pageMap = new Page<>(page, size);
        List<String> listSiteIds = new ArrayList<>();
        if (siteId != null && !"".equals(siteId)) {
            String[] split = siteId.split(",");
            for (String s : split) {
                listSiteIds.add(s);
            }
        }
        //todo  查询基础数据
        pageMap = adjustExcelMapper.searchPRBaseData(pageMap, projectCode, region, listSiteIds, itemCode, phase, unId);
        List<Map<String, Object>> baseData = pageMap.getRecords();
        Map<String, Map<String, Object>> assembledReport = baseData.stream()
                .filter(row -> row.get("unId") != null) // 确保关联键存在
                .collect(Collectors.toMap(
                        row -> (String) row.get("unId"), // Map 的 Key
                        Function.identity(),               // Map 的 Value
                        (existing, replacement) -> {
                            System.err.println("警告: 发现重复的 siteUn Key: " + existing.get("siteUn") + "，数据可能不一致。");
                            return existing; // 保留第一个遇到的记录
                        }
                ));

        // 提取后续查询所需的关联键列表
        // uniqueFields: uf.uniqueness_field 列表，假设大部分模块通过此字段关联
//        List<String> uniqueFields = new ArrayList<>(assembledReport.keySet());
        List<String> ufIds = extractUfIds(baseData);
//        pageMap = adjustExcelMapper.searchPR(pageMap, projectCode, region, siteId, itemCode, phase, unId);
        List<Map<String, Object>> otherInfo = adjustExcelMapper.selectOtherInfoByUnIds(ufIds);
        mergeData(assembledReport, otherInfo, "unId");
        List<Map<String, Object>> maps =new ArrayList<>();
        for (String k : assembledReport.keySet()) {
            maps.add(assembledReport.get(k));
        }
        List<Map<String, Object>> deptInfos = adjustExcelMapper.selectDeptInfo(SecurityUtils.getUserTenantId());
        Map<String, Object> deptInfo = new HashMap<>();
        for (Map<String, Object> di : deptInfos) {
            deptInfo.put(di.get("deptId").toString(),di.get("deptName"));
        }
        pageMap.setRecords(finalMap(maps, deptInfo));
        return R.ok(pageMap);
    }
    // 你提供的 keys 字符串
    private static final String keys = "id,unId,Region,siteID,siteName,siteAllocationDate,Phase,Type_of_service,Site_model,Item_code,BOQ_item,Quantity,Unit_price,Site_value,isDownloaded,projectCode,projectName,deptName,Site_belong_to,PIC_PC_PM,Start_Working_date,Completed_work_date,E_ATP_Pass";

    //构造最终map
    public List<Map<String, Object>> finalMap(List<Map<String, Object>> maps, Map<String, Object> deptInfo ) {
        // 将 keys 转换为 Set
        Set<String> allKeys = new HashSet<>(Arrays.asList(keys.split(",")));
        // 结果列表
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> originalMap : maps) {
            Map<String, Object> completeMap = new HashMap<>();

            // 先放入已有的键值
            completeMap.putAll(originalMap);

            // 补全缺失的 key
            for (String key : allKeys) {
                completeMap.putIfAbsent(key, null);
            }
            //添加部门名称
            completeMap.put("deptName", deptInfo.get(originalMap.get("deptId").toString()));

            result.add(completeMap);
        }

        return result;
    }


    private void mergeData(Map<String, Map<String, Object>> targetMap, List<Map<String, Object>> sourceData, String keyColumn) {
        // 如果来源数据为空，则无需合并
        if (CollectionUtils.isEmpty(sourceData)) {
            return;
        }
        // 遍历来源数据列表中的每一行
        for (Map<String, Object> sourceRow : sourceData) {
            // 获取当前来源行的关联键的值
            String key = (String) sourceRow.get(keyColumn);
            // 如果键不为空，并且在目标 Map 中存在这个键
            if (key != null && targetMap.containsKey(key)) {
                // 将当前来源行 (sourceRow) 的所有键值对合并到目标 Map 中对应的行数据中
                // 注意: 如果 sourceRow 中有和 targetMap 中已存在键相同的键，会被覆盖 (putAll 的行为)
                targetMap.get(key).putAll(sourceRow);
            } else {
                // 如果 key 为 null 或在 targetMap 中找不到匹配项，可以选择记录日志或忽略
                if (key != null) {
                    System.err.println("警告: 模块数据中的 key '" + key + "' (来自列 '" + keyColumn + "') 在基础数据中未找到匹配项，该行数据无法合并。");
                } else {
                    System.err.println("警告: 模块数据中的关联键 (来自列 '" + keyColumn + "') 为 null，该行数据无法合并。");
                }
            }
        }
    }

    private List<String> extractUfIds(List<Map<String, Object>> baseData) {
        if (CollectionUtils.isEmpty(baseData)) {
            return Collections.emptyList();
        }
        return baseData.stream()
                // 假设基础数据查询中 uf.id 被别名为 "ufId"
                .map(row -> row.get("id").toString())
                .filter(Objects::nonNull) // 过滤掉 null 值
                .distinct() // 去重
                .collect(Collectors.toList());
    }

    // 分批插入
    @Transactional
    public void batchInsertPRDownload(String projectCode,List<Map<String, Object>> baseData, String prNo) {
        List<PRDownloadEntity> pRDownLoadRecords = excPrDownloadEntities(projectCode, baseData, prNo);
        int batchSize = 1000;
        try{}catch (Exception e){
            throw new RuntimeException("批量插入错误");
        }
        for (int i = 0; i < pRDownLoadRecords.size(); i += batchSize) {
            int end = Math.min(i + batchSize, pRDownLoadRecords.size());
            adjustExcelMapper.insertPRDownLoad(pRDownLoadRecords.subList(i, end));
        }
    }

    public R<Page<Map<String, Object>>> searchDeatil(String ufId, Integer page, Integer size) {
        if (StringUtils.isBlank(ufId)) {
            return null;
        }
        Page<Map<String, Object>> pageMap = new Page<>(page, size);
        pageMap = adjustExcelMapper.searchDeatil(pageMap,ufId);
        return R.ok(pageMap);
    }

    public R<Page<Map<String, Object>>> searchTotal(String projectCode, Long userId, Integer page, Integer size) {
        Page<Map<String, Object>> pageMap = new Page<>(page, size);
        pageMap = adjustExcelMapper.searchTotal(pageMap, projectCode, userId);
        return R.ok(pageMap);
    }
}