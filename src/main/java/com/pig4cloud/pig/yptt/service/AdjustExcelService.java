package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.pig4cloud.pig.admin.api.entity.SysRole;
import com.pig4cloud.pig.common.security.service.PigUser;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.dto.operation.OperationPageDTO;
import com.pig4cloud.pig.me.api.dto.operation.QueryDTO;
import com.pig4cloud.pig.yptt.entity.vo.*;
import com.pig4cloud.pig.yptt.mapper.AdjustExcelMapper;
import com.pig4cloud.pig.yptt.mapper.AdjustMapper;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.mapper.RoleMapper;
import com.pig4cloud.pig.yptt.utils.ExcelUtil;
import com.pig4cloud.pig.yptt.utils.LockTimeV3Util;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import com.pig4cloud.pig.yptt.utils.MetaDataUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.misc.Hash;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AdjustService
 * @Description 调整
 * @date 2025/1/11 16:45
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdjustExcelService {
    private static final List<String>  DATE_FILED_LIST_Y2 = Arrays.asList("Unit_price", "Milestone_4th","Milestone_3rd","Milestone_2nd","Milestone_1st","Pre_payment","PO_gap","PO_value","quantity_reduce","Quantity");
    private final AdjustExcelMapper adjustExcelMapper;
    private final RoleMapper roleMapper;
    private final AdjustService adjustService;
    private final AdjustMapper adjustMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    protected final BasicMapper basicMapper;
    private static final String Y2_KEY = "y2:";
    private static final String Y1_KEY = "y1:";
    private static final String Y4_KEY = "y4:";
    private final static List<String> FILED_JSON_LIST = Arrays.asList("uniqueness_field", "PO", "site", "Customer_project");
    private final static List<String> DATE_FILED_LIST = Arrays.asList("site_allocation_date");

    public List<Map<String, Object>> exportY2(OperationPageDTO pageDTO) {
        List<QueryDTO> conditions = pageDTO.getConditions();
        HashMap<String, Object> mapCondition = new HashMap<>();
        if (CollUtil.isNotEmpty(conditions)) {
            conditions.forEach(condition -> {
                String name = condition.getName();
                Object value = condition.getValue();
                String symbol = condition.getSymbol();
                if (Objects.nonNull(value) && StrUtil.isNotBlank(value.toString())) {
                    if (FILED_JSON_LIST.contains(name)) {
                        mapCondition.put(name, MetaDataUtil.handleDataIdJson2Long(value.toString()));
                    }else if (DATE_FILED_LIST_Y2.contains(name)){
                        // 处理日期比较符号
                        if (StrUtil.isNotBlank(symbol)) {
                            switch (symbol) {
                                case "lt":
                                    condition.setSymbol("<");
                                    break;
                                case "le":
                                    condition.setSymbol("<=");
                                    break;
                                case "eq":
                                    condition.setSymbol("=");
                                    break;
                                case "ge":
                                    condition.setSymbol(">=");
                                    break;
                                case "gt":
                                    condition.setSymbol(">");
                                    break;
                                case "range":
                                    // 处理日期范围
                                    if (value instanceof String) {
                                        try {
                                            com.alibaba.fastjson.JSONArray rangeArray = com.alibaba.fastjson.JSONArray.parseArray((String) value);
//                                            JSONArray rangeArray = JSON.parseArray((String) value);
                                            if (rangeArray.size() == 2) {
                                                condition.setValue(rangeArray);
                                                condition.setSymbol("range");
                                            }
                                        } catch (Exception e) {
                                            log.error("解析日期范围失败", e);
                                        }
                                    }
                                    break;
                                default:
                                    condition.setSymbol("=");
                            }
                        }
                    } else {
                        mapCondition.put(name, value);
                    }
                }
            });
        }
        PigUser pigUser = SecurityUtils.getUser();
        if (Objects.isNull(pigUser)) {
            return null;
        }
        String flag = getFlag();
//        return adjustExcelMapper.exportY2(mapCondition, SecurityUtils.getRoles(), pigUser.getId(), flag);
        return adjustExcelMapper.exportY2V2(conditions, SecurityUtils.getRoles(), pigUser.getId(), flag, DATE_FILED_LIST_Y2);
    }

//    @Transactional(rollbackFor = Exception.class)
    public List<ImportResultVO> updateY2(MultipartFile file, String key) {
        String redisKey = Y2_KEY + key;
        List<Map<String, Object>> mapList = read2Map(file);
        Integer maxSize = 2000; //最大更改条数
        List<ImportResultVO> checkResult = new ArrayList<>();
        if (CollUtil.isEmpty(mapList)) {
            return Collections.emptyList();
        }
        Assert.isTrue(mapList.size() <= maxSize, "Exceed the limit for uploading data {} !", maxSize);
        check(mapList, checkResult, "y2");
        //        // 存入key
        redisTemplate.opsForValue().set(redisKey, new ProgressY3VO(Collections.emptyList(), 0.0), 5, TimeUnit.MINUTES);
        List<String> failedCollect = checkResult.stream()
                .map(ImportResultVO::getStatus)
                .filter(status -> Objects.equals(status, ImportResultVO.STATUS_FAILED))
                .collect(Collectors.toList());
        // 无错误信息则开始导入
        if (CollUtil.isEmpty(failedCollect)) {
            SecurityContext context = SecurityContextHolder.getContext();
//            CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            List<ImportResultVO> result = new ArrayList<>();
            ProgressY2VO vo = new ProgressY2VO();
            int total = mapList.size();
            int progress = 0;

            List<Map<String, Object>> mapLists = null;
            Map<String, Map<String, Map<String, Object>>> projectMap = new HashMap<>(); //存放projectCode
            List<Map<String, Object>> poItems = new ArrayList<>();  //调整y2的金额或者数量
            List<Map<String, Object>> settlementDatas = new ArrayList<>(); //更新y5关联金额
            List<Map<String, Object>> productivityDatas = new ArrayList<>(); //更新y6关联金额
            List<Map<String, Object>> YPTTSettlements = new ArrayList<>(); //更新关联的y9的数据
            Map<String, Map<String, Object>> mapCache = null; //缓存maplist key-value
            for (Map<String, Object> map : mapList) {
                Dict data = new Dict(map);
                Long id = Long.parseLong(data.getStr("id"));
                if (ObjectUtils.isEmpty(id)) {
                    continue;
                }
                String projectCode = data.getStr("YPTT_Project_code");
                String uniField = data.getStr("uniqueness_field");
                BigDecimal quantity = data.getBigDecimal("quantity");
                BigDecimal unitPrice = data.getBigDecimal("Unit_price");
                BigDecimal quantityReduce = data.getBigDecimal("quantity_reduce");
                String remark = data.getStr("Remark");

                BigDecimal milestone_1st = data.getBigDecimal("Milestone_1st");
                BigDecimal milestone_2nd = data.getBigDecimal("Milestone_2nd");
                BigDecimal milestone_3rd = data.getBigDecimal("Milestone_3rd");
                BigDecimal milestone_4th = data.getBigDecimal("Milestone_4th");
                Map<String, Object> poItem = new HashMap<>();
                poItem.put("id", id);
//                poItem.put("uniqueness_field", uniField);
//                    poItem.put("quantity", quantity); //数量不直接更改，通过quantityReduce减少数量
                poItem.put("Unit_price", unitPrice);
                poItem.put("quantity_reduce", quantityReduce);
//                    poItem.put("Remark", remark);
                poItem.put("Milestone_1st", milestone_1st);
                poItem.put("Milestone_2nd", milestone_2nd);
                poItem.put("Milestone_3rd", milestone_3rd);
                poItem.put("Milestone_4th", milestone_4th);

                if (!projectMap.containsKey(projectCode)) {
                    mapLists = adjustMapper.selectReport(projectCode);
//                    mapCache = mapLists.stream()
//                            .collect(Collectors.toMap(
//                                    m -> (String) m.get("poItemUn"),
//                                    Function.identity()
//                            ));
                    mapCache = mapLists.stream()
                            .filter(m -> m.get("poItemUn") != null) // 过滤null
                            .collect(Collectors.toMap(
                                    m -> (String) m.get("poItemUn"),
                                    Function.identity(),
                                    (oldValue, newValue) -> oldValue // 仍然需要合并函数
                            ));
                    projectMap.put(projectCode, mapCache);
                }
                mapCache = projectMap.get(projectCode);
                if (ObjectUtils.isEmpty(mapLists)) {
                    throw new RuntimeException("当前项目为查询到记录" + projectCode);
                }
                try {
                    asyncDoy2(projectCode, uniField, quantity, unitPrice, quantityReduce, mapLists, poItem,
                            poItems, settlementDatas, productivityDatas, YPTTSettlements, mapCache);
                    //批量更新

                } catch (Exception e) {
                    log.info("Y2批量更新失败: {}", e.getMessage());
                    ImportResultVO failed = new ImportResultVO();
                    failed.setStatus(ImportResultVO.STATUS_FAILED);
                    failed.setIndex(mapList.indexOf(map));
                    failed.setImportData(map);
                    failed.addWrongReason(e.getMessage());
                    result.add(failed);
                } finally {
                    if (++progress % 5 == 0 || progress == total) {
                        vo.setProgress((double) progress / (double) total * 100);
                        redisTemplate.setValueSerializer(RedisSerializer.java());
                        redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
                    }
                }
            }
            //更新数据
            try {
                updateInfo(poItems, settlementDatas, productivityDatas, YPTTSettlements);
            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("更新异常");
            }
            redisTemplate.setValueSerializer(RedisSerializer.java());
            Object o = redisTemplate.opsForValue().get(redisKey);
            vo = o instanceof ProgressY2VO ? (ProgressY2VO) o : new ProgressY2VO(result, 100.0);
            vo.setResultList(result);
            redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
//            });
        }
        return checkResult;
    }

//    @Transactional(rollbackFor = Exception.class)
    void asyncDoy2(String projectCode, String uniField, BigDecimal quantity, BigDecimal unitPrice,
                           BigDecimal quantityReduce, List<Map<String, Object>> mapLists, Map<String, Object> poItem,
                           List<Map<String, Object>> poItems, List<Map<String, Object>> settlementDatas,
                           List<Map<String, Object>> productivityDatas, List<Map<String, Object>> YPTTSettlements,
                           Map<String, Map<String, Object>> mapCache) {
//        CompletableFuture.runAsync(() -> {
        try {

            //异步更新关联数据
            adjustService.adjustPoItem(projectCode, uniField, mapLists, quantity, unitPrice, quantityReduce,
                    poItems, settlementDatas, productivityDatas, YPTTSettlements, mapCache);
        } catch (Exception e) {
            e.printStackTrace();
        }
//        });
    }

    @Transactional(rollbackFor = Exception.class)
    void updateInfo(List<Map<String, Object>> poItems, List<Map<String, Object>> settlementDatas,
                    List<Map<String, Object>> productivityDatas, List<Map<String, Object>> YPTTSettlements) {
        try {
            if (poItems.size() < 1){
                throw new RuntimeException("poItems--------->长度为0");
            }
            adjustMapper.updatePoItemDatas(poItems);
            if (settlementDatas.size() < 1){
                throw new RuntimeException("settlementDatas--------->长度为0");
            }
            adjustMapper.updateSettlementDatas(settlementDatas);
            if (productivityDatas.size() < 1){
                throw new RuntimeException("productivityDatas--------->长度为0");
            }
            adjustMapper.updateProductivityDatas(productivityDatas);
            if (YPTTSettlements.size() < 1){
                throw new RuntimeException("YPTTSettlements--------->长度为0");
            }
            adjustMapper.updateYPTTSettlements(YPTTSettlements);
        } catch (Exception e) {

            throw new RuntimeException("数据或者相关联数据更新异常" + e.getMessage());
        }
    }
    @NotNull
    private List<Long> getRoles() {
        List<SysRole> roleList = SecurityUtils.getUser().getRoleList();
        List<Long> roles = new ArrayList<>();
        for (SysRole sysRole : roleList) {
            roles.add(sysRole.getRoleId());
        }
        return roles;
    }
    private void check(List<Map<String, Object>> mapList, List<ImportResultVO> checkResult, String flag) {
        List<Long> roles = getRoles();
        // 导入校验
        for (Map<String, Object> map : mapList) {
            ImportResultVO importResultVO = new ImportResultVO();
            importResultVO.setImportData(map);
            importResultVO.setStatus(ImportResultVO.STATUS_SUCCEED);
            importResultVO.setIndex(mapList.indexOf(map));
            map.forEach((k, v) -> {
                if (ArrayUtil.contains(DataMangeService.datetime_fields, k) && Objects.nonNull(v)) {
                    try {
                        String dateStr = v instanceof LocalDateTime ? ((LocalDateTime) v).toLocalDate().toString()
                                : v.toString();
                        MetaDataUtil.dateStr2LocalDateTime(dateStr);
                    } catch (Exception e) {
                        importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                        importResultVO.setIndex(mapList.indexOf(map));
                        importResultVO.addWrongReason("This filed 【" + k + "】Incorrect date format");
                    }
                }
                if (Objects.equals(k, "id") && (Objects.isNull(v) || StrUtil.isBlank(v.toString()))) {
                    importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                    importResultVO.addWrongReason("This filed 【" + k + "】cannot be null");
                }
                if (Objects.equals(k, "id") && (!Objects.isNull(v) && !StrUtil.isBlank(v.toString()))) {
                    judeSIteItemStatus(flag, roles, importResultVO, k, v);
                }
                if (Objects.equals(k, "YPTT_Project_code")) {
                    // 判断是否有编辑权限
                    String userIdStr = roleMapper.getUserIdListByPerType("y2_update", v.toString());
                    JSONArray jsonArray = JSONUtil.parseArray(userIdStr);
                    if (!jsonArray.contains(SecurityUtils.getUser().getId())) {
                        importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                        importResultVO.addWrongReason("NO EDITING PERMISSION !");
                    }
                }
            });
            if ("y2".equals(flag)) { //y2 需要校验日期锁定功能
                //检查时间锁是否能够正确操作
                String YPTT_Project_code = map.get("YPTT_Project_code").toString();
                String id = map.get("id").toString();
                String quantity = map.get("quantity").toString();
                String Unit_price = map.get("Unit_price").toString();
                Object quantityReduceObj = map.get("quantity_reduce");
                BigDecimal quantityReduce = new BigDecimal("0");
                if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(quantityReduceObj)){
                    quantityReduce = new BigDecimal(quantityReduceObj.toString());
                }
                String Milestone_1st = map.get("Milestone_1st").toString();
                String Milestone_2nd = map.get("Milestone_2nd").toString();
                String Milestone_3rd = map.get("Milestone_3rd").toString();
                String Milestone_4th = map.get("Milestone_4th").toString();
                LockTimeV3Util lockTimeV3Util = new LockTimeV3Util();
                if (lockTimeV3Util.checkTimeLock(importResultVO, YPTT_Project_code, null)) {
                    importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                    importResultVO.addWrongReason(String.format("The current time is locked and cannot be modified!"));
                }
                Map<String, Object> poItemInfo = adjustMapper.selectPoItemById(id);

                BigDecimal dbQuantity = (BigDecimal) poItemInfo.get("Quantity");
                BigDecimal dbUnitPrice = (BigDecimal) poItemInfo.get("Unit_price");
                BigDecimal milestone_1st = (BigDecimal) poItemInfo.get("Milestone_1st");
                BigDecimal milestone_2nd = (BigDecimal) poItemInfo.get("Milestone_2nd");
                BigDecimal milestone_3rd = (BigDecimal) poItemInfo.get("Milestone_3rd");
                BigDecimal milestone_4th = (BigDecimal) poItemInfo.get("Milestone_4th");
                Object uniquenessObj = poItemInfo.get("uniqueness_field").toString();
                JSONArray uniqueness_field;
                if (uniquenessObj instanceof JSONArray) {
                    uniqueness_field = (JSONArray) uniquenessObj;
                } else if (uniquenessObj instanceof String) {
                    uniqueness_field = JSONUtil.parseArray((String) uniquenessObj);
                } else if (uniquenessObj instanceof List) {
                    uniqueness_field = new JSONArray((List<?>) uniquenessObj);
                } else {
                    throw new IllegalStateException("uniqueness_field 字段类型不支持: " + uniquenessObj.getClass());
                }
                //查看y6 和 y5 的数据， 确认当前数据能否进行修改
                List<Map<String, Object>> report = basicMapper
                        .findProductivityReportByUniquenessId(Long.valueOf(uniqueness_field.get(0).toString()));
                checkMilestones(importResultVO, YPTT_Project_code, quantity, Unit_price, Milestone_1st, Milestone_2nd, Milestone_3rd, Milestone_4th, roles, lockTimeV3Util, dbQuantity, dbUnitPrice, milestone_1st, milestone_2nd, milestone_3rd, milestone_4th, report, quantityReduce);

            }
            checkResult.add(importResultVO);
        }
    }

    private void judeSIteItemStatus(String flag, List<Long> roles, ImportResultVO importResultVO, String k, Object v) {
        Map<String, Object> siteItemInfo = null;
        if ("y1".equals(flag)) { //siteItem批量上传 并且不是管理员
            siteItemInfo = adjustMapper.selectSiteItemById(v.toString());
        }else if("y2".equals(flag)){
            siteItemInfo = adjustMapper.selectSiteItemByPOItemId(v.toString());
        }else if("y4".equals(flag)){
            siteItemInfo = adjustMapper.selectSiteItemBySubItemId(v.toString());
        }
        //管理员、pd、pm
        if (!roles.contains(1694550407313264642L) && !roles.contains(1694899426594713602L) && !roles.contains(1705102200719081473L)) {
            //!roles.contains(1694550407313264642l) && !roles.contains(1694899426594713602l) && !roles.contains(1705102200719081473l)
            if (ObjectUtils.isEmpty(siteItemInfo)) {
                importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                importResultVO.addWrongReason("This filed 【" + k + "】-- value [+" + v.toString() + "]  Site Item is not EXISTS");
            }
            Object site_item_status = siteItemInfo.get("Site_item_status");
            if (!"[\"unclose\"]".equals(site_item_status)) {
                System.out.println("===========site_item_status"+ site_item_status);
                importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                importResultVO.addWrongReason("This filed 【" + k + "】-- value [+" + v.toString() + "]  Site Status is not equals unclose");
            }
        }
    }


    private List<Map<String, Object>> read2Map(MultipartFile file) {
        try {
            return ExcelUtil.readExcelToMap(file);
        } catch (IOException e) {
            throw new IllegalArgumentException("Parsing excel error", e);
        }
    }

    public List<?> exportY1(OperationPageDTO pageDTO) {
        List<QueryDTO> conditions = pageDTO.getConditions();
        HashMap<String, Object> mapCondition = new HashMap<>();
        if (CollUtil.isNotEmpty(conditions)) {
            conditions.forEach(condition -> {
                String name = condition.getName();
                Object value = condition.getValue();
                String symbol = condition.getSymbol();
                if (Objects.nonNull(value) && StrUtil.isNotBlank(value.toString())) {
                    if (FILED_JSON_LIST.contains(name)) {
                        mapCondition.put(name, MetaDataUtil.handleDataIdJson2Long(value.toString()));
                        condition.setValue(MetaDataUtil.handleDataIdJson2Long(value.toString()));
                    }else if(DATE_FILED_LIST.contains(name)){
                        // 处理日期比较符号
                        if (StrUtil.isNotBlank(symbol)) {
                            switch (symbol) {
                                case "lt":
                                    condition.setSymbol("<");
                                    break;
                                case "le":
                                    condition.setSymbol("<=");
                                    break;
                                case "eq":
                                    condition.setSymbol("=");
                                    break;
                                case "ge":
                                    condition.setSymbol(">=");
                                    break;
                                case "gt":
                                    condition.setSymbol(">");
                                    break;
                                case "range":
                                    // 处理日期范围
                                    if (value instanceof String) {
                                        try {
                                            com.alibaba.fastjson.JSONArray rangeArray = com.alibaba.fastjson.JSONArray.parseArray((String) value);
//                                            JSONArray rangeArray = JSON.parseArray((String) value);
                                            if (rangeArray.size() == 2) {
                                                condition.setValue(rangeArray);
                                                condition.setSymbol("range");
                                            }
                                        } catch (Exception e) {
                                            log.error("解析日期范围失败", e);
                                        }
                                    }
                                    break;
                                default:
                                    condition.setSymbol("=");
                            }
                        }
                    } else {
//                        mapCondition.put(name, value);
                    }
                }
            });
        }
        PigUser pigUser = SecurityUtils.getUser();
        if (Objects.isNull(pigUser)) {
            return null;
        }
        String flag = getFlag();
//        return adjustExcelMapper.exportY1(mapCondition, SecurityUtils.getRoles(), pigUser.getId(), flag);
        return adjustExcelMapper.exportY1V2(conditions, SecurityUtils.getRoles(), pigUser.getId(),flag,DATE_FILED_LIST);
    }

    //标识是否属于管理员\PD\PM
    private String getFlag() {
        List<Long> roles = getRoles();
        if (!roles.contains(1694550407313264642l) && !roles.contains(1694899426594713602l) && !roles.contains(1705102200719081473l)){
            return "F"; //
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<ImportResultVO> updateY1New(MultipartFile file, String key) {
        String redisKey = Y1_KEY + key;
        List<Map<String, Object>> mapList = read2Map(file);
        Integer maxSize = 10000; //最大更改条数
        List<ImportResultVO> checkResult = new ArrayList<>();
        if (CollUtil.isEmpty(mapList)) {
            return Collections.emptyList();
        }
        Assert.isTrue(mapList.size() <= maxSize, "Exceed the limit for uploading data {} !", maxSize);
        check(mapList, checkResult, "y1");
        //        // 存入key
        redisTemplate.opsForValue().set(redisKey, new ProgressY3VO(Collections.emptyList(), 0.0), 5, TimeUnit.MINUTES);
        List<String> failedCollect = checkResult.stream()
                .map(ImportResultVO::getStatus)
                .filter(status -> Objects.equals(status, ImportResultVO.STATUS_FAILED))
                .collect(Collectors.toList());
        System.out.println("==============无错误信息则开始导入" + failedCollect);
        // 无错误信息则开始导入
        List<Map<String, Object>> siteItemList = new ArrayList<>();
        List<Map<String, Object>> poItemList = new ArrayList<>();
        Integer i = 0;
        if (CollUtil.isEmpty(failedCollect)) {
            System.out.println("==============进来了");
            SecurityContext context = SecurityContextHolder.getContext();
            SecurityContextHolder.setContext(context);
//            List<Map<String, Object>> mapLists = adjustMapper.selectReportNoCondition();
            List<Map<String, Object>> mapLists = adjustMapper.selectPOItem();
            Map<String, Map<String, Object>> mapT = new HashMap<>();
            for (Map<String, Object> list : mapLists) {
                String keyPo = (String) list.get("uniField");
                mapT.put(keyPo, list);
            }
            System.out.println("==============查询结束" + mapList.size());
            for (Map<String, Object> map : mapList) {
                Dict data = new Dict(map);
                Long id = Long.parseLong(data.getStr("id"));
                if (ObjectUtils.isEmpty(id)) {
                    continue;
                }
                String siteItemStatus = data.getStr("Site_item_status"); //站点（条目）状态
                String status = null;
                if ("unclose".equals(siteItemStatus)) {
                    status = "unclose";
                }
                if ("close".equals(siteItemStatus)) {
                    status = "close";
                }
                if ("invalid".equals(siteItemStatus)){
                    status = "invalid";
                }
                String uniField = data.getStr("uniqueness_field");
                BigDecimal quantity = data.getBigDecimal("quantity");
                if (quantity == null) {
                    quantity = new BigDecimal("0");
                }
                BigDecimal unitPrice = data.getBigDecimal("Unit_price");
                if (unitPrice == null) {
                    unitPrice = new BigDecimal("0");
                }
                BigDecimal quantityReduce = data.getBigDecimal("quantity_reduce");
                BigDecimal afterQuantity = new BigDecimal("0");
                if (quantityReduce != null) {
                    afterQuantity = quantity.subtract(quantityReduce);
                }

                Map<String, Object> siteItem = new HashMap<>();
                BigDecimal siteValue = afterQuantity.multiply(unitPrice);
                siteItem.put("Quantity", afterQuantity);
                siteItem.put("Unit_price", unitPrice);
                siteItem.put("Site_value", siteValue);
                if (status != null) {
                    siteItem.put("Site_item_status", status);
                }
                siteItem.put("id", id);
                siteItemList.add(siteItem);


                //更新poItem中的pogap
                BigDecimal poValue = new BigDecimal(0); //  PO_Item-采购订单条目 - 条目单价
                Long poItemId = null;
                Map<String, Object> val = (Map<String, Object>) mapT.get(uniField);
                if (val == null){
                    System.out.println("========================uniField" + uniField);
                    System.out.println("========================uniField" + val);
                    throw new RuntimeException("为获取到正确的数据 唯一关键识别字段"+uniField);

                }
                poValue = new BigDecimal(val.get("PO_value") == null ? "0" : val.get("PO_value").toString());
                poItemId = (Long) val.get("id");

                BigDecimal poGAP = siteValue.subtract(poValue);
                Map<String, Object> poItem = new HashMap<>();
                poItem.put("id", poItemId);
                poItem.put("PO_gap", poGAP);
                poItemList.add(poItem);
            }
            // 更新siteItem 和 poItem
            try {
                System.out.println("=================siteItemList" + siteItemList.size());
                System.out.println("=================poItemList" + poItemList.size());
                CompletableFuture.runAsync(() -> {
                    doSql(siteItemList, poItemList);
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return checkResult;
    }
    @Transactional(rollbackFor = Exception.class)
    void doSql(List<Map<String, Object>> siteItemList, List<Map<String, Object>> poItemList) {
        System.out.println("=========执行数据库操作===============");
        try {
            adjustMapper.updateSiteItemDataes(siteItemList);
            adjustMapper.updateSitePoDataes(poItemList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<ImportResultVO> updateY1(MultipartFile file, String key) {
        String redisKey = Y1_KEY + key;
        List<Map<String, Object>> mapList = read2Map(file);
        Integer maxSize = 2000; //最大更改条数
        List<ImportResultVO> checkResult = new ArrayList<>();
        if (CollUtil.isEmpty(mapList)) {
            return Collections.emptyList();
        }
        Assert.isTrue(mapList.size() <= maxSize, "Exceed the limit for uploading data {} !", maxSize);
        check(mapList, checkResult, "y1");
        //        // 存入key
        redisTemplate.opsForValue().set(redisKey, new ProgressY3VO(Collections.emptyList(), 0.0), 5, TimeUnit.MINUTES);
        List<String> failedCollect = checkResult.stream()
                .map(ImportResultVO::getStatus)
                .filter(status -> Objects.equals(status, ImportResultVO.STATUS_FAILED))
                .collect(Collectors.toList());
        // 无错误信息则开始导入
        if (CollUtil.isEmpty(failedCollect)) {
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                List<ImportResultVO> result = new ArrayList<>();
                ProgressY2VO vo = new ProgressY2VO();
                int total = mapList.size();
                int progress = 0;
                for (Map<String, Object> map : mapList) {
                    Dict data = new Dict(map);
                    Long id = Long.parseLong(data.getStr("id"));
                    if (ObjectUtils.isEmpty(id)) {
                        continue;
                    }
                    String siteItemStatus = data.getStr("Site_item_status"); //站点（条目）状态
                    String status = null;
                    if ("未关闭".equals(siteItemStatus)) {
                        status = "unclose";
                    }
                    if ("已关闭".equals(siteItemStatus)) {
                        status = "close";
                    }
                    String projectCode = data.getStr("YPTT_Project_code");
                    String uniField = data.getStr("uniqueness_field");
                    BigDecimal quantity = data.getBigDecimal("quantity");
                    if (quantity == null) {
                        quantity = new BigDecimal("0");
                    }
                    BigDecimal unitPrice = data.getBigDecimal("Unit_price");
                    if (unitPrice == null) {
                        unitPrice = new BigDecimal("0");
                    }
                    BigDecimal quantityReduce = data.getBigDecimal("quantity_reduce");
                    BigDecimal afterQuantity = new BigDecimal("0");
                    if (quantityReduce != null) {
                        afterQuantity = quantity.subtract(quantityReduce);
                    }

                    Map<String, Object> siteItem = new HashMap<>();
                    BigDecimal siteValue = afterQuantity.multiply(unitPrice);
                    siteItem.put("Quantity", afterQuantity);
                    siteItem.put("Unit_price", unitPrice);
                    siteItem.put("Site_value", siteValue);
                    if (status != null) {
                        siteItem.put("Site_item_status", siteItemStatus);
                        try {
                            adjustMapper.changeStatusSiteItem(id, status);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    siteItem.put("id", id);

                    List<Map<String, Object>> mapLists = adjustMapper.selectReport(projectCode);
                    try {
                        asyncDoy1(projectCode, uniField, afterQuantity, unitPrice, mapLists);
                    } catch (Exception e) {
                        log.info("Y1批量更新失败: {}", e.getMessage());
                        ImportResultVO failed = new ImportResultVO();
                        failed.setStatus(ImportResultVO.STATUS_FAILED);
                        failed.setIndex(mapList.indexOf(map));
                        failed.setImportData(map);
                        failed.addWrongReason(e.getMessage());
                        result.add(failed);
                    } finally {
                        if (++progress % 5 == 0 || progress == total) {
                            vo.setProgress((double) progress / (double) total * 100);
                            redisTemplate.setValueSerializer(RedisSerializer.java());
                            redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
                        }
                    }
                }
                redisTemplate.setValueSerializer(RedisSerializer.java());
                Object o = redisTemplate.opsForValue().get(redisKey);
                vo = o instanceof ProgressY2VO ? (ProgressY2VO) o : new ProgressY2VO(result, 100.0);
                vo.setResultList(result);
                redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
            });
        }
        return checkResult;
    }

    private void asyncDoy1(String projectCode, String uniField, BigDecimal quantity, BigDecimal unitPrice, List<Map<String, Object>> mapLists) {
//        CompletableFuture.runAsync(() -> {
        adjustService.adjustSiteItem(projectCode, uniField, mapLists, quantity, unitPrice);
//        });
    }

    public ProgressY2VO queryProgressY2(String key) {
        redisTemplate.setValueSerializer(RedisSerializer.java());
        Object o = redisTemplate.opsForValue().get(Y2_KEY + key);
        return o instanceof ProgressY2VO ? (ProgressY2VO) o : new ProgressY2VO(null, 100.0);
    }

    public ProgressY1VO queryProgressY1(String key) {
        redisTemplate.setValueSerializer(RedisSerializer.java());
        Object o = redisTemplate.opsForValue().get(Y2_KEY + key);
        return o instanceof ProgressY1VO ? (ProgressY1VO) o : new ProgressY1VO(null, 100.0);
    }

    public List<?> exportY4(OperationPageDTO pageDTO) {
        List<QueryDTO> conditions = pageDTO.getConditions();
        HashMap<String, Object> mapCondition = new HashMap<>();
        if (CollUtil.isNotEmpty(conditions)) {
            conditions.forEach(condition -> {
                String name = condition.getName();
                Object value = condition.getValue();
                if (Objects.nonNull(value) && StrUtil.isNotBlank(value.toString())) {
                    if (FILED_JSON_LIST.contains(name)) {
                        mapCondition.put(name, MetaDataUtil.handleDataIdJson2Long(value.toString()));
                    } else {
                        mapCondition.put(name, value);
                    }
                }
            });
        }
        PigUser pigUser = SecurityUtils.getUser();
        if (Objects.isNull(pigUser)) {
            return null;
        }
        String flag = getFlag();
        return adjustExcelMapper.exportY4(mapCondition, SecurityUtils.getRoles(), pigUser.getId(), flag);
    }

    public ProgressY4VO queryProgressY4(String key) {
        redisTemplate.setValueSerializer(RedisSerializer.java());
        Object o = redisTemplate.opsForValue().get(Y4_KEY + key);
        return o instanceof ProgressY4VO ? (ProgressY4VO) o : new ProgressY4VO(null, 100.0);
    }

    public List<ImportResultVO> updateY4(MultipartFile file, String key) {
        String redisKey = Y4_KEY + key;
        List<Map<String, Object>> mapList = read2Map(file);
        Integer maxSize = 2000; //最大更改条数
        List<ImportResultVO> checkResult = new ArrayList<>();
        if (CollUtil.isEmpty(mapList)) {
            return Collections.emptyList();
        }
        Assert.isTrue(mapList.size() <= maxSize, "Exceed the limit for uploading data {} !", maxSize);
        check(mapList, checkResult, "y4");
        //        // 存入key
        redisTemplate.opsForValue().set(redisKey, new ProgressY3VO(Collections.emptyList(), 0.0), 5, TimeUnit.MINUTES);
        List<String> failedCollect = checkResult.stream()
                .map(ImportResultVO::getStatus)
                .filter(status -> Objects.equals(status, ImportResultVO.STATUS_FAILED))
                .collect(Collectors.toList());
        // 无错误信息则开始导入
        if (CollUtil.isEmpty(failedCollect)) {
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                List<ImportResultVO> result = new ArrayList<>();
                ProgressY2VO vo = new ProgressY2VO();
                int total = mapList.size();
                int progress = 0;
                for (Map<String, Object> map : mapList) {
                    Dict data = new Dict(map);
                    Long id = Long.parseLong(data.getStr("id"));
                    if (ObjectUtils.isEmpty(id)) {
                        continue;
                    }
                    String projectCode = data.getStr("YPTT_Project_code");
                    String uniField = data.getStr("uniqueness_field");
                    BigDecimal quantity = data.getBigDecimal("Quantity");
                    if (quantity == null) {
                        quantity = new BigDecimal("0");
                    }
                    BigDecimal unitPrice = data.getBigDecimal("Unit_price");
                    if (unitPrice == null) {
                        unitPrice = new BigDecimal("0");
                    }
                    BigDecimal quantityReduce = data.getBigDecimal("quantity_reduce");
                    BigDecimal afterQuantity = new BigDecimal("0");
                    if (quantityReduce != null) {
                        afterQuantity = quantity.subtract(quantityReduce);
                    }

                    BigDecimal SubconSettlement_1st = data.getBigDecimal("Milestone_1st") == null ? data.getBigDecimal("Milestone_1st") : new BigDecimal("0");
                    BigDecimal SubconSettlement_2nd = data.getBigDecimal("Milestone_2nd") == null ? data.getBigDecimal("Milestone_2nd") : new BigDecimal("0");
                    BigDecimal SubconSettlement_3rd = data.getBigDecimal("Milestone_3rd") == null ? data.getBigDecimal("Milestone_3rd") : new BigDecimal("0");
                    BigDecimal SubconSettlement_4th = data.getBigDecimal("Milestone_4th") == null ? data.getBigDecimal("Milestone_4th") : new BigDecimal("0");


                    BigDecimal subConValue = afterQuantity.multiply(unitPrice);
                    Map<String, Object> subConItem = new HashMap<>();
                    subConItem.put("Quantity", quantity);
                    subConItem.put("Unit_price", unitPrice);
                    subConItem.put("Subcon_PO_amount", subConValue);
                    subConItem.put("Milestone_1st", SubconSettlement_1st);
                    subConItem.put("Milestone_2nd", SubconSettlement_2nd);
                    subConItem.put("Milestone_3rd", SubconSettlement_3rd);
                    subConItem.put("Milestone_4th", SubconSettlement_4th);
//                try {
//                    adjustMapper.updateSubPoItemData(subConItem);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }

                    List<Map<String, Object>> mapLists = adjustMapper.selectReport(projectCode);
                    try {
                        asyncDoy4(projectCode, uniField, quantity, unitPrice, mapLists, quantityReduce, SubconSettlement_1st, SubconSettlement_2nd, SubconSettlement_3rd, SubconSettlement_4th);
                    } catch (Exception e) {
                        log.info("Y1批量更新失败: {}", e.getMessage());
                        ImportResultVO failed = new ImportResultVO();
                        failed.setStatus(ImportResultVO.STATUS_FAILED);
                        failed.setIndex(mapList.indexOf(map));
                        failed.setImportData(map);
                        failed.addWrongReason(e.getMessage());
                        result.add(failed);
                    } finally {
                        if (++progress % 5 == 0 || progress == total) {
                            vo.setProgress((double) progress / (double) total * 100);
                            redisTemplate.setValueSerializer(RedisSerializer.java());
                            redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
                        }
                    }
                }
                redisTemplate.setValueSerializer(RedisSerializer.java());
                Object o = redisTemplate.opsForValue().get(redisKey);
                vo = o instanceof ProgressY2VO ? (ProgressY2VO) o : new ProgressY2VO(result, 100.0);
                vo.setResultList(result);
                redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
            });
        }
        return checkResult;
    }

    private void asyncDoy4(String projectCode, String uniField, BigDecimal quantity, BigDecimal unitPrice,
                           List<Map<String, Object>> mapLists, BigDecimal quantityReduce, BigDecimal subconSettlement_1st,
                           BigDecimal subconSettlement_2nd, BigDecimal subconSettlement_3rd, BigDecimal subconSettlement_4th) {
        adjustService.adjustSubPoItem(projectCode, uniField, mapLists, quantity, unitPrice, quantityReduce,
                subconSettlement_1st, subconSettlement_2nd, subconSettlement_3rd, subconSettlement_4th);
    }

    @Nullable
    private ImportResultVO checkMilestones(ImportResultVO valid, String YPTT_Project_code, String quantity, String unit_price, String milestone_1st, String milestone_2nd, String milestone_3rd, String milestone_4th, List<Long> roles, LockTimeV3Util lockTimeV3Util, BigDecimal dbQuantity, BigDecimal dbUnitPrice, BigDecimal milestone_1st2, BigDecimal milestone_2nd2, BigDecimal milestone_3rd2, BigDecimal milestone_4th2, List<Map<String, Object>> report, BigDecimal quantityReduce) {
        if (CollUtil.isNotEmpty(report)) {
            //如果存在y6的数据 ,判断当前对应的产值是否存在， 不允许修改
            Map<String, Object> map = report.get(0);
            LocalDate report_date_1st = LockTimeV3Util.toLocalDate(map.get("report_date_1st"));
            if (report_date_1st != null && lockTimeV3Util.checkTimeLock(valid, YPTT_Project_code, report_date_1st)) {
                //如果存在第一次产值申报日期 + 当前日期在日期锁定范围之外 不能修改任何当前阶段内容
                //只要存在一次产值申报 就不能修改Unit price 、 quantity 并且不能修改第一次结算金额
                if (dbQuantity != null && dbQuantity.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.numberStr2BigDecimal(quantity, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the quantity cannot be modified");
                    return valid;
                }
                if (dbUnitPrice != null && dbUnitPrice.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.numberStr2BigDecimal(unit_price, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the Unit_price cannot be modified");
                    return valid;
                }
                if (milestone_1st2 != null && milestone_1st2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.numberStr2BigDecimal(milestone_1st, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the Milestone_1st cannot be modified");
                    return valid;
                }
                if (quantityReduce != null && quantityReduce.compareTo(BigDecimal.ZERO) != 0){
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the quantityReduce cannot be modified");
                    return valid;
                }

            }
            LocalDate report_date_2nd = LockTimeV3Util.toLocalDate(map.get("report_date_2nd"));
            if (report_date_2nd != null && lockTimeV3Util.checkTimeLock(valid, YPTT_Project_code, report_date_2nd)) {
                if (milestone_2nd2 != null && milestone_2nd2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.numberStr2BigDecimal(milestone_2nd, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the Milestone_2nd cannot be modified");
                    return valid;
                }
            }
            LocalDate report_date_3rd = LockTimeV3Util.toLocalDate(map.get("report_date_3rd"));
            if (report_date_3rd != null && lockTimeV3Util.checkTimeLock(valid, YPTT_Project_code, report_date_3rd)) {
                if (milestone_3rd2 != null && milestone_3rd2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.numberStr2BigDecimal(milestone_3rd, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the Milestone_3rd cannot be modified");
                    return valid;
                }
            }

            LocalDate report_date_4th = LockTimeV3Util.toLocalDate(map.get("report_date_4th"));
            if (report_date_4th != null && lockTimeV3Util.checkTimeLock(valid, YPTT_Project_code, report_date_4th)) {
                if (milestone_4th2 != null && milestone_4th2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.numberStr2BigDecimal(milestone_4th, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the Milestone_4th cannot be modified");
                    return valid;
                }
            }
        }

//            List<Map<String, Object>> readySettlement = basicMapper
//                    .findReadySettlementByUniquenessId(existingUniqueness.getDataId());
//            if (CollUtil.isNotEmpty(readySettlement)) {
//                // 如果存在y5的数据
//            }

        if (!roles.contains(1694550407313264642l)) { //不是管理员，就需要校验结算里程碑的数据
            if (milestone_1st2 != null && milestone_1st2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_1st, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Milestone_1st [%s] has been a change", milestone_1st));
                return valid;
            }

            if (milestone_2nd2 != null && milestone_2nd2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_2nd, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Milestone_2nd [%s] has been a change", milestone_2nd));
                return valid;
            }

            if (milestone_3rd2 != null && milestone_3rd2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_3rd, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Milestone_3rd [%s] has been a change", milestone_3rd));
                return valid;
            }

            if (milestone_4th2 != null && milestone_4th2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_4th, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Milestone_4th [%s] has been a change", milestone_4th));
                return valid;
            }
        }
        return null;
    }

}