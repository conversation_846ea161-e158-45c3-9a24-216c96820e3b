package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.collection.CollUtil;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.list.UnmodifiableList;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/09/28
 */
@Slf4j
@Service
public class TransformManager implements InitializingBean, ApplicationContextAware {

	private List<Transformer> transformers;

	private ApplicationContext applicationContext;

	public ImportResultVO validate(Transformer.TransformContext context, int index, Map<String, Object> raw) {
		for (Transformer transformer : getTransformers()) {
			if (transformer.support(context)) {
				try {
					return transformer.validate(context, index, raw);
				}
				catch (Exception e) {
					log.error("unknown transform error, cause: {}", e.getMessage(), e);
					return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED,
							"Unknown error: " + e.getMessage());
				}
			}
		}
		throw new UnsupportedOperationException("Data Transformer: Not supported module type");
	}

	public ImportResultVO transform(Transformer.TransformContext context, int index, Map<String, Object> raw) {
		for (Transformer transformer : getTransformers()) {
			if (transformer.support(context)) {
				try {
					return transformer.transform(context, index, raw);
				}
				catch (Exception e) {
					log.error("unknown transform error, cause: {}", e.getMessage(), e);
					return new ImportResultVO(index, raw, ImportResultVO.STATUS_FAILED,
							"Unknown error: " + e.getMessage());
				}
			}
		}
		throw new UnsupportedOperationException("Data Transformer: Not supported module type");
	}

	@Override
	public void afterPropertiesSet() {
		Map<String, Transformer> beans = applicationContext.getBeansOfType(Transformer.class);
		if (CollUtil.isNotEmpty(beans)) {
			this.transformers = new UnmodifiableList<>(
					new ArrayList<>(applicationContext.getBeansOfType(Transformer.class).values()));
		}
	}

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}

	public List<Transformer> getTransformers() {
		// TODO 优化获取beans的逻辑
		Collection<Transformer> transformers = this.applicationContext.getBeansOfType(Transformer.class).values();
		if (CollUtil.isEmpty(transformers)) {
			throw new RuntimeException("Not found transformer components");
		}
		return new UnmodifiableList<>(new ArrayList<>(transformers));
		// if (Objects.isNull(transformers)) {
		// synchronized (this) {
		// if (Objects.isNull(transformers)) {
		// afterPropertiesSet();
		// }
		// }
		// }
		// return transformers;
	}

}
