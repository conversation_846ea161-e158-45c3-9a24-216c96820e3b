package com.pig4cloud.pig.yptt.service.transform;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.admin.api.entity.SysRole;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.DataPermissionsService;
import com.pig4cloud.pig.yptt.utils.LockTimeV2Util;
import com.pig4cloud.pig.yptt.utils.LockTimeV3Util;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import com.pig4cloud.pig.yptt.utils.MetaDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Y2 PO信息 转为 项目、唯一标识、PO、PO条目
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
@Component
@Slf4j
public class Y2Transformer extends AbstractTransformer {

    private final static String MODULE_NAME = GlobalConstants.Y2.NAME;

    public Y2Transformer(RemoteAppService remoteAppService, ViewConfProperties viewConfProperties,
                         DataPermissionsService dataPermissionsService, BasicMapper basicMapper, RedissonClient redissonClient,
                         DataMangeService dataMangeService) {
        super(remoteAppService, viewConfProperties, dataPermissionsService, basicMapper, redissonClient,
                dataMangeService);
    }

    @Override
    public ImportResultVO doValidate(TransformContext context, int index, Map<String, Object> raw,
                                     ImportResultVO valid) {
        final String appid = context.getAppid();
        final List<MetaDataDTOWrapper> customerProjectCache = context.getCustomerProjectCache();
        final List<MetaDataDTOWrapper> uniquenessCache = context.getUniquenessCache();
        final List<MetaDataDTOWrapper> poCache = context.getPoCache();
        final List<MetaDataDTOWrapper> poItemCache = context.getPoItemCache();
        final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
        final List<MetaDataDTOWrapper> siteItemCache = context.getSiteItemCache();

        Dict dict = new Dict(raw);
        String YPTT_Project_code = dict.getStr("YPTT_Project_code");
        String Site_ID = dict.getStr("Site_ID");
        String uniqueness_field = MetaDataUtil.getUniquenessField(dict, GlobalConstants.Y2.NAME);
        String PO_Number = dict.getStr("PO_Number");
        String Contract_number = dict.getStr("Contract_number");
        String Custom_project_name = dict.getStr("Custom_project_name");
        String quantity = dict.getStr("quantity");
        String Unit_price = dict.getStr("Unit_price");

        String Milestone_1st = dict.getStr("Milestone_1st");
        String Milestone_2nd = dict.getStr("Milestone_2nd");
        String Milestone_3rd = dict.getStr("Milestone_3rd");
        String Milestone_4th = dict.getStr("Milestone_4th");

//        LockTimeV2Util lockTimeV2Util = new LockTimeV2Util();
//        if (!lockTimeV2Util.checkTimeLock(valid, YPTT_Project_code)) {
//            return valid;
//        }

        // 查询 客户项目
        MetaDataDTOWrapper existingCustomerProject = findCustomerProjectByContractNumber(appid, customerProjectCache,
                Contract_number, Custom_project_name);
        if (Objects.isNull(existingCustomerProject)) {
            valid.addWrongReason(String.format("Customer project [%s] will be added.", Contract_number));
        } else {
            customerProjectCache.add(existingCustomerProject);
        }

        // 查询 唯一标识
        MetaDataDTOWrapper existingUniqueness = findUniquenessByUniquenessField(appid, uniquenessCache,
                uniqueness_field);
        if (Objects.nonNull(existingUniqueness)) {
            uniquenessCache.add(existingUniqueness);
        }
        // 项目 权限验证
        this.validatePerm(context, YPTT_Project_code);

        // 查询 站点
        MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
        if (Objects.isNull(existingSite)) {
            valid.addWrongReason(String.format("Site [%s] will be added.", Site_ID));
        } else {
            siteCache.add(existingSite);
        }

        // 查询 站点条目, 验证站点条目是否关闭
        MetaDataDTOWrapper existingSiteItem = Objects.isNull(existingUniqueness) ? null
                : findSiteItemByUniquenessField(appid, siteItemCache, existingUniqueness.getDataId());
        if (Objects.nonNull(existingSiteItem)) {
            boolean isClosedSiteItem = isClosedSiteItem(existingSiteItem);
            if (isClosedSiteItem) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Site item [%s] is closed.", uniqueness_field));
                return valid;
            } else {
                valid.addWrongReason(String.format("Site item [%s] will be updated.", uniqueness_field));
            }
            siteItemCache.add(existingSiteItem);
        }

        // 查询 PO
        MetaDataDTOWrapper existingPo = findPoByPoNumber(appid, poCache, PO_Number);
        if (Objects.isNull(existingPo)) {
            valid.addWrongReason(String.format("PO [%s] will be added.", PO_Number));
        } else {
            poCache.add(existingPo);
        }

        // 查询 PO条目
        MetaDataDTOWrapper existingPOItem = Objects.isNull(existingUniqueness) ? null
                : findPOItemByUniquenessId(appid, poItemCache, existingUniqueness.getDataId());
        List<Long> roles = getRoles();
        if (Objects.isNull(existingPOItem)
                || (Objects.nonNull(existingPOItem.getDataId()) && Objects.isNull(existingPOItem.getValue("PO")))) {
            valid.addWrongReason(String.format("PO Item [%s] will be added.", uniqueness_field));
            addRequire(raw, context, valid);
        } else {
            //检查时间锁是否能够正确操作
            LockTimeV3Util lockTimeV3Util = new LockTimeV3Util();
            if (lockTimeV3Util.checkTimeLock(valid, YPTT_Project_code, null)) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("The current time is locked and cannot be modified!"));
                return valid;
            }

            BigDecimal dbQuantity = (BigDecimal) existingPOItem.getValue("Quantity");
            BigDecimal dbUnitPrice = (BigDecimal) existingPOItem.getValue("Unit_price");
            BigDecimal milestone_1st = (BigDecimal) existingPOItem.getValue("Milestone_1st");
            BigDecimal milestone_2nd = (BigDecimal) existingPOItem.getValue("Milestone_2nd");
            BigDecimal milestone_3rd = (BigDecimal) existingPOItem.getValue("Milestone_3rd");
            BigDecimal milestone_4th = (BigDecimal) existingPOItem.getValue("Milestone_4th");


            //查看y6 和 y5 的数据， 确认当前数据能否进行修改
            List<Map<String, Object>> report = basicMapper
                    .findProductivityReportByUniquenessId(existingUniqueness.getDataId());
            ImportResultVO valid1 = checkMilestones(valid, YPTT_Project_code, quantity, Unit_price, Milestone_1st, Milestone_2nd, Milestone_3rd, Milestone_4th, roles, lockTimeV3Util, dbQuantity, dbUnitPrice, milestone_1st, milestone_2nd, milestone_3rd, milestone_4th, report);
            if (valid1 != null) return valid1;
            valid.addWrongReason(String.format("PO Item [%s] will be updated.", uniqueness_field));
            poItemCache.add(existingPOItem);
            updateSupport(raw, context, valid);
        }


        return valid;
    }

    @Nullable
    private ImportResultVO checkMilestones(ImportResultVO valid, String YPTT_Project_code, String quantity, String unit_price, String milestone_1st, String milestone_2nd, String milestone_3rd, String milestone_4th, List<Long> roles, LockTimeV3Util lockTimeV3Util, BigDecimal dbQuantity, BigDecimal dbUnitPrice, BigDecimal milestone_1st2, BigDecimal milestone_2nd2, BigDecimal milestone_3rd2, BigDecimal milestone_4th2, List<Map<String, Object>> report) {
        if (CollUtil.isNotEmpty(report)) {
            //如果存在y6的数据 ,判断当前对应的产值是否存在， 不允许修改
            Map<String, Object> map = report.get(0);
            LocalDate report_date_1st = LockTimeV3Util.toLocalDate(map.get("report_date_1st"));
            if (report_date_1st != null && lockTimeV3Util.checkTimeLock(valid, YPTT_Project_code, report_date_1st)) {
                //如果存在第一次产值申报日期 + 当前日期在日期锁定范围之外 不能修改任何当前阶段内容
                //只要存在一次产值申报 就不能修改Unit price 、 quantity 并且不能修改第一次结算金额
                if (dbQuantity != null && dbQuantity.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.numberStr2BigDecimal(quantity, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the quantity cannot be modified");
                    return valid;
                }
                if (dbUnitPrice != null && dbUnitPrice.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.numberStr2BigDecimal(unit_price, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the Unit_price cannot be modified");
                    return valid;
                }
                if (milestone_1st2 != null && milestone_1st2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_1st, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the Milestone_1st cannot be modified");
                    return valid;
                }

            }
            LocalDate report_date_2nd = LockTimeV3Util.toLocalDate(map.get("report_date_2nd"));
            if (report_date_2nd != null && lockTimeV3Util.checkTimeLock(valid, YPTT_Project_code, report_date_2nd)) {
                if (milestone_2nd2 != null && milestone_2nd2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_2nd, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the Milestone_2nd cannot be modified");
                    return valid;
                }
            }
            LocalDate report_date_3rd = LockTimeV3Util.toLocalDate(map.get("report_date_3rd"));
            if (report_date_3rd != null && lockTimeV3Util.checkTimeLock(valid, YPTT_Project_code, report_date_3rd)) {
                if (milestone_3rd2 != null && milestone_3rd2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_3rd, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the Milestone_3rd cannot be modified");
                    return valid;
                }
            }

            LocalDate report_date_4th = LockTimeV3Util.toLocalDate(map.get("report_date_4th"));
            if (report_date_4th != null && lockTimeV3Util.checkTimeLock(valid, YPTT_Project_code, report_date_4th)) {
                if (milestone_4th2 != null && milestone_4th2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_4th, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                    valid.setStatus(ImportResultVO.STATUS_FAILED);
                    valid.addWrongReason("The output value has already been declared, and the Milestone_4th cannot be modified");
                    return valid;
                }
            }
        }

//            List<Map<String, Object>> readySettlement = basicMapper
//                    .findReadySettlementByUniquenessId(existingUniqueness.getDataId());
//            if (CollUtil.isNotEmpty(readySettlement)) {
//                // 如果存在y5的数据
//            }

        if (!roles.contains(1694550407313264642l)) { //不是管理员，就需要校验结算里程碑的数据
            if (milestone_1st2 != null && milestone_1st2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_1st, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Milestone_1st [%s] has been a change", milestone_1st));
                return valid;
            }

            if (milestone_2nd2 != null && milestone_2nd2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_2nd, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Milestone_2nd [%s] has been a change", milestone_2nd));
                return valid;
            }

            if (milestone_3rd2 != null && milestone_3rd2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_3rd, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Milestone_3rd [%s] has been a change", milestone_3rd));
                return valid;
            }

            if (milestone_4th2 != null && milestone_4th2.setScale(4, RoundingMode.HALF_UP).compareTo(MetaDataUtil.percentageStr2BigDecimal(milestone_4th, 4, false).setScale(4, RoundingMode.HALF_UP)) != 0) {
                valid.setStatus(ImportResultVO.STATUS_FAILED);
                valid.addWrongReason(String.format("Milestone_4th [%s] has been a change", milestone_4th));
                return valid;
            }
        }
        return null;
    }

    @NotNull
    private List<Long> getRoles() {
        List<SysRole> roleList = SecurityUtils.getUser().getRoleList();
        List<Long> roles = new ArrayList<>();
        for (SysRole sysRole : roleList) {
            roles.add(sysRole.getRoleId());
        }
        return roles;
    }


    @Override
    public ImportResultVO doTransform(TransformContext context, int index, Map<String, Object> raw) {
        final String appid = context.getAppid();
        final List<MetaDataDTOWrapper> customerProjectCache = context.getCustomerProjectCache();
        final List<MetaDataDTOWrapper> uniquenessCache = context.getUniquenessCache();
        final List<MetaDataDTOWrapper> poCache = context.getPoCache();
        final List<MetaDataDTOWrapper> poItemCache = context.getPoItemCache();
        final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
        final List<MetaDataDTOWrapper> projectCache = context.getProjectCache();
        final List<MetaDataDTOWrapper> incomeExpenditureCache = context.getIncomeExpenditure();

        Dict dict = new Dict(raw);
        String YPTT_Project_code = dict.getStr("YPTT_Project_code");
        String Region = dict.getStr("Region");
        String Site_ID = dict.getStr("Site_ID");
        String Phase = dict.getStr("Phase");
        String Item_code = dict.getStr("Item_code");
        String uniqueness_field = MetaDataUtil.getUniquenessField(dict, GlobalConstants.Y2.NAME);
        String PO_Received_date = dict.getStr("PO_Received_date");
        String PO_Number = dict.getStr("PO_Number");
        String Contract_number = dict.getStr("Contract_number");
        String Custom_project_name = dict.getStr("Custom_project_name");
        String Site_Name = dict.getStr("Site_Name");
        String BOQ_item = dict.getStr("BOQ_item");
        String quantity = dict.getStr("quantity");
        String quantityReduce = dict.getStr("quantity_reduce");
        String Unit_price = dict.getStr("Unit_price");
        String Pre_payment = dict.getStr("Pre_payment");
        String Milestone_1st = dict.getStr("Milestone_1st");
        String Milestone_2nd = dict.getStr("Milestone_2nd");
        String Milestone_3rd = dict.getStr("Milestone_3rd");
        String Milestone_4th = dict.getStr("Milestone_4th");
        String record = dict.getStr("re_record");
        String Remark = dict.getStr("Remark");
        Long userId = SecurityUtils.getUser().getId();
        List<Long> roles = getRoles();

        // 查询YPTTProject
        MetaDataDTOWrapper existingYPTTProject = findYPTTProjectByYPTTProjectCode(appid, projectCache,
                YPTT_Project_code);
        projectCache.add(existingYPTTProject);

        // 初始化 警告阈值
        initWarningThreshold(appid, existingYPTTProject.getDataId(), new MetaDataDTOWrapper());

        // 查询项目权限表
        MetaDataDTOWrapper project = new MetaDataDTOWrapper(
                Collections.singletonList(basicMapper.findProjectPerById(existingYPTTProject.getDataId())));

        // 查询 客户项目
        MetaDataDTOWrapper existingCustomerProject = findCustomerProjectByContractNumber(appid, customerProjectCache,
                Contract_number, Custom_project_name);
        // 客户项目只新增、不更新
        if (Objects.isNull(existingCustomerProject)) {
            existingCustomerProject = new MetaDataDTOWrapper();
            existingCustomerProject.setValue(CUSTOMER_PROJECT_META_ATTR_CONTRACT_NUMBER, Contract_number);
            existingCustomerProject.setValue("Custom_project_name", Custom_project_name);
            saveCustomerProject(appid, existingCustomerProject);
        }
        customerProjectCache.add(existingCustomerProject);

        // 查询 唯一标识
        MetaDataDTOWrapper existingUniqueness = findUniquenessByUniquenessField(appid, uniquenessCache,
                uniqueness_field);
        // 唯一标识 只新增、不更新
        if (Objects.isNull(existingUniqueness)) {
            existingUniqueness = new MetaDataDTOWrapper();
            existingUniqueness.setValue("Project_code", YPTT_Project_code);
            existingUniqueness.setValue("Region", Region);
            existingUniqueness.setValue("Site_ID", Site_ID);
            existingUniqueness.setValue("Phase", Phase);
            existingUniqueness.setValue("Item_code", Item_code);
            // 基础信息
            existingUniqueness.setValue("create_by", userId);
            existingUniqueness.setValue("create_time", LocalDateTime.now());
            existingUniqueness.setValue("update_by", userId);
            existingUniqueness.setValue("update_time", LocalDateTime.now());
            existingUniqueness.setValue(UNI_META_ATTR_UNIQUENESS_FIELD, uniqueness_field);
            saveUniqueness(appid, existingUniqueness);
        }
        uniquenessCache.add(existingUniqueness);
        String uniquenessIdJsonString = MetaDataUtil.handleDataId2Json(existingUniqueness.getDataId());

        // 查询 站点
        MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
        // 站点 只新增、不更新
        if (Objects.isNull(existingSite)) {
            existingSite = new MetaDataDTOWrapper();
            existingSite.setValue("Site_Serial_number", Site_ID);
            existingSite.setValue("Site_register_date", LocalDateTimeUtil.now());
            existingSite.setValue("Site_status", MetaDataUtil.handleValues2Json(SITE_STATUE_UNCLOSE));
            existingSite.setValue("site_name", Site_Name);
            existingSite.setValue("Region", Region);
            // 添加权限
            existingSite.setValue("query", project.getValue("y1_query"));
            existingSite.setValue("update", project.getValue("y1_update"));
            existingSite.setValue("del", project.getValue("y1_del"));
        } else {
            existingSite.setValue("Region", Region);
            existingSite.setValue("site_name", Site_Name);
        }
        saveSite(appid, existingSite);
        siteCache.add(existingSite);

        // 查询 PO
        MetaDataDTOWrapper existingPO = findPoByPoNumber(appid, poCache, PO_Number);
        // PO 只新增、不更新
        if (Objects.isNull(existingPO)) {
            existingPO = new MetaDataDTOWrapper();
            existingPO.setValue("PO_number", PO_Number);
            existingPO.setValue("Project_code", YPTT_Project_code);
            existingPO.setValue("PO_Received_date", DateUtil.parse(PO_Received_date));
            // 添加权限
            existingPO.setValue("query", project.getValue("y2_query"));
            existingPO.setValue("update", project.getValue("y2_update"));
            existingPO.setValue("del", project.getValue("y2_del"));
            savePO(appid, existingPO);
        }
        poCache.add(existingPO);

        BigDecimal reduceBigDecimal = Objects.isNull(quantityReduce) ? new BigDecimal("0")
                : MetaDataUtil.numberStr2BigDecimal(quantityReduce, 4, false);
        BigDecimal quantityBigDecimal = MetaDataUtil.numberStr2BigDecimal(quantity, 4, false);
        BigDecimal unitPrice = MetaDataUtil.numberStr2BigDecimal(Unit_price);
        // po价值
        BigDecimal poValue = Objects.isNull(quantityBigDecimal) ? new BigDecimal("0")
                : quantityBigDecimal.add(reduceBigDecimal).multiply(unitPrice).setScale(6, RoundingMode.HALF_UP);
        BigDecimal prePayment = MetaDataUtil.numberStr2BigDecimal(Pre_payment);

        addY3456789Module(uniquenessIdJsonString, project, context, appid);

        // 查询 站点条目
        final List<MetaDataDTOWrapper> siteItemCache = context.getSiteItemCache();
        MetaDataDTOWrapper existingSiteItem = findSiteItemByUniquenessField(appid, siteItemCache,
                existingUniqueness.getDataId());
        // 新增站点条目信息
        if (Objects.isNull(existingSiteItem)) {
            existingSiteItem = new MetaDataDTOWrapper();
            existingSiteItem.setValue("uniqueness_field", uniquenessIdJsonString);
            existingSiteItem.setValue("warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
            existingSiteItem.setValue("Site_item_status", MetaDataUtil.handleValues2Json(SITE_STATUE_UNCLOSE));
            // 添加权限
            existingSiteItem.setValue("query", project.getValue("y1_query"));
            existingSiteItem.setValue("update", project.getValue("y1_update"));
            existingSiteItem.setValue("del", project.getValue("y1_del"));
            saveSiteItem(existingSite.getDataId(), existingSiteItem);
        }

        // 查询 PO条目
        MetaDataDTOWrapper existingPOItem = findPOItemByUniquenessId(appid, poItemCache,
                existingUniqueness.getDataId());
//		System.out.println("==========================existingPOItem"+existingPOItem);
//		System.out.println("===========================existingPOItem.getDataId()"+existingPOItem.getDataId());
//		System.out.println("=================================existingPOItem.getValue(\"PO\")" + existingPOItem.getValue("PO"));
        if (Objects.isNull(existingPOItem)
                || (Objects.nonNull(existingPOItem.getDataId()) && Objects.isNull(existingPOItem.getValue("PO")))) {
            if (Objects.isNull(existingPOItem)) {
                existingPOItem = new MetaDataDTOWrapper();
                existingPOItem.setValue("uniqueness_field", uniquenessIdJsonString);
            }
            existingPOItem.setValue("Project_code", YPTT_Project_code);
            existingPOItem.setValue("Region", Region);
            existingPOItem.setValue("Site_ID", Site_ID);
            existingPOItem.setValue("Phase", Phase);
            existingPOItem.setValue("Item_code", Item_code);
            existingPOItem.setValue("PO", MetaDataUtil.handleDataId2Json(existingPO.getDataId()));
            existingPOItem.setValue("Contract_number", Contract_number);
            existingPOItem.setValue("Customer_project",
                    MetaDataUtil.handleDataId2Json(existingCustomerProject.getDataId()));
            existingPOItem.setValue("site", MetaDataUtil.handleDataId2Json(existingSite.getDataId()));
            existingPOItem.setValue("Phase", Phase);
            existingPOItem.setValue("Project_code", YPTT_Project_code);
            existingPOItem.setValue("BOQ_item", BOQ_item);
            existingPOItem.setValue("Item_code", Item_code);
            existingPOItem.setValue("Quantity", quantityBigDecimal);
            existingPOItem.setValue("Unit_price", unitPrice);
            existingPOItem.setValue("PO_value", poValue);
            existingPOItem.setValue("PoAmount_Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
            existingPOItem.setValue("siteInfo_Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
            existingPOItem.setValue("Pre_payment", prePayment);
            existingPOItem.setValue("Milestone_1st", MetaDataUtil.numberStr2BigDecimal(Milestone_1st, 4, false));
            existingPOItem.setValue("Milestone_2nd", MetaDataUtil.numberStr2BigDecimal(Milestone_2nd, 4, false));
            existingPOItem.setValue("Milestone_3rd", MetaDataUtil.numberStr2BigDecimal(Milestone_3rd, 4, false));
            existingPOItem.setValue("Milestone_4th", MetaDataUtil.numberStr2BigDecimal(Milestone_4th, 4, false));
            existingPOItem.setValue("Remark", Remark);
            existingPOItem.setValue("re_record", record);
            // 基础信息
            existingPOItem.setValue("create_by", userId);
            existingPOItem.setValue("create_time", LocalDateTime.now());
            existingPOItem.setValue("update_by", userId);
            existingPOItem.setValue("update_time", LocalDateTime.now());

        } else {
            existingPOItem.setValue("Quantity", quantityBigDecimal);
            existingPOItem.setValue("Unit_price", unitPrice);
            existingPOItem.setValue("PO_value", poValue);
            if (StrUtil.isNotBlank(Remark)) {
                String remark = existingPOItem.getValue("Remark") + "\n\n" + StrUtil.emptyIfNull(Remark);
                existingPOItem.setValue("Remark", remark);
            }

            if (roles.contains(1694550407313264642l)) { //管理员在第二次上传y2的时候， 有权限更改以下值
                if (!StringUtils.isBlank(Milestone_1st)){
                    existingPOItem.setValue("Milestone_1st", MetaDataUtil.numberStr2BigDecimal(Milestone_1st, 4, false));
                }
                if (!StringUtils.isBlank(Milestone_2nd)) {
                    existingPOItem.setValue("Milestone_2nd", MetaDataUtil.numberStr2BigDecimal(Milestone_2nd, 4, false));
                }
                if (!StringUtils.isBlank(Milestone_3rd)) {
                    existingPOItem.setValue("Milestone_3rd", MetaDataUtil.numberStr2BigDecimal(Milestone_3rd, 4, false));
                }
                if (!StringUtils.isBlank(Milestone_4th)) {
                    existingPOItem.setValue("Milestone_4th", MetaDataUtil.numberStr2BigDecimal(Milestone_4th, 4, false));
                }
            }
        }
        // 计算poGap
        Object itemValue = existingSiteItem.getValue("Site_value");
        BigDecimal siteValue = Objects.isNull(itemValue) ? BigDecimal.ZERO : new BigDecimal(itemValue.toString());
        // 执行连接器
        y2connector(existingPOItem);
        System.out.println("----------siteValue" + siteValue);
        System.out.println("-----------poValue"+existingPOItem.getValue("PO_value"));
        existingPOItem.setValue("PO_gap",
                siteValue.subtract(MetaDataUtil.handleObject2BigDecimal(existingPOItem.getValue("PO_value"), true)));
        savePOItem(appid, existingPO.getDataId(), existingPOItem);
        poItemCache.add(existingPOItem);

        // 保存关系
        saveRel(existingPO.getDataId(), existingPOItem.getDataId(), context);

        // 查询 收支统计
        MetaDataDTOWrapper existingIncomeExpenditure = findIncomeExpenditureByUniquenessField(appid,
                incomeExpenditureCache, existingUniqueness.getDataId());
        if (Objects.isNull(existingIncomeExpenditure)) {
            existingIncomeExpenditure = new MetaDataDTOWrapper();
            existingIncomeExpenditure.setValue("uniqueness_field",
                    MetaDataUtil.handleDataId2Json(existingUniqueness.getDataId()));
            existingIncomeExpenditure.setValue("project_name",
                    MetaDataUtil.handleDataId2Json(existingYPTTProject.getDataId()));
        }
        existingIncomeExpenditure.setValue("PO_Value", poValue);
        existingIncomeExpenditure.setValue("PO_Value_date", DateUtil.parse(PO_Received_date));
        saveIncomeExpenditure(existingIncomeExpenditure);
        incomeExpenditureCache.add(existingIncomeExpenditure);
        List<Map<String, Object>> report = basicMapper
                .findProductivityReportByUniquenessId(existingUniqueness.getDataId());
        if (CollUtil.isNotEmpty(report)) {
            Map<String, Object> map = report.get(0);
            map.put("PO_number", PO_Number);
            map.put("site_name", Site_Name);
            saveProductivityReport(new MetaDataDTOWrapper(report));
        }
        List<Map<String, Object>> readySettlement = basicMapper
                .findReadySettlementByUniquenessId(existingUniqueness.getDataId());
        if (CollUtil.isNotEmpty(readySettlement)) {
            Map<String, Object> map = readySettlement.get(0);
            map.put("PO_number", PO_Number);
            map.put("Pre_Settlement_date", PO_Received_date);
            saveReadySettlement(new MetaDataDTOWrapper(readySettlement));
        }

        return new ImportResultVO(index, raw, ImportResultVO.STATUS_SUCCEED, "");
    }

    /**
     * 保存po与poItem的关系
     *
     * @param poId     poId
     * @param poItemId poItemId
     */
    private void saveRel(Long poId, Long poItemId, TransformContext context) {
        if (CollUtil.isEmpty(basicMapper.findPoRelPoItem(poId, poItemId))) {
            String now = DateUtil.now();
            Long userId = context.getUserId();
            basicMapper.addPoRelPoItem(IdUtil.getSnowflakeNextId(), userId, poId, poItemId);
        }
    }

    @Override
    public boolean support(TransformContext context) {
        return Objects.equals(MODULE_NAME, context.getModuleName());
    }

    /**
     * 客户合同编码 查询 客户项目
     *
     * @param appid          appid
     * @param localStore     本地缓存
     * @param customerNumber 客户合同编码
     * @param customerName   客户合同名字
     * @return 客户项目
     */
    private MetaDataDTOWrapper findCustomerProjectByContractNumber(String appid, List<MetaDataDTOWrapper> localStore,
                                                                   String customerNumber, String customerName) {
        List<Map<String, Object>> contractNumber = basicMapper.findCustomerProjectByContractNumber(customerNumber,
                customerName);
        return CollUtil.isEmpty(contractNumber) ? null : new MetaDataDTOWrapper(contractNumber);
    }

    /**
     * 保存 客户项目
     *
     * @param appid           appid
     * @param customerProject 客户项目
     */
    private void saveCustomerProject(String appid, MetaDataDTOWrapper customerProject) {
        Map<String, Object> readySettlementMap = customerProject.toMap();
        customerProject.clearNullValue();
        if (Objects.isNull(customerProject.getDataId())) {
            Long dataId = IdUtil.getSnowflakeNextId();
            readySettlementMap.put("id", dataId);
            basicMapper.saveItemData(readySettlementMap, viewConfProperties.getCustomerProject().getTableName());
            customerProject.setDataId(dataId);
        } else {
            basicMapper.updateItemData(readySettlementMap, viewConfProperties.getCustomerProject().getTableName());
        }
    }

    /**
     * 保存唯一标识
     *
     * @param appid      appid
     * @param uniqueness 唯一标识完整信息
     */
    private void saveUniqueness(String appid, MetaDataDTOWrapper uniqueness) {
        Map<String, Object> uniquenessMap = uniqueness.toMap();
        if (Objects.isNull(uniqueness.getDataId())) {
            Long dataId = IdUtil.getSnowflakeNextId();
            uniquenessMap.put("id", dataId);
            basicMapper.saveItemData(uniquenessMap, viewConfProperties.getUniqueIdentification().getTableName());
            uniqueness.setDataId(dataId);
        } else {
            basicMapper.updateItemData(uniquenessMap, viewConfProperties.getUniqueIdentification().getTableName());
        }
    }

    /**
     * PO编号 查询 PO完整信息
     *
     * @param appid      appid
     * @param localStore 本地缓存
     * @param PO_number  PO编号
     * @return PO完整信息
     */
    private MetaDataDTOWrapper findPoByPoNumber(String appid, List<MetaDataDTOWrapper> localStore, String PO_number) {
        MetaDataDTOWrapper hit = localFindOne(localStore, PO_META_ATTR_PO_NUMBER, PO_number);
        if (Objects.nonNull(hit)) {
            return hit;
        }
        List<Map<String, Object>> poByPoNumber = basicMapper.findPoByPoNumber(PO_number);
        return CollUtil.isEmpty(poByPoNumber) ? null : new MetaDataDTOWrapper(poByPoNumber);
    }

    /**
     * 保存站点信息
     *
     * @param appid appid
     * @param site  站点信息
     */
    private void saveSite(String appid, MetaDataDTOWrapper site) {
        Map<String, Object> siteMap = site.toMap();
        if (Objects.isNull(site.getDataId())) {
            Long dataId = IdUtil.getSnowflakeNextId();
            siteMap.put("id", dataId);
            basicMapper.saveItemData(siteMap, viewConfProperties.getSite().getTableName());
            site.setDataId(dataId);
        } else {
            basicMapper.updateItemData(siteMap, viewConfProperties.getSite().getTableName());

        }
    }

    /**
     * 保存 PO信息
     *
     * @param appid appid
     * @param po    PO信息
     */
    private void savePO(String appid, MetaDataDTOWrapper po) {
        Map<String, Object> poMap = po.toMap();
        if (Objects.isNull(po.getDataId())) {
            Long dataId = IdUtil.getSnowflakeNextId();
            poMap.put("id", dataId);
            basicMapper.saveItemData(poMap, viewConfProperties.getPo().getTableName());
            po.setDataId(dataId);
        } else {
            basicMapper.updateItemData(poMap, viewConfProperties.getPo().getTableName());
        }
    }

}
