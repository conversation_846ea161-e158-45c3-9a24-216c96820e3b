package com.pig4cloud.pig.yptt.service.standingbook;

import com.pig4cloud.pig.yptt.entity.dto.CustomerProjectStandingBookDTO;
import com.pig4cloud.pig.yptt.mapper.CustomerProjectStandingBookMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerProjectStandingBookUpdater extends StandingBookUpdater<CustomerProjectStandingBookDTO> {

	private final CustomerProjectStandingBookMapper customerProjectStandingBookMapper;

	@Override
	protected String getName() {
		return "CustomerProject";
	}

	@Override
	protected List<CustomerProjectStandingBookDTO> generate(int i, int size) {
		return customerProjectStandingBookMapper.generateCustomerProjectStandingBookList(i, size);
	}

	@Override
	protected int save(CustomerProjectStandingBookDTO customerProjectStandingBookDTO) {
		return customerProjectStandingBookMapper.update(customerProjectStandingBookDTO);
	}

}
