package com.pig4cloud.pig.yptt.service.fs;

import com.pig4cloud.pig.yptt.mapper.PersonalLoansAndPrepaymentsMapper;
import com.pig4cloud.pig.yptt.service.ViewModelRelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * @ClassName: 个人借款和预付款
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-08-12  10:39
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PersonalLoansAndPrepaymentsService {
    private final PersonalLoansAndPrepaymentsMapper personalLoansAndPrepaymentsMapper;
    private final ViewModelRelService viewModelRelService;

    private final String modelTable = "person_loan_dt2";
    /**
     * 冲销借款
     **/
    public Boolean writeOffLoan(Long dataId, BigDecimal money) {
        // 获取借款记录物理表
        String modelTableName = viewModelRelService.getModelTableNameByModelName(modelTable);
        // 扣减借款金额
        boolean flag = personalLoansAndPrepaymentsMapper.updateSYWH(modelTableName, dataId, money);

        if(!flag){
            return false;
        }
        return true;
    }
}