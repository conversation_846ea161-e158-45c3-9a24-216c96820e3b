package com.pig4cloud.pig.yptt.service.fs;

import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.yptt.controller.ApiRes;
import com.pig4cloud.pig.yptt.mapper.PaymentApplicationMapper;
import com.pig4cloud.pig.yptt.mapper.ViewModelRel;
import com.pig4cloud.pig.yptt.service.ViewModelRelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: PaymentApplicationService
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-07-29  14:29
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentApplicationService {
    private final PaymentApplicationMapper paymentApplicationMapper;
    private final ViewModelRel viewModelRel;
    private final ViewModelRelService viewModelRelService;

    private final String leftModelName = "payment_application";
    private final String rightModelName = "payment_a_dt1";

    /**
     * @description: 付款完成后修改状态
     * @param: dataId 数据id
     * @author: lijianpan
     **/
    public ApiRes paymentEndByMainDataId(Long mainDataId,List<Long> dataId,BigDecimal paymentMoney) {
        // 获取关联物理表名
        String modelRelTableName = null;
        try {
            modelRelTableName = viewModelRelService.getModelRelTableNameByModelName(leftModelName, rightModelName);

            if(StrUtil.isBlank(modelRelTableName)){
                return ApiRes.failed("未找到付款申请单与付款明细的关联表");
            }
        }catch (Exception e){
            return ApiRes.failed(e.getMessage());
        }

        // 获取付款明细表名
        String rightModelTable = viewModelRelService.getModelTableNameByModelName(rightModelName);
        // 获取付款申请单表名
        String leftModelTable = viewModelRelService.getModelTableNameByModelName(leftModelName);

        Map<String,Object> params = new HashMap<>();
        params.put("dataIds",dataId);
        params.put("paymentMoney",paymentMoney);

        if (!UpdatePayStatus(mainDataId,modelRelTableName,leftModelTable,rightModelTable,params)){
            return ApiRes.failed("修改支付状态失败");
        }

        return ApiRes.ok("");
    }

    /**
     * @description: 付款完成后修改状态
     * @param: dataId 数据id
     * @param: mainDataId 主数据id
     * @author: lijianpan
     **/
    public ApiRes paymentEndByMainDataId(Long mainDataId,Long dataId,BigDecimal paymentMoney) {
        // 获取关联物理表名
        String modelRelTableName = null;
        try {
            modelRelTableName = viewModelRelService.getModelRelTableNameByModelName(leftModelName, rightModelName);

            if(StrUtil.isBlank(modelRelTableName)){
                return ApiRes.failed("未找到付款申请单与付款明细的关联表");
            }
        }catch (Exception e){
            return ApiRes.failed(e.getMessage());
        }

        // 获取付款明细表名
        String rightModelTable = viewModelRelService.getModelTableNameByModelName(rightModelName);
        // 获取付款申请单表名
        String leftModelTable = viewModelRelService.getModelTableNameByModelName(leftModelName);

        Map<String,Object> params = new HashMap<>();
        params.put("dataId",dataId);
        params.put("paymentMoney",paymentMoney);

        if (!UpdatePayStatus(mainDataId,modelRelTableName,leftModelTable,rightModelTable,params)){
            return ApiRes.failed("修改支付状态失败");
        }

        return ApiRes.ok("");
    }

    private Boolean UpdatePayStatus(Long mainDataId, String modelRelTableName, String leftModelTable, String rightModelTable, Map<String,Object> params){
        // 获取付款明细数据id
        List<Long> dataIds = viewModelRel.getRightModelDataIdByLeftModelDataId(mainDataId, modelRelTableName);
        // 计算实付金额
        Long excludeDataId = null;
        BigDecimal realPaymentMoney = BigDecimal.ZERO;
        if(params!=null){
            excludeDataId = (Long) params.get("dataId");
            realPaymentMoney = realPaymentMoney.add((BigDecimal) params.get("paymentMoney"));
            log.info("本次付款金额：{}", realPaymentMoney);
        }
        if (dataIds.size()>0){
            realPaymentMoney = realPaymentMoney.add(paymentApplicationMapper.getRealPaymentMoney(rightModelTable, dataIds,excludeDataId));
        }

        log.info("实际已付款金额：{}", realPaymentMoney);

        // 获取应该付款的金额
        BigDecimal paymentMoney = paymentApplicationMapper.getPaymentMoney(leftModelTable,mainDataId);

        log.info("应付金额：{}", paymentMoney);

        String status = "[\"0\"]";
        // 如果实付金额大于等于应付金额，则修改状态
        if (realPaymentMoney.compareTo(paymentMoney) >= 0){
            status = "[\"1\"]";
            log.info("付款完成");
        }

        log.info("修改状态：{}", status);

        boolean flag = paymentApplicationMapper.updateStatus(leftModelTable,status,mainDataId);

        if (!flag){
            log.error("修改支付状态失败");
            return false;
        }
        log.info("修改支付状态成功");
        return true;
    }
}