package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.pig4cloud.pig.admin.api.entity.SysUser;
import com.pig4cloud.pig.admin.api.feign.RemoteUserServiceV2;
import com.pig4cloud.pig.base.coms.consts.NoticeConstant;
import com.pig4cloud.pig.base.coms.entity.ComsNotice;
import com.pig4cloud.pig.base.coms.feign.RemoteNoticeService;
import com.pig4cloud.pig.common.core.constant.SecurityConstants;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.consts.SkipParams;
import com.pig4cloud.pig.me.api.consts.SymbolEnum;
import com.pig4cloud.pig.me.api.dto.modelOperation.BatchOperationParams;
import com.pig4cloud.pig.me.api.dto.operation.*;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.yptt.config.RuntimeVisibleThreadPoolExecutor;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.controller.ApiRes;
import com.pig4cloud.pig.yptt.entity.ModuleRolePer;
import com.pig4cloud.pig.yptt.entity.UserRoleInfo;
import com.pig4cloud.pig.yptt.entity.dto.ProjectRolePermissionMapDTO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.mapper.RoleMapper;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataPermissionsService {

	private final ViewConfProperties viewConfProperties;

	private final RemoteNoticeService remoteNoticeService;

	private final TransformManager transformManager;

	private final RemoteAppService remoteAppService;

	private final RoleMapper roleMapper;

	private final BasicMapper basicMapper;

	private final ThreadPoolExecutor refreshPerExecutor;

	private final RemoteUserServiceV2 remoteUserServiceV2;

	private final List<String> SKIP_PARAMS = Arrays.asList(SkipParams.SKIP_DELETE_POST, SkipParams.SKIP_DELETE_PRE,
			SkipParams.SKIP_INSERT_POST, SkipParams.SKIP_INSERT_PRE, SkipParams.SKIP_UPDATE_POST,
			SkipParams.SKIP_UPDATE_PRE);

	/**
	 * 新增成员更新权限
	 * @param dto 新增dto
	 * @return Boolean
	 */
	public Boolean saveMember(JSONObject dto) {
//		long userId = Long.parseLong(new JSONArray(dto.getValue("Project_Member")).get(0).toString());
//		String projectRole = dto.getValue("Project_Role").toString();
		System.out.println("==========================================1");
		Long projectId = dto.getLong("mainDataId");
		Integer integer = basicMapper.selectPermissByProjectId(projectId);
		if (integer == null || integer < 1) {
			String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
			JSONArray jsonArray = new JSONArray();
			jsonArray.put(projectId.toString());
			String jsonString = jsonArray.toString();
			basicMapper.addPermissPro(jsonString, snowflakeNextIdStr);
		}
		JSONObject data = dto.getJSONObject("data");
		Object memberObj = data.get("Project_Member");
		JSONArray project_member = JSONUtil.parseArray(memberObj.toString());
		// 方案1：通过 JSON 序列化/反序列化
		long userId = Long.parseLong((String) project_member.get(0));
		String projectRole = data.getJSONArray("Project_Role").toJSONString();
		updatePer(projectId, Collections.singletonList(new UserRoleInfo(dto.getLong("dataId"), userId, projectRole)),
				GlobalConstants.UpdatePerType.INSERT);
		System.out.println("==========================================2");
		return Boolean.TRUE;
	}

	/**
	 * 新增成员校验
	 * @param dto 新增dto
	 * @return Boolean
	 */
	public ApiRes saveMemberCheck(OperationInsertDTO dto) {
		Long projectId = dto.getRootDataId();
//		long userId = Long.parseLong(new JSONArray(dto.getValue("Project_Member")).get(0).toString());
		String appId = viewConfProperties.getAppId();
		// 查询该用户更新前的数据
		List<QueryDTO> conditions = new ArrayList<>();
		QueryDTO queryDTO = new QueryDTO();
		queryDTO.setName("Project_Member");
		queryDTO.setSymbol(SymbolEnum.EQUAL.getCode());
		queryDTO.setValue(dto.getValue("Project_Member"));
		conditions.add(queryDTO);
		OperationPageDTO operationPageDTO = new OperationPageDTO();
		operationPageDTO.setConditions(conditions);
		operationPageDTO.setRootDataId(projectId);
		operationPageDTO.setMainDataId(projectId);
		operationPageDTO.setViewId(viewConfProperties.getProjectMember().getViewId());
		operationPageDTO.setViewGroupId(viewConfProperties.getProjectMember().getViewGroupId());
		PageResponseDTO pageResponseDTO = remoteAppService.operationPage(operationPageDTO, SecurityConstants.FROM_IN,
				appId);
		if (Objects.nonNull(pageResponseDTO) && CollUtil.isNotEmpty(pageResponseDTO.getRecords())) {
			return ApiRes.failed("user already exists!");
		}
		else {
			return ApiRes.ok(Boolean.TRUE);
		}
	}

	/**
	 * 更新成员-更新权限
	 * @param dto 更新
	 * @return Boolean
	 */
	public Boolean updateMember(BatchOperationParams.Payload dto) {
		Long projectId = roleMapper.getProjectIdByMemberId(dto.getDataId());
		Object memberObj = dto.getData().get("Project_Member");
		JSONArray project_member = JSONUtil.parseArray(memberObj.toString());
	// 方案1：通过 JSON 序列化/反序列化
		long userId = Long.parseLong((String) project_member.get(0));

		// 安全获取并转换Project_Role对象
		Object rawProjectRole = dto.getData().get("Project_Role");

		JSONArray projectRoleArray = JSONUtil.parseArray(rawProjectRole.toString());
//		JSONObject projectRoleObj = null;
//
//		try {
//			// 处理可能的类型转换问题
//			if (rawProjectRole instanceof JSONObject) {
//				projectRoleObj = (JSONObject) rawProjectRole;
//			} else {
//				// 兼容其他JSON库的反序列化结果
//				projectRoleObj = JSONObject.parseObject(JSONObject.toJSONString(rawProjectRole));
//			}
//		} catch (Exception e) {
//			throw new IllegalArgumentException("Project_Role字段解析失败", e);
//		}
//
//// 安全获取value数组
//		com.alibaba.fastjson.JSONArray valueArray = new com.alibaba.fastjson.JSONArray();
//		if (projectRoleObj != null && projectRoleObj.containsKey("value")) {
//			try {
//				valueArray = projectRoleObj.getJSONArray("value");
//			} catch (ClassCastException ce) {
//				// 处理单值转数组的情况
//				Object singleValue = projectRoleObj.get("value");
//				if (singleValue != null) {
//					valueArray.add(singleValue);
//				}
//			}
//		}


		String projectRole = projectRoleArray.toString();

// 构造用户角色对象（增加空值校验）
		if (StringUtils.isBlank(projectRole)) {
			throw new IllegalArgumentException("项目角色不能为空");
		}
		UserRoleInfo userRoleInfo = new UserRoleInfo(dto.getDataId(), userId, projectRole);
		updatePer(projectId, Collections.singletonList(userRoleInfo), GlobalConstants.UpdatePerType.UPDATE);
		return Boolean.TRUE;
	}

	/**
	 * 更新成员-校验
	 * @param projectId 项目id
	 * @param list 更新用户集合
	 * @param operation 操作(增删改){@link GlobalConstants.UpdatePerType}
	 */
	private void updatePer(Long projectId, List<UserRoleInfo> list, String operation) {
		String appId = viewConfProperties.getAppId();
		SecurityContext context = SecurityContextHolder.getContext();
		// 异步执行更新
		refreshPerExecutor.execute(() -> {
			SecurityContextHolder.setContext(context);
			// 查询项目最新用户数据
			List<UserRoleInfo> userRoleInfo = basicMapper.getUserRoleInfo(projectId);
			// check数据
			if (Objects.equals(operation, GlobalConstants.UpdatePerType.INSERT)) {
				userRoleInfo.addAll(list);
			}
			else if (Objects.equals(operation, GlobalConstants.UpdatePerType.UPDATE)) {
				for (UserRoleInfo roleInfo : userRoleInfo) {
					UserRoleInfo info = list.get(0);
					if (Objects.equals(roleInfo.getProjectMemberId(), info.getProjectMemberId())) {
						roleInfo.setRoleIds(info.getRoleIds());
						roleInfo.setUserId(info.getUserId());
					}
				}
			}
			else if (Objects.equals(operation, GlobalConstants.UpdatePerType.DEL)) {
				userRoleInfo.removeIf(info -> list.stream()
					.map(UserRoleInfo::getUserId)
					.collect(Collectors.toList())
					.contains(info.getUserId()));
			}
			// 去重
			ArrayList<UserRoleInfo> collect = userRoleInfo.stream()
				.collect(Collectors.collectingAndThen(
						Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(UserRoleInfo::getUserId))),
						ArrayList::new));
			Map<String, Set<Long>> initPerUpdate = initPerUpdate();
			if (CollUtil.isNotEmpty(collect)) {
				// 更新最新权限数据表权限
				for (UserRoleInfo record : collect) {
					Map<Object, ModuleRolePer> perMap = getPerMap(record, initPerUpdate);
				}
			}
			Map<String, Object> updatePer = new HashMap<>(39);
			initPerUpdate.forEach((k, v) -> updatePer.put(k, new JSONArray(v).toString()));
			updatePer.put("project", projectId);
			System.out.println("updatePer"+updatePer);
			basicMapper.updateProjectPerByProjectId(updatePer);

			for (UserRoleInfo info : list) {
				Long userId = info.getUserId();
				// 发消息通告
				sendMsg(msgInfo(userId, projectId), "PROJECT PERMISSIONS TABLE UPDATE",
						Arrays.asList(userId, SecurityUtils.getUser().getId()));
			}
		});
	}

	/**
	 * 删除成员更新权限
	 * @param o 删除
	 * @return Boolean
	 */
	public Boolean delMember(BatchOperationParams.Payload o) {
		List<Long> dataIds = o.getDataIds();
		if (CollUtil.isEmpty(dataIds)) {
			return Boolean.TRUE;
		}
		Long projectId = roleMapper.getProjectIdByMemberId(dataIds.get(0));
//		Long projectId = o.getMainDataId();
		List<UserRoleInfo> userIdList = basicMapper.getUserIdByMemberId(dataIds);
		updatePer(projectId, userIdList, GlobalConstants.UpdatePerType.DEL);
		return Boolean.TRUE;
	}

	/**
	 * 查询 项目角色权限矩阵 列表
	 * @param ypttProjectCode 项目编号
	 * @param userId 用户id
	 * @return 项目角色权限矩阵 列表
	 */
	public List<ProjectRolePermissionMapDTO> queryProjectRolePermMapList(String ypttProjectCode, Long userId) {
		List<ProjectRolePermissionMapDTO> perms = roleMapper.queryProjectRolesByProjectCode(ypttProjectCode, userId);
		log.trace("query perms: conditions: {},{}, perms: {}", ypttProjectCode, userId, perms);
		return perms;
	}

	/**
	 * 查询 项目角色权限矩阵 列表
	 * @param ypttProjectDataId 项目数据id
	 * @param userId 用户id
	 * @return 项目角色权限矩阵 列表
	 */
	public List<ProjectRolePermissionMapDTO> queryProjectRolePermMapList(Long ypttProjectDataId, Long userId) {
		List<ProjectRolePermissionMapDTO> perms = roleMapper.queryProjectRolesByProjectId(ypttProjectDataId, userId);
		log.trace("query perms: conditions: {},{}, perms: {}", ypttProjectDataId, userId, perms);
		return perms;
	}

	/**
	 * 获取role-module的map
	 * @param dto 数据
	 * @return map
	 */
	private Map<Object, ModuleRolePer> getPerMap(UserRoleInfo dto, Map<String, Set<Long>> initPerUpdate) {
		Long userId = dto.getUserId();
		Map<Object, ModuleRolePer> modulePerMap = new HashMap<>(12);
		String appId = viewConfProperties.getAppId();
		Optional<Object> projectRoleOpt = Optional.ofNullable(dto.getRoleIds());
		if (projectRoleOpt.isPresent()) {
			Object role = projectRoleOpt.get();
			List<String> roleList = new ArrayList<>();
			for (Object o : new JSONArray(role.toString())) {
				roleList.add(o.toString());
			}
			Assert.isTrue(CollUtil.isNotEmpty(roleList), "No right to restrict access!");
			List<ModuleRolePer> roleModuleList = roleMapper.getRoleModuleList(roleList);
			modulePerMap = mergeModulePer(roleModuleList);
		}
		modulePerMap.forEach((k, v) -> {
			Set<Long> query = initPerUpdate.get(k + StrUtil.UNDERLINE + GlobalConstants.UpdatePerType.QUERY);
			Set<Long> update = initPerUpdate.get(k + StrUtil.UNDERLINE + GlobalConstants.UpdatePerType.UPDATE);
			Set<Long> del = initPerUpdate.get(k + StrUtil.UNDERLINE + GlobalConstants.UpdatePerType.DEL);
			if (Objects.equals(v.getQuery(), 1)) {
				query.add(userId);
			}
			if (Objects.equals(v.getUpdate(), 1)) {
				update.add(userId);
			}
			if (Objects.equals(v.getDel(), 1)) {
				del.add(userId);
			}
			initPerUpdate.put(k + StrUtil.UNDERLINE + GlobalConstants.UpdatePerType.QUERY, query);
			initPerUpdate.put(k + StrUtil.UNDERLINE + GlobalConstants.UpdatePerType.UPDATE, update);
			initPerUpdate.put(k + StrUtil.UNDERLINE + GlobalConstants.UpdatePerType.DEL, del);
		});
		return modulePerMap;
	}

	private HashMap<String, Set<Long>> initPerUpdate() {
		HashMap<String, Set<Long>> perUpdate = new HashMap<>(39);
		for (String s : Arrays.asList("y1", "y2", "y3", "y4", "y5", "y6", "y7", "y8", "y9")) {
			perUpdate.put(s + StrUtil.UNDERLINE + GlobalConstants.UpdatePerType.QUERY, new HashSet<>());
			perUpdate.put(s + StrUtil.UNDERLINE + GlobalConstants.UpdatePerType.UPDATE, new HashSet<>());
			perUpdate.put(s + StrUtil.UNDERLINE + GlobalConstants.UpdatePerType.DEL, new HashSet<>());
		}
		return perUpdate;
	}

	/**
	 * 合并不同角色相同的权限
	 * @param roleModuleList 模型权限集合
	 * @return map
	 */
	private HashMap<Object, ModuleRolePer> mergeModulePer(List<ModuleRolePer> roleModuleList) {
		HashMap<Object, ModuleRolePer> resMap = new HashMap<>(12);
		for (ModuleRolePer moduleRolePer : roleModuleList) {
			if (StrUtil.isNotBlank(moduleRolePer.getModuleList())) {
				for (Object o : new JSONArray(moduleRolePer.getModuleList())) {
					if (resMap.containsKey(o)) {
						ModuleRolePer temp = resMap.get(o);
						Integer query = temp.getQuery();
						Integer update = temp.getUpdate();
						Integer del = temp.getDel();
						temp.setQuery(Objects.equals(query, 1) ? query : moduleRolePer.getQuery());
						temp.setUpdate(Objects.equals(update, 1) ? update : moduleRolePer.getUpdate());
						temp.setDel(Objects.equals(del, 1) ? del : moduleRolePer.getDel());
					}
					else {
						ModuleRolePer save = new ModuleRolePer();
						BeanUtil.copyProperties(moduleRolePer, save, "moduleList");
						resMap.put(o, save);
					}
				}
			}
		}
		return resMap;
	}

	public String msgInfo(Long userId, Long projectId) {
		String msg = "【Project Name: {projectName},Project Code: {projectCode},User Name: {userName},Phone: {phone}】Your project permissions have been updated.";
		SysUser user = remoteUserServiceV2.getUserById(userId, SecurityConstants.FROM_IN);
		if (Objects.nonNull(user)) {
			Dict msgDict = new Dict();
			Dict project = new Dict(roleMapper.getProjectById(projectId));
			msgDict.set("projectName", project.getStr("YPTT_Project_name"));
			msgDict.set("projectCode", project.getStr("YPTT_Project_code"));
			msgDict.set("userName", user.getFullname());
			msgDict.set("phone", user.getPhone());
			return StrUtil.format(msg, msgDict);
		}
		return msg;
	}

	/**
	 * 发送通告
	 * @param msg 信息
	 * @param title 标题
	 * @param userId 被通知用户集合
	 */
	public void sendMsg(String msg, String title, List<Long> userId) {
		log.info("发送消息通知,msg: [{}]", msg);
		ComsNotice comsNotice = new ComsNotice();
		comsNotice.setTitle(title);
		comsNotice.setMsgContent(msg);
		comsNotice.setPriority(NoticeConstant.PRIORITY_M);
		comsNotice.setMsgCategory(NoticeConstant.TYPE_ANNOUNCEMENT);
		comsNotice.setReceiverType(NoticeConstant.ReceiverType.RECEIVER_TYPE_TENANT_USER);
		comsNotice.setTenantIds(viewConfProperties.getTenantId().toString());
		if (CollUtil.isEmpty(userId)) {
			comsNotice.setUserIds("1");
			comsNotice.setSender("system");
		}
		else {
			StringJoiner joiner = new StringJoiner(",");
			userId.stream().distinct().map(String::valueOf).forEach(joiner::add);
			comsNotice.setUserIds(joiner.toString());
			comsNotice.setSender(SecurityUtils.getUser().getName());
		}
		comsNotice.setOpenType(NoticeConstant.OPEN_TYPE_URL);
		comsNotice.setSendStatus(NoticeConstant.NO_SEND);
		comsNotice.setStartTime(Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()));
		comsNotice.setEndTime(Date.from(LocalDate.now().plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()));
		// 新增并发布通知信息
		remoteNoticeService.addAndReleaseNotice(comsNotice, SecurityConstants.FROM_IN);
		log.info("==========发送消息通知结束=========");
	}

	/**
	 * 检验是否有编辑权限
	 * @param dto 更细dto
	 * @return ApiRes
	 */
	public ApiRes updatePerCheck(OperationUpdateDTO dto) {
		String perType = getModelTypePer(dto.getViewId(), GlobalConstants.UpdatePerType.UPDATE);
		MetaDataDTOWrapper metaDataDTOWrapper = new MetaDataDTOWrapper(dto);
		Object projectCode = metaDataDTOWrapper.getValue("Project_code");
		if (Objects.isNull(projectCode)) {
			Object siteId = metaDataDTOWrapper.getValue("Site_ID");
			if (Objects.isNull(siteId)) {
				return ApiRes.failed("NO EDITING PERMISSION !");
			}
			projectCode = roleMapper.getProjectCodeBySiteId(siteId.toString());
		}
		// 判断是否有编辑权限
		String userIdStr = roleMapper.getUserIdListByPerType(perType, projectCode.toString());
		JSONArray jsonArray = JSONUtil.parseArray(userIdStr);
		if (jsonArray.contains(SecurityUtils.getUser().getId())) {
			return ApiRes.ok("SUCCESS");
		}
		return ApiRes.failed("NO EDITING PERMISSION !");
	}

	/**
	 * 获取模型类型权限字段
	 * @param viewId 模型id
	 * @param operation 操作
	 * @return String
	 */
	private String getModelTypePer(Long viewId, String operation) {
		String modelType = "";
		if (viewId.equals(viewConfProperties.getSiteItem().getWarnViewId())) {
			modelType = "y1";
		}
		else if (viewId.equals(viewConfProperties.getSite().getWarnViewId())) {
			modelType = "y1";
		}
		else if (viewId.equals(viewConfProperties.getPoItem().getWarnViewId())) {
			modelType = "y2";
		}
		else if (viewId.equals(viewConfProperties.getPo().getWarnViewId())) {
			modelType = "y2";
		}
		else if (viewId.equals(viewConfProperties.getSiteDelivery().getWarnViewId())) {
			modelType = "y3";
		}
		else if (viewId.equals(viewConfProperties.getSubconPO().getWarnViewId())) {
			modelType = "y4";
		}
		else if (viewId.equals(viewConfProperties.getSubconPOItem().getWarnViewId())) {
			modelType = "y4";
		}
		else if (viewId.equals(viewConfProperties.getSubconPayment().getWarnViewId())) {
			modelType = "y8";
		}
		else if (viewId.equals(viewConfProperties.getYpttSettlement().getWarnViewId())) {
			modelType = "y9";
		}
		return modelType + StrUtil.UNDERLINE + operation;
	}

}
