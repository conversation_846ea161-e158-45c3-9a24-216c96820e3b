package com.pig4cloud.pig.yptt.service.standingbook;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.yptt.bizcode.ExpectedOneButGotMany;
import com.pig4cloud.pig.yptt.entity.dto.ProjectStandingBookDTO;
import com.pig4cloud.pig.yptt.mapper.ProjectStandingBookMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectStandingBookUpdater extends StandingBookUpdater<ProjectStandingBookDTO> {

	private final ProjectStandingBookMapper projectStandingBookMapper;

	@Override
	protected String getName() {
		return "ProjectStandingBook";
	}

	@Override
	protected List<ProjectStandingBookDTO> generate(int i, int size) {
		return projectStandingBookMapper.generateProjectStandingBookList(i, size);
	}

	@Override
	protected int save(ProjectStandingBookDTO generateProjectStandingBookDTO) {
		ProjectStandingBookDTO existingVersion = queryProjectStandingBookByProjectId(
				generateProjectStandingBookDTO.getProjectId());
		if (Objects.isNull(existingVersion)) {
			generateProjectStandingBookDTO.setId(IdUtil.getSnowflakeNextId());
			return projectStandingBookMapper.save(generateProjectStandingBookDTO);
		}
		else {
			return projectStandingBookMapper.update(generateProjectStandingBookDTO);
		}
	}

	private ProjectStandingBookDTO queryProjectStandingBookByProjectId(Long projectId) {
		List<ProjectStandingBookDTO> results = projectStandingBookMapper.queryByProjectId(projectId);
		if (CollUtil.isEmpty(results)) {
			return null;
		}
		else if (CollUtil.size(results) > 1) {
			throw new BizException(ExpectedOneButGotMany.EXPECT_ONE_BUT_GOT_MANY, projectId);
		}
		return CollUtil.getFirst(results);
	}

}
