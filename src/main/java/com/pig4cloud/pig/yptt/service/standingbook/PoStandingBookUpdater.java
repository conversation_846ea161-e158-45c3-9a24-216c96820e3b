package com.pig4cloud.pig.yptt.service.standingbook;

import com.pig4cloud.pig.yptt.entity.dto.PoStandingBookDTO;
import com.pig4cloud.pig.yptt.mapper.PoStandingBookMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PoStandingBookUpdater extends StandingBookUpdater<PoStandingBookDTO> {

	private final PoStandingBookMapper poStandingBookMapper;

	@Override
	protected String getName() {
		return "PoStandingBook";
	}

	@Override
	protected List<PoStandingBookDTO> generate(int i, int size) {
		return poStandingBookMapper.generatePoStandingBookList(i, size);
	}

	@Override
	protected int save(PoStandingBookDTO dto) {
		return poStandingBookMapper.update(dto);
	}

}
