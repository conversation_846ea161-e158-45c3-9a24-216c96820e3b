package com.pig4cloud.pig.yptt.service.workflow;

import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlow.request.RequestInfo;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlow.request.TableInfo;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlowDTO;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlowDataDTO;
import com.pig4cloud.pig.yptt.mapper.ViewModelRel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: WorkFlowServiceCommon
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-07-24  16:32
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkFlowServiceCommon {
    private final ViewModelRel viewModelRel;

    public RequestInfo getRequestInfo(WorkFlowDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("request cannot be null");
        }

        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setRequest(request);

        try {
            requestInfo.setTableInfo(iterativePackagSubTableInfo(request));
        } catch (Exception e) {
            throw new RuntimeException("Failed to process requestInfo.tableInfo", e);
        }

        return requestInfo;
    }


    public TableInfo iterativePackagSubTableInfo(WorkFlowDTO request) {
        TableInfo tableInfo = new TableInfo();
        tableInfo.setDataId(request.getDataId());
        tableInfo.setViewId(request.getViewId());
        tableInfo.setViewGroupId(request.getViewGroupId());

        if (request.getData() != null) {
            Map<String, WorkFlowDataDTO> data = new HashMap<>();
            for (WorkFlowDataDTO item : request.getData()) {
                if (item != null && item.getName() != null) {
                    data.put(item.getName(), item);
                }
            }
            tableInfo.setData(data);
        }

        try {
            if (StrUtil.isNotEmpty(request.getViewId())) {
                tableInfo.setModelName(viewModelRel.getModelNameByViewId(Long.valueOf(request.getViewId())));
            }
        } catch (NumberFormatException e) {
            // 处理 viewId 转换异常
            throw new IllegalArgumentException("Invalid viewId format: " + request.getViewId(), e);
        }

        List<WorkFlowDTO> subList = request.getSub();
        if (subList != null && !subList.isEmpty()) {
            List<TableInfo> sub = new ArrayList<>();

            for (WorkFlowDTO item : subList) {
                if (item != null) {
                    sub.add(iterativePackagSubTableInfo(item));
                }
            }

            tableInfo.setSub(sub);
        }

        return tableInfo;
    }
}