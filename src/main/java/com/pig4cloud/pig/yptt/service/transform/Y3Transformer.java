package com.pig4cloud.pig.yptt.service.transform;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.mapper.ImportMapper;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.DataPermissionsService;
import com.pig4cloud.pig.yptt.utils.ApplicationContextUtil;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import com.pig4cloud.pig.yptt.utils.MetaDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Y3站点交付信息 转为 供应商、唯一标识、站点交付信息
 *
 * TODO 导入模板中，日期格式固定，并配备相关说明；导入模板中，金额格式固定？
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
@Component
@Slf4j
public class Y3Transformer extends AbstractTransformer {

	private final static String MODULE_NAME = GlobalConstants.Y3.NAME;

	public Y3Transformer(RemoteAppService remoteAppService, ViewConfProperties viewConfProperties,
			DataPermissionsService dataPermissionsService, BasicMapper basicMapper, RedissonClient redissonClient,
			DataMangeService dataMangeService) {
		super(remoteAppService, viewConfProperties, dataPermissionsService, basicMapper, redissonClient,
				dataMangeService);
	}

	@Override
	public ImportResultVO doValidate(TransformContext context, int index, Map<String, Object> raw,
			ImportResultVO valid) {
		final String appid = context.getAppid();
		final List<MetaDataDTOWrapper> uniquenessCache = context.getUniquenessCache();
		final List<MetaDataDTOWrapper> subconCache = context.getSubconCache();
		final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
		final List<MetaDataDTOWrapper> siteItemCache = context.getSiteItemCache();
		final List<MetaDataDTOWrapper> poItemCache = context.getPoItemCache();
		final List<MetaDataDTOWrapper> siteDeliveryInfoCache = context.getSiteDeliveryInfoCache();

		Dict dict = new Dict(raw);
		String Site_ID = dict.getStr("Site_ID");
		String uniqueness_field = MetaDataUtil.getUniquenessField(dict, GlobalConstants.Y3.NAME);
		String Site_belong_to = dict.getStr("Site_belong_to");

		// 查询 唯一标识
		MetaDataDTOWrapper existingUniqueness = findUniquenessByUniquenessField(appid, uniquenessCache,
				uniqueness_field);
		if (Objects.isNull(existingUniqueness)) {
			valid.setStatus(ImportResultVO.STATUS_FAILED);
			valid.addWrongReason(
					String.format("Uniqueness [%s] not found, please add site or PO first.", uniqueness_field));
			return valid;
		}
		else {
			uniquenessCache.add(existingUniqueness);
		}

		// 项目 权限验证
		final String ypttProjectCode = (String) existingUniqueness.getValue(UNI_META_ATTR_PROJECT_CODE);
		this.validatePerm(context, ypttProjectCode);

		// 查询 站点条目, 验证站点条目是否关闭
		MetaDataDTOWrapper existingSiteItem = findSiteItemByUniquenessField(appid, siteItemCache,
				existingUniqueness.getDataId());
		if (Objects.nonNull(existingSiteItem) && Objects.nonNull(existingSiteItem.getValue("site"))) {
			boolean isClosedSiteItem = isClosedSiteItem(existingSiteItem);
			if (isClosedSiteItem) {
				valid.setStatus(ImportResultVO.STATUS_FAILED);
				valid.addWrongReason(String.format("Site item [%s] is closed.", uniqueness_field));
				return valid;
			}
			siteItemCache.add(existingSiteItem);
		}
		else {
			valid.setStatus(ImportResultVO.STATUS_FAILED);
			valid.addWrongReason(
					String.format("Site item [%s] has not been added yet. Please add the site item first", Site_ID));
			return valid;
		}

		// 查询 po条目
		// MetaDataDTOWrapper existingPoItem = findPOItemByUniquenessId(appid,
		// poItemCache,
		// existingUniqueness.getDataId());
		// if (Objects.nonNull(existingPoItem) &&
		// Objects.isNull(existingPoItem.getValue("PO"))) {
		// valid.setStatus(ImportResultVO.STATUS_FAILED);
		// valid.addWrongReason(String.format("PO item [%s] has not been added yet. Please
		// add the PO item first.",
		// existingUniqueness.getValue("uniqueness_field")));
		// return valid;
		// }

		// 查询 站点
		MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
		if (Objects.isNull(existingSite)) {
			valid.addWrongReason(String.format("Site [%s] has not been added yet. Please add the site first", Site_ID));
			return valid;
		}
		else {
			siteCache.add(existingSite);
		}

		// 查询 Subcon
		MetaDataDTOWrapper existingSubcon = findSubconBySubconName(appid, subconCache, Site_belong_to);
		if (Objects.isNull(existingSubcon)) {
			valid.addWrongReason(String.format("Subcon [%s] will be added.", Site_belong_to));
		}
		else {
			subconCache.add(existingSubcon);
		}

		// 查询 站点条目交付信息
		MetaDataDTOWrapper existingSiteDeliveryInfo = findSiteDeliveryInfoByUniquenessId(appid, siteDeliveryInfoCache,
				existingUniqueness.getDataId());
		if (Objects.isNull(existingSiteDeliveryInfo) || (Objects.nonNull(existingSiteDeliveryInfo.getDataId())
				&& Objects.isNull(existingSiteDeliveryInfo.getValue("Subcon")))) {
			valid.addWrongReason(String.format("Site delivery info [%s] will be added.", uniqueness_field));
			addRequire(raw, context, valid);
		}
		else {
			valid.addWrongReason(String.format("Site delivery info [%s] will be updated.", uniqueness_field));
			siteDeliveryInfoCache.add(existingSiteDeliveryInfo);
			updateSupport(raw, context, valid);
		}

		return valid;
	}

	@Override
	public ImportResultVO doTransform(TransformContext context, int index, Map<String, Object> raw) {
		final String appid = context.getAppid();
		final List<MetaDataDTOWrapper> uniquenessCache = context.getUniquenessCache();
		final List<MetaDataDTOWrapper> subconCache = context.getSubconCache();
		final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
		final List<MetaDataDTOWrapper> siteItemCache = context.getSiteItemCache();
		final List<MetaDataDTOWrapper> siteDeliveryInfoCache = context.getSiteDeliveryInfoCache();
		final List<MetaDataDTOWrapper> projectCache = context.getProjectCache();
		final List<MetaDataDTOWrapper> poItemCache = context.getPoItemCache();

		Dict dict = new Dict(raw);
		String YPTT_Project_code = dict.getStr("YPTT_Project_code");
		String Region = dict.getStr("Region");
		String Site_ID = dict.getStr("Site_ID");
		String Phase = dict.getStr("Phase");
		String Item_code = dict.getStr("Item_code");
		String BOQ_item = dict.getStr("BOQ_item");
		String uniqueness_field = MetaDataUtil.getUniquenessField(dict, GlobalConstants.Y3.NAME);
		String Site_belong_to = dict.getStr("Site_belong_to");
		String Team_Leader_DT = dict.getStr("Team_Leader_DT");
		String engineer_DTA_SPV = dict.getStr("engineer_DTA_SPV");
		String PLO_PC_Others = dict.getStr("PLO_PC_Others");
		String PIC_PC_PM = dict.getStr("PIC_PC_PM");
		String Start_Working_date = dict.getStr("Start_Working_date");
		String Completed_work_date = dict.getStr("Completed_work_date");
		String air_CI_Report_submit = dict.getStr("air_CI_Report_submit");
		String Site_manager_Report = dict.getStr("Site_manager_Report");
		String E_ATP_Pass = dict.getStr("E_ATP_Pass");
		String F_PAC_Pass = dict.getStr("F_PAC_Pass");
		String G_FAC = dict.getStr("G_FAC");
		String Remark = dict.getStr("Remark");
		String settlement_1st = dict.getStr("settlement_1st");
		String settlement_2nd = dict.getStr("settlement_2nd");
		String settlement_3rd = dict.getStr("settlement_3rd");
		String SubconSettlement_1st = dict.getStr("SubconSettlement_1st");
		String SubconSettlement_2nd = dict.getStr("SubconSettlement_2nd");
		String SubconSettlement_3rd = dict.getStr("SubconSettlement_3rd");
		String record = dict.getStr("re_record");

		// 查询 唯一标识
		MetaDataDTOWrapper existingUniqueness = findUniquenessByUniquenessField(appid, uniquenessCache,
				uniqueness_field);
		// 查询 站点
		MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
		MetaDataDTOWrapper existingSiteItem = findSiteItemByUniquenessField(appid, siteItemCache,
				existingUniqueness.getDataId());
		// 查询 项目
		MetaDataDTOWrapper existingYPTTProject = findYPTTProjectByYPTTProjectCode(appid, projectCache,
				YPTT_Project_code);

		// 查询 Subcon
		MetaDataDTOWrapper existingSubcon = findSubconBySubconName(appid, subconCache, Site_belong_to);
		// Subcon 只新增、不更新
		if (Objects.isNull(existingSubcon)) {
			existingSubcon = new MetaDataDTOWrapper();
			existingSubcon.setValue(SUBCON_META_ATTR_SUBCON_NAME, Site_belong_to);
			saveSubcon(appid, existingSubcon);
		}
		subconCache.add(existingSubcon);

		String uniquenessIdJsonString = MetaDataUtil.handleDataId2Json(existingUniqueness.getDataId());

		// 查询 站点条目交付信息
		MetaDataDTOWrapper existingSiteDeliveryInfo = findSiteDeliveryInfoByUniquenessId(appid, siteDeliveryInfoCache,
				existingUniqueness.getDataId());

		if (Objects.isNull(existingSiteDeliveryInfo) || (Objects.nonNull(existingSiteDeliveryInfo.getDataId())
				&& Objects.isNull(existingSiteDeliveryInfo.getValue("Site_belong_to")))) {
			if (Objects.isNull(existingSiteDeliveryInfo)) {
				existingSiteDeliveryInfo = new MetaDataDTOWrapper();
				existingSiteDeliveryInfo.setValue("uniqueness_field", uniquenessIdJsonString);
				existingSiteDeliveryInfo.setValue("Project_code", existingUniqueness.getValue("Project_code"));
				existingSiteDeliveryInfo.setValue("Start_Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
				existingSiteDeliveryInfo.setValue("Check_Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
				existingSiteDeliveryInfo.setValue("SubconPo_Warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
			}

			existingSiteDeliveryInfo.setValue("BOQ_item", existingSiteItem.getValue("BOQ_item"));
			existingSiteDeliveryInfo.setValue("Project_code", YPTT_Project_code);
			existingSiteDeliveryInfo.setValue("Project_name", existingYPTTProject.getValue("YPTT_Project_name"));
			existingSiteDeliveryInfo.setValue("Phase", Phase);
			existingSiteDeliveryInfo.setValue("Site_ID", Site_ID);
			existingSiteDeliveryInfo.setValue("site_name", existingSite.getValue("site_name"));
			existingSiteDeliveryInfo.setValue("Item_code", Item_code);
			existingSiteDeliveryInfo.setValue("Site_Model", existingSiteItem.getValue("Site_model"));
			existingSiteDeliveryInfo.setValue("Subcon", MetaDataUtil.handleDataId2Json(existingSubcon.getDataId()));
			existingSiteDeliveryInfo.setValue("remark", Remark);
			// 基础信息
			Long userId = SecurityUtils.getUser().getId();
			existingSiteDeliveryInfo.setValue("create_by", userId);
			existingSiteDeliveryInfo.setValue("create_time", LocalDateTime.now());
			existingSiteDeliveryInfo.setValue("update_by", userId);
			existingSiteDeliveryInfo.setValue("update_time", LocalDateTime.now());
		}
		if (StrUtil.isNotBlank(Remark)) {
			String remark = existingSiteDeliveryInfo.getValue("remark") + "\n\n" + Remark;
			existingSiteDeliveryInfo.setValue("remark", remark);
		}

		existingSiteDeliveryInfo.setValue("Site_belong_to", Site_belong_to);
		existingSiteDeliveryInfo.setValue("Team_Leader_DT", Team_Leader_DT);
		existingSiteDeliveryInfo.setValue("PIC_PC_PM", PIC_PC_PM);
		existingSiteDeliveryInfo.setValue("engineer_DTA_SPV", engineer_DTA_SPV);
		existingSiteDeliveryInfo.setValue("PLO_PC_Others", PLO_PC_Others);
		existingSiteDeliveryInfo.setValue("Start_Working_date", DateUtil.parse(Start_Working_date));
		existingSiteDeliveryInfo.setValue("Completed_work_date", DateUtil.parse(Completed_work_date));
		existingSiteDeliveryInfo.setValue("air_CI_Report_submit", DateUtil.parse(air_CI_Report_submit));
		existingSiteDeliveryInfo.setValue("Site_manager_Report", DateUtil.parse(Site_manager_Report));
		existingSiteDeliveryInfo.setValue("E_ATP_Pass", DateUtil.parse(E_ATP_Pass));
		existingSiteDeliveryInfo.setValue("F_PAC_Pass", DateUtil.parse(F_PAC_Pass));
		existingSiteDeliveryInfo.setValue("G_FAC", DateUtil.parse(G_FAC));
		existingSiteDeliveryInfo.setValue("re_record", record);

		saveSiteDeliveryInfo(existingSiteDeliveryInfo);
		siteDeliveryInfoCache.add(existingSiteDeliveryInfo);

		//查询 po条目
		MetaDataDTOWrapper existingPoItem = findPOItemByUniquenessId(appid, poItemCache, existingUniqueness.getDataId());
		if (Objects.nonNull(existingPoItem) &&
				Objects.nonNull(existingPoItem.getValue("PO"))) {
			BigDecimal po_value = (BigDecimal) existingPoItem.getValue("PO_value");
//			1: Y611:决算日期（KPI Archive date），这个日期等于Y315的日期
//			2：Y612：决算金额（KPI Archive amount），如果Y315有日期，则金额自动等于 Y215 PO value
			ImportMapper importMapper = ApplicationContextUtil.getBean(ImportMapper.class);
			try {
				//查询y2的信息
				Map<String, Object> mapsY2 = importMapper.selectY2Amount(uniqueness_field);
//				log.info("=====================mapsY2::" + mapsY2);
				Long id = (Long) mapsY2.get("id");
				//更改y6 信息
				if (E_ATP_Pass != null && id != null) {
					importMapper.updateY6(DateUtil.parse(E_ATP_Pass), po_value, id);
				}
			} catch (Exception e) {
				log.info("更新y6 信息错误");
			}
		}
		return new ImportResultVO(index, raw, ImportResultVO.STATUS_SUCCEED, "");

	}

	@Override
	public boolean support(TransformContext context) {
		return Objects.equals(MODULE_NAME, context.getModuleName());
	}

	/**
	 * 供应商名称 查询 供应商
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param subconName 供应商名称
	 * @return 供应商
	 */
	private MetaDataDTOWrapper findSubconBySubconName(String appid, List<MetaDataDTOWrapper> localStore,
			String subconName) {
		MetaDataDTOWrapper hit = localFindOne(localStore, SUBCON_META_ATTR_SUBCON_NAME, subconName);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> subconBySubconName = basicMapper.findSubconBySubconName(subconName);
		return CollUtil.isEmpty(subconBySubconName) ? null : new MetaDataDTOWrapper(subconBySubconName);
	}

	/**
	 * 保存 供应商
	 * @param appid appid
	 * @param subcon 供应商
	 */
	private void saveSubcon(String appid, MetaDataDTOWrapper subcon) {
		Map<String, Object> readySettlementMap = subcon.toMap();
		subcon.clearNullValue();
		if (Objects.isNull(subcon.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			readySettlementMap.put("id", dataId);
			basicMapper.saveItemData(readySettlementMap, viewConfProperties.getSubcon().getTableName());
			subcon.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(readySettlementMap, viewConfProperties.getSubcon().getTableName());
		}
	}

}
