package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.constants.LockConstants;
import com.pig4cloud.pig.yptt.controller.ApiRes;
import com.pig4cloud.pig.yptt.entity.dto.AbilityOperateDateDTO;
import com.pig4cloud.pig.yptt.entity.dto.LockDataDTO;
import com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo;

import com.pig4cloud.pig.yptt.mapper.LockDataTimeMapper;
import com.pig4cloud.pig.yptt.utils.LockTimeV2Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName LockDataTimeService
 * @Description
 * @date 2025/3/18 9:36
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LockDataTimeService {
    protected final RedissonClient redissonClient;
    private final LockDataTimeMapper lockDataTimeMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public ApiRes execData(LockDataDTO lockDataDTO) {
        // 参数校验
        if (lockDataDTO == null || lockDataDTO.getStartTime() == null ||
                lockDataDTO.getEndTime() == null || StringUtils.isBlank(lockDataDTO.getModule()) ||
                ObjectUtils.isEmpty(lockDataDTO.getProjectCodes())) {
            throw new IllegalArgumentException("The start/end date or module or projectCode cannot be empty");
        }

        if (lockDataDTO.getStartTime().isAfter(lockDataDTO.getEndTime())) {
            throw new IllegalArgumentException("Start time must be before end time");
        }

        LocalDate startTime = lockDataDTO.getStartTime();
        LocalDate endTime = lockDataDTO.getEndTime();
        String module = lockDataDTO.getModule().toLowerCase();
        List<String> projectCodes = lockDataDTO.getProjectCodes();
        String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();

        for (String projectCode : projectCodes) {
            try {
                String key = projectCode + "," + module;
                Map<String, LockDataTimeVo> redisValue = getRedisValue(module, projectCode);

                if (redisValue == null || redisValue.isEmpty()) {
                    // 新增时间锁
                    lockDataTimeMapper.execData(startTime, endTime, module, snowflakeNextIdStr, projectCode);
                } else {
                    LockDataTimeVo lockDataTimeVo = redisValue.get(key);
                    LocalDate existingStart = lockDataTimeVo.getStartTime();
                    LocalDate existingEnd = lockDataTimeVo.getEndTime();

                    // 计算新的时间范围（取并集）
                    LocalDate newStart = startTime.isBefore(existingStart) ? startTime : existingStart;
                    LocalDate newEnd = endTime.isAfter(existingEnd) ? endTime : existingEnd;

                    lockDataTimeMapper.updateTime(newStart, newEnd, module, projectCode);
                }

                setRedis(module, projectCode);
            } catch (Exception e) {
                log.error("Failed to process lock data for project: {}", projectCode, e);
                // 考虑是否继续处理其他项目还是直接返回错误
            }
        }
        return ApiRes.ok("success");
    }

    /**
     * 获取module + projectCode的最小startTime和最大endTime
     *
     * @return Map<String, LockDataVO> key为module，value为对应的时间范围
     */
    public Map<String, LockDataTimeVo> getRedisValue(String module, String projectCode) {
        List<LockDataTimeVo> list = lockDataTimeMapper.list(module, projectCode);
        if (ObjectUtils.isEmpty(list)) {
            log.warn("LockDataVO list is empty.");
            return new HashMap<>(); // 返回空Map，避免返回null
        }

        // 使用Map来存储每个module的最小startTime和最大endTime
        Map<String, LockDataTimeVo> moduleTimeMap = new HashMap<>();

        for (LockDataTimeVo lockDataVO : list) {
            try {
                // 检查module是否为null
                if (lockDataVO.getModuleType() == null) {
                    log.warn("Found null module in LockDataVO: {}", lockDataVO);
                    continue; // 如果module为null，跳过当前记录
                }

                String moduleType = lockDataVO.getModuleType();
                LocalDate startTime = lockDataVO.getStartTime();
                LocalDate endTime = lockDataVO.getEndTime();
                String voProjectCode = lockDataVO.getProjectCode();
                String key = voProjectCode + "," + moduleType;
                // 检查startTime和endTime是否为null
                if (startTime == null || endTime == null) {
                    log.warn("Found null startTime or endTime in LockDataVO: {}", lockDataVO);
                    continue; // 如果startTime或endTime为null，跳过当前记录
                }

                // 如果Map中已经存在该key，则更新最小startTime和最大endTime
                if (moduleTimeMap.containsKey(key)) {
                    LockDataTimeVo existingData = moduleTimeMap.get(key);

                    // 更新最小startTime
                    if (startTime.isBefore(existingData.getStartTime())) {
                        existingData.setStartTime(startTime);
                    }

                    // 更新最大endTime
                    if (endTime.isAfter(existingData.getEndTime())) {
                        existingData.setEndTime(endTime);
                    }
                } else {
                    // 如果Map中不存在该key，则直接放入Map中
                    moduleTimeMap.put(key, lockDataVO);
                }
            } catch (Exception e) {
                log.error("Error processing LockDataVO: {}", lockDataVO, e);
            }
        }

        return moduleTimeMap;
    }

    /**
     * 根据project+moduleType从Redis中获取LockDataVO
     *
     * @param
     * @return LockDataVO 对应模块的时间范围
     */
    public LockDataTimeVo getRedisValueByModule(String module, String projectCode) {
        String key = projectCode + "," + module;
        if (key == null) {
            log.warn("ModuleType is null.");
            return null;
        }
        try {
            Object o = redisTemplate.opsForValue().get(GlobalConstants.lockDateTimeRedisKey + key);
            if (ObjectUtils.isEmpty(o)) {
                setRedis(module, projectCode); // 如果Redis中没有数据，更新Redis
                o = redisTemplate.opsForValue().get(GlobalConstants.lockDateTimeRedisKey + key);
            }

            if (o instanceof LockDataTimeVo) {
                return (LockDataTimeVo) o;
            } else {
                log.warn("Invalid data type in Redis for moduleType: {}", key);
                return null;
            }
        } catch (Exception e) {
            log.error("Error getting Redis value for moduleType: {}", key, e);
            return null;
        }
    }

    /**
     * 更新Redis中的值
     */
    public void setRedis(String module, String projectCode) {
        try {
            Map<String, LockDataTimeVo> redisValue = getRedisValue(module, projectCode);
            if (ObjectUtils.isEmpty(redisValue)) {
                log.warn("No data to update in Redis.");
                return;
            }

            // 输出或返回每个key的时间范围
            for (Map.Entry<String, LockDataTimeVo> entry : redisValue.entrySet()) {
                String key = entry.getKey();
                LockDataTimeVo timeRange = entry.getValue();

                try {
                    redisTemplate.setValueSerializer(RedisSerializer.java());
                    redisTemplate.opsForValue().set(GlobalConstants.lockDateTimeRedisKey + key, timeRange);
                    log.info("Updated Redis for module: {}, Min StartTime: {}, Max EndTime: {}",
                            key, timeRange.getStartTime(), timeRange.getEndTime());
                } catch (Exception e) {
                    log.error("Error updating Redis for module: {}", key, e);
                }
            }
        } catch (Exception e) {
            log.error("Error updating Redis.", e);
        }
    }

    public R<Page<LockDataTimeVo>> list(String module, String projectCode, Integer page, Integer size) {
        List<String> projectCodes = new ArrayList<>();
        if (StringUtils.isNotBlank(projectCode)) {
            String[] split = projectCode.split(",");
            projectCodes.addAll(Arrays.asList(split));
        } else {
            projectCodes = null;
        }
        Page<LockDataTimeVo> pageMap = new Page<>(page, size);
        Page<LockDataTimeVo> lockDataTimeVoPage = lockDataTimeMapper.selectPage(pageMap, module, projectCodes);
        return R.ok(lockDataTimeVoPage);
    }

    public ApiRes del(List<String> idList) {
        if (ObjectUtils.isEmpty(idList) || idList.size() < 1) {
            return ApiRes.ok("success");
        }
        try {
            List<LockDataTimeVo> lockDataTimeVos = lockDataTimeMapper.selectByIds(idList);
            for (LockDataTimeVo lockDataTimeVo : lockDataTimeVos) {
                String moduleType = lockDataTimeVo.getModuleType();
                String projectCode = lockDataTimeVo.getProjectCode();
                String key = projectCode + "," + moduleType;
                delRedis(key, idList);
                setRedis(moduleType, projectCode);
            }

        } catch (Exception e) {
            return ApiRes.ok("failed");
        }

        return ApiRes.ok("success");
    }

    void delRedis(String key, List<String> idList) {
        try {
            if (redisTemplate.hasKey(GlobalConstants.lockDateTimeRedisKey + key)) {
                redisTemplate.delete(GlobalConstants.lockDateTimeRedisKey + key);
                lockDataTimeMapper.del(idList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public ApiRes abilityOperateDate(AbilityOperateDateDTO abilityOperateDateDTO) {
        // todo  记录下操作人
        // 参数校验
        if (abilityOperateDateDTO == null || abilityOperateDateDTO.getStartTime() == null ||
                abilityOperateDateDTO.getEndTime() == null || ObjectUtils.isEmpty(abilityOperateDateDTO.getProjectCodes())) {
            throw new IllegalArgumentException("The start/end date or module or projectCode cannot be empty");
        }

        if (abilityOperateDateDTO.getStartTime().isAfter(abilityOperateDateDTO.getEndTime())) {
            throw new IllegalArgumentException("Start time must be before end time");
        }

        LocalDate startTime = abilityOperateDateDTO.getStartTime();
        LocalDate endTime = abilityOperateDateDTO.getEndTime();
        List<String> projectCodes = abilityOperateDateDTO.getProjectCodes();

        try {
            LockTimeV2Util lockTimeV2Util = new LockTimeV2Util();
            //获取所有释放日期数据
            Map<String, LockDataTimeVo> lockDataTimMap = getReleaseDate(null);

            for (String projectCode : projectCodes) {
                String redisKey = lockTimeV2Util.getRedisKey(projectCode);
                if (StringUtils.isBlank(redisKey)) {
                    throw new IllegalArgumentException("projectCode is error!");
                }
                if (!lockDataTimMap.containsKey(projectCode)) {//插入数据库
                    String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
                    lockDataTimeMapper.addData(startTime, endTime, projectCode, LockConstants.type_lock, snowflakeNextIdStr);
                    LockDataTimeVo lockDataTimeVo = new LockDataTimeVo();
                    lockDataTimeVo.setEndTime(endTime);
                    lockDataTimeVo.setStartTime(startTime);
                    lockDataTimeVo.setProjectCode(projectCode);
                    lockDataTimeVo.setId(Long.valueOf(snowflakeNextIdStr));
                    setRedisV2(redisKey, lockDataTimeVo);
                } else {

                    lockDataTimeMapper.updateTimeV2(startTime, endTime, projectCode);
                    LockDataTimeVo lockDataTimeVo = lockDataTimMap.get(projectCode);
                    lockDataTimeVo.setEndTime(endTime);
                    lockDataTimeVo.setStartTime(startTime);
                    //更新相关redis数据
                    setRedisV2(redisKey, lockDataTimeVo);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("error" + e.getMessage());
        }
        return ApiRes.ok("success");
    }


    //获取相关释放日期
    public Map<String, LockDataTimeVo> getReleaseDate(List<String> projectCodes) {
        Map<String, LockDataTimeVo> lockDataTimMap = new HashMap<>();
        List<LockDataTimeVo> lockDataTimeVos = lockDataTimeMapper.selectAllV2(projectCodes);
        for (LockDataTimeVo lockDataTimeVo : lockDataTimeVos) {
            lockDataTimMap.put(lockDataTimeVo.getProjectCode(), lockDataTimeVo);
        }
        return lockDataTimMap;
    }

    /**
     * 更新Redis中的值
     */
    public void setRedisV2(String redisKey, LockDataTimeVo lockDataTimeVo) {
        try {
            try {
                redisTemplate.setValueSerializer(RedisSerializer.java());
                redisTemplate.opsForValue().set(redisKey, lockDataTimeVo);
                log.info("Updated Redis for module: {}, Min StartTime: {}, Max EndTime: {}",
                        redisKey, lockDataTimeVo.getStartTime(), lockDataTimeVo.getEndTime());
            } catch (Exception e) {
                log.error("Error updating Redis for module: {}", redisKey, e);
            }
        } catch (Exception e) {
            log.error("Error updating Redis.", e);
        }
    }

    public R<Page<LockDataTimeVo>> listV2(String projectCode, Integer page, Integer size) {
        List<String> projectCodes = new ArrayList<>();
        if (StringUtils.isNotBlank(projectCode)) {
            String[] split = projectCode.split(",");
            projectCodes.addAll(Arrays.asList(split));
        } else {
            projectCodes = null;
        }
        Page<LockDataTimeVo> pageMap = new Page<>(page, size);
        Page<LockDataTimeVo> lockDataTimeVoPage = lockDataTimeMapper.selectPageV2(pageMap, projectCodes);
        return R.ok(lockDataTimeVoPage);
    }

    public LockDataTimeVo getRedisValueByModuleV2(String projectCode, String key) {
        if (StringUtils.isBlank(key)) {
            log.warn("ModuleType is null.");
            return null;
        }
        try {
            Object o = redisTemplate.opsForValue().get(key);
            if (ObjectUtils.isEmpty(o)) {
                return null;
            }

            if (o instanceof LockDataTimeVo) {
                return (LockDataTimeVo) o;
            } else {
                log.warn("Invalid data type in Redis for moduleType: {}", key);
                return null;
            }
        } catch (Exception e) {
            log.error("Error getting Redis value for moduleType: {}", key, e);
            return null;
        }
    }
}
