package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.yptt.entity.BaseReportDataQueryParam;
import com.pig4cloud.pig.yptt.entity.vo.ReportDataFiledVO;
import com.pig4cloud.pig.yptt.mapper.OptimizeBasicMapper;
import com.pig4cloud.pig.yptt.utils.DataFilterOptimized;
import com.pig4cloud.pig.yptt.utils.LockTimeV3Util;
import com.pig4cloud.pig.yptt.utils.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName OptimizeBiPanelService
 * @Description 优化BiPanelService执行效率
 * @date 2025/4/8 11:21
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OptimizeBiPanelService {
    private final OptimizeBasicMapper basicMapper;

    private final ExecutorService executorService = Executors.newFixedThreadPool(9);

    /**
     * 解析逗号分隔的 moduleTypes 字符串为 Set 集合。
     *
     * @param moduleTypes 逗号分隔的模块类型字符串 (例如 "Y1,Y2,Y3")
     * @return 包含模块类型代码的 Set 集合。如果输入为 null 或空，则包含所有模块 ("Y1" 到 "Y9")。
     */
    private Set<String> parseModuleTypes(String moduleTypes) {
        // 如果 moduleTypes 为空，默认需要查询所有模块的数据
        if (!StringUtils.hasText(moduleTypes)) {
            return new HashSet<>(Arrays.asList("Y1", "Y2", "Y3", "Y4", "Y5", "Y6", "Y7", "Y8", "Y9"));
        }
        // 按逗号分割，去除空白，过滤空字符串，收集到 Set 中
        return Arrays.stream(moduleTypes.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }

    /**
     * 从基础数据列表中提取 uf 表的 ID (主键)。
     *
     * @param baseData 基础数据列表，每个 Map 至少应包含 "ufId" 键。
     * @return 去重后的 uf ID 列表。
     */
    private List<String> extractUfIds(List<Map<String, Object>> baseData) {
        if (CollectionUtils.isEmpty(baseData)) {
            return Collections.emptyList();
        }
        return baseData.stream()
                // 假设基础数据查询中 uf.id 被别名为 "ufId"
                .map(row -> row.get("ufId").toString())
                .filter(Objects::nonNull) // 过滤掉 null 值
                .distinct() // 去重
                .collect(Collectors.toList());
    }

    /**
     * 从基础数据列表中提取 uf 表的 uniqueness_field (通常作为与其他模块关联的键)。
     *
     * @param baseData 基础数据列表，每个 Map 至少应包含 "siteUn" 键。
     * @return 去重后的 uniqueness_field 列表。
     */
    private List<String> extractUniquenessFields(List<Map<String, Object>> baseData) {
        if (CollectionUtils.isEmpty(baseData)) {
            return Collections.emptyList();
        }
        return baseData.stream()
                // 假设基础数据查询中 uf.uniqueness_field 被别名为 "siteUn"
                .map(row -> (String) row.get("siteUn"))
                .filter(Objects::nonNull) // 过滤掉 null 值
                .distinct() // 去重
                .collect(Collectors.toList());
    }

    BaseReportDataQueryParam createParmBase( String dateStrStart,
                         String dateStrEnd,
                        String dateType,
                        String area,
                        String nation,
                        List<String> projectIds){
        BaseReportDataQueryParam baseReportDataQueryParam = new BaseReportDataQueryParam();
        baseReportDataQueryParam.setArea(area);
        baseReportDataQueryParam.setDateStrEnd(dateStrEnd);
        baseReportDataQueryParam.setDateStrStart(dateStrStart);
        baseReportDataQueryParam.setDateType(dateType);
        baseReportDataQueryParam.setNation(nation);
        baseReportDataQueryParam.setProjectIds(projectIds);
        return baseReportDataQueryParam;
    }

    /**
     * 生成报表数据。
     * 该方法通过分步查询和在业务逻辑层组装数据来替代单一复杂 SQL。
     *
     * @param projectId    单个项目 ID (可能用于 toProjectIds 辅助方法)
     * @param dateStrStart 查询起始日期字符串 (格式: 'YYYY-MM-DD')
     * @param dateStrEnd   查询结束日期字符串 (格式: 'YYYY-MM-DD')
     * @param dateType     日期过滤类型 (例如 "ufCreateTime", "projectStartDate")，用于 SQL 判断过滤哪个日期字段
     * @param area         区域过滤条件
     * @param projectIds   逗号分隔的多个项目 ID 字符串 (可由 toProjectIds 处理)
     * @param moduleTypes  逗号分隔的需要包含的数据模块代码 ("Y1", "Y2"...)，为空则表示所有模块
     * @param nation       国家/地区过滤条件
     * @return 组装好的报表数据列表，每个 Map 代表一行数据。
     */
    public ReportDataFiledVO getReportFiled(Page<Map<String, Object>> page, String projectId, String dateStrStart, String dateStrEnd, String dateType, String area, String projectIds, String moduleTypes, String nation, String unId) {
        ReportDataFiledVO reportDataFiledVO = new ReportDataFiledVO();
        String heads = getHeard(moduleTypes);
        reportDataFiledVO.setHeaders(JSONUtil.toList(heads, ReportDataFiledVO.Header.class));
        long startTime = System.currentTimeMillis(); // 记录开始时间
        // --- 1. 准备工作 ---
        // 如果日期范围无效，则清除日期类型，避免无效的日期过滤
        if (!StringUtils.hasText(dateStrStart) || !StringUtils.hasText(dateStrEnd)) {
            dateType = null;
        }
        // 处理项目 ID，将单个 ID 和逗号分隔的 ID 列表合并为一个集合
        List<String> targetProjectIds = toProjectIds(projectId, projectIds);
        // 如果没有有效的项目 ID，可以直接返回空结果或抛出异常
        if (CollectionUtils.isEmpty(targetProjectIds) && org.apache.commons.lang.StringUtils.isBlank(nation)) {

            System.err.println("警告: 未提供有效的项目 ID  或者国籍 用于报表查询。");
            return reportDataFiledVO;
        }

        // 解析需要查询的数据模块
        Set<String> activeModules = parseModuleTypes(moduleTypes);
        System.out.println("需要加载的模块: " + activeModules); // 打印日志，方便调试

        // --- 2. 获取基础数据 ---
        // 查询核心的 uf 表以及必须关联的 YPTTProject, site, dept 等表信息
        // 应用项目、日期、区域、国家等核心过滤条件
        // **关键**: 此查询需要返回 uf.id (别名 ufId) 和 uf.uniqueness_field (别名 siteUn) 作为后续关联的键
        System.out.println("开始查询基础数据...");
        List<String> projectIdList = null;
        if (ObjectUtils.isNotEmpty(targetProjectIds)){
            projectIdList = new ArrayList<>(targetProjectIds);
        }
        Integer current = (int) page.getCurrent();
        Integer size = (int)  page.getSize();
        List<String> listUnIds = new ArrayList<>();
        if (unId != null && !"".equals(unId)) {
            String[] split = unId.split(",");
            for (String s : split) {
                listUnIds.add(s);
            }
        }

        List<Map<String, Object>> baseReportData = basicMapper.getBaseReportData(
                dateStrStart, dateStrEnd, dateType, area, nation,
                projectIdList, listUnIds);
        List<Map<String, Object>> baseDatas = new DataFilterOptimized().baseDataFilter(baseReportData, dateStrStart, dateStrEnd, dateType);
        Page<Map<String, Object>> pageInfo = PageUtil.getPageInfo(current, size, baseDatas);
        List<Map<String, Object>> baseData = pageInfo.getRecords();
        // 如果没有找到任何基础数据，直接返回空列表
        if (CollectionUtils.isEmpty(baseData)) {
            System.out.println("未找到符合条件的基础数据。");
            return reportDataFiledVO;
        }
        System.out.println("基础数据查询完成，共获取 " + baseData.size() + " 条记录。");

        // --- 3. 准备数据组装容器 ---
        // 使用 Map 来存储最终结果，Key 是 uf.uniqueness_field (siteUn)，Value 是该行完整数据的 Map
        // 这样可以方便地根据 siteUn 将后续查询到的模块数据合并到对应的行中
        // 使用 Function.identity() 表示值就是原始的 Map 对象
        // (existing, replacement) -> existing 表示如果遇到重复的 key (理论上不应发生，除非 uf.uniqueness_field 不唯一)，保留已存在的记录
        Map<String, Map<String, Object>> assembledReport = baseData.stream()
                .filter(row -> row.get("siteUn") != null) // 确保关联键存在
                .collect(Collectors.toMap(
                        row -> (String) row.get("siteUn"), // Map 的 Key
                        Function.identity(),               // Map 的 Value
                        (existing, replacement) -> {
                            System.err.println("警告: 发现重复的 siteUn Key: " + existing.get("siteUn") + "，数据可能不一致。");
                            return existing; // 保留第一个遇到的记录
                        }
                ));

        // 提取后续查询所需的关联键列表
        // uniqueFields: uf.uniqueness_field 列表，假设大部分模块通过此字段关联
        List<String> uniqueFields = new ArrayList<>(assembledReport.keySet());
        // ufIds: uf.id 列表，用于处理那些通过 JSON_ARRAY(CONCAT(uf.id)) 进行关联的低效场景 (如 siteItem, poItem)
        List<String> ufIds = extractUfIds(baseData);
        System.out.println("提取到的 uniqueFields 数量: " + uniqueFields.size());
        System.out.println("提取到的 ufIds 数量: " + ufIds.size());

        // --- 4. 按需获取并合并各模块数据 异步方式 ---
        System.out.println("开始按需查询并合并模块数据...");

        findDataAnyc(activeModules, assembledReport, uniqueFields, ufIds, startTime);


        // --- 5. 返回最终结果 ---
        // 将组装好的 Map 的 values (即每一行的数据 Map) 转换为 List 返回
        System.out.println("所有模块数据查询和合并完成，返回最终结果。");
        List<Map<String, Object>> maps = new ArrayList<>(assembledReport.values());
        pageInfo.setRecords(maps);
        reportDataFiledVO.setPage(pageInfo);

        return reportDataFiledVO;
    }

    private String getHeard(String moduleTypes) {
        String heads = null;
        if (org.apache.commons.lang.StringUtils.isBlank(moduleTypes)) {
            heads = ResourceUtil.readUtf8Str("json/report-head.json");
        } else {
            JSONArray headsArray = new JSONArray();
            if (!moduleTypes.contains("Y1")) {
                heads = ResourceUtil.readUtf8Str("json/report-head-common.json");
                com.alibaba.fastjson.JSONArray headsArrayT = com.alibaba.fastjson.JSONArray.parseArray(heads);
                headsArray.add(headsArrayT.get(0));
            }
            String[] split = moduleTypes.split(",");
            for (int i = 0; i < split.length; i++) {
                heads = ResourceUtil.readUtf8Str("json/report-head-" + split[i] + ".json");
                JSONArray headsArrayT = JSONArray.parseArray(heads);
                headsArray.add(headsArrayT.get(0));
            }
            heads = headsArray.toJSONString();
        }
        return heads;
    }

    //异步查询模块数据
    private void findDataAnyc(Set<String> activeModules, Map<String, Map<String, Object>> assembledReport, List<String> uniqueFields, List<String> ufIds, Long startTime) {
        // 使用 Map 来存储每个模块的 CompletableFuture，方便后续获取结果
        Map<String, CompletableFuture<List<Map<String, Object>>>> futuresMap = new HashMap<>();

        // 定义一个通用的异步任务提交方法，包含日志和错误处理
        Supplier<List<Map<String, Object>>> defaultSupplier = Collections::emptyList; // 默认空列表 supplier

        // 提交 Y1 查询任务
        if (activeModules.contains("Y1") && !ufIds.isEmpty()) {
            CompletableFuture<List<Map<String, Object>>> futureY1 = CompletableFuture.supplyAsync(() -> {
                try {
                    return basicMapper.getSiteItemDataForReport(ufIds);
                } catch (Exception e) {
                    log.error("异步查询 Y1 (SiteItem) 数据时出错", e);
                    return defaultSupplier.get(); // 出错时返回空列表
                }
            }, executorService); // 使用指定的线程池
            futuresMap.put("Y1", futureY1);
        }

        // 提交 Y2 查询任务
        if (activeModules.contains("Y2") && !ufIds.isEmpty()) {
            CompletableFuture<List<Map<String, Object>>> futureY2 = CompletableFuture.supplyAsync(() -> {
                try {
                    return basicMapper.getPODataForReport(ufIds);
                } catch (Exception e) {
                    log.error("异步查询 Y2 (PO) 数据时出错", e);
                    return defaultSupplier.get();
                }
            }, executorService);
            futuresMap.put("Y2", futureY2);
        }

        // 提交 Y3 查询任务
        if (activeModules.contains("Y3") && !uniqueFields.isEmpty()) {
            CompletableFuture<List<Map<String, Object>>> futureY3 = CompletableFuture.supplyAsync(() -> {
                try {
                    return basicMapper.getSiteDeliveryDataForReport(ufIds); //uniqueFields
                } catch (Exception e) {
                    log.error("异步查询 Y3 (SiteDelivery) 数据时出错", e);
                    return defaultSupplier.get();
                }
            }, executorService);
            futuresMap.put("Y3", futureY3);
        }

        // 提交 Y4 查询任务
        if (activeModules.contains("Y4") && !uniqueFields.isEmpty()) {
            CompletableFuture<List<Map<String, Object>>> futureY4 = CompletableFuture.supplyAsync(() -> {
                try {
                    return basicMapper.getSubcontractorDataForReport(ufIds); //uniqueFields
                } catch (Exception e) {
                    log.error("异步查询 Y4 (Subcon) 数据时出错", e);
                    return defaultSupplier.get();
                }
            }, executorService);
            futuresMap.put("Y4", futureY4);
        }

        // 提交 Y5 查询任务
        if (activeModules.contains("Y5") && !uniqueFields.isEmpty()) {
            CompletableFuture<List<Map<String, Object>>> futureY5 = CompletableFuture.supplyAsync(() -> {
                try {
                    return basicMapper.getReadySettlementDataForReport(ufIds); //uniqueFields
                } catch (Exception e) {
                    log.error("异步查询 Y5 (ReadySettlement) 数据时出错", e);
                    return defaultSupplier.get();
                }
            }, executorService);
            futuresMap.put("Y5", futureY5);
        }

        // 提交 Y6 查询任务
        if (activeModules.contains("Y6") && !uniqueFields.isEmpty()) {
            CompletableFuture<List<Map<String, Object>>> futureY6 = CompletableFuture.supplyAsync(() -> {
                try {
                    return basicMapper.getProductivityDataForReport(ufIds); //uniqueFields
                } catch (Exception e) {
                    log.error("异步查询 Y6 (ProductivityReport) 数据时出错", e);
                    return defaultSupplier.get();
                }
            }, executorService);
            futuresMap.put("Y6", futureY6);
        }

        // 提交 Y7 查询任务
        if (activeModules.contains("Y7") && !uniqueFields.isEmpty()) {
            CompletableFuture<List<Map<String, Object>>> futureY7 = CompletableFuture.supplyAsync(() -> {
                try {
                    return basicMapper.getSubconSettlementDataForReport(ufIds); //uniqueFields
                } catch (Exception e) {
                    log.error("异步查询 Y7 (SubconSettlement) 数据时出错", e);
                    return defaultSupplier.get();
                }
            }, executorService);
            futuresMap.put("Y7", futureY7);
        }

        // 提交 Y8 查询任务
        if (activeModules.contains("Y8") && !uniqueFields.isEmpty()) {
            CompletableFuture<List<Map<String, Object>>> futureY8 = CompletableFuture.supplyAsync(() -> {
                try {
                    return basicMapper.getSubconPaymentDataForReport(ufIds); //uniqueFields
                } catch (Exception e) {
                    log.error("异步查询 Y8 (SubconPayment) 数据时出错", e);
                    return defaultSupplier.get();
                }
            }, executorService);
            futuresMap.put("Y8", futureY8);
        }

        // 提交 Y9 查询任务
        if (activeModules.contains("Y9") && !uniqueFields.isEmpty()) {
            CompletableFuture<List<Map<String, Object>>> futureY9 = CompletableFuture.supplyAsync(() -> {
                try {
                    return basicMapper.getYPTTSettlementDataForReport(ufIds); //uniqueFields
                } catch (Exception e) {
                    log.error("异步查询 Y9 (YPTTSettlement) 数据时出错", e);
                    return defaultSupplier.get();
                }
            }, executorService);
            futuresMap.put("Y9", futureY9);
        }

        // --- 5. 等待所有异步任务完成 ---
        if (!futuresMap.isEmpty()) {
            log.debug("等待 {} 个异步查询任务完成...", futuresMap.size());
            // CompletableFuture.allOf 用于等待所有 Future 完成
            // 需要将 futuresMap 的 values (CompletableFuture 列表) 转换为数组
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futuresMap.values().toArray(new CompletableFuture[0])
            );

            try {
                // join() 会等待所有任务完成，如果任何任务异常完成，它会抛出 CompletionException
                allFutures.join();
                log.debug("所有异步查询任务已完成。");
            } catch (CompletionException ce) {
                // 处理异步任务中未捕获的异常 (虽然上面 supplyAsync 内部已尝试捕获)
                log.error("等待异步查询任务完成时发生错误: {}", ce.getMessage(), ce.getCause());
                // 根据业务需求决定是否继续执行或抛出异常
                // 这里选择继续执行，使用已成功获取的数据或空列表
            } catch (Exception e) {
                // 处理其他可能的异常，例如线程中断等
                log.error("等待异步查询任务完成时发生未知错误", e);
                // 可以选择抛出异常中断流程
                // throw new RuntimeException("报表生成过程中异步查询失败", e);
            }
        } else {
            log.debug("没有需要执行的异步查询任务。");
        }

        // --- 6. 合并查询结果 (串行执行，因为需要操作共享的 assembledReport) ---
        log.debug("开始合并异步查询结果...");

        // 遍历 futuresMap，获取每个已完成 Future 的结果并合并
        // 注意：即使 join() 抛出异常，部分 Future 可能已经成功完成，可以尝试获取它们的结果
        futuresMap.forEach((moduleKey, future) -> {
            try {
                // isDone() 检查任务是否完成 (正常或异常)
                // isCompletedExceptionally() 检查是否异常完成
                if (future.isDone() && !future.isCompletedExceptionally()) {
                    List<Map<String, Object>> moduleData = future.get(); // 使用 get() 获取结果 (join() 也可以)
                    log.debug("合并模块 {} 的数据，共 {} 条记录。", moduleKey, moduleData == null ? 0 : moduleData.size());
                    mergeData(assembledReport, moduleData, "siteUn"); // 使用之前的 mergeData 方法
                } else if (future.isCompletedExceptionally()) {
                    log.warn("模块 {} 的异步查询任务异常完成，跳过合并。", moduleKey);
                    // 可以尝试获取异常信息: future.exceptionNow()
                } else {
                    log.warn("模块 {} 的异步查询任务未完成(理论上不应发生在此处，因为 allOf 已等待)，跳过合并。", moduleKey);
                }
            } catch (InterruptedException | ExecutionException e) {
                // 处理获取 Future 结果时可能发生的异常
                log.error("获取模块 {} 的异步查询结果时出错", moduleKey, e);
                // 根据需要处理错误，例如记录日志，但通常不中断整个合并过程
            }
        });

        log.debug("所有模块数据合并完成。");


        // --- 7. 返回最终结果 ---
        long endTime = System.currentTimeMillis();
        log.info("报表生成完成，总耗时: {} 毫秒。", (endTime - startTime));
    }

    //同步查询模块数据
    private void findData(Set<String> activeModules, Map<String, Map<String, Object>> assembledReport, List<String> uniqueFields, List<String> ufIds) {
        // Y1: siteItem 数据 (与 uf 通过 JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field 关联)
        // **注意**: 这种关联方式效率极低，强烈建议优化数据库表结构，添加直接的 uf_id 外键。
        if (activeModules.contains("Y1") && !ufIds.isEmpty()) {
            System.out.println("查询 Y1 (SiteItem) 数据...");
            List<Map<String, Object>> siteItemData = basicMapper.getSiteItemDataForReport(ufIds);
            // **重要**: getSiteItemDataForReport 查询结果需要包含能关联回来的键 (例如 siteUn 或 ufId)。
            // 这里假设它返回了 'siteUn'。
            mergeData(assembledReport, siteItemData, "siteUn"); // 使用 'siteUn' 作为合并的键
            System.out.println("Y1 数据合并完成。获取 " + (siteItemData == null ? 0 : siteItemData.size()) + " 条记录。");
        }

        // Y2: poItem, po, cusProject 数据 (poItem 与 uf 通过 JSON_ARRAY(CONCAT(uf.id)) = poItem.uniqueness_field 关联)
        // **注意**: 同样是低效关联。
        if (activeModules.contains("Y2") && !ufIds.isEmpty()) {
            System.out.println("查询 Y2 (PO) 数据...");
            List<Map<String, Object>> poData = basicMapper.getPODataForReport(ufIds);
            // 假设 getPODataForReport 内部处理了 poItem, po, cusProject 的连接，并返回 'siteUn'。
            mergeData(assembledReport, poData, "siteUn");
            System.out.println("Y2 数据合并完成。获取 " + (poData == null ? 0 : poData.size()) + " 条记录。");
        }

        // Y3: siteDelivery 数据 (假设 siteDelivery.uniqueness_field 直接等于 uf.uniqueness_field)
        // 这是基于原始复杂 SQL 的关联逻辑推断的，请根据实际情况确认。
        if (activeModules.contains("Y3") && !uniqueFields.isEmpty()) {
            System.out.println("查询 Y3 (SiteDelivery) 数据...");
            List<Map<String, Object>> deliveryData = basicMapper.getSiteDeliveryDataForReport(uniqueFields);
            // 假设 getSiteDeliveryDataForReport 返回 'siteUn'。
            mergeData(assembledReport, deliveryData, "siteUn");
            System.out.println("Y3 数据合并完成。获取 " + (deliveryData == null ? 0 : deliveryData.size()) + " 条记录。");
        }

        // Y4: subItem, subPo, sub 数据 (假设 subItem.uniqueness_field 直接等于 uf.uniqueness_field)
        if (activeModules.contains("Y4") && !uniqueFields.isEmpty()) {
            System.out.println("查询 Y4 (Subcon) 数据...");
            List<Map<String, Object>> subData = basicMapper.getSubcontractorDataForReport(uniqueFields);
            // 假设 getSubcontractorDataForReport 内部处理了 subItem, subPo, sub 的连接，并返回 'siteUn'。
            // 注意: 原 SQL 中的 subconUniquenessField 需要在此查询或 Java 层构建。
            mergeData(assembledReport, subData, "siteUn");
            System.out.println("Y4 数据合并完成。获取 " + (subData == null ? 0 : subData.size()) + " 条记录。");
        }

        // Y5: ReadySettlement 数据 (假设 ReadySettlement.uniqueness_field 直接等于 uf.uniqueness_field)
        if (activeModules.contains("Y5") && !uniqueFields.isEmpty()) {
            System.out.println("查询 Y5 (ReadySettlement) 数据...");
            List<Map<String, Object>> readySettlementData = basicMapper.getReadySettlementDataForReport(uniqueFields);
            mergeData(assembledReport, readySettlementData, "siteUn");
            System.out.println("Y5 数据合并完成。获取 " + (readySettlementData == null ? 0 : readySettlementData.size()) + " 条记录。");
        }

        // Y6: ProductivityReport 数据 (假设 ProductivityReport.uniqueness_field 直接等于 uf.uniqueness_field)
        if (activeModules.contains("Y6") && !uniqueFields.isEmpty()) {
            System.out.println("查询 Y6 (ProductivityReport) 数据...");
            List<Map<String, Object>> productivityData = basicMapper.getProductivityDataForReport(uniqueFields);
            mergeData(assembledReport, productivityData, "siteUn");
            System.out.println("Y6 数据合并完成。获取 " + (productivityData == null ? 0 : productivityData.size()) + " 条记录。");
        }

        // Y7: SubconSettlement 数据 (假设 SubconSettlement.uniqueness_field 直接等于 uf.uniqueness_field)
        if (activeModules.contains("Y7") && !uniqueFields.isEmpty()) {
            System.out.println("查询 Y7 (SubconSettlement) 数据...");
            List<Map<String, Object>> subconSettlementData = basicMapper.getSubconSettlementDataForReport(uniqueFields);
            mergeData(assembledReport, subconSettlementData, "siteUn");
            System.out.println("Y7 数据合并完成。获取 " + (subconSettlementData == null ? 0 : subconSettlementData.size()) + " 条记录。");
        }

        // Y8: SubconPayment 数据 (假设 SubconPayment.uniqueness_field 直接等于 uf.uniqueness_field)
        if (activeModules.contains("Y8") && !uniqueFields.isEmpty()) {
            System.out.println("查询 Y8 (SubconPayment) 数据...");
            List<Map<String, Object>> subconPaymentData = basicMapper.getSubconPaymentDataForReport(uniqueFields);
            mergeData(assembledReport, subconPaymentData, "siteUn");
            System.out.println("Y8 数据合并完成。获取 " + (subconPaymentData == null ? 0 : subconPaymentData.size()) + " 条记录。");
        }

        // Y9: YPTTSettlement 数据 (假设 YPTTSettlement.uniqueness_field 直接等于 uf.uniqueness_field)
        if (activeModules.contains("Y9") && !uniqueFields.isEmpty()) {
            System.out.println("查询 Y9 (YPTTSettlement) 数据...");
            List<Map<String, Object>> ypttSettlementData = basicMapper.getYPTTSettlementDataForReport(uniqueFields);
            mergeData(assembledReport, ypttSettlementData, "siteUn");
            System.out.println("Y9 数据合并完成。获取 " + (ypttSettlementData == null ? 0 : ypttSettlementData.size()) + " 条记录。");
        }
    }

    /**
     * 辅助方法：将来源数据列表 (sourceData) 合并到目标 Map (targetMap) 中。
     *
     * @param targetMap  目标 Map，Key 是关联字段的值，Value 是代表一整行数据的 Map。
     * @param sourceData 来源数据列表，每个 Map 代表一个模块的一行数据。
     * @param keyColumn  来源数据中用于和目标 Map 的 Key 进行匹配的列名 (例如 "siteUn")。
     */
    private void mergeData(Map<String, Map<String, Object>> targetMap, List<Map<String, Object>> sourceData, String keyColumn) {
        // 如果来源数据为空，则无需合并
        if (CollectionUtils.isEmpty(sourceData)) {
            return;
        }
        // 遍历来源数据列表中的每一行
        for (Map<String, Object> sourceRow : sourceData) {
            // 获取当前来源行的关联键的值
            String key = (String) sourceRow.get(keyColumn);
            // 如果键不为空，并且在目标 Map 中存在这个键
            if (key != null && targetMap.containsKey(key)) {
                // 将当前来源行 (sourceRow) 的所有键值对合并到目标 Map 中对应的行数据中
                // 注意: 如果 sourceRow 中有和 targetMap 中已存在键相同的键，会被覆盖 (putAll 的行为)
                targetMap.get(key).putAll(sourceRow);
            } else {
                // 如果 key 为 null 或在 targetMap 中找不到匹配项，可以选择记录日志或忽略
                if (key != null) {
                    System.err.println("警告: 模块数据中的 key '" + key + "' (来自列 '" + keyColumn + "') 在基础数据中未找到匹配项，该行数据无法合并。");
                } else {
                    System.err.println("警告: 模块数据中的关联键 (来自列 '" + keyColumn + "') 为 null，该行数据无法合并。");
                }
            }
        }
    }

//    /**
//     * 辅助方法：双向合并数据，确保targetMap中所有条目都经过处理
//     *
//     * @param targetMap  目标数据集（需保留所有key）
//     * @param sourceData 来源数据集（可能少于targetMap条目）
//     * @param keyColumn  关联字段名（需确保两端数据使用相同键规则）
//     */
//    private void mergeData(Map<String, Map<String, Object>> targetMap,
//                           List<Map<String, Object>> sourceData,
//                           String keyColumn) {
//
//        // 空数据特殊处理：清除所有目标数据或标记空状态
//        if (CollectionUtils.isEmpty(sourceData)) {
//            targetMap.values().forEach(row -> row.put("DATA_STATUS", "EMPTY_SOURCE"));
//            return;
//        }
//
//        // 将sourceData转换为临时Map（键标准化处理）
//        Map<String, Map<String, Object>> sourceMap = sourceData.stream()
//                .filter(map -> map.get(keyColumn) != null)
//                .collect(Collectors.toMap(
//                        map -> normalizeKey(map.get(keyColumn)),
//                        Function.identity(),
//                        (existing, replacement) -> {
//                            System.err.println("警告: 来源数据中存在重复键: " + existing.get(keyColumn));
//                            return replacement; // 保留最后出现的值
//                        }));
//
//        // 主合并流程：处理targetMap中所有现有条目
//        targetMap.forEach((targetKey, targetRow) -> {
//            // 键标准化匹配
//            String normalizedKey = normalizeKey(targetKey);
//
//            if (sourceMap.containsKey(normalizedKey)) {
//                // 安全合并：仅覆盖非空字段
//                Map<String, Object> sourceRow = sourceMap.get(normalizedKey);
//                sourceRow.forEach((k, v) -> {
//                    if (v != null) {
//                        targetRow.put(k, v);
//                    }
//                });
//                targetRow.put("MERGE_STATUS", "SUCCESS");
//            } else {
//                // 未找到匹配项的处理
//                targetRow.put("MERGE_STATUS", "MISSING_IN_SOURCE");
//                System.err.println("警告: 目标键 '" + targetKey + "' 在来源数据中未找到匹配");
//            }
//        });
//
//        // 处理来源数据中存在但目标中不存在的条目（根据需求选择）
//        sourceMap.forEach((sourceKey, sourceRow) -> {
//            if (!targetMap.containsKey(sourceKey)) {
//                System.err.println("警告: 来源键 '" + sourceKey + "' 在目标数据中不存在");
//                // 可选：创建新条目或忽略
//                // targetMap.put(sourceKey, new HashMap<>(sourceRow));
//            }
//        });
//    }
//
//    // 键标准化方法（防止大小写/空格等问题）
//    private String normalizeKey(Object key) {
//        return key.toString().trim().toUpperCase();
//    }

    /**
     * 将逗号分隔的项目 ID 字符串转换为去重的 List<String>。
     *
     * @param projectId  单个项目 ID (此版本未使用，可以考虑是否需要合并)
     * @param projectIds 逗号分隔的项目 ID 字符串
     * @return 包含所有有效且去重的项目 ID 的 List<String>；如果 projectIds 为空或 null，返回 null。
     */

    List<String> toProjectIds(String projectId, String projectIds) {
        if (org.apache.commons.lang.StringUtils.isNotBlank(projectId)) {
            projectIds = projectIds + "," + projectId;
        }
        if (org.apache.commons.lang.StringUtils.isEmpty(projectIds)) {
            return null;
        }
        String[] array = projectIds.split(",");
        // 将数组转换为List
        List<String> list = Arrays.asList(array);
        return list;
    }

    public List<Map<String, Object>> reportForm(String projectId, String dateStrStart, String dateStrEnd, String dateType, String area, String projectIds, String moduleTypes, String nation, String unId) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        // --- 1. 准备工作 ---
        // 如果日期范围无效，则清除日期类型，避免无效的日期过滤
        if (!StringUtils.hasText(dateStrStart) || !StringUtils.hasText(dateStrEnd)) {
            dateType = null;
        }
        // 处理项目 ID，将单个 ID 和逗号分隔的 ID 列表合并为一个集合
        List<String> targetProjectIds = toProjectIds(projectId, projectIds);
        // 如果没有有效的项目 ID，可以直接返回空结果或抛出异常
        if (CollectionUtils.isEmpty(targetProjectIds) && org.apache.commons.lang.StringUtils.isBlank(nation)) {

            System.err.println("警告: 未提供有效的项目 ID  或者国籍 用于报表查询。");
            return null;
        }

        // 解析需要查询的数据模块
        Set<String> activeModules = parseModuleTypes(moduleTypes);
        System.out.println("需要加载的模块: " + activeModules); // 打印日志，方便调试

        // --- 2. 获取基础数据 ---
        // 查询核心的 uf 表以及必须关联的 YPTTProject, site, dept 等表信息
        // 应用项目、日期、区域、国家等核心过滤条件
        // **关键**: 此查询需要返回 uf.id (别名 ufId) 和 uf.uniqueness_field (别名 siteUn) 作为后续关联的键
        System.out.println("开始查询基础数据...");
        List<String> projectIdList = null;
        if (ObjectUtils.isNotEmpty(targetProjectIds)) {
            projectIdList = new ArrayList<>(targetProjectIds);
        }
        List<String> listUnIds = new ArrayList<>();
        if (unId != null && !"".equals(unId)) {
            String[] split = unId.split(",");
            for (String s : split) {
                listUnIds.add(s);
            }
        }
        List<Map<String, Object>> baseDatas = basicMapper.getBaseReportData(
                dateStrStart, dateStrEnd, dateType, area, nation,
                projectIdList, listUnIds);
        //todo 筛选数据 根据时间进行筛选
        List<Map<String, Object>> baseData = new DataFilterOptimized().baseDataFilter(baseDatas, dateStrStart, dateStrEnd, dateType);
//        List<Map<String, Object>> baseData = baseDataFilter(baseDatas, dateStrStart, dateStrEnd, dateType);
        // 如果没有找到任何基础数据，直接返回空列表
        if (CollectionUtils.isEmpty(baseData)) {
            System.out.println("未找到符合条件的基础数据。");
            return null;
        }
        System.out.println("基础数据查询完成，共获取 " + baseData.size() + " 条记录。");

        // --- 3. 准备数据组装容器 ---
        // 使用 Map 来存储最终结果，Key 是 uf.uniqueness_field (siteUn)，Value 是该行完整数据的 Map
        // 这样可以方便地根据 siteUn 将后续查询到的模块数据合并到对应的行中
        // 使用 Function.identity() 表示值就是原始的 Map 对象
        // (existing, replacement) -> existing 表示如果遇到重复的 key (理论上不应发生，除非 uf.uniqueness_field 不唯一)，保留已存在的记录
        Map<String, Map<String, Object>> assembledReport = baseData.stream()
                .filter(row -> row.get("siteUn") != null) // 确保关联键存在
                .collect(Collectors.toMap(
                        row -> (String) row.get("siteUn"), // Map 的 Key
                        Function.identity(),               // Map 的 Value
                        (existing, replacement) -> {
                            System.err.println("警告: 发现重复的 siteUn Key: " + existing.get("siteUn") + "，数据可能不一致。");
                            return existing; // 保留第一个遇到的记录
                        }
                ));

        // 提取后续查询所需的关联键列表
        // uniqueFields: uf.uniqueness_field 列表，假设大部分模块通过此字段关联
        List<String> uniqueFields = new ArrayList<>(assembledReport.keySet());
        // ufIds: uf.id 列表，用于处理那些通过 JSON_ARRAY(CONCAT(uf.id)) 进行关联的低效场景 (如 siteItem, poItem)
        List<String> ufIds = extractUfIds(baseData);
        System.out.println("提取到的 uniqueFields 数量: " + uniqueFields.size());
        System.out.println("提取到的 ufIds 数量: " + ufIds.size());

        // --- 4. 按需获取并合并各模块数据 异步方式 ---
        System.out.println("开始按需查询并合并模块数据...");

        findDataAnyc(activeModules, assembledReport, uniqueFields, ufIds, startTime);


        // --- 5. 返回最终结果 ---
        // 将组装好的 Map 的 values (即每一行的数据 Map) 转换为 List 返回
        System.out.println("所有模块数据查询和合并完成，返回最终结果。");
        List<Map<String, Object>> maps =new ArrayList<>();
        for (String k : assembledReport.keySet()) {
            maps.add(assembledReport.get(k));
        }
        return finalMap(maps, dateStrStart, dateStrEnd, dateType);
    }

    // 你提供的 keys 字符串
    private static final String keys = "Invoice-Amount-Diff-4th,PO_value,po_BOQ_item,ypttsettlement-CN-remark-2nd,Custom_project_name,4thProductivityReportDate,Invoice-Amount-Diff-3rd,SubconTotallySettlementAmount,ypttsettlement-Totally-CN-amount,poItemSiteName,Team_Leader_DT,site-Subcon-settlementDate-3rd,site-Subcon-settlementDate-4th,ypttsettlement-CN-amount-1st,readySettlementAmountGap,poItemProjectcode,additional_cost,Subcon-payment-amount-2nd,SubconTotallyPaymentAmount,YPTT_Project_code,Unit_price,Completed_work_date,site-ReadyForSettlement-1st,Site_value,Pre_payment_amount,PO_number,deliveryRemark,SubconTotallySettlementGap,Invoice-Amount-3rd,poItemRegion,Invoice-Amount-4th,settlement%-3rd,subQuantity,settlement%-4th,SubconSettlementMilestone3rd,ypttsettlement-CN-remark-1st,SubconSettlementMilestone4th,SiteSettlementMilestone2nd%,subcon-Totally-CN-amount,release_date,poItemPhase,site-ReadyForSettlement-2nd,subcon-CN-amount-3rd,PO_Received_date,Subcon-payment-number-1st,subBOQitem,Subcon-payment-amount-1st,Subcon_PO_amount,Invoice-Amount-Diff-2nd,poItemcode,PLO_PC_Others,3rdProductivityReportDate,subSiteName,subQuantityReduce,subPayRemark,Start_Working_date,Subcon-settlement%-3rd,subcon-CN-remark-1st,ypttsettlement-CN-amount-4st,1stProductivityReportAmount,Type_of_service,Pre_payment_ratio,SiteSettlementMilestone1st%,Subcon-settlement-amount-2nd,Site_model,Site_item_status,Subcon-settlement-amount-1st,settlementAmount-1st,subSiteID,3rdProductivityReportAmount,Subcon-Payment-time-3rd,ypttsettlement-CN-amount-2nd,Invoice-Amount-Diff-1st,settlement%-2nd,Invoice-remark-2nd,BOQ_item,subUp,subItemcode,Invoice-remark-1st,Invoice-date-1st,2ndProductivityReportAmount,Subcon-Payment-time-4st,siteUn,settlement%-1st,settlementAmount-2nd,subcon-CN-remark-2nd,Subcon-payment-number-2st,ProductivityAmount,SubconSettlementMilestone1st,siteItemRemark,ypttRemark,ReadySettlementAmount,subcon-CN-amount-1st,Invoice-Amount-1st,Invoice-date-2nd,poItemUn,subcon-CN-remark-4st,Invoice-number-3rd,Invoice-number-4th,ReadyForSettlement-1st,KPI-Archive-date,Subcon-settlement%-2nd,SubconTotallyPaymentAmountGap,poItemQuantityReduce,subRemark,Subcon-settlement-amount-3rd,air_CI_Report_submit,1stProductivityReportDate,Subcon-settlement-amount-4th,Pre_payment,PO_gap,Subcon-settlement-time-3rd,Contract_number,Subcon-settlement-time-4th,subcon-CN-remark-3rd,E_ATP_Pass,Subcon-settlement%-4st,engineer_DTA_SPV,Subcon-Payment-time-2nd,ypttsettlement-CN-amount-3rd,poItemSiteID,YPTT_Project_name,4thProductivityReportAmount,ReadyForSettlement-2nd,Invoice-remark-3rd,Invoice-remark-4th,SiteSettlementMilestone3rd%,site_name,poItemRemark,Area,Item_code,F_PAC_Pass,Subcon_PO_number,name,settlementAmount-3rd,Region,KPI-Archive-amount,Subcon-Payment-time-1st,Subcon_name,Subcon-payment-number-3st,Pre_Settlement_date,settlementAmount-4th,Phase,site-Subcon-settlementDate-1st,Invoice-date-3rd,ProductivityDeclarationRatio%,PIC_PC_PM,Subcon-settlement-time-1st,subcon-CN-amount-4st,Invoice-date-4th,poUp,Site_Serial_number,Site_belong_to,site-Subcon-settlementDate-2nd,Subcon-payment-amount-3rd,Invoice-number-2nd,site_allocation_date,ypttsettlement-CN-remark-3rd,TotallyInvoiceAmount,poItemQuantity,Subcon-settlement-time-2nd,InvoiceAmountGAP,Subcon-payment-amount-4st,G_FAC,ReadyForSettlement-3rd,Invoice-Amount-2nd,ReadyForSettlement-4th,Quantity,ypttsettlement-CN-remark-4st,site-ReadyForSettlement-3rd,Subcon-settlement%-1st,SiteSettlementMilestone4th%,SubconSettlementMilestone2nd,2ndProductivityReportDate,subcon-CN-amount-2nd,Site_manager_Report,site-ReadyForSettlement-4th,subconUniquenessField,Site_register_date,Subcon-payment-number-4st,Invoice-number-1st,ypttsettlement-CN-date-4st,ypttsettlement-CN-number-4st,ypttsettlement-CN-date-3rd,ypttsettlement-CN-number-3rd,ypttsettlement-CN-date-2nd,ypttsettlement-CN-number-2nd,ypttsettlement-CN-date-1st,ypttsettlement-CN-number-1st,subcon-CN-number-4st,subcon-CN-date-4st,subcon-CN-number-3rd,subcon-CN-date-3rd,subcon-CN-number-2nd,subcon-CN-date-2nd,subcon-CN-number-1st,subcon-CN-date-1st";

    // 定义常量
    private static final String[] REPORT_DATE_KEYS = {
            "1stProductivityReportDate", "2ndProductivityReportDate",
            "3rdProductivityReportDate", "4thProductivityReportDate"
    };
    private static final String[] INVOICE_DATE_KEYS = {
            "Invoice-date-1st", "Invoice-date-2nd",
            "Invoice-date-3rd", "Invoice-date-4th"
    };
    private static final String[] REPORT_AMOUNT_KEYS = {
            "1stProductivityReportAmount", "2ndProductivityReportAmount",
            "3rdProductivityReportAmount", "4thProductivityReportAmount"
    };
    private static final String[] INVOICE_AMOUNT_KEYS = {
            "Invoice-Amount-1st", "Invoice-Amount-2nd",
            "Invoice-Amount-3rd", "Invoice-Amount-4th"
    };
    private static final String[] CN_AMOUNT_KEYS = {
            "ypttsettlement-CN-amount-1st", "ypttsettlement-CN-amount-2nd",
            "ypttsettlement-CN-amount-3rd", "ypttsettlement-CN-amount-4st"
    };
    public List<Map<String, Object>> finalMap(List<Map<String, Object>> maps,
                                              String dateStrStart, String dateStrEnd, String dateType) {
        Set<String> allKeys = new HashSet<>(Arrays.asList(keys.split(",")));
        List<Map<String, Object>> result = new ArrayList<>();

        // 提前转换日期，避免重复转换
        LocalDate startDate = org.apache.commons.lang3.StringUtils.isNotBlank(dateStrStart) ? LockTimeV3Util.toLocalDate(dateStrStart) : null;
        LocalDate endDate = org.apache.commons.lang3.StringUtils.isNotBlank(dateStrEnd) ? LockTimeV3Util.toLocalDate(dateStrEnd) : null;

        for (Map<String, Object> originalMap : maps) {
            Map<String, Object> completeMap = new HashMap<>();
            completeMap.putAll(originalMap);

            // 补全缺失的 key
            for (String key : allKeys) {
                completeMap.putIfAbsent(key, null);
            }

            // 初始化BigDecimal变量，使用安全转换方法
            BigDecimal reportBegin = BigDecimal.ZERO;
            BigDecimal reportCurrent = BigDecimal.ZERO;
            BigDecimal reportEnd = BigDecimal.ZERO;

            BigDecimal settlementBegin = BigDecimal.ZERO;
            BigDecimal settlementCurrent = BigDecimal.ZERO;
            BigDecimal settlementEnd = BigDecimal.ZERO;

            BigDecimal cnBegin = BigDecimal.ZERO;
            BigDecimal cnCurrent = BigDecimal.ZERO;
            BigDecimal cnEnd = BigDecimal.ZERO;

            // 处理四个报告项 - WIP_END始终计算
            for (int i = 0; i < 4; i++) {
                LocalDate reportDate = LockTimeV3Util.toLocalDate(originalMap.get(REPORT_DATE_KEYS[i]));

                LocalDate invoiceDate = LockTimeV3Util.toLocalDate(originalMap.get(INVOICE_DATE_KEYS[i]));
//                if (reportDate == null) continue;

                BigDecimal reportAmount = toBigDecimal(originalMap.get(REPORT_AMOUNT_KEYS[i]));
                BigDecimal invoiceAmount = toBigDecimal(originalMap.get(INVOICE_AMOUNT_KEYS[i]));
                BigDecimal cnAmount = toBigDecimal(originalMap.get(CN_AMOUNT_KEYS[i]));

                // 期末累计 - 始终计算
                reportEnd = safeAdd(reportEnd, reportAmount);
                settlementEnd = safeAdd(settlementEnd, invoiceAmount);
                cnEnd = safeAdd(cnEnd, cnAmount);

                // 只有在日期参数有效时才计算期初和当期
                if (startDate != null && endDate != null && org.apache.commons.lang3.StringUtils.isNotBlank(dateType)) {
                    // 期初判断 (小于开始日期)
                    if (reportDate != null && reportDate.isBefore(startDate)) {
                        reportBegin = safeAdd(reportBegin, reportAmount);
                    }

                    if (invoiceDate != null && invoiceDate.isBefore(startDate)){
                        settlementBegin = safeAdd(settlementBegin, invoiceAmount);
                        cnBegin = safeAdd(cnBegin, cnAmount);
                    }

//                    // 当期判断 (大于等于开始日期且小于结束日期)
//                    if (!reportDate.isBefore(startDate) && reportDate.isBefore(endDate)) {
//                        reportCurrent = safeAdd(reportCurrent, reportAmount);
//
//                    }
//                    if (invoiceDate!= null && !invoiceDate.isBefore(startDate) && invoiceDate.isBefore(endDate)) {
//                        settlementCurrent = safeAdd(settlementCurrent, invoiceAmount);
//                        cnCurrent = safeAdd(cnCurrent, cnAmount);
//                    }
                    // 当期判断 (大于等于开始日期且小于等于结束日期)
                    if (reportDate != null && !reportDate.isBefore(startDate) && !reportDate.isAfter(endDate)) {
                        reportCurrent = safeAdd(reportCurrent, reportAmount);
                    }

                    if (invoiceDate!= null && !invoiceDate.isBefore(startDate) && !invoiceDate.isAfter(endDate)) {
                        settlementCurrent = safeAdd(settlementCurrent, invoiceAmount);
                        cnCurrent = safeAdd(cnCurrent, cnAmount);
                    }

                }
            }

            // 计算WIP值 - wip_end始终计算
            BigDecimal wipEnd = reportEnd.subtract(settlementEnd).subtract(cnEnd);
            completeMap.put("wip_end", wipEnd);
            completeMap.put("report_end", reportEnd);
            completeMap.put("settlement_end", settlementEnd);
            completeMap.put("cnEnd", cnEnd);

            // 只有在日期参数有效时才计算期初和当期的WIP
            if (startDate != null && endDate != null && "product_invoice_date".equals(dateType)) {
                BigDecimal wipBegin = reportBegin.subtract(settlementBegin).subtract(cnBegin);
                BigDecimal wipCurrent = reportCurrent.subtract(settlementCurrent).subtract(cnCurrent);
                completeMap.put("wip_begin", wipBegin);
                completeMap.put("report_begin", reportBegin);
                completeMap.put("settlement_begin", settlementBegin);
                completeMap.put("cnBegin", cnBegin);
                completeMap.put("wip_current", wipCurrent.add(wipBegin));  //wip当期值 重新计算， 加上期初的wip值
                completeMap.put("report_current", reportCurrent);
                completeMap.put("settlement_current", settlementCurrent);
                completeMap.put("cnCurrent", cnCurrent);
            } else {
                // 如果日期参数无效，设置期初和当期WIP为null或0
                completeMap.put("wip_begin", null);
                completeMap.put("report_begin", null);
                completeMap.put("settlement_begin", null);
                completeMap.put("cnBegin", null);
                completeMap.put("wip_current", null);
                completeMap.put("report_current", null);
                completeMap.put("settlement_current", null);
                completeMap.put("cnCurrent", null);
            }

            result.add(completeMap);
        }
        return result;
    }
//    //构造最终map
//    public List<Map<String, Object>> finalMap(List<Map<String, Object>> maps, String dateStrStart, String dateStrEnd, String dateType) {
//        // 将 keys 转换为 Set
//        Set<String> allKeys = new HashSet<>(Arrays.asList(keys.split(",")));
//        // 结果列表
//        List<Map<String, Object>> result = new ArrayList<>();
//
//        for (Map<String, Object> originalMap : maps) {
//            Map<String, Object> completeMap = new HashMap<>();
//            //产值
//            BigDecimal report_begin = new BigDecimal("0");
//            BigDecimal report_current = new BigDecimal("0");
//            BigDecimal report_end = new BigDecimal("0");
//
//            //开票
//            BigDecimal settlement_begin = new BigDecimal("0");
//            BigDecimal settlement_current = new BigDecimal("0");
//            BigDecimal settlement_end = new BigDecimal("0");
//
//            //CN
//            BigDecimal CN_begin = new BigDecimal("0");
//            BigDecimal CN_current = new BigDecimal("0");
//            BigDecimal CN_end = new BigDecimal("0");
//
//            //wip = 产值金额 -(开票金额-cn金额)
//            BigDecimal wip_begin = new BigDecimal("0");
//            BigDecimal wip_current = new BigDecimal("0");
//            BigDecimal wip_end = new BigDecimal("0");
//
//            // 先放入已有的键值
//            completeMap.putAll(originalMap);
//
//            // 补全缺失的 key
//            for (String key : allKeys) {
//                completeMap.putIfAbsent(key, null);
//            }
//            if (!org.apache.commons.lang3.StringUtils.isAnyBlank(dateStrStart, dateStrEnd, dateType)) {
//                //计算每条数据的wip 期初、期中、期末
//
//                //产值数据
//                LocalDate reportDate1 = LockTimeV3Util.toLocalDate(originalMap.get("1stProductivityReportDate"));
//                BigDecimal report_amount1 = (BigDecimal)originalMap.get("1stProductivityReportAmount");
//                LocalDate reportDate2 = LockTimeV3Util.toLocalDate(originalMap.get("2ndProductivityReportDate"));
//                BigDecimal report_amount2 = (BigDecimal)originalMap.get("2ndProductivityReportAmount");
//                LocalDate reportDate3 = LockTimeV3Util.toLocalDate(originalMap.get("3rdProductivityReportDate"));
//                BigDecimal report_amount3 = (BigDecimal)originalMap.get("3rdProductivityReportAmount");
//                LocalDate reportDate4 = LockTimeV3Util.toLocalDate(originalMap.get("4thProductivityReportDate"));
//                BigDecimal report_amount4 = (BigDecimal)originalMap.get("4thProductivityReportAmount");
//                //开票金额
//                BigDecimal invoice_amount1 = (BigDecimal)originalMap.get("Invoice-Amount-1st");
//                BigDecimal invoice_amount2 = (BigDecimal)originalMap.get("Invoice-Amount-2nd");
//                BigDecimal invoice_amount3 = (BigDecimal)originalMap.get("Invoice-Amount-3rd");
//                BigDecimal invoice_amount4 = (BigDecimal)originalMap.get("Invoice-Amount-4th");
//                //cn金额
//                BigDecimal CN_amount1 = (BigDecimal)originalMap.get("ypttsettlement-CN-amount-1st");
//                BigDecimal CN_amount2 = (BigDecimal)originalMap.get("ypttsettlement-CN-amount-2nd");
//                BigDecimal CN_amount3 = (BigDecimal)originalMap.get("ypttsettlement-CN-amount-3rd");
//                BigDecimal CN_amount4 = (BigDecimal)originalMap.get("ypttsettlement-CN-amount-4st");
//
//
//                //期末产值计算
//                report_end = report_end.add(report_amount1).add(report_amount2).add(report_amount3).add(report_amount4);
//                settlement_end = settlement_end.add(invoice_amount1).add(invoice_amount2).add(invoice_amount3).add(invoice_amount4);
//                CN_end = CN_end.add(CN_amount1).add(CN_amount2).add(CN_amount3).add(CN_amount4);
//
//                //初期产值计算
//                if (reportDate1 != null && reportDate1.isBefore(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrStart)))){
//                    report_begin = report_begin.add(report_amount1);
//                    settlement_begin = settlement_begin.add(invoice_amount1);
//                    CN_begin = CN_begin.add(CN_amount1);
//                }
//                if (reportDate2 != null && reportDate2.isBefore(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrStart)))){
//                    report_begin = report_begin.add(report_amount2);
//                    settlement_begin = settlement_begin.add(invoice_amount2);
//                    CN_begin = CN_begin.add(CN_amount2);
//                }
//                if (reportDate3 != null && reportDate3.isBefore(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrStart)))){
//                    report_begin = report_begin.add(report_amount3);
//                    settlement_begin = settlement_begin.add(invoice_amount3);
//                    CN_begin = CN_begin.add(CN_amount3);
//                }
//                if (reportDate4 != null && reportDate4.isBefore(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrStart)))){
//                    report_begin = report_begin.add(report_amount4);
//                    settlement_begin = settlement_begin.add(invoice_amount4);
//                    CN_begin = CN_begin.add(CN_amount4);
//                }
//                //当期产值计算
//                if (reportDate1 != null && reportDate1.isAfter(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrStart)))
//                        && reportDate1.isBefore(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrEnd)))){
//                    report_current = report_current.add(report_amount1);
//                    settlement_current = settlement_current.add(invoice_amount1);
//                    CN_current = CN_current.add(CN_amount1);
//                }
//                if (reportDate2 != null && reportDate2.isAfter(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrStart)))
//                        && reportDate2.isBefore(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrEnd)))){
//                    report_current = report_current.add(report_amount2);
//                    settlement_current = settlement_current.add(invoice_amount2);
//                    CN_current = CN_current.add(CN_amount2);
//                }
//                if (reportDate3 != null && reportDate3.isAfter(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrStart)))
//                        && reportDate3.isBefore(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrEnd)))){
//                    report_current = report_current.add(report_amount3);
//                    settlement_current = settlement_current.add(invoice_amount3);
//                    CN_current = CN_current.add(CN_amount3);
//                }
//                if (reportDate4 != null && reportDate4.isAfter(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrStart)))
//                        && reportDate4.isBefore(Objects.requireNonNull(LockTimeV3Util.toLocalDate(dateStrEnd)))){
//                    report_current = report_current.add(report_amount4);
//                    settlement_current = settlement_current.add(invoice_amount4);
//                    CN_current = CN_current.add(CN_amount4);
//                }
//                //期末开票计算
//                wip_begin = report_begin.subtract(settlement_begin).add(CN_begin);
//                wip_current = report_current.subtract(settlement_current).add(CN_current);
//                wip_end = report_end.subtract(settlement_end).add(CN_end);
//
//            }
//            completeMap.put("wip_begin",wip_begin);
//            completeMap.put("wip_current",wip_current);
//            completeMap.put("wip_end",wip_end);
//            result.add(completeMap);
//        }
//
//        return result;
//    }

    // 安全转换为BigDecimal
    private BigDecimal toBigDecimal(Object value) {
        if (value == null) return BigDecimal.ZERO;
        if (value instanceof BigDecimal) return (BigDecimal) value;
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    // 安全的BigDecimal相加
    private BigDecimal safeAdd(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.add(b);
    }
}
