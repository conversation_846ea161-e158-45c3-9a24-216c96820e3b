package com.pig4cloud.pig.yptt.service.workflow;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.pig4cloud.pig.yptt.controller.WorkFlowApiRes;
import com.pig4cloud.pig.yptt.entity.PaymentApplication;
import com.pig4cloud.pig.yptt.entity.PendingPaymentBill;
import com.pig4cloud.pig.yptt.entity.ReimburseInvoiceEntity;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlow.request.RequestInfo;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlow.request.TableInfo;
import com.pig4cloud.pig.yptt.mapper.PaymentApplicationMapper;
import com.pig4cloud.pig.yptt.mapper.PendingPaymentBillMapper;
import com.pig4cloud.pig.yptt.service.ViewModelRelService;
import com.pig4cloud.pig.yptt.service.fs.AccountsSuspenseService;
import com.pig4cloud.pig.yptt.service.fs.PersonalLoansAndPrepaymentsService;
import com.pig4cloud.pig.yptt.service.fs.ReimburseInvoiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * @ClassName: WorkFlowEndInterceptService
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-07-22  16:11
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkFlowEndInterceptService {
    private final PendingPaymentBillMapper pendingPaymentBillMapper;
    private final PaymentApplicationMapper paymentApplicationMapper;
    private final ViewModelRelService viewModelRelService;
    private final AccountsSuspenseService accountsSuspenseService;
    private final PersonalLoansAndPrepaymentsService personalLoansAndPrepaymentsService;
    private final ReimburseInvoiceService reimburseInvoiceService;

    @Transactional
    public WorkFlowApiRes otherReimbursement(RequestInfo requestInfo){
        List<PendingPaymentBill> pendingPaymentBills = new ArrayList<>();
        TableInfo tableInfo = requestInfo.getTableInfo();
        List<TableInfo> sub = tableInfo.getSub();

        if(sub.isEmpty()){
            return WorkFlowApiRes.ok();
        }

        List<ReimburseInvoiceEntity> reimburseInvoiceEntities = new ArrayList<>();
        String invoiceNumbers = sub.stream()
                .filter(item -> item.getModelName().equals("com_expense_dt1"))
                .map(item -> item.getValue("fphm"))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(","));

        for (TableInfo subInfo : sub) {
            if (subInfo.getModelName().equals("com_expense_dt2")) {
                PendingPaymentBill pendingPaymentBill = newPendingPaymentBill(requestInfo);
                pendingPaymentBill.setBz(tableInfo.getValue("currency"));
                pendingPaymentBill.setCompany(tableInfo.getValue("gs"));
                pendingPaymentBill.setFybm(tableInfo.getValue("cdbm"));

                pendingPaymentBill.setAccount(subInfo.getValue("zhxx"));
                BigDecimal money = BigDecimal.valueOf(subInfo.getDoubleValue("fkje"));
                pendingPaymentBill.setPayMoney(money);
                pendingPaymentBill.setPayMoneyLeave(money);
                pendingPaymentBill.setBank(subInfo.getValue("bank"));
                pendingPaymentBill.setBankNum(subInfo.getValue("bank_num"));
                pendingPaymentBill.setZflx(subInfo.getValue("zflx"));
                pendingPaymentBill.setInvoice(invoiceNumbers);
                pendingPaymentBill.setReimType(tableInfo.getValue("bxlx"));

                pendingPaymentBills.add(pendingPaymentBill);
            }else if (subInfo.getModelName().equals("com_expense_dt3")){
                // 冲销
                Long dataId = Long.parseLong(subInfo.getJsonArrayValue("jkjl").get(0));
                BigDecimal money = BigDecimal.valueOf(subInfo.getDoubleValue("cxje"));
                if(!personalLoansAndPrepaymentsService.writeOffLoan(dataId,money)){
                    return WorkFlowApiRes.error("冲销失败");
                }
            }else if(subInfo.getModelName().equals("com_expense_dt1")){
                if (StrUtil.isBlank(subInfo.getValue("fphm"))){
                    continue;
                }
                // 报销明细
                ReimburseInvoiceEntity reimburseInvoiceEntity = new ReimburseInvoiceEntity();
                reimburseInvoiceEntity.setInvoiceNum(subInfo.getValue("fphm"));
                reimburseInvoiceEntity.setTaxMoney(BigDecimal.valueOf(subInfo.getDoubleValue("fphsje")));
                reimburseInvoiceEntity.setNoTaxMoney(BigDecimal.valueOf(subInfo.getDoubleValue("fpbhsje")));
                reimburseInvoiceEntity.setTaxes(BigDecimal.valueOf(subInfo.getDoubleValue("sj")));
                reimburseInvoiceEntity.setCreateBy(subInfo.getLongValue("create_by"));
                reimburseInvoiceEntity.setUpdateBy(subInfo.getLongValue("update_by"));
                reimburseInvoiceEntity.setSupplier(subInfo.getValue("fpdw"));

                reimburseInvoiceEntities.add(reimburseInvoiceEntity);
            }
        }

        if(!addPendingPaymentBills(pendingPaymentBills)){
            return WorkFlowApiRes.error("添加挂账支付失败");
        }

        if(!reimburseInvoiceService.addReimburseInvoice(reimburseInvoiceEntities)){
            return WorkFlowApiRes.error("记录已报销发票失败");
        }

        return WorkFlowApiRes.ok();
    }

    @Transactional
    public WorkFlowApiRes SubConAndMaterialReimbursement(RequestInfo requestInfo){
        List<PendingPaymentBill> pendingPaymentBills = new ArrayList<>();
        TableInfo tableInfo = requestInfo.getTableInfo();
        List<TableInfo> sub = tableInfo.getSub();

        if(sub.isEmpty()){
            return WorkFlowApiRes.ok();
        }

        List<ReimburseInvoiceEntity> reimburseInvoiceEntities = new ArrayList<>();
        String invoiceNumbers = sub.stream()
                .filter(item -> item.getModelName().equals("lw_cl_expense_dt1"))
                .map(item -> item.getValue("fphm"))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(","));

        for (TableInfo subInfo : sub) {
            if (subInfo.getModelName().equals("lw_cl_expense_dt2")) {
                PendingPaymentBill pendingPaymentBill = newPendingPaymentBill(requestInfo);
                pendingPaymentBill.setBz(tableInfo.getValue("currency"));
                pendingPaymentBill.setCompany(tableInfo.getValue("gs"));
                pendingPaymentBill.setFybm(tableInfo.getValue("fycdbm"));

                pendingPaymentBill.setAccount(subInfo.getValue("account"));
                BigDecimal money = BigDecimal.valueOf(subInfo.getDoubleValue("money"));
                pendingPaymentBill.setPayMoney(money);
                pendingPaymentBill.setPayMoneyLeave(money);
                pendingPaymentBill.setBank(subInfo.getValue("bank"));
                pendingPaymentBill.setBankNum(subInfo.getValue("bank_num"));
                pendingPaymentBill.setZflx("[\"0\"]");
                pendingPaymentBill.setInvoice(invoiceNumbers);
                pendingPaymentBill.setReimType(tableInfo.getValue("expense_type"));

                pendingPaymentBills.add(pendingPaymentBill);
            }else if (subInfo.getModelName().equals("lw_cl_expense_dt3")){
                // 冲销
                Long dataId = Long.parseLong(subInfo.getJsonArrayValue("jkjl").get(0));
                BigDecimal money = BigDecimal.valueOf(subInfo.getDoubleValue("cxje"));
                if(!personalLoansAndPrepaymentsService.writeOffLoan(dataId,money)){
                    return WorkFlowApiRes.error("冲销失败");
                }
            }else if(subInfo.getModelName().equals("lw_cl_expense_dt1")){
                if (StrUtil.isBlank(subInfo.getValue("fphm"))){
                    continue;
                }
                // 报销明细
                ReimburseInvoiceEntity reimburseInvoiceEntity = new ReimburseInvoiceEntity();
                reimburseInvoiceEntity.setInvoiceNum(subInfo.getValue("fphm"));
                reimburseInvoiceEntity.setTaxMoney(BigDecimal.valueOf(subInfo.getDoubleValue("hsje")));
                reimburseInvoiceEntity.setNoTaxMoney(BigDecimal.valueOf(subInfo.getDoubleValue("bhsje")));
                reimburseInvoiceEntity.setTaxes(BigDecimal.valueOf(subInfo.getDoubleValue("sj")));
                reimburseInvoiceEntity.setCreateBy(subInfo.getLongValue("create_by"));
                reimburseInvoiceEntity.setUpdateBy(subInfo.getLongValue("update_by"));
                reimburseInvoiceEntity.setSupplier(subInfo.getValue("fpdw"));

                reimburseInvoiceEntities.add(reimburseInvoiceEntity);
            }
        }

        if(!addPendingPaymentBills(pendingPaymentBills)){
            return WorkFlowApiRes.error("添加挂账支付失败");
        }

        if(!reimburseInvoiceService.addReimburseInvoice(reimburseInvoiceEntities)){
            return WorkFlowApiRes.error("记录已报销发票失败");
        }

        return WorkFlowApiRes.ok();
    }

    @Transactional
    public WorkFlowApiRes travelReimbursement(RequestInfo requestInfo){
        TableInfo tableInfo = requestInfo.getTableInfo();
        List<TableInfo> sub = tableInfo.getSub();

        if(sub.isEmpty()){
            return WorkFlowApiRes.ok();
        }

        for (TableInfo subInfo : sub) {
            if (subInfo.getModelName().equals("out_expense_dt5")){
                // 冲销
                Long dataId = Long.parseLong(subInfo.getJsonArrayValue("jkjl").get(0));
                BigDecimal money = BigDecimal.valueOf(subInfo.getDoubleValue("cjje"));
                if(!personalLoansAndPrepaymentsService.writeOffLoan(dataId,money)){
                    return WorkFlowApiRes.error("冲销失败");
                }
            }
        }

        return WorkFlowApiRes.ok();
    }

    @NotNull
    private Boolean addPendingPaymentBills(List<PendingPaymentBill> pendingPaymentBills) {
        if(pendingPaymentBills.isEmpty()){
            return true;
        }

        log.info("添加挂账支付数据：{}", pendingPaymentBills.toString());

        String modelTable = null;

        modelTable = viewModelRelService.getModelTableNameByModelName("f_ca_loan");

        Integer i = pendingPaymentBillMapper.add(modelTable,pendingPaymentBills);

        if(i>0){
            return true;
        }else {
            return false;
        }
    }

    private PendingPaymentBill newPendingPaymentBill(RequestInfo requestInfo){
        TableInfo tableInfo = requestInfo.getTableInfo();

        PendingPaymentBill pendingPaymentBill = new PendingPaymentBill();
        pendingPaymentBill.setId(IdWorker.getId());
        pendingPaymentBill.setCreateBy(tableInfo.getValue("create_by"));
        pendingPaymentBill.setCreateTime(DateUtil.parse(tableInfo.getValue("create_time")));
        pendingPaymentBill.setUpdateBy(tableInfo.getValue("create_by"));
        pendingPaymentBill.setUpdateTime(DateUtil.parse(tableInfo.getValue("create_time")));
        pendingPaymentBill.setCurrProcInstId("-1");

        pendingPaymentBill.setCode(tableInfo.getValue("code"));
        pendingPaymentBill.setDepartmentid(tableInfo.getValue("sqbm"));
        pendingPaymentBill.setYjxmmc(tableInfo.getValue("yjxmmc"));
        pendingPaymentBill.setYjxmbh(tableInfo.getValue("yjxmbh"));
        pendingPaymentBill.setSqr(tableInfo.getValue("sqr"));
        String sqrqValue = tableInfo.getValue("sqrq");
        if (sqrqValue != null && !sqrqValue.isEmpty()) {
            try {
                pendingPaymentBill.setSqrq(DateUtil.parse(sqrqValue).toLocalDateTime());
            } catch (Exception e) {
                // 处理日期解析异常，可以根据业务需求进行相应处理
                // 例如：记录日志、设置默认值等
                throw new RuntimeException("日期解析失败，sqrq值：" + sqrqValue, e);
            }
        } else {
            // 处理null或空值情况，可以根据业务需求设置默认值或抛出异常
            pendingPaymentBill.setSqrq(null);
        }

        pendingPaymentBill.setFysy(tableInfo.getValue("fysy"));

        return pendingPaymentBill;
    }
    
    /**
     * 预借付款申请
     **/
    public WorkFlowApiRes advancePaymentsAndPersonalLoans(RequestInfo requestInfo){
        TableInfo tableInfo = requestInfo.getTableInfo();
        List<TableInfo> sub = tableInfo.getSub();

        List<PaymentApplication> paymentApplications = new ArrayList<>();
        for (TableInfo subInfo : sub){
            if(subInfo.getModelName().equals("person_loan_dt2")){
                PaymentApplication paymentApplication = newPaymentApplication(tableInfo,subInfo);

                paymentApplication.setSy(tableInfo.getValue("jksqsy"));
                paymentApplication.setCurrency(tableInfo.getValue("money_type"));
                paymentApplication.setPaymentMoney(BigDecimal.valueOf(subInfo.getDoubleValue("zfje")));

                paymentApplications.add(paymentApplication);
            }
        }

        try {
            boolean flag = addPayApp(paymentApplications);

            if(!flag){
                return WorkFlowApiRes.error("新增付款申请单失败！");
            }
        }catch (Exception e){
            log.error(e.getMessage());
            return WorkFlowApiRes.error("新增付款申请单异常！");
        }

        return WorkFlowApiRes.ok();
    }

    /**
     * 财务付款申请
     **/
    @Transactional
    public WorkFlowApiRes financialPayments(RequestInfo requestInfo) {
        TableInfo tableInfo = requestInfo.getTableInfo();
        List<TableInfo> sub = tableInfo.getSub();
        boolean flag = true;

        List<PaymentApplication> paymentApplications = new ArrayList<>();
        for (TableInfo subInfo : sub){
            if(subInfo.getModelName().equals("f_payment_dt1")){
                PaymentApplication paymentApplication = newPaymentApplication(tableInfo,subInfo);

                paymentApplication.setSy(subInfo.getValue("yt"));
                paymentApplication.setCurrency(tableInfo.getValue("currency"));
                paymentApplication.setPaymentMoney(BigDecimal.valueOf(subInfo.getDoubleValue("zfje_bz")));

                paymentApplications.add(paymentApplication);

                String dataId = "";
                try {
                    dataId = subInfo.getJsonArrayValue("zfjl").get(0);
                }catch (Exception e){
                    log.error(e.getMessage());
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return WorkFlowApiRes.error("没有选择挂账流程");
                }

                if(StrUtil.isBlank(dataId)){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return WorkFlowApiRes.error("没有选择挂账流程");
                }
                // 财务挂账支付剩余金额减掉付款金额
                flag = accountsSuspenseService.reducePaymentMoney(Long.valueOf(dataId),BigDecimal.valueOf(subInfo.getDoubleValue("zfje_bz")));

                if(!flag){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return WorkFlowApiRes.error("财务挂账支付金额冻结失败！");
                }
            }
        }

        try {
            flag = addPayApp(paymentApplications);

            if(!flag){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return WorkFlowApiRes.error("新增付款申请单失败！");
            }
        }catch (Exception e){
            log.error(e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return WorkFlowApiRes.error("新增付款申请单异常！");
        }

        return WorkFlowApiRes.ok();
    }

    @NotNull
    public boolean addPayApp(List<PaymentApplication> paymentApplications) {
        String modelTable = null;
        try {
            modelTable = viewModelRelService.getModelTableNameByModelName("payment_application");
        }catch (Exception e){
            log.error(e.getMessage());
            throw new RuntimeException("获取 payment_application 物理表失败");
        }

        int i = paymentApplicationMapper.add(modelTable,paymentApplications);

        if(i>0){
            return true;
        }else {
            return false;
        }
    }

    private PaymentApplication newPaymentApplication(TableInfo tableInfo,TableInfo subInfo){
        PaymentApplication paymentApplication = new PaymentApplication();
        paymentApplication.setId(IdWorker.getId());
        paymentApplication.setCreateBy(Long.valueOf(tableInfo.getValue("create_by")));
        paymentApplication.setCreateTime(DateUtil.date());
        paymentApplication.setUpdateBy(Long.valueOf(tableInfo.getValue("create_by")));
        paymentApplication.setUpdateTime(DateUtil.date());
        paymentApplication.setCurrProcInstId("-1");
        paymentApplication.setIsDeleted(0L);

        paymentApplication.setProject(tableInfo.getValue("yjxmmc"));
        paymentApplication.setProjectCode(tableInfo.getValue("yjxmbh"));
        paymentApplication.setCode(tableInfo.getValue("code"));
        paymentApplication.setSqr(tableInfo.getValue("sqr"));
        paymentApplication.setSqrq(DateUtil.parse(tableInfo.getValue("sqrq")));
        paymentApplication.setSqbm(tableInfo.getValue("sqbm"));

        paymentApplication.setAccount(subInfo.getValue("account"));
        paymentApplication.setBankNum(subInfo.getValue("bank_num"));
        paymentApplication.setBankName(subInfo.getValue("bank"));

        paymentApplication.setStatus("[\"0\"]");

        return paymentApplication;
    }
}