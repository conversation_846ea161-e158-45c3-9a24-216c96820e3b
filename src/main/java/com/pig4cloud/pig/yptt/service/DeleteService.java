package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.entity.vo.ProgressDelVO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.utils.ExcelUtil;
import com.pig4cloud.pig.yptt.utils.LockTimeV3Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @ClassName DeleteService
 * @Description 删除功能 （批量删除）
 * @date 2025/6/12 19:39
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeleteService {
    private final BasicMapper basicMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    protected final RedissonClient redissonClient;
    private final DataMangeService dataMangeService;
    private static final String DELETE_KEY = "deleteBatch:";

    public List<ImportResultVO> deleteBatch(MultipartFile file, String key) {
        final long startTime = System.currentTimeMillis();
        String redisKey = DELETE_KEY + key;
        // 1. 数据读取与基础校验
        List<Map<String, Object>> mapList = read2Map(file);
        if (CollUtil.isEmpty(mapList)) {
            log.warn("Empty file uploaded for deletion");
            return Collections.emptyList();
        }

        // 配置项
        final int MAX_BATCH_SIZE = 100;
        final int PROGRESS_UPDATE_INTERVAL = 5;
        final int REDIS_EXPIRE_MINUTES = 30;
        Assert.isTrue(mapList.size() <= MAX_BATCH_SIZE,
                "Exceed the limit for uploading data {} !", MAX_BATCH_SIZE);
        Map<String, Object> projectIdMap = new HashMap<>();
        // 2. 数据校验阶段
        List<ImportResultVO> checkResult = validateImportData(mapList, projectIdMap);

        // 3. 如果没有错误则异步执行删除
        if (checkResult.stream().noneMatch(r -> ImportResultVO.STATUS_FAILED.equals(r.getStatus()))) {
            executeDeletionAsync(mapList, redisKey, PROGRESS_UPDATE_INTERVAL, REDIS_EXPIRE_MINUTES, projectIdMap);
        }

        log.info("Validation completed in {} ms", System.currentTimeMillis() - startTime);
        return checkResult;
    }

    private List<Map<String, Object>> read2Map(MultipartFile file) {
        try {
            return ExcelUtil.readExcelToMap(file);
        } catch (IOException e) {
            throw new IllegalArgumentException("Parsing excel error", e);
        }
    }

    // 数据校验方法（提取为独立方法）
    private List<ImportResultVO> validateImportData(List<Map<String, Object>> mapList, Map<String, Object> projectIdMap) {
        LockTimeV3Util lockTimeV3Util = new LockTimeV3Util();
        List<ImportResultVO> checkResult = new ArrayList<>();
        Map<String, Object> projectMapTem = new HashMap<>();
        for (int i = 0; i < mapList.size(); i++) {
            Map<String, Object> map = mapList.get(i);
            ImportResultVO resultVO = new ImportResultVO();
            resultVO.setImportData(map);
            resultVO.setStatus(ImportResultVO.STATUS_SUCCEED);
            resultVO.setIndex(i);

            // 必填字段校验
            validateRequiredField(map, resultVO);
            // 项目有效性校验
            if (resultVO.getStatus().equals(ImportResultVO.STATUS_SUCCEED)) {
                String projectCode = map.get("YPTT_Project_code").toString();
                if (!projectMapTem.containsKey(projectCode)) {
                    List<Map<String, Object>> ypttProjectByCode = basicMapper.findYpttProjectByCode(projectCode);
                    if (CollUtil.isEmpty(ypttProjectByCode)) {
                        failValidation(resultVO, "Invalid project number: " + projectCode);
                    }
                    Map<String, Object> projectMap = ypttProjectByCode.get(0);
                    String projectId = projectMap.get("id").toString();
                    projectIdMap.put(projectCode, projectId);
                    // 检查项目锁定状态
                    lockTimeV3Util.checkTimeLock(resultVO, projectCode, null);
                }
                projectMapTem.put(projectCode, projectCode);
            }

            checkResult.add(resultVO);
        }
        return checkResult;
    }

    private void failValidation(ImportResultVO resultVO, String s) {
        resultVO.setStatus(ImportResultVO.STATUS_FAILED);
        resultVO.addWrongReason("YPTT_Project does not exist!");
    }

    private void validateRequiredField(Map<String, Object> map, ImportResultVO resultVO) {
        map.forEach((k, v) -> {
            if (Objects.equals(k, "YPTT_Project_code")) {
                if (ObjectUtil.isEmpty(v)) {
                    resultVO.setStatus(ImportResultVO.STATUS_FAILED);
                    resultVO.addWrongReason("YPTT_Project_code does not exist!");
                }
                String projectCode = v.toString();
                List<Map<String, Object>> ypttProjectByCode = basicMapper.findYpttProjectByCode(projectCode);
                if (ObjectUtil.isEmpty(ypttProjectByCode)) {
                    resultVO.setStatus(ImportResultVO.STATUS_FAILED);
                    resultVO.addWrongReason("Project number incorrect!");
                }
            }
            if (Objects.equals(k, "module")) {
                if (ObjectUtil.isEmpty(v)) {
                    resultVO.setStatus(ImportResultVO.STATUS_FAILED);
                    resultVO.addWrongReason("module does not exist!");
                }
            }
            if (Objects.equals(k, "uniqueness_field")) {
                if (ObjectUtil.isEmpty(v)) {
                    resultVO.setStatus(ImportResultVO.STATUS_FAILED);
                    resultVO.addWrongReason("uniqueness_field does not exist!");
                }

            }
        });
    }

    // 异步执行删除（提取为独立方法）
    private void executeDeletionAsync(List<Map<String, Object>> mapList, String redisKey,
                                      int updateInterval, int expireMinutes, Map<String, Object> projectIdMap) {
        // 初始化进度跟踪
        ProgressDelVO progressVO = new ProgressDelVO(Collections.emptyList(), 0.0);
        redisTemplate.opsForValue().set(redisKey, progressVO, expireMinutes, TimeUnit.MINUTES);

        CompletableFuture.runAsync(() -> {
            List<ImportResultVO> resultList = new ArrayList<>();
            int total = mapList.size();

            for (int i = 0; i < total; i++) {
                Map<String, Object> map = mapList.get(i);
                ImportResultVO resultVO = new ImportResultVO();
                resultVO.setImportData(map);
                resultVO.setIndex(i);
                Map<String, Object> importData = new HashMap<>();
                try {
                    Dict data = new Dict(map);
                    String projectCode = data.getStr("YPTT_Project_code");
                    String module = "all".equals(data.getStr("module")) ? null : data.getStr("module");
                    String uniqueId = data.getStr("uniqueness_field");
                    String projectId = projectIdMap.get(projectCode).toString();
                    importData.put("uniqueness_field",uniqueId);
                    // 执行删除（包含备份）
                    executeDeleteWithBackup(projectCode, module, uniqueId, projectId);
                    resultVO.setStatus(ImportResultVO.STATUS_SUCCEED);
                    resultVO.setImportData(importData);
                } catch (Exception e) {
                    log.error("Deletion failed for record {}: {}", i, e.getMessage(), e);
                    resultVO.setStatus(ImportResultVO.STATUS_FAILED);
                    resultVO.addWrongReason(e.getMessage());
                    resultVO.setImportData(importData);
                } finally {
                    resultList.add(resultVO);
                    updateProgress(redisKey, i+1, total, resultList, updateInterval, expireMinutes);
                }
            }

            // 最终完成状态
//            progressVO = new ProgressDelVO(resultList, 100.0);
            progressVO.setProgress(100.0);
            progressVO.setResultList(resultList);
            redisTemplate.opsForValue().set(redisKey, progressVO, expireMinutes, TimeUnit.MINUTES);
        });
    }

    // 带备份的删除执行
    private void executeDeleteWithBackup(String projectCode, String module, String uniqueId, String projectId) {
//        // 1. 查询要删除的数据并备份
//        List<Map<String, Object>> dataToDelete = basicMapper.findDataToDelete(projectCode, module, uniqueId);
//        if (CollUtil.isEmpty(dataToDelete)) {
//            throw new RuntimeException("No data found matching the criteria");
//        }
//
//        // 2. 创建备份
//        createBackup(dataToDelete);

        // 3. 执行删除
        boolean affectedRows = dataMangeService.deleteData(projectId, projectCode, module, null, null, null, null, null, uniqueId);
        log.info("Deleted {} records for project {} (module: {}, uniqueId: {})",
                affectedRows, projectCode, module, uniqueId);

        // 4. 验证删除结果
        if (!affectedRows) {
            throw new RuntimeException("No records were deleted");
        }
    }

    // 进度更新方法
    private void updateProgress(String redisKey, int processed, int total,
                                List<ImportResultVO> results, int interval, int expireMinutes) {
        if (processed % interval == 0 || processed == total) {
            double progress = (double) processed / total * 100;
            ProgressDelVO vo = new ProgressDelVO(results, progress);
            redisTemplate.opsForValue().set(redisKey, vo, expireMinutes, TimeUnit.MINUTES);
        }
    }

    public ProgressDelVO queryProgressDel(String key) {
        redisTemplate.setValueSerializer(RedisSerializer.java());
        Object o = redisTemplate.opsForValue().get(DELETE_KEY + key);
        return o instanceof ProgressDelVO ? (ProgressDelVO) o : new ProgressDelVO(null, 100.0);
    }

}