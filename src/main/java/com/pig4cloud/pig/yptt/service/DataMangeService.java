package com.pig4cloud.pig.yptt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.pig4cloud.pig.admin.api.dto.UserInfo;
import com.pig4cloud.pig.admin.api.feign.RemoteDeptServiceV2;
import com.pig4cloud.pig.admin.api.feign.RemoteUserServiceV2;
import com.pig4cloud.pig.base.fms.feign.RemoteFileService;
import com.pig4cloud.pig.common.core.constant.SecurityConstants;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.service.PigUser;
import com.pig4cloud.pig.common.security.util.ProxyAuthenticateContext;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.consts.DataLogSource;
import com.pig4cloud.pig.me.api.consts.DataLogType;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataDTO;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataValueDTO;
import com.pig4cloud.pig.me.api.dto.operation.*;
import com.pig4cloud.pig.me.api.entity.DataLog;
import com.pig4cloud.pig.me.api.entity.MetaAttr;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.me.api.feign.RemoteOperationService;
import com.pig4cloud.pig.me.api.vo.ViewConfVO;
import com.pig4cloud.pig.me.api.vo.ViewGroupConfVO;
import com.pig4cloud.pig.yptt.bizcode.RefreshPerCode;
import com.pig4cloud.pig.yptt.bizcode.YpttBizCode;
import com.pig4cloud.pig.yptt.config.RuntimeVisibleThreadPoolExecutor;
import com.pig4cloud.pig.yptt.config.ThreadPoolExecutorProperties;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.ImportRecord;
import com.pig4cloud.pig.yptt.entity.dto.YPTTBatchImportDTO;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo;
import com.pig4cloud.pig.yptt.entity.vo.ProgressY3VO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.mapper.ImportMapper;
import com.pig4cloud.pig.yptt.mapper.RoleMapper;
import com.pig4cloud.pig.yptt.task.ImportTask;
import com.pig4cloud.pig.yptt.task.event.ImportTaskEvent;
import com.pig4cloud.pig.yptt.utils.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.CharEncoding;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/09/04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataMangeService {

    private final RemoteAppService remoteAppService;

    private final DataPermissionsService dataPermissionsService;

    private final ViewConfProperties viewConfProperties;

    private final RemoteFileService remoteFileService;

    private final RemoteDeptServiceV2 remoteDeptService;

    private final RemoteUserServiceV2 remoteUserService;

    private final TransformManager transformManager;

    private final ImportMapper importMapper;

    private final BasicMapper basicMapper;

    private final RoleMapper roleMapper;

    private final RedisTemplate<String, Object> redisTemplate;

    private final Connector2codeService connector2codeService;

    private final TransactionTemplate transactionTemplate;

    private final RuntimeVisibleThreadPoolExecutor importTaskExecutor;

    private final RemoteOperationService remoteOperationService;

    private final ThreadPoolExecutorProperties properties;

    protected final RedissonClient redissonClient;

    private static final String Y3_KEY = "y3:";

    private static final String DELETE_KEY = "delete:";

    private final RedisUtil redisUtil;

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    // @formatter:off
    public final static String[] datetime_fields = new String[]{
            "site_allocation_date", "PO_Received_date", "Start_Working_date", "Completed_work_date",
            "air_CI_Report_submit", "Site_manager_Report", "E_ATP_Pass", "F_PAC_Pass", "G_FAC",

            "settlement_1st", "settlement_2nd", "settlement_3rd", "settlement_4th",
            "SubconSettlement_1st", "SubconSettlement_2nd", "SubconSettlement_3rd", "SubconSettlement_4th",
            "release_date", "Payment_time_1st", "Payment_time_2st", "Payment_time_4st", "Payment_time_3st",
            "Invoice_date_1st", "Invoice_date_2st", "Invoice_date_2nd", "Invoice_date_3rd", "Invoice_date_4st",
    };

    public static final String[] percentage_fields = new String[]{
            "PrePayment_milestone", "Milestone_1st", "Milestone_2nd", "Milestone_3rd", "Milestone_4th",
            "Pre_payment", "Milestone_1st", "Milestone_2nd", "Milestone_3rd", "Milestone_4th"
    };

    public static final String[] amount_fields = new String[]{
            "Unit_price", "Site_value", "Unit_price", "PO_Value", "Subcon_PO_amount", "Totally_payment", "quantity", "Quantity",
            "payment_amount_1st", "payment_amount_2st", "payment_amount_3st", "payment_amount_4st",
            "Invoice_Amount_1st", "Invoice_Amount_2nd", "Invoice_Amount_3rd", "Invoice_Amount_4st", "Invoice_amount"
    };
    // @formatter:on

    /**
     * 异步导入数据
     *
     * @param param OperationBatchImportDTO
     * @return map
     */
    public R<Object> importDataTable(YPTTBatchImportDTO param) {
        PigUser user = SecurityUtils.getUser();
        MultipartFile file = param.getFile();
        String moduleType = param.getModuleType();
        String appId = param.getAppId();
        Long mainViewId = param.getMainViewId();
        Long mainViewGroupId = param.getMainViewGroupId();
        List<Map<String, Object>> maps = read2Map(file);
        Assert.isTrue(maps.size() < 10000, "Exceed the limit for uploading data 10000 !");
        Transformer.TransformContext context = new Transformer.TransformContext(user.getId(), param, maps,
                GlobalConstants.Import.IMPORT_DATA);
        Map<String, Long> departmentCache = context.getDepartmentCache();
        List<Long> ids = new ArrayList<>();
        Map<String, Object> tmpMap = new HashMap<>(); //零时存储 uniqueness_field 和 E_ATP_Pass
        // 处理新增数据
        for (Map<String, Object> map : maps) {
            long snowflakeNextId = IdUtil.getSnowflakeNextId();
            ids.add(snowflakeNextId);
            map.forEach((k, v) -> {
                if (Objects.nonNull(v) && StrUtil.isNotBlank(v.toString())) {
                    if (ArrayUtil.contains(datetime_fields, k)) {
                        v = MetaDataUtil.dateStr2LocalDateTime(v.toString());
                    } else if (ArrayUtil.contains(percentage_fields, k)) {
                        v = MetaDataUtil.percentageStr2BigDecimal(v.toString(), 4, false);
                    } else if (ArrayUtil.contains(amount_fields, k)) {
                        v = MetaDataUtil.numberStr2BigDecimal(v.toString());
                    }
                    map.put(k, v);
                } else {
                    map.put(k, null);
                }
            });
//            if ("y9".equals(moduleType)) {
//                BigDecimal invoice_amount_1st = (BigDecimal) map.get("Invoice_Amount_1st");
//                if (invoice_amount_1st == null){
//                    invoice_amount_1st = new BigDecimal("0");
//                }
//                BigDecimal invoice_amount_2st = (BigDecimal) map.get("Invoice_Amount_2nd");
//                if (invoice_amount_2st == null){
//                    invoice_amount_2st = new BigDecimal("0");
//                }
//                BigDecimal invoice_amount_3st = (BigDecimal) map.get("Invoice_Amount_3rd");
//                if (invoice_amount_3st == null){
//                    invoice_amount_3st = new BigDecimal("0");
//                }
//                BigDecimal invoice_amount_4st = (BigDecimal) map.get("Invoice_Amount_4st");
//                if (invoice_amount_4st == null){
//                    invoice_amount_4st = new BigDecimal("0");
//                }
//                map.put("Invoice_amount", invoice_amount_1st.add(invoice_amount_2st.add(invoice_amount_3st.add(invoice_amount_4st))));
//            }
            map.put("id", snowflakeNextId);
            map.put("is_deleted", 0);
            map.put("create_by", user.getId());
            map.put("create_time", LocalDateTime.now());
            map.put("update_time", LocalDateTime.now());
            map.put("update_by", user.getId());
            map.put("Import_Status", ImportResultVO.STATUS_READY);
            Object department = map.get("Department");
            if (Objects.nonNull(department)) {
                String deptName = department.toString();
                Long deptId = departmentCache.get(deptName);
                if (Objects.nonNull(deptId)) {
                    map.put("Department", MetaDataUtil.handleDataId2Json(deptId));
                } else {
                    List<Long> deptIds = remoteDeptService.getDeptIdByName(deptName, viewConfProperties.getTenantId(),
                            SecurityConstants.FROM_IN);
                    if (CollUtil.isNotEmpty(deptIds)) {
                        map.put("Department", MetaDataUtil.handleDataId2Json(deptIds.get(0)));
                        departmentCache.put(deptName, deptIds.get(0));
                    }
                }
            }
//            LocalDateTime eATPPass = (LocalDateTime) map.get("E_ATP_Pass"); //初验时间
//            if (ObjectUtil.isNotEmpty(eATPPass)) {
//                String unId = (String) map.get("uniqueness_field"); //初验时间
//                //todo 获取y215的金额 并更新y611和y612
//                tmpMap.put(unId, eATPPass);
//            }

        }
        // 保存主数据
        Long mainDataId = saveMainViewData(maps, file, appId, mainViewGroupId, mainViewId);

        // 保存子数据及元关系
        try {
            transactionTemplate.execute(status -> {
                importMapper.batchInsert(maps, moduleType);
                importMapper.batchInsertRel(ids, mainDataId, moduleType);
                return Boolean.TRUE;
            });
        } catch (Exception e) {
            // 子数据插入失败，更新主数据状态为失败
            importMapper.updateImport(MapUtil.of("Status", ImportResultVO.STATUS_FAILED), moduleType, mainDataId);
            throw new BizException(YpttBizCode.DATA_ERROR, e.getMessage());
        }

//        try {
//            List<String> unIds = new ArrayList<>();
//            tmpMap.keySet().forEach((k -> {
//                unIds.add(k);
//            }));
//            if (unIds.size() > 0) {
//                //todo 查询y2的信息
//                List<Map<String, Object>> mapsY2 = importMapper.selectY2Amount(unIds);
//                for (Map<String, Object> m : mapsY2) {
//                    String uniqueness_field = (String) m.get("uniqueness_field");
//                    BigDecimal po_value = (BigDecimal) m.get("PO_value");
//                    Long id = (Long) m.get("id");
//                    LocalDateTime eATPPass = (LocalDateTime) tmpMap.get(uniqueness_field);
//                    m.put("eATPPass", eATPPass);
//                    //todo 更改y6 信息
//                    importMapper.updateY6(eATPPass, po_value, id);
//                }
//            }
//        } catch (Exception e) {
//            log.info("更新y6 信息错误");
//        }

        // 异步开始执行
        try {
            importTaskExecutor.execute(new ImportTask(0, new ProxyAuthenticateContext(user), this, transformManager,
                    dataPermissionsService, importMapper, viewConfProperties, remoteFileService, mainDataId,
                    ImportTaskEvent.builder().dto(param).mainDataId(mainDataId).build()));
            log.info("[{}]导入任务-id[{}]就绪,将导入[{}]条数据", moduleType, mainDataId, maps.size());
        } catch (RejectedExecutionException e) {
            importMapper.updateImport(MapUtil.of("Status", ImportResultVO.STATUS_FAILED), moduleType, mainDataId);
            return R.failed(Boolean.FALSE, YpttBizCode.IMPORT_TASK_FULL_ERROR);
        }
        return R.ok(Boolean.TRUE);
    }

    /**
     * 校验导入数据
     *
     * @param param OperationBatchImportDTO
     * @return Object
     */
    public R<?> checkImportData(YPTTBatchImportDTO param) {
        // 获取当前线程池队列数量
        BlockingQueue<Runnable> taskExecutorQueue = importTaskExecutor.getQueue();
        if (CollUtil.isNotEmpty(taskExecutorQueue)
                && taskExecutorQueue.size() >= Math.min(properties.getQueueCapacity(), 24)) {
            return R.failed(Boolean.FALSE, YpttBizCode.IMPORT_TASK_FULL_ERROR);
        }
        List<Map<String, Object>> mapFail = new ArrayList<>();
        List<Map<String, Object>> maps = read2Map(param.getFile());
        Assert.isTrue(maps.size() <= 10000, "Exceed the limit for uploading data 10000 !");
        // 获取属性字段
        List<ImportResultVO> res = new ArrayList<>();
        Transformer.TransformContext context = new Transformer.TransformContext(SecurityUtils.getUser().getId(), param,
                maps, GlobalConstants.Import.CHECK_DATA);
        Map<String, Long> departmentCache = context.getDepartmentCache();
        for (Map<String, Object> map : maps) {
            int index = maps.indexOf(map);
            ImportResultVO resultVO = transformManager.validate(context, index, map);
            Object department = map.get("Department");
            if (Objects.nonNull(department) && StrUtil.isNotBlank(department.toString())) {
                String deptName = department.toString();
                Long deptId = departmentCache.get(deptName);
                List<Long> deptIds = remoteDeptService.getDeptIdByName(deptName, viewConfProperties.getTenantId(),
                        SecurityConstants.FROM_IN);
                if (Objects.isNull(deptId) && CollUtil.isEmpty(deptIds)) {
                    resultVO.setStatus(ImportResultVO.STATUS_FAILED);
                    resultVO.addWrongReason("This field【Department】of value doesn't exist");
                } else {
                    departmentCache.put(deptName, Objects.isNull(deptId) ? deptIds.get(0) : deptId);
                }
            }
            if (!"SUCCEED".equals(resultVO.getStatus())){
                mapFail.add(map);
            }
            res.add(resultVO);
        }
        // 按状态排序 failed在前
        res.sort(Comparator.comparing(ImportResultVO::getStatus));
        R<List<ImportResultVO>> result = R.ok(res);
        String key = generateRandomString();
        result.setMsg(generateRandomString());
        setFailRecord(mapFail,key);
        return result;
    }

    // 将 List<Map<String, Object>> 转为字符串
    public static String convertToString(List<Map<String, Object>> list) {
        return JSON.toJSONString(list);
    }

    // 将字符串转回 List<Map<String, Object>>
    public static List<Map<String, Object>> convertFromString(String jsonStr) {
        return JSON.parseObject(jsonStr, new TypeReference<List<Map<String, Object>>>() {});
    }

    /**
     * 生成6位数随机字符串
     *
     */
    private static String generateRandomString() {
        String characterSet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        int length = 6;
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(characterSet.length());
            sb.append(characterSet.charAt(index));
        }
        return sb.toString();
    }



    private static final int MAX_PER_KEY = 1000; // 每个Key最大数据量
    private static final long EXPIRE_SECONDS = 600; // 数据过期时间（s）
    /**
     * 将失败数据分片导入到redis存放（每一个key最多1000条数据）
     *
     * @param
     * @return ResponseEntity
     */
    private void setFailRecord(List<Map<String, Object>> res, String keys) {
        if (CollectionUtils.isEmpty(res)) return;
        // 1. 删除旧的失败记录
        if (redisUtil.exists(keys)) {
            redisUtil.delete(keys);
        }

        // 2. 分片存储新数据
        int totalSize = res.size();
        int shardCount = (totalSize + MAX_PER_KEY - 1) / MAX_PER_KEY; // 计算分片数量

        for (int i = 0; i < shardCount; i++) {
            int fromIndex = i * MAX_PER_KEY;
            int toIndex = Math.min(fromIndex + MAX_PER_KEY, totalSize);
            List<Map<String, Object>> shard = res.subList(fromIndex, toIndex);

            // 序列化为JSON存储
            String json = convertToString(shard);
            String key = keys + i;
            redisUtil.set(key,json,EXPIRE_SECONDS);
        }
    }

    /**
     * 下载导入模板
     *
     * @param type type
     * @return ResponseEntity
     */
    @SneakyThrows
    public ResponseEntity<byte[]> downloadTemplate(String type) {
        Assert.isTrue(GlobalConstants.ALL_ITEM_REL.contains(type), "Illegally import template parameters:{}", type);
        ClassPathResource resource = new ClassPathResource("import-template/" + type + ".xlsx");
        String fileName = URLEncoder.encode(GlobalConstants.ExcelUtil.IMPORT_TEMPLATE + type, CharEncoding.UTF_8);
        byte[] fileBytes = FileUtil.readBytes(resource.getFile());
        // 设置HTTP头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", fileName + ".xlsx");
        return ResponseEntity.ok().headers(headers).body(fileBytes);
    }

    public void updateMainViewData(List<ImportResultVO> res, Long dataId, String type) {
        if (CollUtil.isNotEmpty(res)) {
            // 查询更新前的数据
            Map<String, Object> importMap = importMapper.selectImport(dataId, type);
            BigDecimal total = new BigDecimal(importMap.get("Number_Of_Rows").toString());
            ImportRecord importRecord = importMapper.selectItemNumByMainId(type, dataId);
            Integer failNum = importRecord.getFailNum();
            Integer successNum = importRecord.getSuccessNum();
            Integer progress = failNum + successNum;
            Map<String, Object> updateMap = new HashMap<>(16);
            updateMap.put("Success_Count", successNum);
            updateMap.put("Failed_Count", failNum);
            updateMap.put("Progress", progress);
            if (Objects.equals(total.intValue(), progress)
                    || CollUtil.isEmpty(importMapper.selectRelData(dataId, type))) {
                updateMap.put("Status", ImportResultVO.STATUS_COMPLETE);
                updateMap.put("complete_time", LocalDateTime.now());
            }
            importMapper.updateImport(updateMap, type, dataId);
            log.info("[{}]导入任务-id[{}],成功[{}]条,失败[{}]条,总数[{}]条", type, dataId, successNum, failNum, total);
        }
    }

    @SneakyThrows
    public Long saveMainViewData(List<Map<String, Object>> maps, MultipartFile file, String appId, Long viewGroupId,
                                 Long viewId) {
        OperationInsertDTO mainViewInsert = new OperationInsertDTO();
        ViewGroupConfVO viewGroupConfVO = remoteAppService.getViewGroupConfVO(viewGroupId, viewId,
                SecurityConstants.FROM_IN);
        mainViewInsert.setViewGroupId(viewGroupId);
        mainViewInsert.setViewId(viewId);
        if (Objects.nonNull(viewGroupConfVO)) {
            List<MetaDataValueDTO> metaDataValueList = new ArrayList<>();
            List<MetaAttr> metaAttrs = viewGroupConfVO.getAttrList()
                    .stream()
                    .map(ViewConfVO.MetaViewAttrVO::getMetaAttr)
                    .collect(Collectors.toList());
            for (MetaAttr metaAttr : metaAttrs) {
                if (Objects.equals(metaAttr.getName(), "Confirmed_Time")) {
                    MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
                    metaDataValueDTO.setName(metaAttr.getName());
                    metaDataValueDTO.setValue(LocalDateTime.now());
                    metaDataValueList.add(metaDataValueDTO);
                }
                if (Objects.equals(metaAttr.getName(), "Confirmed")) {
                    MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
                    metaDataValueDTO.setName(metaAttr.getName());
                    metaDataValueDTO.setValue(1);
                    metaDataValueList.add(metaDataValueDTO);
                } else if (Objects.equals(metaAttr.getName(), "Number_Of_Rows")) {
                    MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
                    metaDataValueDTO.setName(metaAttr.getName());
                    metaDataValueDTO.setValue(maps.size());
                    metaDataValueList.add(metaDataValueDTO);
                } else if (Objects.equals(metaAttr.getName(), "Uploaded_File")) {
                    // 上传文件
                    String fileId = remoteFileService.upload("", true, "", file, SecurityConstants.FROM_IN);
                    MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
                    metaDataValueDTO.setName(metaAttr.getName());
                    metaDataValueDTO.setValue("[\"" + fileId + "\"]");
                    metaDataValueList.add(metaDataValueDTO);
                } else if (Objects.equals(metaAttr.getName(), "Success_Count")) {
                    MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
                    metaDataValueDTO.setName(metaAttr.getName());
                    metaDataValueDTO.setValue(0);
                    metaDataValueList.add(metaDataValueDTO);
                } else if (Objects.equals(metaAttr.getName(), "Failed_Count")) {
                    MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
                    metaDataValueDTO.setName(metaAttr.getName());
                    metaDataValueDTO.setValue(0);
                    metaDataValueList.add(metaDataValueDTO);
                } else if (Objects.equals(metaAttr.getName(), "Progress")) {
                    MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
                    metaDataValueDTO.setName(metaAttr.getName());
                    metaDataValueDTO.setValue(0);
                    metaDataValueList.add(metaDataValueDTO);
                } else if (Objects.equals(metaAttr.getName(), "Status")) {
                    MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
                    metaDataValueDTO.setName(metaAttr.getName());
                    metaDataValueDTO.setValue(ImportResultVO.STATUS_READY);
                    metaDataValueList.add(metaDataValueDTO);
                } else if (Objects.equals(metaAttr.getName(), "ready_time")) {
                    MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
                    metaDataValueDTO.setName(metaAttr.getName());
                    metaDataValueDTO.setValue(LocalDateTime.now());
                    metaDataValueList.add(metaDataValueDTO);
                }
            }
            mainViewInsert.setData(metaDataValueList);
        }
        return remoteAppService.operationInsert(mainViewInsert, SecurityConstants.FROM_IN, appId);
    }

    private List<Map<String, Object>> read2Map(MultipartFile file) {
        try {
            return ExcelUtil.readExcelToMap(file);
        } catch (IOException e) {
            throw new IllegalArgumentException("Parsing excel error", e);
        }
    }

    public Boolean addExternalCost(OperationInsertDTO dto) {
        Object externalCost = dto.getValue("ExternalCost");
        BigDecimal init = Objects.isNull(externalCost) ? BigDecimal.ZERO : new BigDecimal(externalCost.toString());
        return updateSubconPoExternalCost(dto.getRootDataId(), init, dto.getDataId());
    }

    public Boolean updateExternalCost(OperationUpdateDTO dto) {
        Object externalCost = dto.getValue("ExternalCost");
        BigDecimal init = Objects.isNull(externalCost) ? BigDecimal.ZERO : new BigDecimal(externalCost.toString());
        return updateSubconPoExternalCost(dto.getRootDataId(), init, dto.getDataId());
    }

    /**
     * 更新额外成本
     *
     * @param rootId rootId
     * @param init   当前额外成本
     * @return Boolean
     */
    public Boolean updateSubconPoExternalCost(Long rootId, BigDecimal init, Long dataId) {
        List<Map<String, Object>> maps = importMapper.selectSubconPoExternalCost(rootId);
        BigDecimal resTotal = init;
        if (Objects.nonNull(maps) && maps.size() > 0) {
            for (Map<String, Object> map : maps) {
                if (Objects.equals(map.get("id"), dataId)) {
                    continue;
                }
                Object externalCost = map.get("ExternalCost");
                BigDecimal decimal = Objects.isNull(externalCost) ? BigDecimal.ZERO
                        : new BigDecimal(externalCost.toString());
                resTotal = resTotal.add(decimal);
            }
        }
        return importMapper.updateSubconPoExternalCost(resTotal, rootId) > 0;
    }

    public synchronized Boolean checkImportTask() {
        // 遍历查询每个导入模块
        for (GlobalConstants.ViewConfEnum value : GlobalConstants.ViewConfEnum.values()) {
            String moduleType = value.getModule();
            // 查询未完成对导入任务id
            List<Long> unfinishedImportDataIds = importMapper.selectUnfinishedImportDataId(moduleType);
            if (CollUtil.isNotEmpty(unfinishedImportDataIds)) {
                // 正在进行对任务id集合
                Set<Long> readyTaskIds = new HashSet<>();
                BlockingQueue<Runnable> queue = importTaskExecutor.getQueue();
                for (Runnable runnable : queue) {
                    if (runnable instanceof ImportTask) {
                        ImportTask importTask = (ImportTask) runnable;
                        readyTaskIds.add(importTask.getTaskId());
                    } else {
                        readyTaskIds.add(importTaskExecutor.getTaskId(runnable));
                    }
                }
                log.info("导入任务等待中:{}, 进行中:{}", readyTaskIds, importTaskExecutor.getRunningTasks());
                // 移除进行和等待中的任务
                unfinishedImportDataIds
                        .removeIf(id -> readyTaskIds.contains(id) || importTaskExecutor.getRunningTasks().contains(id));
                if (CollUtil.isEmpty(unfinishedImportDataIds)) {
                    continue;
                }
                for (Long mainId : unfinishedImportDataIds) {
                    log.info("{}导入任务补偿,id:[{}]", moduleType, mainId);
                    YPTTBatchImportDTO param = new YPTTBatchImportDTO();
                    param.setModuleType(moduleType);
                    param.setAppId(viewConfProperties.getAppId());
                    param.setViewGroupId(Long.parseLong(Objects.requireNonNull(getViewIdOrGroupId(moduleType, 1))));
                    param.setViewId(Long.parseLong(Objects.requireNonNull(getViewIdOrGroupId(moduleType, 0))));
                    // 构建用户信息
                    Long importUserId = importMapper.selectUnfinishedImportUserId(moduleType, mainId);
                    UserInfo admin = remoteUserService.getMultiTenantUserInfo(importUserId,
                            viewConfProperties.getTenantId(), SecurityConstants.FROM_IN);
                    Set<String> dbAuthsSet = new HashSet<>();
                    if (ArrayUtil.isNotEmpty(admin.getRoles())) {
                        // 获取角色
                        Arrays.stream(admin.getRoles()).forEach(role -> dbAuthsSet.add(SecurityConstants.ROLE + role));
                        // 获取资源
                        dbAuthsSet.addAll(Arrays.asList(admin.getPermissions()));
                    }
                    Collection<? extends GrantedAuthority> authorities = AuthorityUtils
                            .createAuthorityList(dbAuthsSet.toArray(new String[0]));
                    PigUser pigUser = new PigUser(admin.getSysUser().getUserId(), admin.getDeptId(),
                            admin.getRoleList(), admin.getPostList(), admin.getTenantId(),
                            admin.getSysUser().getPassword(), admin.getSysUser().getPhone(), true, true, true, true,
                            authorities);
                    // 执行任务
                    try {
                        importTaskExecutor
                                .execute(new ImportTask(1, new ProxyAuthenticateContext(pigUser), this, transformManager,
                                        dataPermissionsService, importMapper, viewConfProperties, remoteFileService, mainId,
                                        ImportTaskEvent.builder().dto(param).mainDataId(mainId).build()));
                    } catch (RejectedExecutionException e) {
                        log.error("导入任务补偿失败,id:[{}]", mainId);
                    }
                }
            }
        }
        return Boolean.TRUE;
    }

    private final static Integer PAGE_SIZE = 500;

    public synchronized Boolean incomeCorrection() {
        BiConsumer<Integer, Boolean> incomeCorrection = (i, hasNext) -> {
            while (hasNext) {
                List<Map<String, Object>> maps = basicMapper.incomeTotal(i, PAGE_SIZE);
                if (CollUtil.isNotEmpty(maps)) {
                    basicMapper.incomeCorrection(maps);
                }
                hasNext = maps.size() == PAGE_SIZE;
                i += PAGE_SIZE;
            }
        };
        incomeCorrection.accept(0, Boolean.TRUE);
        return Boolean.TRUE;
    }

    public synchronized Boolean updateSiteState() {
        BiConsumer<Integer, Boolean> updateSiteState = (i, hasNext) -> {
            while (hasNext) {
                List<Map<String, Object>> mapYpttList = basicMapper.selectYPTTSiteStateList(i, PAGE_SIZE);
                List<Map<String, Object>> mapList = basicMapper.selectSiteStateList(i, PAGE_SIZE);
                List<String> ids = new ArrayList<>();
                // YPTT处理
                if (CollUtil.isNotEmpty(mapYpttList)) {
                    for (Map<String, Object> map : mapYpttList) {
                        BigDecimal poValue = MetaDataUtil.handleObject2BigDecimal(map.get("PO_value"), false);
                        BigDecimal siteValue = MetaDataUtil.handleObject2BigDecimal(map.get("Site_value"), false);
                        BigDecimal settlementAmount = MetaDataUtil.handleObject2BigDecimal(map.get("settlement_Amount"),
                                false);
                        BigDecimal invoiceAmount = MetaDataUtil.handleObject2BigDecimal(map.get("Invoice_amount"),
                                false);
                        if (Objects.isNull(poValue) || Objects.isNull(siteValue) || Objects.isNull(settlementAmount)
                                || Objects.isNull(invoiceAmount)) {
                            continue;
                        }
                        if (poValue.compareTo(siteValue) == 0 && poValue.compareTo(settlementAmount) == 0
                                && invoiceAmount.compareTo(settlementAmount) == 0) {
                            ids.add(MetaDataUtil.handleObject2String(map.get("id")));
                        }
                    }
                }

                // 非YPTT处理
                if (CollUtil.isNotEmpty(mapList)) {
                    for (Map<String, Object> map : mapYpttList) {
                        BigDecimal poValue = MetaDataUtil.handleObject2BigDecimal(map.get("PO_value"), false);
                        BigDecimal siteValue = MetaDataUtil.handleObject2BigDecimal(map.get("Site_value"), false);
                        BigDecimal settlementAmount = MetaDataUtil.handleObject2BigDecimal(map.get("settlement_Amount"),
                                false);
                        BigDecimal invoiceAmount = MetaDataUtil.handleObject2BigDecimal(map.get("Invoice_amount"),
                                false);
                        BigDecimal totallyPayment = MetaDataUtil.handleObject2BigDecimal(map.get("Totally_payment"),
                                false);
                        BigDecimal subconPoAmount = MetaDataUtil.handleObject2BigDecimal(map.get("Subcon_PO_amount"),
                                false);
                        if (Objects.isNull(poValue) || Objects.isNull(siteValue) || Objects.isNull(settlementAmount)
                                || Objects.isNull(invoiceAmount) || Objects.isNull(subconPoAmount)
                                || Objects.isNull(totallyPayment)) {
                            continue;
                        }
                        if (poValue.compareTo(siteValue) == 0 && poValue.compareTo(invoiceAmount) == 0
                                && invoiceAmount.compareTo(settlementAmount) == 0
                                && subconPoAmount.compareTo(totallyPayment) == 0) {
                            ids.add(MetaDataUtil.handleObject2String(map.get("id")));
                        }
                    }
                }

                if (CollUtil.isNotEmpty(ids)) {
                    basicMapper.updateSiteState(ids);
                }
                hasNext = (mapYpttList.size() == PAGE_SIZE || mapList.size() == PAGE_SIZE);
                i += PAGE_SIZE;
            }
        };
        updateSiteState.accept(0, Boolean.TRUE);
        return Boolean.TRUE;
    }

    /**
     * 获取视图组信息
     *
     * @param type 导入类型
     * @param num  1 or 0
     * @return
     */
    private String getViewIdOrGroupId(String type, Integer num) {
        if (Objects.equals(type, GlobalConstants.Y1.NAME)) {
            return Objects.equals(num, 0) ? viewConfProperties.getY1Import().getItemViewId()
                    : viewConfProperties.getY1Import().getItemViewGroupId();
        } else if (Objects.equals(type, GlobalConstants.Y2.NAME)) {
            return Objects.equals(num, 0) ? viewConfProperties.getY2Import().getItemViewId()
                    : viewConfProperties.getY2Import().getItemViewGroupId();
        } else if (Objects.equals(type, GlobalConstants.Y3.NAME)) {
            return Objects.equals(num, 0) ? viewConfProperties.getY3Import().getItemViewId()
                    : viewConfProperties.getY3Import().getItemViewGroupId();
        } else if (Objects.equals(type, GlobalConstants.Y4.NAME)) {
            return Objects.equals(num, 0) ? viewConfProperties.getY4Import().getItemViewId()
                    : viewConfProperties.getY4Import().getItemViewGroupId();
        } else if (Objects.equals(type, GlobalConstants.Y8.NAME)) {
            return Objects.equals(num, 0) ? viewConfProperties.getY8Import().getItemViewId()
                    : viewConfProperties.getY8Import().getItemViewGroupId();
        } else if (Objects.equals(type, GlobalConstants.Y9.NAME)) {
            return Objects.equals(num, 0) ? viewConfProperties.getY9Import().getItemViewId()
                    : viewConfProperties.getY9Import().getItemViewGroupId();
        }
        return null;
    }

    public List<ImportResultVO> updateY3(MultipartFile file, String key) {
        Map<String, Object> dateTmp = new HashMap<>(); //存放时间映射表
        String redisKey = Y3_KEY + key;
        List<Map<String, Object>> mapList = read2Map(file);
        Integer maxSize = viewConfProperties.getY3UpdateMaxSize();
        List<ImportResultVO> checkResult = new ArrayList<>();
        if (CollUtil.isEmpty(mapList)) {
            return Collections.emptyList();
        }
        Assert.isTrue(mapList.size() <= maxSize, "Exceed the limit for uploading data {} !", maxSize);
        // 导入校验
        for (Map<String, Object> map : mapList) {
            ImportResultVO importResultVO = new ImportResultVO();
            importResultVO.setImportData(map);
            importResultVO.setStatus(ImportResultVO.STATUS_SUCCEED);
            importResultVO.setIndex(mapList.indexOf(map));
            map.forEach((k, v) -> {
                if (ArrayUtil.contains(DataMangeService.datetime_fields, k) && Objects.nonNull(v)) {
                    try {
                        String dateStr = v instanceof LocalDateTime ? ((LocalDateTime) v).toLocalDate().toString()
                                : v.toString();
                        MetaDataUtil.dateStr2LocalDateTime(dateStr);
                    } catch (Exception e) {
                        importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                        importResultVO.setIndex(mapList.indexOf(map));
                        importResultVO.addWrongReason("This filed 【" + k + "】Incorrect date format");
                    }
                }
                if (Objects.equals(k, "id") && (Objects.isNull(v) || StrUtil.isBlank(v.toString()))) {
                    importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                    importResultVO.addWrongReason("This filed 【" + k + "】cannot be null");
                }
                if (Objects.equals(k, "YPTT_Project_code")) {
                    // 判断是否有编辑权限
                    String userIdStr = roleMapper.getUserIdListByPerType("y3_update", v.toString());
                    JSONArray jsonArray = JSONUtil.parseArray(userIdStr);
                    if (!jsonArray.contains(SecurityUtils.getUser().getId())) {
                        importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                        importResultVO.addWrongReason("NO EDITING PERMISSION !");
                    }
                }
                dateTmp.put(k, v);
            });

            String yptt_project_code = null;
            try {
                yptt_project_code = (String) map.get("YPTT_Project_code");
            } catch (Exception e) {
                importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                importResultVO.addWrongReason("have no projectCode");
            }
            Dict data = new Dict(map);
//            System.out.println("============data.getStr(id)"+data.getStr("id"));
            long id = Long.parseLong(data.getStr("id"));
            System.out.println("==========id"+id);
            Map<String, Object> y3Data = basicMapper.findY3ById(Long.valueOf(data.getStr("id")));
            if (y3Data == null) {
                throw new IllegalStateException("未查询到Y3结算数据，id = " + id);
            }
//            JSONArray uniqueness_field = (JSONArray) y3Data.get("uniqueness_field");
            Object uniquenessObj = y3Data.get("uniqueness_field");
            JSONArray uniqueness_field;
            if (uniquenessObj instanceof JSONArray) {
                uniqueness_field = (JSONArray) uniquenessObj;
            } else if (uniquenessObj instanceof String) {
                uniqueness_field = JSONUtil.parseArray((String) uniquenessObj);
            } else if (uniquenessObj instanceof List) {
                uniqueness_field = new JSONArray((List<?>) uniquenessObj);
            } else {
                throw new IllegalStateException("uniqueness_field 字段类型不支持: " + uniquenessObj.getClass());
            }
            if (ObjectUtil.isEmpty(uniqueness_field)) {
                importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                importResultVO.addWrongReason("y3Data is error ,have no uniqueness_field");
                continue;
            }

            //当前日期是否在锁定日期范围内
            LockTimeV3Util lockTimeV3Util = new LockTimeV3Util();
            if (lockTimeV3Util.checkTimeLock(importResultVO, yptt_project_code, null)) {
                importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                importResultVO.addWrongReason("The current time is locked");
            }
            LocalDate settlement_1st = LockTimeV3Util.toLocalDate(data.get("settlement_1st"));

            LocalDate settlement_2nd = LockTimeV3Util.toLocalDate(data.get("settlement_2nd"));

            LocalDate settlement_3rd = LockTimeV3Util.toLocalDate(data.get("settlement_3rd"));

            LocalDate settlement_4th = LockTimeV3Util.toLocalDate(data.get("settlement_4th"));

            //检查y3的结算日期
            checkY3Settlement(importResultVO, yptt_project_code, data, y3Data, uniqueness_field, lockTimeV3Util, settlement_1st,  settlement_2nd, settlement_3rd, settlement_4th);
            checkResult.add(importResultVO);
        }

        // 存入key
        redisTemplate.opsForValue().set(redisKey, new ProgressY3VO(Collections.emptyList(), 0.0), 5, TimeUnit.MINUTES);
        List<String> failedCollect = checkResult.stream()
                .map(ImportResultVO::getStatus)
                .filter(status -> Objects.equals(status, ImportResultVO.STATUS_FAILED))
                .collect(Collectors.toList());
        // 无错误信息则开始导入
        if (CollUtil.isEmpty(failedCollect)) {
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                List<ImportResultVO> result = new ArrayList<>();
                List<Map<String, Object>> updateList = new ArrayList<>();
                ProgressY3VO vo = new ProgressY3VO();
                int total = mapList.size();
                int progress = 0;
                for (Map<String, Object> map : mapList) {
                    List<MetaDataDTO> dataList = new ArrayList<>();
                    Dict data = new Dict(map);
                    long id = Long.parseLong(data.getStr("id"));
                    Long uniquenessFieldId = basicMapper.findUniquenessIdByY3Id(data.getStr("id"));


                    Map<String, Object> siteDelivery = new HashMap<>();
                    if (!StrUtil.isBlank(data.getStr("settlement_1st"))) {
                        siteDelivery.put("settlement_1st",
                                StrUtil.isBlank(data.getStr("settlement_1st")) ? null : data.getStr("settlement_1st"));
                    }
                    if (!StrUtil.isBlank(data.getStr("settlement_2nd"))) {
                        siteDelivery.put("settlement_2nd",
                                StrUtil.isBlank(data.getStr("settlement_2nd")) ? null : data.getStr("settlement_2nd"));
                    }
                    if (!StrUtil.isBlank(data.getStr("settlement_3rd"))) {
                        siteDelivery.put("settlement_3rd",
                                StrUtil.isBlank(data.getStr("settlement_3rd")) ? null : data.getStr("settlement_3rd"));
                    }
                    if (!StrUtil.isBlank(data.getStr("settlement_4th"))) {
                        siteDelivery.put("settlement_4th",
                                StrUtil.isBlank(data.getStr("settlement_4th")) ? null : data.getStr("settlement_4th"));
                    }
                    if (!StrUtil.isBlank(data.getStr("SubconSettlement_1st"))) {
                        siteDelivery.put("SubconSettlement_1st", StrUtil.isBlank(data.getStr("SubconSettlement_1st")) ? null
                                : data.getStr("SubconSettlement_1st"));
                    }
                    if (!StrUtil.isBlank(data.getStr("SubconSettlement_2nd"))) {
                        siteDelivery.put("SubconSettlement_2nd", StrUtil.isBlank(data.getStr("SubconSettlement_2nd")) ? null
                                : data.getStr("SubconSettlement_2nd"));
                    }
                    if (!StrUtil.isBlank(data.getStr("SubconSettlement_3rd"))) {
                        siteDelivery.put("SubconSettlement_3rd", StrUtil.isBlank(data.getStr("SubconSettlement_3rd")) ? null
                                : data.getStr("SubconSettlement_3rd"));
                    }
                    if (!StrUtil.isBlank(data.getStr("SubconSettlement_4th"))) {
                        siteDelivery.put("SubconSettlement_4th", StrUtil.isBlank(data.getStr("SubconSettlement_4th")) ? null
                                : data.getStr("SubconSettlement_4th"));
                    }
                    siteDelivery.put("id", id);
                    siteDelivery.put("Site_ID", map.get("Site_ID"));
                    siteDelivery.put("Item_code", map.get("Item_code"));
                    siteDelivery.put("Phase", map.get("Phase"));
                    siteDelivery.put("Region", map.get("Region"));
                    siteDelivery.put("Project_code", map.get("YPTT_Project_code"));
                    siteDelivery.put("Site_belong_to", map.get("Site_belong_to"));
                    updateList.add(siteDelivery);
                    try {
                        toY3Connector(siteDelivery, uniquenessFieldId, id);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.info("Y3批量更新失败: {}", e.getMessage());
                        ImportResultVO failed = new ImportResultVO();
                        failed.setStatus(ImportResultVO.STATUS_FAILED);
                        failed.setIndex(mapList.indexOf(map));
                        failed.setImportData(map);
                        failed.addWrongReason(e.getMessage());
                        result.add(failed);
                    } finally {
                        if (++progress % 5 == 0 || progress == total) {
                            vo.setProgress((double) progress / (double) total * 100);
                            redisTemplate.setValueSerializer(RedisSerializer.java());
                            redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
                        }
                    }
                }
                redisTemplate.setValueSerializer(RedisSerializer.java());
                Object o = redisTemplate.opsForValue().get(redisKey);
                vo = o instanceof ProgressY3VO ? (ProgressY3VO) o : new ProgressY3VO(result, 100.0);
                vo.setResultList(result);
                redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
                basicMapper.batchUpdateY3(updateList);
            });
        }
        return checkResult;
    }

    private void checkY3Settlement(ImportResultVO importResultVO, String yptt_project_code, Dict data, Map<String, Object> y3Data, JSONArray uniqueness_field, LockTimeV3Util lockTimeV3Util, LocalDate settlement_1st, LocalDate settlement_2nd, LocalDate settlement_3rd, LocalDate settlement_4th) {
//        //y6的产值申报信息
//        List<Map<String, Object>> report = basicMapper
//                .findProductivityReportByUniquenessId(Long.valueOf(uniqueness_field.get(0).toString()));
//        //数据库中不存在当前结算日期 and 当前上传结算日期在日期锁定范围之内 可以更新上传
//        //数据库中存在当前结算日期 and 当前上传结算日期在日期等于数据库中的日期 and 当前上传日期在锁定范围之内 可以更新上传
//        //数据库中存在当前结算日期 and 当前上传结算日期在日期不等于数据库中的日期 and 当前上传日期在锁定范围之内 and 数据库中日期在日期锁定范围之内 可以更新
//        Map<String, Object> y6Data = report.get(0);

        if (isSettlementDateLocked(LockTimeV3Util.toLocalDate(y3Data.get("settlement_1st")),settlement_1st, yptt_project_code, lockTimeV3Util,importResultVO)) {
            importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
            importResultVO.addWrongReason("The settlement_1st time is locked！");
        }

        if (isSettlementDateLocked(LockTimeV3Util.toLocalDate(y3Data.get("settlement_2nd")),settlement_2nd, yptt_project_code, lockTimeV3Util,importResultVO)) {
            importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
            importResultVO.addWrongReason("The settlement_2nd time is locked！");
        }

        if (isSettlementDateLocked(LockTimeV3Util.toLocalDate(y3Data.get("settlement_3rd")),settlement_3rd, yptt_project_code, lockTimeV3Util,importResultVO)) {
            importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
            importResultVO.addWrongReason("The settlement_3rd time is locked！");
        }

        if (isSettlementDateLocked(LockTimeV3Util.toLocalDate(y3Data.get("settlement_4th")),settlement_4th, yptt_project_code, lockTimeV3Util,importResultVO)) {
            importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
            importResultVO.addWrongReason("The settlement_4th time is locked！");
        }
    }

    /**
     * 检查结算日期是否被锁定（不允许更新）
     *
     * @param dbDate  数据库中的结算日期（可能为 null）
     * @param uploadSettlementDate  上传的结算日期
     * @param projectCode  项目代码（用于锁定检查）
     * @param lockTimeUtil  锁定时间工具类
     * @param importResultVO  用于存储错误信息（可选，如果仅需校验可传 null）
     * @return true=日期被锁定（不允许更新），false=日期未锁定（允许更新）
     */
    public boolean isSettlementDateLocked(
            LocalDate dbDate,
            LocalDate uploadSettlementDate,
            String projectCode,
            LockTimeV3Util lockTimeUtil,
            ImportResultVO importResultVO) {

        // 数据库无记录时，仅检查上传日期是否在锁定范围
        if (dbDate == null) {
            boolean isLocked = lockTimeUtil.checkTimeLock(importResultVO, projectCode, uploadSettlementDate);
            return isLocked; // 锁定则不允许更新
        }

        boolean isUploadDateLocked = lockTimeUtil.checkTimeLock(importResultVO, projectCode, uploadSettlementDate);
        boolean isDbDateLocked = lockTimeUtil.checkTimeLock(importResultVO, projectCode, dbDate);

        // 情况1：上传日期 ≠ 数据库日期，且（上传日期或数据库日期被锁定）
        if (!dbDate.equals(uploadSettlementDate) && (isUploadDateLocked || isDbDateLocked)) {
            return true;
        }
        // 情况2：上传日期 = 数据库日期，但上传日期被锁定
        else if (dbDate.equals(uploadSettlementDate) && isUploadDateLocked) {
            return false;
        }

        return false; // 默认允许更新
    }

    private List<DataLog> buildDataLog(Dict afterData, Long dataId) {
        List<String> filedList = Arrays.asList("settlement_1st", "settlement_2nd", "settlement_3rd", "settlement_4th",
                "SubconSettlement_1st", "SubconSettlement_2nd", "SubconSettlement_3rd", "SubconSettlement_4th");
        Long userId = SecurityUtils.getUser().getId();
        ViewConfProperties.SiteDelivery siteDelivery = viewConfProperties.getSiteDelivery();
        ViewGroupConfVO viewGroupConfVO = remoteAppService.getViewGroupConfVO(siteDelivery.getWarnViewGroupId(),
                siteDelivery.getWarnViewId(), SecurityConstants.FROM_IN);
        List<DataLog> res = new ArrayList<>();
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 查询更新前的数据
        Map<String, Object> beforeData = basicMapper.findY3ById(dataId);
        for (String s : filedList) {
            String beforeTime = Objects.isNull(beforeData.get(s)) ? StrUtil.EMPTY
                    : pattern.format((LocalDateTime) beforeData.get(s));
            if (!Objects.equals(afterData.get(s), beforeTime)) {
                Optional<MetaAttr> metaAttr = viewGroupConfVO.getAttrList()
                        .stream()
                        .map(ViewConfVO.MetaViewAttrVO::getMetaAttr)
                        .filter(attr -> Objects.equals(attr.getPhysicalName(), s))
                        .findFirst();
                DataLog dataLog = new DataLog();
                dataLog.setId(IdUtil.getSnowflakeNextId());
                dataLog.setDataId(dataId);
                dataLog.setModelId(viewGroupConfVO.getMetaModel().getId());
                dataLog.setBeforeData(beforeTime);
                dataLog.setAfterData(Objects.nonNull(afterData.get(s)) ? afterData.get(s).toString() : StrUtil.EMPTY);
                metaAttr.ifPresent(attr -> dataLog.setMetaAttrId(attr.getId()));
                dataLog.setSource(DataLogSource.VIEW_UPDATE.toString());
                dataLog.setType(DataLogType.UPDATE.toString());
                dataLog.setGroupKey(IdUtil.simpleUUID());
                dataLog.setTenantId(viewConfProperties.getTenantId());
                dataLog.setCreateTime(LocalDateTime.now());
                dataLog.setUpdateTime(LocalDateTime.now());
                dataLog.setCreateBy(userId);
                dataLog.setUpdateBy(userId);
                dataLog.setIsDeleted(0L);
                res.add(dataLog);
            }
        }
        return res;
    }

    public void toY3Connector(Map<String, Object> map, Long unFieldId, Long dataId) {
        MetaDataDTOWrapper siteDelivery = new MetaDataDTOWrapper(Collections.singletonList(map));
        siteDelivery.setValue("uniqueness_field", MetaDataUtil.handleDataId2Json(unFieldId));
        OperationUpdateDTO updateDTO = new OperationUpdateDTO();
        updateDTO.setViewGroupId(viewConfProperties.getSiteDelivery().getViewGroupId());
        updateDTO.setRootDataId(null);
        updateDTO.setViewId(viewConfProperties.getSiteDelivery().getViewId());
        updateDTO.setDataId(dataId);
        updateDTO.setData(siteDelivery.getData());
        updateDTO.setChildren(null);
        updateDTO.setPerm(null);
        connector2codeService.y3Connector2code(updateDTO);
    }

    private final static Pattern PATTERN = Pattern.compile("(?!\\[).*(?<!\\])");

    private final static List<String> FILED_JSON_LIST = Arrays.asList("uniqueness_field", "Subcon");
    private final static List<String> DATE_FILED_LIST = Arrays.asList("Start_Working_date", "Completed_work_date", "air_CI_Report_submit", "Site_manager_Report",
            "E_ATP_Pass", "F_PAC_Pass", "G_FAC", "settlement_1st", "settlement_2nd", "settlement_3rd", "settlement_4th",
             "SubconSettlement_1st", "SubconSettlement_2nd", "SubconSettlement_3rd", "SubconSettlement_4th");

    public List<Map<String, Object>> exportY3(OperationPageDTO pageDTO) {
        List<QueryDTO> conditions = pageDTO.getConditions();
        HashMap<String, Object> mapCondition = new HashMap<>();
        if (CollUtil.isNotEmpty(conditions)) {
            conditions.forEach(condition -> {
                String name = condition.getName();
                Object value = condition.getValue();
                String symbol = condition.getSymbol();
                if (Objects.nonNull(value) && StrUtil.isNotBlank(value.toString())) {
                    if (FILED_JSON_LIST.contains(name)) {
//                        mapCondition.put(name, MetaDataUtil.handleDataIdJson2Long(value.toString()));
                        condition.setValue(MetaDataUtil.handleDataIdJson2Long(value.toString()));
                    }else if (DATE_FILED_LIST.contains(name)){
                        // 处理日期比较符号
                        if (StrUtil.isNotBlank(symbol)) {
                            switch (symbol) {
                                case "lt":
                                    condition.setSymbol("<");
                                    break;
                                case "le":
                                    condition.setSymbol("<=");
                                    break;
                                case "eq":
                                    condition.setSymbol("=");
                                    break;
                                case "ge":
                                    condition.setSymbol(">=");
                                    break;
                                case "gt":
                                    condition.setSymbol(">");
                                    break;
                                case "range":
                                    // 处理日期范围
                                    if (value instanceof String) {
                                        try {
                                            com.alibaba.fastjson.JSONArray rangeArray = com.alibaba.fastjson.JSONArray.parseArray((String) value);
//                                            JSONArray rangeArray = JSON.parseArray((String) value);
                                            if (rangeArray.size() == 2) {
                                                condition.setValue(rangeArray);
                                                condition.setSymbol("range");
                                            }
                                        } catch (Exception e) {
                                            log.error("解析日期范围失败", e);
                                        }
                                    }
                                    break;
                                default:
                                    condition.setSymbol("=");
                            }
                        }
                    } else {
//                        mapCondition.put(name, value);
                    }
                }
            });
        }
        PigUser pigUser = SecurityUtils.getUser();
        if (Objects.isNull(pigUser)) {
            return null;
        }
        return basicMapper.exportY3V2(conditions, SecurityUtils.getRoles(), pigUser.getId(),DATE_FILED_LIST);
//        return basicMapper.exportY3(mapCondition, SecurityUtils.getRoles(), pigUser.getId());
    }

    public ProgressY3VO queryProgressY3(String key) {
        redisTemplate.setValueSerializer(RedisSerializer.java());
        Object o = redisTemplate.opsForValue().get(Y3_KEY + key);
        return o instanceof ProgressY3VO ? (ProgressY3VO) o : new ProgressY3VO(null, 100.0);
    }

    public List<Map<String, Object>> deletePreview(String projectCode, String type, String region, String siteId,
                                                   String itemCode, String phase, String PONumber, String unId) {
        return basicMapper.deletePreview(projectCode, type, region, siteId, itemCode, phase, unId);
    }

    private final static List<String> MODULE_LIST = Arrays.asList("y1", "y2", "y3", "y4", "y5", "y6", "y7", "y8", "y9");

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteData(String projectId, String projectCode, String type, String region, String siteId,
                              String itemCode, String phase, String PONumber, String unId) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        LockTimeV3Util lockTimeV3Util = new LockTimeV3Util();
        ImportResultVO importResultVO = new ImportResultVO();
        if (lockTimeV3Util.checkTimeLock(importResultVO, projectCode, null)) {
            throw new BizException(YpttBizCode.LOCK_DELETE_ERROR);
        }
        List<Map<String, Object>> maps = deletePreview(projectCode, "y1", region, siteId, itemCode, phase, PONumber, unId);
        for (Map<String, Object> map : maps) {
            String site_item_status = map.get("Site_item_status").toString();
            //条目状态是否正确 （未关闭状态）
            if (!Objects.equals("未关闭", site_item_status)) {
                //当前站点状态不是未关闭状态就无法删除
                throw new BizException(YpttBizCode.LOCK_DELETE_ERROR_STATUS);
            }
            String uniqueness_field = map.get("uniqueness_field").toString();
            Long id = Long.valueOf(map.get("id").toString());
//            // 查询 唯一标识
//            List<Map<String, Object>> uniquenessByUniquenessField = basicMapper
//                    .findUniquenessByUniquenessField(uniqueness_field);
//            MetaDataDTOWrapper metaDataDTOWrapper = new MetaDataDTOWrapper(uniquenessByUniquenessField); // 当前能够优化数据
            // 判断当前数据是否存在已经锁定的数据 (y6、y9)
            List<Map<String, Object>> report = basicMapper
                    .findProductivityReportByUniquenessId(id);
            if (CollUtil.isNotEmpty(report)) {
                //如果存在y6的数据 ,判断当前对应的产值是否存在， 不允许修改
                Map<String, Object> reportMap = report.get(0);
                //第一次产值申报
                LocalDate report_date_1st = LockTimeV3Util.toLocalDate(reportMap.get("report_date_1st"));
                if (report_date_1st != null) {
                    if (lockTimeV3Util.checkTimeLock(importResultVO, projectCode, report_date_1st)) {
                        //存在第一次产值申报，并且第一次产值申报在锁定的允许时间之外
                        throw new BizException(YpttBizCode.LOCK_DELETE_ERROR_Y6);
                    }
                }
            }
            //判断y9
            List<Map<String, Object>> settlement = basicMapper
                    .findYpttSettlementByUniquenessIdJson(id);
            MetaDataDTOWrapper existingYPTTSettlement = new MetaDataDTOWrapper(settlement);
            LocalDate oldDate1 = LockTimeV3Util.toLocalDate(existingYPTTSettlement.getValue("Invoice_date_1st"));
            if (oldDate1 != null) {
                //已经存在结算金额，并且结算金额在锁定时间之外
                if (lockTimeV3Util.checkTimeLock(importResultVO, projectCode, oldDate1)) {
                    throw new BizException(YpttBizCode.LOCK_DELETE_ERROR_Y9);
                }
            }
            LocalDate oldDate2 = LockTimeV3Util.toLocalDate(existingYPTTSettlement.getValue("Invoice_date_2st"));
            if (oldDate2 != null) {
                //已经存在结算日期，并且结算日期在锁定时间之外
                if (lockTimeV3Util.checkTimeLock(importResultVO, projectCode, oldDate2)) {
                    throw new BizException(YpttBizCode.LOCK_DELETE_ERROR_Y9);

                }
            }
            LocalDate oldDate3 = LockTimeV3Util.toLocalDate(existingYPTTSettlement.getValue("Invoice_date_3st"));
            if (oldDate3 != null) {
                //已经存在结算金额，并且结算金额在锁定时间之外
                if (lockTimeV3Util.checkTimeLock(importResultVO, projectCode, oldDate3)) {
                    throw new BizException(YpttBizCode.LOCK_DELETE_ERROR_Y9);

                }
            }
            LocalDate oldDate4 = LockTimeV3Util.toLocalDate(existingYPTTSettlement.getValue("Invoice_date_4st"));
            if (oldDate4 != null) {
                //已经存在结算金额，并且结算金额在锁定时间之外
                if (lockTimeV3Util.checkTimeLock(importResultVO, projectCode, oldDate4)) {
                    throw new BizException(YpttBizCode.LOCK_DELETE_ERROR_Y9);

                }
            }
        }

        if (CollUtil.isNotEmpty(importTaskExecutor.getRunningTasks())) {
            throw new BizException(YpttBizCode.IMPORT_TASK_RUNNING);
        }
        long endTime = System.currentTimeMillis();
        System.out.println("校验2: " + (endTime - startTime) + " 毫秒。");
//        List<Long> roles = SecurityUtils.getRoles();
//        if (CollUtil.isEmpty(roles) || !roles.contains(1694550407313264642L)) {
//            throw new BizException(YpttBizCode.FORBIDDEN_DELETE_ERROR);
//        }
        long startTimeLock = System.currentTimeMillis(); // 记录开始时间
        RLock redissonClientLock = redissonClient.getLock("delete:" + projectCode);
        boolean lock = false;
        try {
            //等待0秒重试获取锁  表示不再重试
            lock = redissonClientLock.tryLock(0, 120, TimeUnit.SECONDS);
            if (!lock) {
                System.out.println("=======" + projectCode + "-=" + type + unId);
                throw new BizException(RefreshPerCode.LOCK_TIMED_OUT);
            }
            long endTimeLock = System.currentTimeMillis();
            System.out.println("redis分布式锁: " + (endTimeLock - startTimeLock) + " 毫秒。");
            // 删除所有模块
            if (Objects.isNull(type)) {
                for (String moduleType : MODULE_LIST) {
                    deleteIncomeExpenditureByType(projectCode, moduleType, region, siteId, itemCode, phase, unId);
                    basicMapper.deleteData(projectCode, moduleType, region, siteId, itemCode, phase, unId);
                }
                basicMapper.deleteIncomeExpenditure(projectCode, region, siteId, itemCode, phase, unId);
                basicMapper.deleteUniqueness(projectCode, region, siteId, itemCode, phase, unId);
            } else {
                // 1、删除对应条目及模块的收支统计
                deleteIncomeExpenditureByType(projectCode, type, region, siteId, itemCode, phase, unId);
                // 2、删除对应项目条目
                basicMapper.deleteData(projectCode, type, region, siteId, itemCode, phase, unId);
                // 若该条目所有模块删除完毕，删除唯一标识
                Integer mark = null;
                for (String s : MODULE_LIST) {
                    Integer temp = basicMapper.getDataByProjectCode(projectCode, s, region, siteId, itemCode, phase, unId);
                    if (Objects.nonNull(temp)) {
                        mark = temp;
                        break;
                    }
                }
                if (Objects.isNull(mark)) {
                    basicMapper.deleteIncomeExpenditure(projectCode, region, siteId, itemCode, phase, unId);
                    // 删除唯一标识
                    basicMapper.deleteUniqueness(projectCode, region, siteId, itemCode, phase, unId);
                }
            }
            // 清空告警信息
            basicMapper.deleteAllWarnInfo(projectId);
            long endTimeExec = System.currentTimeMillis();
            System.out.println("执行时间: " + (endTimeExec - startTimeLock) + " 毫秒。");
            System.out.println(unId+":删除成功");
        } catch (Exception e) {
            log.error("删除数据失败", e);
//            Thread.currentThread().interrupt(); // 恢复中断状态
            throw new BizException(YpttBizCode.DELETE_DATA_ERROR);
        } finally {
//            if (redissonClientLock.isLocked()) {
//                redissonClientLock.unlock();
//            }
            if (lock && redissonClientLock.isHeldByCurrentThread()){
                redissonClientLock.unlock();
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 根据模块类型删除收支统计
     *
     * @param projectCode 项目编码
     * @param moduleType  模块类型
     * @param region      区域
     * @param siteId      站点id
     * @param itemCode    物料编码
     * @param phase       阶段
     */
    public void deleteIncomeExpenditureByType(String projectCode, String moduleType, String region, String siteId,
                                              String itemCode, String phase, String unId) {
        List<String> uniquenessList = new ArrayList<>();
        List<Map<String, Object>> mapList = basicMapper.deletePreview(projectCode, moduleType, region, siteId, itemCode,
                phase, unId);
        for (Map<String, Object> map : mapList) {
            uniquenessList.add(map.get("id").toString());
        }
        if (CollUtil.isNotEmpty(uniquenessList) && !Objects.equals(moduleType, "y3")) {
            basicMapper.deleteIncomeExpenditureByUniquenessId(uniquenessList, moduleType);
        }

    }

    public List<ImportResultVO> deleteBatch(MultipartFile file, String key) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        String redisKey = DELETE_KEY + key;
        List<Map<String, Object>> mapList = read2Map(file);
        Integer maxSize = 100;
        List<ImportResultVO> checkResult = new ArrayList<>();
        if (CollUtil.isEmpty(mapList)) {
            return Collections.emptyList();
        }
        Assert.isTrue(mapList.size() <= maxSize, "Exceed the limit for uploading data {} !", maxSize);
        // 导入校验
        //1、当前项目是否锁定日期中； 2、删除必须存在唯一识别码删除
        LockTimeV3Util lockTimeV3Util = new LockTimeV3Util();
        for (Map<String, Object> map : mapList) {
            ImportResultVO importResultVO = new ImportResultVO();
            importResultVO.setImportData(map);
            importResultVO.setStatus(ImportResultVO.STATUS_SUCCEED);
            importResultVO.setIndex(mapList.indexOf(map));


            map.forEach((k, v) -> {
                if (Objects.equals(k, "YPTT_Project_code")) {
                    if (ObjectUtil.isEmpty(v)) {
                        importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                        importResultVO.addWrongReason("YPTT_Project_code does not exist!");
                    }
                    String projectCode = v.toString();
                    List<Map<String, Object>> ypttProjectByCode = basicMapper.findYpttProjectByCode(projectCode);
                    if (ObjectUtil.isEmpty(ypttProjectByCode)) {
                        importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                        importResultVO.addWrongReason("Project number incorrect!");
                    }
                    lockTimeV3Util.checkTimeLock(importResultVO, projectCode, null);
                }
                if (Objects.equals(k, "module")) {
                    if (ObjectUtil.isEmpty(v)) {
                        importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                        importResultVO.addWrongReason("module does not exist!");
                    }
                }
                if (Objects.equals(k, "uniqueness_field")) {
                    if (ObjectUtil.isEmpty(v)) {
                        importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                        importResultVO.addWrongReason("uniqueness_field does not exist!");
                    }

                }
            });
            checkResult.add(importResultVO);
        }
        long endTime = System.currentTimeMillis();
        System.out.println("校验: " + (endTime - startTime) + " 毫秒。");
        // 存入key
        redisTemplate.opsForValue().set(redisKey, new ProgressY3VO(Collections.emptyList(), 0.0), 30, TimeUnit.MINUTES);
        List<String> failedCollect = checkResult.stream()
                .map(ImportResultVO::getStatus)
                .filter(status -> Objects.equals(status, ImportResultVO.STATUS_FAILED))
                .collect(Collectors.toList());
        // 无错误信息则开始执行删除功能
        if (CollUtil.isEmpty(failedCollect)) {
            CompletableFuture.runAsync(() -> {
                List<ImportResultVO> result = new ArrayList<>();
                ProgressY3VO vo = new ProgressY3VO();
                int total = mapList.size();
                int progress = 0;
                for (Map<String, Object> map : mapList) {
                    Dict data = new Dict(map);
                    try {
                        String projectCode = data.getStr("YPTT_Project_code");
                        String module = data.getStr("module");
                        String unId = data.getStr("uniqueness_field");
                        List<Map<String, Object>> ypttProjectByCode = basicMapper.findYpttProjectByCode(projectCode);
                        if (ObjectUtil.isEmpty(ypttProjectByCode)) {
                            log.info("当前项目编号错误");
                            throw new RuntimeException("Project number incorrect");
                        }

                        Map<String, Object> projectMap = ypttProjectByCode.get(0);
                        String projectId = projectMap.get("id").toString();
                        if ("all".equals(module)) {
                            module = null;
                        }
                        //执行删除功能
                        deleteData(projectId, projectCode, module, null, null, null, null, null, unId);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.info("批量删除失败: {}", e.getMessage());
                        ImportResultVO failed = new ImportResultVO();
                        failed.setStatus(ImportResultVO.STATUS_FAILED);
                        failed.setIndex(mapList.indexOf(map));
                        failed.setImportData(map);
                        failed.addWrongReason(e.getMessage());
                        result.add(failed);
                    } finally {
                        if (++progress % 5 == 0 || progress == total) {
                            vo.setProgress((double) progress / (double) total * 100);
                            redisTemplate.setValueSerializer(RedisSerializer.java());
                            redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
                        }
                    }
                }
                redisTemplate.setValueSerializer(RedisSerializer.java());
                Object o = redisTemplate.opsForValue().get(redisKey);
                vo = o instanceof ProgressY3VO ? (ProgressY3VO) o : new ProgressY3VO(result, 100.0);
                vo.setResultList(result);
                redisTemplate.opsForValue().set(redisKey, vo, 5, TimeUnit.MINUTES);
            });
        }

        return checkResult;
    }

    @SneakyThrows
    public ResponseEntity<byte[]> downloadDelete() {

        ClassPathResource resource = new ClassPathResource("import-template/delete-batch.xlsx");
        String fileName = URLEncoder.encode(GlobalConstants.ExcelUtil.IMPORT_TEMPLATE, CharEncoding.UTF_8);
        byte[] fileBytes = FileUtil.readBytes(resource.getFile());
        // 设置HTTP头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", fileName + ".xlsx");
        return ResponseEntity.ok().headers(headers).body(fileBytes);
    }

    public Boolean deleteDataPM(String projectId, String projectCode, String type, String region, String siteId, String itemCode, String phase, String poNumber, String unId) {
        return deleteData(projectId, projectCode, type, null, null, null, null, null, unId);
    }

    public List<Map<String, Object>> downloadFailData(String keyPrefix) {
        List<Map<String, Object>> data = new ArrayList<>();
        int shardIndex = 0;
        boolean lastShardFound = false;

        // 设置最大分片数避免无限循环 (15)
        final int MAX_SHARDS = 15;

        while (shardIndex < MAX_SHARDS && !lastShardFound) {
            String redisKey = keyPrefix + shardIndex;

            // 检查键是否存在
            if (!redisUtil.exists(redisKey)) {
                // 遇到不存在的分片，说明已到末尾
                break;
            }

            // 安全获取并转换值
            Object value = redisUtil.get(redisKey);
            if (value == null) {
                // 空值处理：记录日志并继续
                log.warn("Empty value found for key: {}", redisKey);
                shardIndex++;
                continue;
            }

            String jsonData;
            try {
                // 安全类型转换
                jsonData = (String) value;
            } catch (ClassCastException e) {
                log.error("Invalid data type for key: {}", redisKey, e);
                shardIndex++;
                continue;
            }

            // 转换JSON数据
            List<Map<String, Object>> maps = convertFromString(jsonData);

            if (maps == null || maps.isEmpty()) {
                // 空数据分片，可能是异常情况
                log.warn("Empty data list for key: {}", redisKey);
            } else {
                data.addAll(maps);

                // 判断是否为最后一个分片
                if (maps.size() < MAX_PER_KEY) {
                    lastShardFound = true;
                }
            }

            shardIndex++;
        }

        return data;
    }
}
