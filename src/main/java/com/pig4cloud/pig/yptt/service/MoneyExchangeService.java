package com.pig4cloud.pig.yptt.service;

import com.pig4cloud.pig.yptt.controller.ApiRes;
import com.pig4cloud.pig.yptt.mapper.MoneyExchangeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName MoneyExchangeService
 * @Description
 * @date 2025/6/9 10:52
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MoneyExchangeService {
    private final MoneyExchangeMapper moneyExchangeMapper;

    public ApiRes transfer(String moneyInput, String moneyOutput, BigDecimal amount) {
        // 参数校验
        if (StringUtils.isBlank(moneyInput) || StringUtils.isBlank(moneyOutput)) {
            return ApiRes.failed("货币对不能为空");
        }

        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return ApiRes.failed("金额必须是正数");
        }

        // 尝试获取正向汇率
        List<Map<String, Object>> rates = moneyExchangeMapper.selectByParis(moneyInput, moneyOutput);
        boolean isReverseRate = false;

        // 如果正向汇率不存在，尝试获取反向汇率
        if (rates == null || rates.isEmpty()) {
            rates = moneyExchangeMapper.selectByParis(moneyOutput, moneyInput);
            if (rates == null || rates.isEmpty()) {
                return ApiRes.failed("找不到该货币对的汇率信息");
            }
            isReverseRate = true;
        }

        // 提取并验证汇率
        Map<String, Object> rateInfo = rates.get(0);
        Object rateObj = rateInfo.get("exchange_rate");
        if (!(rateObj instanceof BigDecimal)) {
            return ApiRes.failed("汇率格式无效");
        }

        BigDecimal exchangeRate = (BigDecimal) rateObj;
        if (exchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
            return ApiRes.failed("汇率必须是正数");
        }

        // 计算兑换金额
        try {
            BigDecimal result;
            if (isReverseRate) {
                // 反向汇率计算：金额 × 汇率
                result = amount.multiply(exchangeRate).setScale(6, RoundingMode.HALF_UP);
            } else {
                // 正向汇率计算：金额 ÷ 汇率
                result = amount.divide(exchangeRate, 6, RoundingMode.HALF_UP);
            }

            log.info("货币兑换计算: 从{}到{} 金额{} 汇率{} 结果{}",
                    moneyInput, moneyOutput, amount, exchangeRate, result);
            return ApiRes.ok(result);
        } catch (ArithmeticException e) {
            log.error("货币兑换计算错误", e);
            return ApiRes.failed("货币兑换计算错误");
        }
    }
}