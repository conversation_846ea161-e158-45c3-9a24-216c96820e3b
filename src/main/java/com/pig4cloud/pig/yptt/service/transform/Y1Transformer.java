package com.pig4cloud.pig.yptt.service.transform;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.admin.api.feign.RemoteDeptServiceV2;
import com.pig4cloud.pig.common.core.constant.SecurityConstants;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.yptt.bizcode.ExpectedOneButGotMany;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.DataPermissionsService;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import com.pig4cloud.pig.yptt.utils.MetaDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Y1站点信息 转为 项目、唯一标识、站点、站点条目
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
@Component
@Slf4j
public class Y1Transformer extends AbstractTransformer {

	private final static String MODULE_NAME = GlobalConstants.Y1.NAME;

	private final RemoteDeptServiceV2 remoteDeptService;

	public Y1Transformer(RemoteAppService remoteAppService, ViewConfProperties viewConfProperties,
			RemoteDeptServiceV2 remoteDeptService, DataPermissionsService dataPermissionsService,
			BasicMapper basicMapper, RedissonClient redissonClient, DataMangeService dataMangeService) {
		super(remoteAppService, viewConfProperties, dataPermissionsService, basicMapper, redissonClient,
				dataMangeService);
		this.remoteDeptService = remoteDeptService;
	}

	@Override
	public ImportResultVO doValidate(TransformContext context, int index, Map<String, Object> raw,
			ImportResultVO valid) {
		final String appid = context.getAppid();
		final Map<String, Long> departmentCache = context.getDepartmentCache();
		final List<MetaDataDTOWrapper> projectCache = context.getProjectCache();
		final List<MetaDataDTOWrapper> uniquenessCache = context.getUniquenessCache();
		final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
		final List<MetaDataDTOWrapper> siteItemCache = context.getSiteItemCache();

		Dict dict = new Dict(raw);
		String YPTT_Project_code = dict.getStr("YPTT_Project_code");
		String Site_ID = dict.getStr("Site_ID");
		String uniqueness_field = MetaDataUtil.getUniquenessField(dict, GlobalConstants.Y1.NAME);
		String Department = dict.getStr("Department");

		// // 查询 部门
		// Long deptId = findDepartmentIdByName(departmentCache, Department);
		// if (Objects.isNull(deptId)) {
		// valid.setStatus(ImportResultVO.STATUS_FAILED);
		// valid.addWrongReason(String.format("Department [%s] not found.", Department));
		// }

		// 查询 项目
		MetaDataDTOWrapper existingYPTTProject = findYPTTProjectByYPTTProjectCode(appid, projectCache,
				YPTT_Project_code);
		if (Objects.isNull(existingYPTTProject)) {
			valid.setStatus(ImportResultVO.STATUS_FAILED);
			valid.addWrongReason(String.format("Project [%s] not found, please add project first.", YPTT_Project_code));
			return valid;
		}
		else {
			projectCache.add(existingYPTTProject);
		}
		// 项目 权限验证
		this.validatePerm(context, YPTT_Project_code);

		// 查询 站点
		MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
		if (Objects.isNull(existingSite)) {
			valid.addWrongReason(String.format("Site [%s] will be added.", Site_ID));
		}
		else {
			valid.addWrongReason(String.format("Site [%s] will be updated.", Site_ID));
			siteCache.add(existingSite);
		}

		// 查询 唯一标识
		MetaDataDTOWrapper existingUniqueness = findUniquenessByUniquenessField(appid, uniquenessCache,
				uniqueness_field);
		if (Objects.nonNull(existingUniqueness)) {
			uniquenessCache.add(existingUniqueness);
		}

		// 查询 站点条目
		MetaDataDTOWrapper existingSiteItem = Objects.isNull(existingUniqueness) ? null
				: findSiteItemByUniquenessField(appid, siteItemCache, existingUniqueness.getDataId());
		if (Objects.isNull(existingSiteItem) || (Objects.nonNull(existingSiteItem.getDataId())
				&& Objects.isNull(existingSiteItem.getValue("site")))) {
			valid.addWrongReason(String.format("Site item [%s] will be added.", uniqueness_field));
			addRequire(raw, context, valid);
		}
		else {
			boolean isClosedSiteItem = isClosedSiteItem(existingSiteItem);
			if (isClosedSiteItem) {
				valid.setStatus(ImportResultVO.STATUS_FAILED);
				valid.addWrongReason(String.format("Site item [%s] is closed.", uniqueness_field));
				return valid;
			}
			else {
				valid.addWrongReason(String.format("Site item [%s] will be updated.", uniqueness_field));
				updateSupport(raw, context, valid);
			}
			siteItemCache.add(existingSiteItem);
		}

		return valid;
	}

	@Override
	public ImportResultVO doTransform(TransformContext context, int index, Map<String, Object> raw) {
		final String appid = context.getAppid();
		final Map<String, Long> departmentCache = context.getDepartmentCache();
		final List<MetaDataDTOWrapper> projectCache = context.getProjectCache();
		final List<MetaDataDTOWrapper> uniquenessCache = context.getUniquenessCache();
		final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
		final List<MetaDataDTOWrapper> siteItemCache = context.getSiteItemCache();

		Dict dict = new Dict(raw);
		String YPTT_Project_code = dict.getStr("YPTT_Project_code");
		String Region = dict.getStr("Region");
		String Site_ID = dict.getStr("Site_ID");
		String Phase = dict.getStr("Phase");
		String Item_code = dict.getStr("Item_code");
		String uniqueness_field = MetaDataUtil.getUniquenessField(dict, GlobalConstants.Y1.NAME);
		String Department = dict.getStr("Department");
		String YPTT_Project_name = dict.getStr("YPTT_Project_name");
		String Area = dict.getStr("Area");
		String Site_Name = dict.getStr("Site_Name");
		String site_allocation_date = dict.getStr("site_allocation_date");
		String Type_of_service = dict.getStr("Type_of_service");
		String Site_Model = dict.getStr("Site_Model");
		String BOQ_item = dict.getStr("BOQ_item");
		String quantity = dict.getStr("quantity");
		String Unit_price = dict.getStr("Unit_price");
		String Remark = dict.getStr("Remark");
		String record = dict.getStr("re_record");
		Long userId = SecurityUtils.getUser().getId();

		// 查询 项目
		MetaDataDTOWrapper existingYPTTProject = findYPTTProjectByYPTTProjectCode(appid, projectCache,
				YPTT_Project_code);

		Assert.notNull(existingYPTTProject, "Project [{}] not found, please add project first.");
		projectCache.add(existingYPTTProject);

		// 初始化 警告阈值
		initWarningThreshold(appid, existingYPTTProject.getDataId(), new MetaDataDTOWrapper());

		// 查询项目权限表
		MetaDataDTOWrapper project = null;
		try {
//			System.out.println(existingYPTTProject);
			project = new MetaDataDTOWrapper(
					Collections.singletonList(basicMapper.findProjectPerById(existingYPTTProject.getDataId())));
		} catch (Exception e) {
			e.printStackTrace();
		}

		// 查询 唯一标识
		MetaDataDTOWrapper existingUniqueness = findUniquenessByUniquenessField(appid, uniquenessCache,
				uniqueness_field);
		// 唯一标识 只新增、不更新
		if (Objects.isNull(existingUniqueness)) {
			existingUniqueness = new MetaDataDTOWrapper();
			existingUniqueness.setValue("Project_code", YPTT_Project_code);
			existingUniqueness.setValue("Region", Region);
			existingUniqueness.setValue("Site_ID", Site_ID);
			existingUniqueness.setValue("Phase", Phase);
			existingUniqueness.setValue("Item_code", Item_code);
			// 基础信息
			existingUniqueness.setValue("create_by", userId);
			existingUniqueness.setValue("create_time", LocalDateTime.now());
			existingUniqueness.setValue("update_by", userId);
			existingUniqueness.setValue("update_time", LocalDateTime.now());
			existingUniqueness.setValue(UNI_META_ATTR_UNIQUENESS_FIELD, uniqueness_field);
			saveUniqueness(appid, existingUniqueness);
		}
		uniquenessCache.add(existingUniqueness);

		// 唯一标识
		String uniquenessIdJsonString = MetaDataUtil.handleDataId2Json(existingUniqueness.getDataId());

		addY3456789Module(uniquenessIdJsonString, project, context, appid);

		// 查询 站点
		MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
		if (Objects.isNull(existingSite)) {
			existingSite = new MetaDataDTOWrapper();
			existingSite.setValue("Site_Serial_number", Site_ID);
			existingSite.setValue("Project_code", YPTT_Project_code);
			existingSite.setValue("Site_register_date", LocalDateTimeUtil.now());
			// existingSite.setValue("Site_status",
			// MetaDataUtil.handleValues2Json(SITE_STATUE_UNCLOSE));
			existingSite.setValue("site_name", Site_Name);
			existingSite.setValue("Region", Region);
			// 添加权限
			existingSite.setValue("query", project.getValue("y1_query"));
			existingSite.setValue("update", project.getValue("y1_update"));
			existingSite.setValue("del", project.getValue("y1_del"));
		}
		// Y2先导入，可能已经新增 站点，Y1补充部分信息
		existingSite.setValue("Area", Area);
		existingSite.setValue("Region", Region);
		existingSite.setValue("site_allocation_date", LocalDateTime.parse(site_allocation_date));
		saveSite(appid, existingSite);
		siteCache.add(existingSite);
		BigDecimal quantityBigDecimal = MetaDataUtil.numberStr2BigDecimal(quantity, 4, false);
		BigDecimal unitPrice = MetaDataUtil.numberStr2BigDecimal(Unit_price);
		// 站点价值
		BigDecimal siteValue = Objects.isNull(quantityBigDecimal) ? new BigDecimal("0")
				: quantityBigDecimal.multiply(unitPrice).setScale(6, RoundingMode.HALF_UP);

		// 查询 站点条目
		MetaDataDTOWrapper existingSiteItem = findSiteItemByUniquenessField(appid, siteItemCache,
				existingUniqueness.getDataId());

		if (Objects.isNull(existingSiteItem) || (Objects.nonNull(existingSiteItem.getDataId())
				&& Objects.isNull(existingSiteItem.getValue("site")))) {
			if (Objects.isNull(existingSiteItem)) {
				existingSiteItem = new MetaDataDTOWrapper();
				existingSiteItem.setValue("uniqueness_field", uniquenessIdJsonString);
			}
			existingSiteItem.setValue("Project_code", YPTT_Project_code);
			existingSiteItem.setValue("Region", Region);
			existingSiteItem.setValue("Site_ID", Site_ID);
			existingSiteItem.setValue("site_allocation_date", LocalDateTime.parse(site_allocation_date));
			existingSiteItem.setValue("TPTT_Project", MetaDataUtil.handleDataId2Json(existingYPTTProject.getDataId()));
			existingSiteItem.setValue("site", MetaDataUtil.handleDataId2Json(existingSite.getDataId()));
			existingSiteItem.setValue("Site_item_status", MetaDataUtil.handleValues2Json(SITE_STATUE_UNCLOSE));
			existingSiteItem.setValue("Type_of_service", Type_of_service);
			existingSiteItem.setValue("Item_code", Item_code);
			existingSiteItem.setValue("BOQ_item", BOQ_item);
			existingSiteItem.setValue("Phase", Phase);
			existingSiteItem.setValue("warning", MetaDataUtil.handleValues2Json(WARNING_NORMAL));
			existingSiteItem.setValue("Quantity", quantityBigDecimal);
			existingSiteItem.setValue("Unit_price", unitPrice);
			existingSiteItem.setValue("Site_value", siteValue);
			existingSiteItem.setValue("Remark", Remark);
			existingSiteItem.setValue("Site_model", Site_Model);
			existingSiteItem.setValue("re_record", record);
			// 基础信息
			existingSiteItem.setValue("create_by", userId);
			existingSiteItem.setValue("create_time", LocalDateTime.now());
			existingSiteItem.setValue("update_by", userId);
			existingSiteItem.setValue("update_time", LocalDateTime.now());
		}
		else {
			// 仅更新 站点价值 和 备注 + Site_model + Type_of_service
			existingSiteItem.setValue("Quantity", quantityBigDecimal);
			existingSiteItem.setValue("Unit_price", unitPrice);
			existingSiteItem.setValue("Site_value", siteValue);
			existingSiteItem.setValue("Site_model", Site_Model);
			existingSiteItem.setValue("Type_of_service", Type_of_service);
			if (StrUtil.isNotBlank(Remark)) {
				String remark = existingSiteItem.getValue("Remark") + "\n\n" + StrUtil.emptyIfNull(Remark);
				existingSiteItem.setValue("Remark", remark);
			}
		}
		// 连接器
		y1connector(existingSiteItem);

		saveSiteItem(existingSite.getDataId(), existingSiteItem);
		siteItemCache.add(existingSiteItem);

		// 新增PO Item
		final List<MetaDataDTOWrapper> poItemCache = context.getPoItemCache();
		MetaDataDTOWrapper existingPOItem = findPOItemByUniquenessId(appid, poItemCache,
				existingUniqueness.getDataId());
		if (Objects.isNull(existingPOItem)) {
			existingPOItem = new MetaDataDTOWrapper();
			existingPOItem.setValue("Project_code", YPTT_Project_code);
			existingPOItem.setValue("Region", Region);
			existingPOItem.setValue("Site_ID", Site_ID);
			existingPOItem.setValue("Phase", Phase);
			existingPOItem.setValue("Item_code", Item_code);
			existingPOItem.setValue("uniqueness_field", uniquenessIdJsonString);
			// 添加权限
			existingPOItem.setValue("query", project.getValue("y2_query"));
			existingPOItem.setValue("update", project.getValue("y2_update"));
			existingPOItem.setValue("del", project.getValue("y2_del"));
			savePOItem(appid, null, existingPOItem);
		}
		else {
			BigDecimal poValueBigDecimal = MetaDataUtil.handleObject2BigDecimal(existingPOItem.getValue("PO_value"),
					false);
			if (Objects.nonNull(poValueBigDecimal)) {
				existingPOItem.setValue("PO_gap", siteValue.subtract(poValueBigDecimal));
				savePOItem(appid, null, existingPOItem);
			}
		}

		// 查询 收支统计
		final List<MetaDataDTOWrapper> incomeExpenditureCache = context.getIncomeExpenditure();
		MetaDataDTOWrapper existingIncomeExpenditure = findIncomeExpenditureByUniquenessField(appid,
				incomeExpenditureCache, existingUniqueness.getDataId());
		if (Objects.isNull(existingIncomeExpenditure)) {
			existingIncomeExpenditure = new MetaDataDTOWrapper();
			existingIncomeExpenditure.setValue("uniqueness_field",
					MetaDataUtil.handleDataId2Json(existingUniqueness.getDataId()));
			existingIncomeExpenditure.setValue("project_name",
					MetaDataUtil.handleDataId2Json(existingYPTTProject.getDataId()));
		}
		existingIncomeExpenditure.setValue("Item_Value", siteValue);
		existingIncomeExpenditure.setValue("Item_Value_date", LocalDateTime.parse(site_allocation_date));
		saveIncomeExpenditure(existingIncomeExpenditure);
		incomeExpenditureCache.add(existingIncomeExpenditure);

		return new ImportResultVO(index, raw, ImportResultVO.STATUS_SUCCEED, "");
	}

	@Override
	public boolean support(TransformContext context) {
		return Objects.equals(MODULE_NAME, context.getModuleName());
	}

	/**
	 * 部门名称 查询 部门id
	 * @param departmentCache 部门缓存
	 * @param departmentName 部门名称
	 * @return 部门id
	 */
	private Long findDepartmentIdByName(Map<String, Long> departmentCache, String departmentName) {
		if (Objects.nonNull(departmentCache.get(departmentName))) {
			return departmentCache.get(departmentName);
		}
		List<Long> deptIds = remoteDeptService.getDeptIdByName(departmentName, viewConfProperties.getTenantId(),
				SecurityConstants.FROM_IN);
		if (Objects.isNull(deptIds)) {
			return null;
		}
		int size = CollUtil.size(deptIds);
		if (size < 1) {
			return null;
		}
		else if (size > 1) {
			throw new BizException(ExpectedOneButGotMany.EXPECT_ONE_BUT_GOT_MANY);
		}
		else {
			Long deptId = CollUtil.getFirst(deptIds);
			departmentCache.put(departmentName, deptId);
			return CollUtil.getFirst(deptIds);
		}
	}

	/**
	 * 保存 唯一标识完整信息
	 * @param appid appid
	 * @param uniqueness 唯一标识完整信息
	 */
	private void saveUniqueness(String appid, MetaDataDTOWrapper uniqueness) {
		Map<String, Object> uniquenessMap = uniqueness.toMap();
		if (Objects.isNull(uniqueness.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			uniquenessMap.put("id", dataId);
			basicMapper.saveItemData(uniquenessMap, viewConfProperties.getUniqueIdentification().getTableName());
			uniqueness.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(uniquenessMap, viewConfProperties.getUniqueIdentification().getTableName());
		}
	}

	/**
	 * 保存 站点
	 * @param appid appid
	 * @param site 站点
	 */
	private void saveSite(String appid, MetaDataDTOWrapper site) {
		Map<String, Object> siteMap = site.toMap();
		if (Objects.isNull(site.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			siteMap.put("id", dataId);
			basicMapper.saveItemData(siteMap, viewConfProperties.getSite().getTableName());
			site.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(siteMap, viewConfProperties.getSite().getTableName());

		}
	}

}
