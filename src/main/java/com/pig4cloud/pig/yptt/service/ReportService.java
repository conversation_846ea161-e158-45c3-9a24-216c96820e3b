package com.pig4cloud.pig.yptt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.admin.api.dto.UserInfo;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.yptt.entity.StatisticsUserDateOperate;
import com.pig4cloud.pig.yptt.entity.StatisticsUserOperate;
import com.pig4cloud.pig.yptt.entity.dto.StatisticsUserOperateDTO;
import com.pig4cloud.pig.yptt.entity.vo.GetProject;
import com.pig4cloud.pig.yptt.entity.vo.GetUserInfo;
import com.pig4cloud.pig.yptt.entity.vo.StatisticsUserOperateVO;
import com.pig4cloud.pig.yptt.mapper.AdjustExcelMapper;
import com.pig4cloud.pig.yptt.mapper.ReportMapper;
import com.pig4cloud.pig.yptt.utils.LockTimeV3Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ReportService
 * @Description 提供基于用户操作的报告生成服务
 * @date 2025/2/24 13:47
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReportService {

    private final ReportMapper reportMapper;
    private static final String[] MODULE_TYPES = {"Y1", "Y2", "Y3", "Y4", "Y8", "Y9"};
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final Integer DAY_BETWEEN = 31;
    private final AdjustExcelMapper adjustExcelMapper;

    /**
     * 生成指定日期范围内的用户操作报告
     *
     * @param dto 输入参数，包含开始和结束日期
     * @return 操作报告视图对象
     */
    public StatisticsUserOperateVO statisticsUserOperate(StatisticsUserOperateDTO dto) {
        // 输入校验
        if (dto == null || dto.getStartDate() == null || dto.getEndDate() == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        String startDate = dto.getStartDate();
        String endDate = dto.getEndDate();
        validateDateFormat(startDate, endDate);

        List<String> moduleTypes = dto.getModuleTypes();
        Map<String, String> mapModule = new HashMap<>();
        if (ObjectUtils.isEmpty(moduleTypes)) {
            moduleTypes = Arrays.asList(MODULE_TYPES);
        }
        moduleTypes.forEach(v -> {
            mapModule.put(v, v);
        });

        String projectCode = dto.getProjectCode();
        Long userId = dto.getUserId();
//        List<StatisticsUserOperate> operateList = generateOperateListold(startDate, endDate, mapModule, projectCode, userId);
        List<StatisticsUserOperate> operateList = generateOperateList(startDate, endDate, mapModule, projectCode, userId);
        StatisticsUserOperateVO vo = new StatisticsUserOperateVO();
        vo.setStatisticsUserOperates(operateList);
        vo.setModuleTypes(moduleTypes);
        return vo;
    }

    /**
     * 生成指定日期范围内的用户操作列表
     */
    private List<StatisticsUserOperate> generateOperateList(String startDate, String endDate,
                                                            Map<String, String> mapModule,
                                                            String projectCode, Long userId) {
        long startTime = System.currentTimeMillis();
        List<String> datesBetween = getDatesBetween(startDate, endDate);

        // 1. 获取数据
        List<StatisticsUserOperate> operateListFromDb = retrieveOperatesFromDatabase(startDate, endDate,
                mapModule, projectCode, userId);
        Map<String, Map<String, Object>> totalQuantityMap = getTotalQuantity(startDate, endDate,
                mapModule, projectCode, userId);

        // 2. 获取用户信息映射
        Map<Long, String> userMap = getUserInfoMap();

        // 3. 构建操作映射
        Map<String, StatisticsUserOperate> operateMap = buildOperateMap(operateListFromDb);
        List<StatisticsUserOperate> statisticsUserOperates = constructDataSource(operateListFromDb);

        // 4. 创建合并结果集（关键修改点）
        Map<String, StatisticsUserOperate> mergedResults = new ConcurrentHashMap<>();

        // 5. 先处理数据库中的记录
        statisticsUserOperates.forEach(op -> {
            String key = op.getYPTTProjectCode() + ":" + op.getCreateBy();
            StatisticsUserOperate result = new StatisticsUserOperate();
            result.setYPTTProjectName(op.getYPTTProjectName());
            result.setFullName(op.getFullName());
            result.setYPTTProjectCode(op.getYPTTProjectCode());
            result.setCreateBy(op.getCreateBy());
            mergedResults.put(key, result);
        });

        // 6. 合并数量数据（确保所有key都被处理）
        totalQuantityMap.forEach((key, quantityInfo) -> {
            // 分割key获取项目代码和用户ID
            String[] parts = key.split(":", 2);
            System.out.println("============parts 0"+parts[0]);
            String projectCodePart = parts[0];
            Long createBy = parts.length > 1 ? safeParseLong(parts[1]) : null;
            System.out.println("============parts 1"+parts[1]);
            System.out.println("============createB"+createBy);
            // 获取或创建结果对象
            StatisticsUserOperate result = mergedResults.computeIfAbsent(key, k -> {
                StatisticsUserOperate newResult = new StatisticsUserOperate();
                newResult.setYPTTProjectCode(projectCodePart);
                newResult.setCreateBy(createBy);
                // 从userMap获取用户名
                System.out.println("--------------------"+userMap.getOrDefault(createBy, ""));
                newResult.setFullName(userMap.getOrDefault(createBy, ""));
                return newResult;
            });

            // 设置有效数量
            result.setEffectiveQuantity(
                    ((Number)quantityInfo.getOrDefault("effectiveQuantity", 0)).intValue()
            );
        });

        // 7. 生成日期操作状态（并行处理）
        List<StatisticsUserOperate> finalResults = mergedResults.values().parallelStream()
                .map(result -> {
                    List<StatisticsUserDateOperate> dateOperates = generateDateOperates(
                            result.getYPTTProjectCode(),
                            result.getCreateBy(),
                            null, // unId可能为null
                            datesBetween,
                            operateMap,
                            mapModule,
                            result
                    );
                    result.setStatisticsUserDateOperate(dateOperates);
                    return result;
                })
                .collect(Collectors.toList());

        log.info("操作列表生成完成，耗时{}ms，生成{}条记录",
                System.currentTimeMillis() - startTime, finalResults.size());
        return finalResults;
    }

// === 辅助方法 ===

    private Map<Long, String> getUserInfoMap() {
        try {
            List<Map<String, Object>> allUserInfo = reportMapper.getAllUserInfo();
            return allUserInfo.stream()
                    .filter(map -> map.get("id") != null)
                    .collect(Collectors.toMap(
                            map -> ((Number)map.get("id")).longValue(),
                            map -> map.get("fullname") != null ? map.get("fullname").toString() : "",
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return new HashMap<>();
        }
    }

    private Map<String, StatisticsUserOperate> buildOperateMap(List<StatisticsUserOperate> operates) {
        return operates.stream()
                .collect(Collectors.toMap(
                        op -> generateKey(op.getYPTTProjectCode(), op.getCreateTime(),
                                op.getCreateBy(), op.getUnId(), op.getModuleType()),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    private Long safeParseLong(String str) {
        try {
            return str == null || str.isEmpty() ? null : Long.valueOf(str);
        } catch (NumberFormatException e) {
            log.warn("无效的Long格式: {}", str);
            return null;
        }
    }

    /**
     * 生成指定日期范围内的用户操作列表
     */
    private List<StatisticsUserOperate> generateOperateListold(String startDate, String endDate,
                                                            Map<String, String> mapModule, String projectCode, Long userId) {

        long startTime = System.currentTimeMillis();
        List<String> datesBetween = getDatesBetween(startDate, endDate);

        // 1. 获取数据库操作数据（可能为空）
        List<StatisticsUserOperate> operateListFromDb = retrieveOperatesFromDatabase(startDate, endDate, mapModule, projectCode, userId);

        // 2. 获取数量映射（必须执行）
        Map<String, Map<String, Object>> totalQuantityMap = getTotalQuantity(startDate, endDate, mapModule, projectCode, userId);
        //查询人员信息
        Map<Long, String> userMap = new HashMap<>();
        try {
            List<Map<String, Object>> allUserInfo = reportMapper.getAllUserInfo();
            userMap = allUserInfo.stream()
                    .filter(map -> map.get("id") != null)
                    .collect(Collectors.toMap(
                            map -> ((Number)map.get("id")).longValue(),
                            map -> map.get("fullname") != null ? map.get("fullname").toString() : "",
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
        }
        // 3. 处理operateListFromDb为空的情况
        if (operateListFromDb.isEmpty()) {
            Map<Long, String> finalUserMap = userMap;
            return totalQuantityMap.entrySet().stream()
                    .map(entry -> {
                        String[] keys = entry.getKey().split(":");
                        String projectCodeFromMap = keys[0];
                        String createByFromMap = keys.length > 1 ? keys[1] : "";
                        Map<String, Object> value = entry.getValue();
                        Object createBy = value.get("create_by");
                        StatisticsUserOperate result = new StatisticsUserOperate();
                        result.setYPTTProjectCode(projectCodeFromMap);
//                        result.setCreateBy(Long.valueOf(createByFromMap));
                        result.setCreateBy(createByFromMap.isEmpty() ? null : Long.valueOf(createByFromMap));
                        result.setFullName(finalUserMap.getOrDefault(createBy, ""));
                        result.setEffectiveQuantity(
                                ((Number)entry.getValue().getOrDefault("effectiveQuantity", 0)).intValue()
                        );
                        result.setStatisticsUserDateOperate(Collections.emptyList());
                        return result;
                    })
                    .collect(Collectors.toList());
        }

        // 4. 原有处理逻辑（operateListFromDb不为空时）
        Map<String, StatisticsUserOperate> operateMap = operateListFromDb.stream()
                .collect(Collectors.toMap(
                        op -> generateKey(op.getYPTTProjectCode(), op.getCreateTime(), op.getCreateBy(), op.getUnId(), op.getModuleType()),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        List<StatisticsUserOperate> statisticsUserOperates = constructDataSource(operateListFromDb);

        // 5. 合并分组逻辑（包含来自totalQuantityMap的额外数据）
        Map<String, List<StatisticsUserOperate>> groupedOperates = statisticsUserOperates.stream()
                .collect(Collectors.groupingBy(
                        op -> op.getYPTTProjectCode() + ":" + op.getCreateBy()
                ));

        // 6. 确保包含所有totalQuantityMap中的key
        totalQuantityMap.keySet().forEach(key -> {
            if (!groupedOperates.containsKey(key)) {
                String[] parts = key.split(":");
                StatisticsUserOperate dummy = new StatisticsUserOperate();
                dummy.setYPTTProjectCode(parts[0]);
                dummy.setCreateBy(Long.valueOf(parts.length > 1 ? parts[1] : ""));
                groupedOperates.put(key, Collections.singletonList(dummy));
            }
        });

        // 7. 并行处理每个分组
        return groupedOperates.entrySet().parallelStream()
                .map(entry -> {
                    String groupKey = entry.getKey();
                    StatisticsUserOperate firstOperate = entry.getValue().get(0);
                    Map<String, Object> quantityMap = totalQuantityMap.get(groupKey);

                    StatisticsUserOperate result = new StatisticsUserOperate();
                    result.setYPTTProjectName(firstOperate.getYPTTProjectName());
                    result.setFullName(firstOperate.getFullName());
                    result.setYPTTProjectCode(firstOperate.getYPTTProjectCode());
                    result.setCreateBy(firstOperate.getCreateBy());
                    result.setEffectiveQuantity(
                            quantityMap != null ?
                                    ((Number)quantityMap.getOrDefault("effectiveQuantity", 0)).intValue() : 0
                    );

                    List<StatisticsUserDateOperate> dateOperates = generateDateOperates(
                            firstOperate.getYPTTProjectCode(),
                            firstOperate.getCreateBy(),
                            firstOperate.getUnId(),
                            datesBetween,
                            operateMap,
                            mapModule,
                            result
                    );
                    result.setStatisticsUserDateOperate(dateOperates);

                    return result;
                })
                .collect(Collectors.toList());
    }
//    private List<StatisticsUserOperate> generateOperateList(String startDate, String endDate, Map<String, String> mapModule, String projectCode, Long userId) {
//        // 获取数据库中的操作数据
//        List<StatisticsUserOperate> operateListFromDb = retrieveOperatesFromDatabase(startDate, endDate, mapModule, projectCode, userId);
//        if (operateListFromDb.isEmpty()) {
//            return Collections.emptyList();
//        }
//        long startTime = System.currentTimeMillis(); // 记录开始时间
//        // 获取日期范围
//        List<String> datesBetween = getDatesBetween(startDate, endDate);
//
//        // 构建操作查找映射
//        Map<String, StatisticsUserOperate> operateMap = new HashMap<>();
//        for (StatisticsUserOperate op : operateListFromDb) {
//            String key = generateKey(op.getYPTTProjectCode(), op.getCreateTime(), op.getCreateBy(), op.getUnId(), op.getModuleType());
//            if (!operateMap.containsKey(key)) {
//                operateMap.put(key, op);
//            }
//        }
//
//        System.out.println("---------构建操作查找映射耗时：" + (System.currentTimeMillis() - startTime));
//
//        //重新构造数据源operateListFromDb
//        List<StatisticsUserOperate> statisticsUserOperates = constructDataSource(operateListFromDb);
//        System.out.println("---------重新构造数据源耗时：" + (System.currentTimeMillis() - startTime));
//        // 按 projectCode 和 createBy 分组
//        Map<String, List<StatisticsUserOperate>> groupedOperates = new HashMap<>();
//
//        for (StatisticsUserOperate op : statisticsUserOperates) {
//            String groupKey = op.getYPTTProjectCode() + "-" + op.getCreateBy();
//            groupedOperates.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(op);
//        }
//        System.out.println("---------projectCode 和 createBy 分组耗时：" + (System.currentTimeMillis() - startTime));
//        Map<String, Map<String, Object>> totalQuantityMap = getTotalQuantity(startDate, endDate, mapModule, projectCode, userId);
//        // 为每个分组生成结果
//        //并行处理每个分组（核心优化点）
//        List<StatisticsUserOperate> resultList = groupedOperates.entrySet().parallelStream()
//                .map(entry -> {
//                    String groupKey = entry.getKey();
//                    List<StatisticsUserOperate> group = entry.getValue();
//                    StatisticsUserOperate firstOperate = group.get(0);
//
//                    // 创建结果对象
//                    StatisticsUserOperate result = new StatisticsUserOperate();
//                    result.setYPTTProjectName(firstOperate.getYPTTProjectName());
//                    result.setFullName(firstOperate.getFullName());
//                    result.setYPTTProjectCode(firstOperate.getYPTTProjectCode());
//                    result.setCreateBy(firstOperate.getCreateBy());
//
//                    // 设置有效数量（线程安全访问）
//                    Map<String, Object> quantityMap = totalQuantityMap.get(groupKey);
//                    int totalQuantity = quantityMap != null ?
//                            ((Number)quantityMap.getOrDefault("effectiveQuantity", 0)).intValue() : 0;
//                    result.setEffectiveQuantity(totalQuantity);
//
//                    // 生成日期操作状态列表
//                    List<StatisticsUserDateOperate> dateOperates = generateDateOperates(
//                            firstOperate.getYPTTProjectCode(),
//                            firstOperate.getCreateBy(),
//                            firstOperate.getUnId(),
//                            datesBetween,
//                            operateMap,
//                            mapModule,
//                            result
//                    );
//                    result.setStatisticsUserDateOperate(dateOperates);
//                    System.out.println("==================相关数据如下项目-人员:"+entry.getKey());
//                    System.out.println("获取数量总耗时：" + (System.currentTimeMillis() - startTime));
//                    result.setYPTTProjectName(firstOperate.getYPTTProjectName());
//                    result.setFullName(firstOperate.getFullName());
//                    result.setYPTTProjectCode(firstOperate.getYPTTProjectCode());
//                    result.setCreateBy(firstOperate.getCreateBy());
//                    result.setEffectiveQuantity(totalQuantity);
//                    result.setStatisticsUserDateOperate(dateOperates);
//                    return result;
//                })
//                .collect(Collectors.toList());
////        List<StatisticsUserOperate> resultList = new ArrayList<>();
////        for (Map.Entry<String, List<StatisticsUserOperate>> entry : groupedOperates.entrySet()) {
////            List<StatisticsUserOperate> group = entry.getValue();
////            StatisticsUserOperate firstOperate = group.get(0);
////            StatisticsUserOperate result = new StatisticsUserOperate();
////            // 生成日期操作状态列表
////            List<StatisticsUserDateOperate> dateOperates = generateDateOperates(
////                    firstOperate.getYPTTProjectCode(),
////                    firstOperate.getCreateBy(),
////                    firstOperate.getUnId(),
////                    datesBetween,
////                    operateMap,
////                    mapModule,
////                    result
////            );
////            System.out.println("生成日期操作状态列表耗时：" + (System.currentTimeMillis() - startTime));
////            Map<String, Object> map = totalQuantityMap.get(entry.getKey());
////            int totalQuantity = map != null ? ((Number)map.getOrDefault("effectiveQuantity", 0)).intValue() : 0;
////
////            System.out.println("==================相关数据如下项目-人员:"+entry.getKey());
////            System.out.println("获取数量总耗时：" + (System.currentTimeMillis() - startTime));
////            result.setYPTTProjectName(firstOperate.getYPTTProjectName());
////            result.setFullName(firstOperate.getFullName());
////            result.setYPTTProjectCode(firstOperate.getYPTTProjectCode());
////            result.setCreateBy(firstOperate.getCreateBy());
////            result.setEffectiveQuantity(totalQuantity);
////            result.setStatisticsUserDateOperate(dateOperates);
////            resultList.add(result);
////        }
//        System.out.println("---------执行完成、总耗时：" + (System.currentTimeMillis() - startTime));
//        return resultList;
//    }

    //获取有效条目数量
    private Map<String, Map<String, Object>> getTotalQuantity(String startDate, String endDate, Map<String, String> mapModule, String projectCode, Long userId) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        //            // 合并数量并创建新对象
        int totalQuantity = 0;
//            for (StatisticsUserOperate op : group) {
//                totalQuantity += op.getEffectiveQuantity();
//            }
        //获取有效更新数量
        List<Map<String, Object>> resultList = new ArrayList<>();
        CompletableFuture<List<Map<String, Object>>> futureY1 = CompletableFuture.supplyAsync(() -> {
            if (mapModule.containsKey("Y1")) {
                return reportMapper.statisticsUserOperateY1V2(startDate, endDate, projectCode, userId);
            }
            return new ArrayList<Map<String, Object>>();
        });

        CompletableFuture<List<Map<String, Object>>> futureY2 = CompletableFuture.supplyAsync(() -> {
            if (mapModule.containsKey("Y2")) {
                return reportMapper.statisticsUserOperateY2V2(startDate, endDate, projectCode, userId);
            }
            return new ArrayList<Map<String, Object>>();
        });

        CompletableFuture<List<Map<String, Object>>> futureY3 = CompletableFuture.supplyAsync(() -> {
            if (mapModule.containsKey("Y3")) {
                return reportMapper.statisticsUserOperateY3V2(startDate, endDate, projectCode, userId);
            }
            return new ArrayList<Map<String, Object>>();
        });
        CompletableFuture<List<Map<String, Object>>> futureY4 = CompletableFuture.supplyAsync(() -> {
            if (mapModule.containsKey("Y4")) {
                return reportMapper.statisticsUserOperateY4V2(startDate, endDate, projectCode, userId);
            }
            return new ArrayList<Map<String, Object>>();
        });

        CompletableFuture<List<Map<String, Object>>> futureY8 = CompletableFuture.supplyAsync(() -> {
            if (!mapModule.containsKey("Y8")) {
                return new ArrayList<Map<String, Object>>();
            }

            List<Map<String, Object>> maps = reportMapper.statisticsUserOperateY8V2(startDate, endDate, projectCode, userId);
            List<Map<String, Object>> processedMaps = new ArrayList<>();

            for (Map<String, Object> map : maps) {
                Map<String, Object> newMap = new HashMap<>(map);
                newMap.put("Project_code", map.get("Project_code"));
                newMap.put("create_by", map.get("create_by"));
                int count = countQuantity(
                        LockTimeV3Util.toLocalDate(map.get("Payment_time_1st")),
                        LockTimeV3Util.toLocalDate(map.get("Payment_time_2st")),
                        LockTimeV3Util.toLocalDate(map.get("Payment_time_3st")),
                        LockTimeV3Util.toLocalDate(map.get("Payment_time_4st")),
                        LockTimeV3Util.toLocalDate(startDate),
                        LockTimeV3Util.toLocalDate(endDate)
                );
                newMap.put("effectiveQuantity", count);
                processedMaps.add(newMap);
            }

            return processedMaps;
        });

        CompletableFuture<List<Map<String, Object>>> futureY9 = CompletableFuture.supplyAsync(() -> {
            if (!mapModule.containsKey("Y9")) {
                return new ArrayList<Map<String, Object>>();
            }

            List<Map<String, Object>> maps = reportMapper.statisticsUserOperateY9V2(startDate, endDate, projectCode, userId);
            List<Map<String, Object>> processedMaps = new ArrayList<>();

            for (Map<String, Object> map : maps) {
                Map<String, Object> newMap = new HashMap<>(map);
                newMap.put("Project_code", map.get("Project_code"));
                newMap.put("create_by", map.get("create_by"));
                int count = countQuantity(
                        LockTimeV3Util.toLocalDate(map.get("Invoice_date_1st")),
                        LockTimeV3Util.toLocalDate(map.get("Invoice_date_2st")),
                        LockTimeV3Util.toLocalDate(map.get("Invoice_date_3st")),
                        LockTimeV3Util.toLocalDate(map.get("Invoice_date_4st")),
                        LockTimeV3Util.toLocalDate(startDate),
                        LockTimeV3Util.toLocalDate(endDate)
                );
                newMap.put("effectiveQuantity", count);
                processedMaps.add(newMap);
            }

            return processedMaps;
        });

        // 等待所有异步任务执行完成
        try {
            addResult(resultList , futureY1.get());
            addResult(resultList , futureY2.get());
            addResult(resultList , futureY3.get());
            addResult(resultList , futureY4.get());
            addResult(resultList , futureY8.get());
            addResult(resultList , futureY9.get());
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("---------查询有效数量执行完成、总耗时：" + (System.currentTimeMillis() - startTime));
        List<Map<String, Object>> projects = reportMapper.selectProjects();
        Map<String, Object> projectTemp = new HashMap<>();
        for (Map<String, Object> project : projects) {
            String pCode = (String) project.get("YPTT_Project_code");
            String projectName = (String) project.get("YPTT_Project_name");
            if (pCode != null && projectName != null) {
                projectTemp.put(pCode, projectName);
            }
        }
        // 将 List 转换为 Map
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        for (Map<String, Object> map : resultList) {
            String key = map.get("Project_code") + ":" + map.get("create_by");
            //添加项目名称
            String project_code = map.get("Project_code").toString();
            map.put("YPTT_Project_name", projectTemp.get(project_code));
            resultMap.put(key, map);
        }
        return resultMap;
    }

    private void addResult(List<Map<String, Object>> result, List<Map<String, Object>> pramResult) {
        // 创建一个临时Map用于快速查找已存在的记录
        Map<String, Map<String, Object>> tempMap = new HashMap<>();

        // 先将result中的记录放入tempMap
        for (Map<String, Object> map : result) {
            String key = map.get("Project_code") + ":" + map.get("create_by");
            tempMap.put(key, map);
        }

        // 处理pramResult中的记录
        for (Map<String, Object> map : pramResult) {
            String key = map.get("Project_code") + ":" + map.get("create_by");
            Map<String, Object> existingMap = tempMap.get(key);

            if (existingMap != null) {
                // 如果key已存在，累加effectiveQuantity
                Object effectiveQuantity = map.get("effectiveQuantity");
                Object existingQuantity = existingMap.get("effectiveQuantity");

                // 处理可能的null值和不同类型
                double newValue = 0;
                if (effectiveQuantity != null) {
                    newValue += ((Number) effectiveQuantity).doubleValue();
                }
                if (existingQuantity != null) {
                    newValue += ((Number) existingQuantity).doubleValue();
                }

                existingMap.put("effectiveQuantity", newValue);
            } else {
                // 如果key不存在，添加新记录
                Map<String, Object> newMap = new HashMap<>(map);
                tempMap.put(key, newMap);
            }
        }

        // 清空原result并重新填充
        result.clear();
        result.addAll(tempMap.values());
    }

    private Integer countQuantity(LocalDate time_1st, LocalDate time_2st,
                                  LocalDate time_3st, LocalDate time_4st,
                                  LocalDate startDate, LocalDate endDate) {
        int count = 0;

        if (time_1st != null && !time_1st.isBefore(startDate) && !time_1st.isAfter(endDate)) {
            count++;
        }
        if (time_2st != null && !time_2st.isBefore(startDate) && !time_2st.isAfter(endDate)) {
            count++;
        }
        if (time_3st != null && !time_3st.isBefore(startDate) && !time_3st.isAfter(endDate)) {
            count++;
        }
        if (time_4st != null && !time_4st.isBefore(startDate) && !time_4st.isAfter(endDate)) {
            count++;
        }

        return count;
    }


    /**
     * 构造数据源，分组并去重
     */
    private List<StatisticsUserOperate> constructDataSource(List<StatisticsUserOperate> operateListFromDb) {
        // 使用 Map 按复合键（YPTTProjectCode + CreateBy + UnId）分组并累加 EffectiveQuantity
        Map<String, StatisticsUserOperate> groupMap = new HashMap<>();
        for (StatisticsUserOperate operate : operateListFromDb) {
            String key = operate.getYPTTProjectCode() + ":" +
                    operate.getCreateBy() + ":" + operate.getUnId();
            groupMap.put(key, operate);
        }

        // 使用 Set 去重 UnId，确保结果中 UnId 唯一
        Set<String> uniqueUnIds = new HashSet<>();
        List<StatisticsUserOperate> result = new ArrayList<>();
        for (StatisticsUserOperate operate : groupMap.values()) {
            if (uniqueUnIds.add(operate.getUnId())) {
                result.add(operate);
            }
        }

        return result;
    }

    /**
     * 生成日期操作状态列表
     */
    private List<StatisticsUserDateOperate> generateDateOperates(String projectCode, Long createBy, String unId,
                                                                 List<String> dates, Map<String, StatisticsUserOperate> operateMap,
                                                                 Map<String, String> mapModule, StatisticsUserOperate result) {
        List<StatisticsUserDateOperate> dateOperates = new ArrayList<>();
        Integer effectiveDays = 0;

        for (String date : dates) {
            StatisticsUserDateOperate dateOperate = new StatisticsUserDateOperate();
            dateOperate.setOperateDate(date);
            Integer effectiveDays1 = 0; //Y1和Y3
            Integer effectiveDays2 = 0; //Y2和Y9
            Integer effectiveDays3 = 0; //Y4和Y8
            for (String moduleType : mapModule.keySet()) {
                String key = generateKey(projectCode, date, createBy, unId, moduleType);
                String operated = operateMap.containsKey(key) ? "Y" : "N";
                setModuleOperate(dateOperate, moduleType, operated);
                if ("Y".equals(operated)) {
                    if ("Y1".equals(moduleType) || "Y3".equals(moduleType)) {
                        effectiveDays1 = 1;
                    }
                    if ("Y2".equals(moduleType) || "Y9".equals(moduleType)) {
                        effectiveDays2 = 1;
                    }
                    if ("Y4".equals(moduleType) || "Y8".equals(moduleType)) {
                        effectiveDays3 = 1;
                    }
                }
            }
            effectiveDays = effectiveDays + effectiveDays1 + effectiveDays2 + effectiveDays3;
            dateOperates.add(dateOperate);
        }
        result.setEffectiveDays(effectiveDays);
        return dateOperates;
    }

//    /**
//     * 从数据库检索操作数据
//     */
//    private List<StatisticsUserOperate> retrieveOperatesFromDatabase(String startDate, String endDate, Map<String, String> mapModule, String projectCode, Long userId) {
//        List<StatisticsUserOperate> result = new ArrayList<>();
//        if (mapModule.containsKey("Y1")) {
//            result.addAll(reportMapper.statisticsUserOperateY1(startDate, endDate, projectCode, userId));
//        }
//        if (mapModule.containsKey("Y2")) {
//            result.addAll(reportMapper.statisticsUserOperateY2(startDate, endDate, projectCode, userId));
//        }
//        if (mapModule.containsKey("Y3")) {
//            result.addAll(reportMapper.statisticsUserOperateY3(startDate, endDate, projectCode, userId));
//        }
//        if (mapModule.containsKey("Y4")) {
//            result.addAll(reportMapper.statisticsUserOperateY4(startDate, endDate, projectCode, userId));
//        }
//        if (mapModule.containsKey("Y8")) {
//            result.addAll(reportMapper.statisticsUserOperateY8(startDate, endDate, projectCode, userId));
//        }
//        if (mapModule.containsKey("Y9")) {
//            result.addAll(reportMapper.statisticsUserOperateY9(startDate, endDate, projectCode, userId));
//        }
//        return result;
//    }

    /**
     * 从数据库检索操作数据（异步方式）
     */
    private List<StatisticsUserOperate> retrieveOperatesFromDatabase(String startDate, String endDate, Map<String, String> mapModule, String projectCode, Long userId) {
        List<StatisticsUserOperate> result = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        System.out.println("执行开始" + simpleDateFormat.format(new Date()));
        // 创建多个异步任务
        CompletableFuture<List<StatisticsUserOperate>> futureY1 = CompletableFuture.supplyAsync(() -> {
            if (mapModule.containsKey("Y1")) {
                return reportMapper.statisticsUserOperateY1(startDate, endDate, projectCode, userId);
            }
            return new ArrayList<StatisticsUserOperate>();
        });

        CompletableFuture<List<StatisticsUserOperate>> futureY2 = CompletableFuture.supplyAsync(() -> {
            if (mapModule.containsKey("Y2")) {
                return reportMapper.statisticsUserOperateY2(startDate, endDate, projectCode, userId);
            }
            return new ArrayList<StatisticsUserOperate>();
        });

        CompletableFuture<List<StatisticsUserOperate>> futureY3 = CompletableFuture.supplyAsync(() -> {
            if (mapModule.containsKey("Y3")) {
                return reportMapper.statisticsUserOperateY3(startDate, endDate, projectCode, userId);
            }
            return new ArrayList<StatisticsUserOperate>();
        });

        CompletableFuture<List<StatisticsUserOperate>> futureY4 = CompletableFuture.supplyAsync(() -> {
            if (mapModule.containsKey("Y4")) {
                return reportMapper.statisticsUserOperateY4(startDate, endDate, projectCode, userId);
            }
            return new ArrayList<StatisticsUserOperate>();
        });

        CompletableFuture<List<StatisticsUserOperate>> futureY8 = CompletableFuture.supplyAsync(() -> {
            if (mapModule.containsKey("Y8")) {
                return reportMapper.statisticsUserOperateY8(startDate, endDate, projectCode, userId);
            }
            return new ArrayList<StatisticsUserOperate>();
        });

        CompletableFuture<List<StatisticsUserOperate>> futureY9 = CompletableFuture.supplyAsync(() -> {
            if (mapModule.containsKey("Y9")) {
                return reportMapper.statisticsUserOperateY9(startDate, endDate, projectCode, userId);
            }
            return new ArrayList<StatisticsUserOperate>();
        });

        // 等待所有异步任务执行完成
        try {
            result.addAll(futureY1.get());
            result.addAll(futureY2.get());
            result.addAll(futureY3.get());
            result.addAll(futureY4.get());
            result.addAll(futureY8.get());

            result.addAll(futureY9.get());
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        System.out.println("执行完成" + simpleDateFormat.format(new Date()));
        return result;
    }


    /**
     * 生成唯一键
     */
    private String generateKey(String projectCode, String date, Long createBy, String unId, String moduleType) {
        return projectCode + ":" + date + ":" + createBy + ":" + moduleType;
//        return projectCode + "-" + date + "-" + createBy + "-" + unId + "-" + moduleType;
    }

    /**
     * 设置模块操作状态
     */
    private void setModuleOperate(StatisticsUserDateOperate dateOperate, String moduleType, String operated) {
        switch (moduleType) {
            case "Y1":
                dateOperate.setModuleY1(operated);
                break;
            case "Y2":
                dateOperate.setModuleY2(operated);
                break;
            case "Y3":
                dateOperate.setModuleY3(operated);
                break;
            case "Y4":
                dateOperate.setModuleY4(operated);
                break;
            case "Y8":
                dateOperate.setModuleY8(operated);
                break;
            case "Y9":
                dateOperate.setModuleY9(operated);
                break;
            default:
                throw new IllegalArgumentException("未知的模块类型: " + moduleType);
        }
    }

    /**
     * 获取两个日期之间的所有日期
     */
    private List<String> getDatesBetween(String startDateStr, String endDateStr) {
        LocalDate start = LocalDate.parse(startDateStr, DATE_FORMATTER);
        LocalDate end = LocalDate.parse(endDateStr, DATE_FORMATTER);
        if (start.isAfter(end)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }
        List<String> dates = new ArrayList<>();
        LocalDate current = start;
        while (!current.isAfter(end)) {
            dates.add(current.format(DATE_FORMATTER));
            current = current.plusDays(1);
        }
        return dates;
    }

    /**
     * 校验日期格式
     */
    private void validateDateFormat(String startDate, String endDate) {
        try {
            LocalDate start = LocalDate.parse(startDate, DATE_FORMATTER);
            LocalDate end = LocalDate.parse(endDate, DATE_FORMATTER);
            //日期相差最大为31天
            long daysBetween = ChronoUnit.DAYS.between(start, end);
            if (daysBetween > DAY_BETWEEN) {
                throw new IllegalArgumentException("当前日期间隔天数最高为31天");
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("日期格式必须为 yyyy-MM-dd");
        }
    }

    public IPage<GetUserInfo> getUserInfo(String name, Integer current, Integer sizePage) {
        return reportMapper.getUserInfo(Page.of(current, sizePage), name, SecurityUtils.getUserTenantId());
    }

    public List<?> exportUserOperate(StatisticsUserOperateDTO statisticsUserOperateDTO) {
        StatisticsUserOperateVO statisticsUserOperateVO = statisticsUserOperate(statisticsUserOperateDTO);
        List<StatisticsUserOperate> statisticsUserOperates = statisticsUserOperateVO.getStatisticsUserOperates();
        List<Map<String, Object>> result = new ArrayList<>();
        Integer num = 1;
        for (StatisticsUserOperate statisticsUserOperate : statisticsUserOperates) {
            Map<String, Object> map = new HashMap<>();
            map.put("num", num);
            num = num + 1;
            map.put("fullName", statisticsUserOperate.getFullName());
            map.put("YPTTProjectCode", statisticsUserOperate.getYPTTProjectCode());
            map.put("YPTTProjectName", statisticsUserOperate.getYPTTProjectName());
            map.put("effectiveQuantity", statisticsUserOperate.getEffectiveQuantity());
            map.put("effectiveDays", statisticsUserOperate.getEffectiveDays());
            result.add(map);
        }
        return result;
    }

    public IPage<GetProject> getProject(String name, Integer current, Integer size) {
        return reportMapper.getProject(Page.of(current, size), name);
    }
}