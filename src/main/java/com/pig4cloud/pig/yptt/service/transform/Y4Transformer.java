package com.pig4cloud.pig.yptt.service.transform;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataValueDTO;
import com.pig4cloud.pig.me.api.dto.operation.OperationInsertDTO;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.DataPermissionsService;
import com.pig4cloud.pig.yptt.utils.LockTimeV2Util;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import com.pig4cloud.pig.yptt.utils.MetaDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Y4分包商PO 转为 分包商、分包商PO、分包商PO条目
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
@Component
@Slf4j
public class Y4Transformer extends AbstractTransformer {

	private final static String MODULE_NAME = GlobalConstants.Y4.NAME;

	public Y4Transformer(RemoteAppService remoteAppService, ViewConfProperties viewConfProperties,
			DataPermissionsService dataPermissionsService, BasicMapper basicMapper, RedissonClient redissonClient,
			DataMangeService dataMangeService) {
		super(remoteAppService, viewConfProperties, dataPermissionsService, basicMapper, redissonClient,
				dataMangeService);
	}

	@Override
	public ImportResultVO doValidate(TransformContext context, int index, Map<String, Object> raw,
			ImportResultVO valid) {
		final String appid = context.getAppid();
		final List<MetaDataDTOWrapper> uniquenessCache = context.getUniquenessCache();
		final List<MetaDataDTOWrapper> subconCache = context.getSubconCache();
		final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
		final List<MetaDataDTOWrapper> siteItemCache = context.getSiteItemCache();
		final List<MetaDataDTOWrapper> subconPoCache = context.getSubconPoCache();
		final List<MetaDataDTOWrapper> poItemCache = context.getPoItemCache();
		final List<MetaDataDTOWrapper> subconPoItemCache = context.getSubconPoItemCache();

		Dict dict = new Dict(raw);
		String Site_ID = dict.getStr("Site_ID");
		String uniqueness_field = MetaDataUtil.getUniquenessField(dict, GlobalConstants.Y4.NAME);
		String Subcon_name = dict.getStr("Subcon_name");
		String Subcon_PO_number = dict.getStr("Subcon_PO_number");

		// 查询 唯一标识
		MetaDataDTOWrapper existingUniqueness = findUniquenessByUniquenessField(appid, uniquenessCache,
				uniqueness_field);
		if (Objects.isNull(existingUniqueness)) {
			valid.setStatus(ImportResultVO.STATUS_FAILED);
			valid.addWrongReason(
					String.format("Uniqueness [%s] not found, please add site or PO first.", uniqueness_field));
			return valid;
		}
		else {
			uniquenessCache.add(existingUniqueness);
		}
		// 项目 权限验证
		final String ypttProjectCode = (String) existingUniqueness.getValue(UNI_META_ATTR_PROJECT_CODE);
		this.validatePerm(context, ypttProjectCode);

//		LockTimeV2Util lockTimeV2Util = new LockTimeV2Util();
//		if (!lockTimeV2Util.checkTimeLock(valid, ypttProjectCode)) {
//			return valid;
//		}

		MetaDataDTOWrapper siteDelivery = findSiteDeliveryInfoByUniquenessId(appid, Collections.emptyList(),
				existingUniqueness.getDataId());
		if (Objects.nonNull(siteDelivery)) {
			String siteBelongTo = MetaDataUtil.handleObject2String(siteDelivery.getValue("Site_belong_to"));
			if (Objects.equals(siteBelongTo, "YPTT")) {
				valid.setStatus(ImportResultVO.STATUS_FAILED);
				valid.addWrongReason("Site_belong_to YPTT and upload are not allowed");
				return valid;
			}else if (!Objects.equals(siteBelongTo, Subcon_name)){
//				System.out.println("=====================siteBelongTo"+siteBelongTo);
//				System.out.println("=====================Subcon_name"+Subcon_name);
//				System.out.println("=====================是否相同"+!Objects.equals(siteBelongTo, Subcon_name));
				valid.setStatus(ImportResultVO.STATUS_FAILED);
				valid.addWrongReason("Site_belong_to is not equals Subcon_name");
				return valid;
			}
		}

		// 查询 站点条目, 验证站点条目是否关闭
		MetaDataDTOWrapper existingSiteItem = findSiteItemByUniquenessField(appid, siteItemCache,
				existingUniqueness.getDataId());
		if (Objects.nonNull(existingSiteItem) && Objects.nonNull(existingSiteItem.getValue("site"))) {
			boolean isClosedSiteItem = isClosedSiteItem(existingSiteItem);
			if (isClosedSiteItem) {
				valid.setStatus(ImportResultVO.STATUS_FAILED);
				valid.addWrongReason(String.format("Site item [%s] is closed.", uniqueness_field));
				return valid;
			}
			siteItemCache.add(existingSiteItem);
		}
		else {
			valid.setStatus(ImportResultVO.STATUS_FAILED);
			valid.addWrongReason(
					String.format("Site item [%s] has not been added yet. Please add the site item first", Site_ID));
			return valid;
		}

		// 查询 po条目
		MetaDataDTOWrapper existingPoItem = findPOItemByUniquenessId(appid, poItemCache,
				existingUniqueness.getDataId());
		if (Objects.nonNull(existingPoItem) && Objects.isNull(existingPoItem.getValue("PO"))) {
			valid.setStatus(ImportResultVO.STATUS_FAILED);
			valid.addWrongReason(String.format("PO item [%s] has not been added yet. Please add the PO item first.",
					existingUniqueness.getValue("uniqueness_field")));
			return valid;
		}

		// 查询 站点
		MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
		if (Objects.isNull(existingSite)) {
			valid.setStatus(ImportResultVO.STATUS_FAILED);
			valid.addWrongReason(String.format("Site [%s] has not been added yet. Please add the site first", Site_ID));
		}
		else {
			siteCache.add(existingSite);
		}

		// 查询 Subcon
		MetaDataDTOWrapper existingSubcon = findSubconBySubconName(appid, subconCache, Subcon_name);
		if (Objects.isNull(existingSubcon)) {
			valid.addWrongReason(String.format("Subcon [%s] will be added.", Subcon_name));
		}
		else {
			subconCache.add(existingSubcon);
		}

		// 查询 SubconPO
		MetaDataDTOWrapper existingSubconPO = findSubconPOBySubconPONumber(appid, subconPoCache, Subcon_PO_number);
		if (Objects.isNull(existingSubconPO)) {
			valid.addWrongReason(String.format("Subcon PO [%s] will be added.", Subcon_PO_number));
		}
		else {
			subconPoCache.add(existingSubconPO);
		}

		// 查询 SubconPO条目
		MetaDataDTOWrapper existingSubconPOItem = findSubconPOItemByUniquenessId(appid, subconPoItemCache,
				existingUniqueness.getDataId());
		if (Objects.isNull(existingSubconPOItem) || (Objects.nonNull(existingSubconPOItem.getDataId())
				&& Objects.isNull(existingSubconPOItem.getValue("Subcon_PO")))) {
			valid.addWrongReason(String.format("Subcon PO [%s] will be added.", uniqueness_field));
			addRequire(raw, context, valid);
		}
		else {
			valid.addWrongReason(String.format("Subcon PO [%s] will be updated.", uniqueness_field));
			subconPoItemCache.add(existingSubconPOItem);
			updateSupport(raw, context, valid);
		}


		return valid;
	}

	@Override
	public ImportResultVO doTransform(TransformContext context, int index, Map<String, Object> raw) {
		final String appid = context.getAppid();
		final List<MetaDataDTOWrapper> uniquenessCache = context.getUniquenessCache();
		final List<MetaDataDTOWrapper> subconCache = context.getSubconCache();
		final List<MetaDataDTOWrapper> siteCache = context.getSiteCache();
		final List<MetaDataDTOWrapper> subconPoCache = context.getSubconPoCache();
		final List<MetaDataDTOWrapper> subconPoItemCache = context.getSubconPoItemCache();
		final List<MetaDataDTOWrapper> poItemCache = context.getPoItemCache();

		Dict dict = new Dict(raw);
		String YPTT_Project_code = dict.getStr("YPTT_Project_code");
		String Region = dict.getStr("Region");
		String Site_ID = dict.getStr("Site_ID");
		String Phase = dict.getStr("Phase");
		String Item_code = dict.getStr("Item_code");
		String BOQ_item = dict.getStr("BOQ_item");
		String uniqueness_field = MetaDataUtil.getUniquenessField(dict, GlobalConstants.Y4.NAME);
		String Subcon_name = dict.getStr("Subcon_name");
		String Subcon_PO_number = dict.getStr("Subcon_PO_number");
		String release_date = dict.getStr("release_date");
		String Site_name = dict.getStr("Site_name");
		String Quantity = dict.getStr("Quantity");
		String unitPrice = dict.getStr("Unit_price");
		String Milestone_1st = dict.getStr("Milestone_1st");
		String Milestone_2nd = dict.getStr("Milestone_2nd");
		String Milestone_3rd = dict.getStr("Milestone_3rd");
		String Milestone_4th = dict.getStr("Milestone_4th");
		String additional_cost = dict.getStr("additional_cost");
		String Remark = dict.getStr("Remark");
		String record = dict.getStr("re_record");

		// 查询 唯一标识
		MetaDataDTOWrapper existingUniqueness = findUniquenessByUniquenessField(appid, uniquenessCache,
				uniqueness_field);
		// 唯一标识 只新增、不更新
		if (Objects.isNull(existingUniqueness)) {
			existingUniqueness = new MetaDataDTOWrapper();
			existingUniqueness.setValue("Project_code", YPTT_Project_code);
			existingUniqueness.setValue("Region", Region);
			existingUniqueness.setValue("Site_ID", Site_ID);
			existingUniqueness.setValue("Phase", Phase);
			existingUniqueness.setValue("Item_code", Item_code);
			existingUniqueness.setValue(UNI_META_ATTR_UNIQUENESS_FIELD, uniqueness_field);
			saveUniqueness(appid, existingUniqueness);
		}
		uniquenessCache.add(existingUniqueness);

		// 查询 站点
		MetaDataDTOWrapper existingSite = findSiteBySiteID(appid, siteCache, Site_ID);
		Assert.notNull(existingSite, "Site [" + Site_ID + "] has not been added yet. Please add the site first");
		siteCache.add(existingSite);

		// 查询 Subcon
		MetaDataDTOWrapper existingSubcon = findSubconBySubconName(appid, subconCache, Subcon_name);
		// Subcon只新增、不更新
		if (Objects.isNull(existingSubcon)) {
			existingSubcon = new MetaDataDTOWrapper();
			existingSubcon.setValue(SUBCON_META_ATTR_SUBCON_NAME, Subcon_name);
			saveSubcon(appid, existingSubcon);
		}
		subconCache.add(existingSubcon);

		// 查询 SubconPO
		MetaDataDTOWrapper existingSubconPO = findSubconPOBySubconPONumber(appid, subconPoCache, Subcon_PO_number);
		// SubconPO 只新增、不更新
		if (Objects.isNull(existingSubconPO)) {
			existingSubconPO = new MetaDataDTOWrapper();
			existingSubconPO.setValue("Project_code", YPTT_Project_code);
			existingSubconPO.setValue("Subcom", MetaDataUtil.handleDataId2Json(existingSubcon.getDataId()));
			existingSubconPO.setValue("Subcon_PO_number", Subcon_PO_number);
			existingSubconPO.setValue("release_date", DateUtil.parse(release_date));
			saveSubconPO(appid, existingSubconPO);
		}
		subconPoCache.add(existingSubconPO);
		BigDecimal poAmount = MetaDataUtil.numberStr2BigDecimal(Quantity, 4, true)
			.multiply(MetaDataUtil.numberStr2BigDecimal(unitPrice, 4, true))
			.setScale(6, RoundingMode.HALF_UP);
		// 查询 SubconPO条目
		MetaDataDTOWrapper existingSubconPOItem = findSubconPOItemByUniquenessId(appid, subconPoItemCache,
				existingUniqueness.getDataId());
		if (Objects.isNull(existingSubconPOItem) || (Objects.nonNull(existingSubconPOItem.getDataId())
				&& Objects.isNull(existingSubconPOItem.getValue("Subcon_PO")))) {
			if (Objects.isNull(existingSubconPOItem)) {
				existingSubconPOItem = new MetaDataDTOWrapper();
				existingSubconPOItem.setValue("uniqueness_field",
						MetaDataUtil.handleDataId2Json(existingUniqueness.getDataId()));
				existingSubconPOItem.setValue("Project_code", existingUniqueness.getValue("Project_code"));
			}
			existingSubconPOItem.setValue("Project_code", YPTT_Project_code);
			existingSubconPOItem.setValue("Region", Region);
			existingSubconPOItem.setValue("Site_ID", Site_ID);
			existingSubconPOItem.setValue("Phase", Phase);
			existingSubconPOItem.setValue("Item_code", Item_code);
			existingSubconPOItem.setValue("Subcon_PO", MetaDataUtil.handleDataId2Json(existingSubconPO.getDataId()));
			existingSubconPOItem.setValue("BOQ_item", BOQ_item);
			existingSubconPOItem.setValue("Subcon", MetaDataUtil.handleDataId2Json(existingSubcon.getDataId()));
			existingSubconPOItem.setValue("Item_code", Item_code);
			existingSubconPOItem.setValue("Site", MetaDataUtil.handleDataId2Json(existingSite.getDataId()));
			existingSubconPOItem.setValue("Quantity", MetaDataUtil.numberStr2BigDecimal(Quantity, 4, true));
			existingSubconPOItem.setValue("Unit_price", MetaDataUtil.numberStr2BigDecimal(unitPrice, 4, true));
			existingSubconPOItem.setValue("Subcon_PO_amount", poAmount);
			existingSubconPOItem.setValue("Milestone_1st", MetaDataUtil.numberStr2BigDecimal(Milestone_1st, 4, false));
			existingSubconPOItem.setValue("Milestone_2nd", MetaDataUtil.numberStr2BigDecimal(Milestone_2nd, 4, false));
			existingSubconPOItem.setValue("Milestone_3rd", MetaDataUtil.numberStr2BigDecimal(Milestone_3rd, 4, false));
			existingSubconPOItem.setValue("Milestone_4th", MetaDataUtil.numberStr2BigDecimal(Milestone_4th, 4, false));
			existingSubconPOItem.setValue("remark", Remark);
			existingSubconPOItem.setValue("re_record", record);
			// 基础信息
			Long userId = SecurityUtils.getUser().getId();
			existingSubconPOItem.setValue("create_by", userId);
			existingSubconPOItem.setValue("create_time", LocalDateTime.now());
			existingSubconPOItem.setValue("update_by", userId);
			existingSubconPOItem.setValue("update_time", LocalDateTime.now());
		}
		else {
			existingSubconPOItem.setValue("Quantity", MetaDataUtil.numberStr2BigDecimal(Quantity, 4, true));
			existingSubconPOItem.setValue("Unit_price", MetaDataUtil.numberStr2BigDecimal(unitPrice, 4, true));
			existingSubconPOItem.setValue("Subcon_PO_amount", poAmount);
			existingSubconPOItem.setValue("re_record", record);
			if (StrUtil.isNotBlank(Remark)) {
				String remark = existingSubconPOItem.getValue("remark") + "\n\n" + Remark;
				existingSubconPOItem.setValue("remark", remark);
			}
			if (!StringUtils.isBlank(Milestone_1st)){
				existingSubconPOItem.setValue("Milestone_1st", MetaDataUtil.numberStr2BigDecimal(Milestone_1st, 4, false));
			}
			if (!StringUtils.isBlank(Milestone_2nd)) {
				existingSubconPOItem.setValue("Milestone_2nd", MetaDataUtil.numberStr2BigDecimal(Milestone_2nd, 4, false));
			}
			if (!StringUtils.isBlank(Milestone_3rd)) {
				existingSubconPOItem.setValue("Milestone_3rd", MetaDataUtil.numberStr2BigDecimal(Milestone_3rd, 4, false));
			}
			if (!StringUtils.isBlank(Milestone_4th)) {
				existingSubconPOItem.setValue("Milestone_4th", MetaDataUtil.numberStr2BigDecimal(Milestone_4th, 4, false));
			}
		}
		// 连接器执行
		y4connector(existingSubconPOItem);

		saveSubconPOItem(existingSubconPO.getDataId(), existingSubconPOItem);
		subconPoItemCache.add(existingSubconPOItem);

		// 分包商PO条目 额外成本计算
		if (StrUtil.isNotBlank(additional_cost)) {
			saveExternalCost(appid, MetaDataUtil.numberStr2BigDecimal(additional_cost),
					existingSubconPOItem.getDataId());
		}
		// 保存关系
		saveRel(existingSubconPO.getDataId(), existingSubconPOItem.getDataId(), context);
		// 查询 poItem
		MetaDataDTOWrapper existingPoItem = findPOItemByUniquenessId(appid, poItemCache,
				existingUniqueness.getDataId());
		// 更新Y78
		updateY78(appid, MetaDataUtil.handleDataId2Json(existingUniqueness.getDataId()), context, existingPoItem,
				existingSubcon, existingSubconPO);

		// 查询 收支统计
		final List<MetaDataDTOWrapper> incomeExpenditureCache = context.getIncomeExpenditure();
		MetaDataDTOWrapper existingIncomeExpenditure = findIncomeExpenditureByUniquenessField(appid,
				incomeExpenditureCache, existingUniqueness.getDataId());
		if (Objects.nonNull(existingIncomeExpenditure)) {
			existingIncomeExpenditure.setValue("Subcon_PO_amount_d", DateUtil.parse(release_date));
			saveIncomeExpenditure(existingIncomeExpenditure);
		}

		return new ImportResultVO(index, raw, ImportResultVO.STATUS_SUCCEED, "");
	}

	/**
	 * 保存额外成本
	 * @param appid
	 * @param externalCost
	 * @param rootId
	 */
	private void saveExternalCost(String appid, BigDecimal externalCost, Long rootId) {
		OperationInsertDTO insertDTO = new OperationInsertDTO();
		List<MetaDataValueDTO> data = new ArrayList<>();
		MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
		metaDataValueDTO.setName("ExternalCost");
		metaDataValueDTO.setValue(externalCost);
		data.add(metaDataValueDTO);
		insertDTO.setData(data);
		insertDTO.setViewGroupId(viewConfProperties.getSubconPOItem().getViewGroupId());
		insertDTO.setMainDataId(rootId);
		insertDTO.setRootDataId(rootId);
		insertDTO.setViewId(viewConfProperties.getSubconPoItemExCost().getViewId());
		insertDTO.setDataId(null);
		insertDTO.setChildren(null);
		insertDTO.setPerm(null);
		HashMap<String, Object> map = new HashMap<>();
		Long dataId = IdUtil.getSnowflakeNextId();
		map.put("id", dataId);
		map.put("ExternalCost", externalCost);
		basicMapper.saveItemData(map, viewConfProperties.getSubconPoItemExCost().getTableName());
		basicMapper.saveRelSubconPoItemExCost(IdUtil.getSnowflakeNextId(), rootId, dataId);
		insertDTO.setDataId(dataId);
		dataMangeService.addExternalCost(insertDTO);
	}

	/**
	 * 保存分包商po和分包商poItem的关系
	 * @param subconPoId 分包商po
	 * @param subconPoItemId 分包商poItem
	 */
	private void saveRel(Long subconPoId, Long subconPoItemId, TransformContext context) {
		if (CollUtil.isEmpty(basicMapper.findSubconPoRelPoItem(subconPoId, subconPoItemId))) {
			String now = DateUtil.now();
			Long id = context.getUserId();
			basicMapper.addSubconPoRelPoItem(IdUtil.getSnowflakeNextId(), id, subconPoId, subconPoItemId);
		}
	}

	@Override
	public boolean support(TransformContext context) {
		return Objects.equals(MODULE_NAME, context.getModuleName());
	}

	/**
	 * 供应商名称 查询 供应商
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param subconName 供应商名称
	 * @return 供应商
	 */
	private MetaDataDTOWrapper findSubconBySubconName(String appid, List<MetaDataDTOWrapper> localStore,
			String subconName) {
		MetaDataDTOWrapper hit = localFindOne(localStore, SUBCON_META_ATTR_SUBCON_NAME, subconName);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> subconBySubconName = basicMapper.findSubconBySubconName(subconName);
		return CollUtil.isEmpty(subconBySubconName) ? null : new MetaDataDTOWrapper(subconBySubconName);
	}

	/**
	 * 保存 供应商
	 * @param appid appid
	 * @param subcon 供应商
	 */
	private void saveSubcon(String appid, MetaDataDTOWrapper subcon) {
		Map<String, Object> readySettlementMap = subcon.toMap();
		subcon.clearNullValue();
		if (Objects.isNull(subcon.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			readySettlementMap.put("id", dataId);
			basicMapper.saveItemData(readySettlementMap, viewConfProperties.getSubcon().getTableName());
			subcon.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(readySettlementMap, viewConfProperties.getSubcon().getTableName());
		}
	}

	/**
	 * 保存 唯一标识完整信息
	 * @param appid appid
	 * @param uniqueness 唯一标识完整信息
	 */
	private void saveUniqueness(String appid, MetaDataDTOWrapper uniqueness) {
		Map<String, Object> uniquenessMap = uniqueness.toMap();
		if (Objects.isNull(uniqueness.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			uniquenessMap.put("id", dataId);
			basicMapper.saveItemData(uniquenessMap, viewConfProperties.getUniqueIdentification().getTableName());
			uniqueness.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(uniquenessMap, viewConfProperties.getUniqueIdentification().getTableName());
		}
	}

	/**
	 * 分包商PO编号 查询 分包商PO
	 * @param appid appid
	 * @param localStore 本地缓存
	 * @param subconPoNumber 分包商PO编号
	 * @return 分包商PO
	 */
	private MetaDataDTOWrapper findSubconPOBySubconPONumber(String appid, List<MetaDataDTOWrapper> localStore,
			String subconPoNumber) {
		MetaDataDTOWrapper hit = localFindOne(localStore, SUBCON_PO_META_ATTR_SUBCON_PO_NUMBER, subconPoNumber);
		if (Objects.nonNull(hit)) {
			return hit;
		}
		List<Map<String, Object>> subconPOBySubconPONumber = basicMapper.findSubconPOBySubconPONumber(subconPoNumber);
		return CollUtil.isEmpty(subconPOBySubconPONumber) ? null : new MetaDataDTOWrapper(subconPOBySubconPONumber);
	}

	/**
	 * 保存 供应商PO
	 * @param appid appid
	 * @param subconPO 供应商PO
	 */
	private void saveSubconPO(String appid, MetaDataDTOWrapper subconPO) {
		Map<String, Object> uniquenessMap = subconPO.toMap();
		if (Objects.isNull(subconPO.getDataId())) {
			Long dataId = IdUtil.getSnowflakeNextId();
			uniquenessMap.put("id", dataId);
			basicMapper.saveItemData(uniquenessMap, viewConfProperties.getSubconPO().getTableName());
			subconPO.setDataId(dataId);
		}
		else {
			basicMapper.updateItemData(uniquenessMap, viewConfProperties.getSubconPO().getTableName());
		}
	}

}
