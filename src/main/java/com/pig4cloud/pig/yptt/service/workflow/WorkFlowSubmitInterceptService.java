package com.pig4cloud.pig.yptt.service.workflow;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.yptt.controller.WorkFlowApiRes;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlow.request.RequestInfo;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlow.request.TableInfo;
import com.pig4cloud.pig.yptt.service.fs.CustomerService;
import com.pig4cloud.pig.yptt.service.fs.ReimburseInvoiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: WorkFlowSubmitInterceptService
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-07-08  14:26
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkFlowSubmitInterceptService {
    private final ReimburseInvoiceService reimburseInvoiceService;
    private final CustomerService customerService;

    /**
     * 综合报销提交拦截
     **/
    public WorkFlowApiRes otherReimbursement(RequestInfo requestInfo) {
        TableInfo tableInfo = requestInfo.getTableInfo();
        String sum = tableInfo.getValue("bxhj");
        String num1 = tableInfo.getValue("zfhj");
        String num2 = tableInfo.getValue("cxhj");
        if(!ReimbursementAmountCheck(sum,num1,num2)){
            return WorkFlowApiRes.error("支付金额+冲销金额必须等于报销金额");
        }

        List<String> invoiceNums = new ArrayList<>();
        for (TableInfo subInfo: tableInfo.getSub()){
            if(subInfo.getModelName().equals("com_expense_dt1")){
                String invoiceNum = subInfo.getValue("fphm");
                if(!StrUtil.isBlank(invoiceNum)){
                    invoiceNums.add(invoiceNum);
                }
            }
        }

        if(reimburseInvoiceService.isDuplicateInvoiceNums(invoiceNums)){
            return WorkFlowApiRes.error("存在已报销发票");
        }

        return WorkFlowApiRes.ok();
    }

    /**
     * 劳务/材料报销提交拦截
     **/
    public WorkFlowApiRes SubConAndMaterialReimbursement(RequestInfo requestInfo) {
        TableInfo tableInfo = requestInfo.getTableInfo();
        String sum = tableInfo.getValue("bxhj");
        String num1 = tableInfo.getValue("zfhj_bz");
        String num2 = tableInfo.getValue("cxhj");
        if(!ReimbursementAmountCheck(sum,num1,num2)){
            return WorkFlowApiRes.error("支付金额+冲销金额必须等于报销金额");
        }

        List<String> invoiceNums = new ArrayList<>();
        for (TableInfo subInfo: tableInfo.getSub()){
            if(subInfo.getModelName().equals("com_expense_dt1")){
                String invoiceNum = subInfo.getValue("fphm");
                if(!StrUtil.isBlank(invoiceNum)){
                    invoiceNums.add(invoiceNum);
                }
            }
        }

        if(reimburseInvoiceService.isDuplicateInvoiceNums(invoiceNums)){
            return WorkFlowApiRes.error("存在已报销发票");
        }

        return WorkFlowApiRes.ok();
    }

    /**
     * 差旅报销提交拦截
     **/
    public WorkFlowApiRes travelReimbursement(RequestInfo requestInfo) {
        TableInfo tableInfo = requestInfo.getTableInfo();
        String sum = tableInfo.getValue("bxhj");
        String num1 = tableInfo.getValue("zfhjje");
        String num2 = tableInfo.getValue("cxhjje");
        if(!ReimbursementAmountCheck(sum,num1,num2)){
            return WorkFlowApiRes.error("支付金额+冲销金额必须等于报销金额");
        }

        return WorkFlowApiRes.ok();
    }

    private boolean ReimbursementAmountCheck(String sum,String num1,String num2){
        double v1 = 0.0;
        double v2 = 0.0;
        double v3 = 0.0;
        try {
            v1 = Double.parseDouble(sum);
            v2 = Double.parseDouble(num1);
            v3 = Double.parseDouble(num2);
        }catch (Exception e){
            throw new RuntimeException("金额格式错误");
        }

        double total = NumberUtil.add(v2, v3);

        return NumberUtil.compare(total, v1)==0;
    }

    /**
     * 财务付款申请提交拦截
     **/
    public WorkFlowApiRes financePayment(RequestInfo requestInfo) {
        return WorkFlowApiRes.ok();
    }

    /**
     * 客户/供应商登记提交拦截
     **/
    public WorkFlowApiRes customerAndSupplierRegistration(RequestInfo requestInfo) {
        TableInfo tableInfo = requestInfo.getTableInfo();
        // 查看名称是否存在
        if(customerService.isExistsCustomer(tableInfo.getValue("name"))){
            return WorkFlowApiRes.error("客户/供应商名称已存在");
        }
        return WorkFlowApiRes.ok();
    }
}