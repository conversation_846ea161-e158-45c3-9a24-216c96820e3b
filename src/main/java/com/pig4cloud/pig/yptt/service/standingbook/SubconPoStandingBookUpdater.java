package com.pig4cloud.pig.yptt.service.standingbook;

import com.pig4cloud.pig.yptt.entity.dto.SubconPoStandingBookDTO;
import com.pig4cloud.pig.yptt.mapper.SubconPoStandingBookMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubconPoStandingBookUpdater extends StandingBookUpdater<SubconPoStandingBookDTO> {

	private final SubconPoStandingBookMapper subconPoStandingBookMapper;

	@Override
	protected String getName() {
		return "SubconPoStandingBook";
	}

	@Override
	protected List<SubconPoStandingBookDTO> generate(int i, int size) {
		return subconPoStandingBookMapper.generateSubconPoStandingBookList(i, size);
	}

	@Override
	protected int save(SubconPoStandingBookDTO dto) {
		return subconPoStandingBookMapper.update(dto);
	}

}
