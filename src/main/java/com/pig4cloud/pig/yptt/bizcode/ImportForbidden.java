package com.pig4cloud.pig.yptt.bizcode;

import com.pig4cloud.pig.common.core.constant.enums.BizCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/10/09
 */
@Getter
@RequiredArgsConstructor
public enum ImportForbidden implements BizCode {

	/**
	 * 没有权限导入
	 */
	IMPORT_FORBIDDEN(403),
	/**
	 * 日期格式错误
	 */
	IMPORT_DATE_FORMAT_ERROR(400);

	private final int httpCode;

	@Override
	public int getHttpCode() {
		return this.httpCode;
	}

}
