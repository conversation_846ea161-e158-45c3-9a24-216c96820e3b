package com.pig4cloud.pig.yptt.bizcode;

import com.pig4cloud.pig.common.core.constant.enums.BizCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/03/28
 */
@Getter
@RequiredArgsConstructor
public enum YpttBizCode implements BizCode {

	/**
	 * 日期为空
	 */
	DATE_IS_NULL_ERROR(400),

	/**
	 * 删除数据错误
	 */
	DELETE_DATA_ERROR(400),

	/**
	 * 数据错误
	 */
	DATA_ERROR(400),

	/**
	 * 无权限删除
	 */
	FORBIDDEN_DELETE_ERROR(400),

	/**
	 * 导入任务已满
	 */
	IMPORT_TASK_FULL_ERROR(400),

	/**
	 * 导入任务正在运行
	 */
	IMPORT_TASK_RUNNING(400),


	/**
	 * 已锁定无法删除
	 */
	LOCK_DELETE_ERROR(400),
	/**
	 * 已锁定无法删除_STATUS
	 */
	LOCK_DELETE_ERROR_STATUS(400),

	/**
	 * 已锁定无法删除_y6
	 */
	LOCK_DELETE_ERROR_Y6(400),

	/**
	 * 已锁定无法删除_y9
	 */
	LOCK_DELETE_ERROR_Y9(400),

	/**
	 * 周期类型错误
	 */
	PROJECT_CYCLE_TYPE_ERROR(400),;

	private final int httpCode;

}
