package com.pig4cloud.pig.yptt.bizcode;

import com.pig4cloud.pig.common.core.constant.enums.BizCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/10/09
 */

@Getter
@RequiredArgsConstructor
public enum ExpectedOneButGotMany implements BizCode {

	/**
	 * 该租户角色已存在
	 */
	EXPECT_ONE_BUT_GOT_MANY(400);

	private final int httpCode;

	@Override
	public int getHttpCode() {
		return this.httpCode;
	}

}
