package com.pig4cloud.pig.yptt.bizcode;

import com.pig4cloud.pig.common.core.constant.enums.BizCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/01/12
 */
@Getter
@RequiredArgsConstructor
public enum RefreshPerCode implements BizCode {

	/**
	 * 刷新权限表错误
	 */
	REFRESH_PER_ERROR(400),

	/**
	 * 获取锁超时
	 */
	LOCK_TIMED_OUT(400),

	/**
	 * 任务中断
	 */
	INTERRUPTED_ERROR(400),

	/**
	 * 任务被拒绝
	 */
	REFRESH_PER_REJECT_ERROR(400);

	private final int httpCode;

	@Override
	public int getHttpCode() {
		return this.httpCode;
	}

}
