package com.pig4cloud.pig.yptt.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.File;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023/09/26
 */
@Aspect
@Component
@Slf4j
public class LogAspect {

	// @Pointcut("execution(public *
	// com.pig4cloud.pig.yptt.controller.*Controller.*(..))")
	@Pointcut("@annotation(com.pig4cloud.pig.yptt.config.OperationLogger)")
	public void cutPoint() {
	}

	@Around("cutPoint()")
	public Object around(ProceedingJoinPoint pjp) throws Throwable {
		String classAndMethodName = this.getCurrentMethodCanonicalName(pjp);
		StringBuilder logString = new StringBuilder();
		StopWatch stopWatch = new StopWatch();
		try {
			stopWatch.start();
			this.processBefore(pjp, classAndMethodName, logString);
			Object result = pjp.proceed();
			// 处理返回值
			processAfter(result, null, logString);
			stopWatch.stop();
			logString.append(",耗时").append(stopWatch.getTotalTimeMillis()).append("毫秒");
			log.info(logString.toString());
			return result;
		}
		catch (Throwable throwable) {
			// 目标方法发生异常
			processAfter(null, throwable, logString);
			stopWatch.stop();
			logString.append(",耗时").append(stopWatch.getTotalTimeMillis()).append("毫秒");
			log.info(logString.toString());
			throw throwable;
		}
	}

	private void processAfter(Object result, Throwable throwable, @NonNull StringBuilder sb) {
		try {
			if (throwable == null) {
				if (result == null) {
					sb.append("返回:【null】");
					return;
				}
				sb.append("返回【")
					.append(JSONObject.toJSONString(result, SerializerFeature.IgnoreNonFieldGetter))
					.append("】");
			}
			else {
				sb.append("异常:【").append(throwable.getMessage()).append("】");
			}
		}
		catch (Throwable e) {
			sb.append("返回【生成返回日志出错】");
			log.info("记录日志出错:" + e);
		}
	}

	private void processBefore(ProceedingJoinPoint pjp, String classAndMethodName, @NonNull StringBuilder sb) {
		try {
			// 打印入参
			if (null == pjp.getArgs() || pjp.getArgs().length == 0) {
				sb.append("方法[").append(classAndMethodName).append("],参数：无;");
			}
			else {
				List<Object> list = Arrays.asList(pjp.getArgs());
				final AtomicInteger index = new AtomicInteger(1);
				sb.append("方法[").append(classAndMethodName).append("],");
				list.stream().filter(Objects::nonNull).forEach(x -> {
					if (isFile(x)) {
						sb.append(classAndMethodName)
							.append("参数[")
							.append(index.get())
							.append("]=[")
							.append(getFileName(x))
							.append("],");
					}
					else {
						sb.append("参数[")
							.append(index.get())
							.append("]=[")
							.append(JSONObject.toJSONString(x, SerializerFeature.IgnoreNonFieldGetter))
							.append("],");
					}
					index.incrementAndGet();
				});
			}
		}
		catch (Throwable e) {
			log.info("记录日志出错:" + e);
			sb.setLength(0);
			sb.append("入参[生成入参日志出错],");
		}
	}

	/**
	 * 得到当前方法
	 */
	private Method getCurrentMethod(ProceedingJoinPoint pjp) throws NoSuchMethodException, SecurityException {
		MethodSignature mig = (MethodSignature) pjp.getSignature();
		return pjp.getTarget().getClass().getMethod(mig.getName(), mig.getParameterTypes());
	}

	private String getCurrentMethodCanonicalName(ProceedingJoinPoint pjp) {
		try {
			String name;
			if (Objects.nonNull(pjp.getTarget()) && pjp.getTarget().getClass().getInterfaces().length > 0) {
				name = pjp.getTarget().getClass().getInterfaces()[0].getSimpleName();
			}
			else {
				name = pjp.getTarget().getClass().getSimpleName();
			}
			return String.format("%s.%s", name, pjp.getSignature().getName());
		}
		catch (Exception exception) {
			log.info("获取方法名失败" + exception);
			return "";
		}
	}

	private boolean isFile(Object obj) {
		return obj instanceof File;
	}

	private String getFileName(Object file) {
		return null == file ? "空文件" : ((File) file).getName();
	}

}
