package com.pig4cloud.pig.yptt.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/10/24
 */
@EnableAsync
@Configuration
@RequiredArgsConstructor
public class AsyncConfig {

	private final ThreadPoolExecutorProperties properties;

	@Bean("importTaskExecutor")
	public RuntimeVisibleThreadPoolExecutor importTaskExecutor() {
		int corePoolSize = Math.min(properties.getCorePoolSize(), 8);
		int maximumPoolSize = Math.min(properties.getMaximumPoolSize(), 8);
		int queueCapacity = Math.min(properties.getQueueCapacity(), 24);
		return new RuntimeVisibleThreadPoolExecutor(corePoolSize, maximumPoolSize, properties.getKeepAliveTime(),
				TimeUnit.SECONDS,
				queueCapacity <= 0 ? new SynchronousQueue<>() : new LinkedBlockingQueue<>(queueCapacity),
				new CustomizableThreadFactory(properties.getThreadNamePrefix()), new ThreadPoolExecutor.AbortPolicy());
	}

	@Bean("refreshPerExecutor")
	public ThreadPoolExecutor refreshPerExecutor() {
		return new ThreadPoolExecutor(12, 12, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(12),
				new CustomizableThreadFactory("refresh-per"), new ThreadPoolExecutor.AbortPolicy());
	}

}
