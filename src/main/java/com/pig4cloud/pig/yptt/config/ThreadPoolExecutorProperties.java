package com.pig4cloud.pig.yptt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2024/01/03
 */
@Data
@ConfigurationProperties(prefix = "thread-param")
public class ThreadPoolExecutorProperties {

	private Integer corePoolSize = 3;

	private Integer maximumPoolSize = 3;

	private Integer keepAliveTime = 60;

	private Integer queueCapacity = 3;

	private String threadNamePrefix = "yptt-thread-";

}
