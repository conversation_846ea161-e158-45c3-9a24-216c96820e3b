package com.pig4cloud.pig.yptt.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/09/13
 */
@Configuration
@EnableConfigurationProperties({ ViewConfProperties.class, YpttPersonalizedApiProperties.class,
		ThreadPoolExecutorProperties.class })
public class ViewConfConfiguration {

}
