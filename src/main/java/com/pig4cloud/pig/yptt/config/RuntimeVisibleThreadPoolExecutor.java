package com.pig4cloud.pig.yptt.config;

import com.pig4cloud.pig.common.security.service.PigUser;
import com.pig4cloud.pig.common.security.util.ProxyAuthenticateContextHolder;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.yptt.task.ImportTask;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.core.context.SecurityContextHolder;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2023/10/30
 */
@Slf4j
public class RuntimeVisibleThreadPoolExecutor extends ThreadPoolExecutor {

	private final Set<Long> taskQueue = new HashSet<>(64);

	public RuntimeVisibleThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
			@NotNull TimeUnit unit, @NotNull BlockingQueue<Runnable> workQueue) {
		super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
	}

	public RuntimeVisibleThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
			@NotNull TimeUnit unit, @NotNull BlockingQueue<Runnable> workQueue, @NotNull ThreadFactory threadFactory) {
		super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
	}

	public RuntimeVisibleThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
			@NotNull TimeUnit unit, @NotNull BlockingQueue<Runnable> workQueue,
			@NotNull RejectedExecutionHandler handler) {
		super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
	}

	public RuntimeVisibleThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
			@NotNull TimeUnit unit, @NotNull BlockingQueue<Runnable> workQueue, @NotNull ThreadFactory threadFactory,
			@NotNull RejectedExecutionHandler handler) {
		super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
	}

	@Override
	protected void beforeExecute(Thread t, Runnable r) {
		log.debug("beforeExecute, thread class: {}, runnable class: {}",
				Optional.ofNullable(t).map(Object::getClass).map(Class::getName).orElse(null),
				Optional.ofNullable(r).map(Object::getClass).map(Class::getName).orElse(null));
		if (Objects.nonNull(r)) {
			taskQueue.add(getTaskId(r));
		}
		super.beforeExecute(t, r);
	}

	@SneakyThrows
	public Long getTaskId(Runnable r) {
		if (r instanceof ImportTask) {
			ImportTask importTask = (ImportTask) r;
			return importTask.getTaskId();
		}
		else {
			// 包装的swRunnableWrapper
			Class<? extends Runnable> aClass = r.getClass();
			Field field = Objects.requireNonNull(aClass).getDeclaredField("runnable");
			field.setAccessible(true);
			// 获取真正的runnable
			Runnable o = (Runnable) field.get(r);
			Class<? extends Runnable> runClass = o.getClass();
			Field taskId = runClass.getDeclaredField("taskId");
			taskId.setAccessible(true);
			return (Long) taskId.get(o);
		}
	}

	@Override
	protected void afterExecute(Runnable r, Throwable t) {
		try {
			if (Objects.nonNull(r)) {
				taskQueue.remove(getTaskId(r));
			}
			super.afterExecute(r, t);
		}
		finally {
			if (r instanceof ImportTask) {
				ImportTask importTask = (ImportTask) r;
				log.debug("afterExecute: completed import task {}", importTask.getTaskId());
			}
			log.debug("clear Context, proxyUser: {}, securityUser: {}",
					Optional.ofNullable(ProxyAuthenticateContextHolder.getUser()).map(PigUser::getId).orElse(null),
					Optional.ofNullable(SecurityUtils.getUser()).map(PigUser::getId).orElse(null));
			SecurityContextHolder.clearContext();
			ProxyAuthenticateContextHolder.clearContext();
		}
	}

	public Set<Long> getRunningTasks() {
		return taskQueue;
	}

}
