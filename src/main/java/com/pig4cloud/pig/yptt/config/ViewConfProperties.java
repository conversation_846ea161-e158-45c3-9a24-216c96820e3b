package com.pig4cloud.pig.yptt.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2023/09/13
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "view-conf")
public class ViewConfProperties {

	private String msgLink = "https://ms.ypttglobal.net/app/1694587608560046082/menu/{menuId}/view/{viewId}/data/{dataId}/read";

	private Long tenantId = 1694550407300681729L;

	private Long adminId = 1544515583013392385L;

	private String appId = "1694587608560046082";

	private UniqueIdentification uniqueIdentification = new UniqueIdentification();

	private SiteItem siteItem = new SiteItem();

	private IncomeExpenditure incomeExpenditure = new IncomeExpenditure();

	private Y1Import y1Import = new Y1Import();

	private Integer y3UpdateMaxSize = 10000;

	private Integer pageSize = 500;

	private Integer operationSize = 1000;

	@Data
	public static class Y1Import {

		private String viewId = "1722089461560025090";

		private String itemViewId = "1696358441342902274";

		private String itemViewGroupId = "1702141812958089218";

		private String menuId = "1722091720259837953";

	}

	private Y2Import y2Import = new Y2Import();

	@Data
	public static class Y2Import {

		private String viewId = "1722089312578347010";

		private String itemViewId = "1699313808400244737";

		private String itemViewGroupId = "1699313808517685250";

		private String menuId = "1722091972248793089";

	}

	private Y3Import y3Import = new Y3Import();

	@Data
	public static class Y3Import {

		private String viewId = "1722089207846576129";

		private String itemViewId = "1699314218154385409";

		private String itemViewGroupId = "1699314218267631617";

		private String menuId = "1722092023981338625";

	}

	private Y4Import y4Import = new Y4Import();

	@Data
	public static class Y4Import {

		private String viewId = "1722089115299258370";

		private String itemViewId = "1699314295866449921";

		private String itemViewGroupId = "1699314295975501826";

		private String menuId = "1722092765832081410";

	}

	private Y8Import y8Import = new Y8Import();

	@Data
	public static class Y8Import {

		private String viewId = "1722089005362356225";

		private String itemViewId = "1699314460123783169";

		private String itemViewGroupId = "1699314460199280641";

		private String menuId = "1722092833884663809";

	}

	private Y9Import y9Import = new Y9Import();

	@Data
	public static class Y9Import {

		private String viewId = "1722088903881170945";

		private String itemViewId = "1699314548350967810";

		private String itemViewGroupId = "1699314548426465281";

		private String menuId = "1722092879984259074";

	}

	@Data
	public static class UniqueIdentification {

		private String tableName = "memm_562ace74337e462289972ce20939e9a7";

		private Long viewId = 1698511341561581569L;

		private Long viewGroupId = 1698511341691604993L;

	}

	private YPTTProject ypttProject = new YPTTProject();

	@Data
	public static class YPTTProject {

		private Long viewGroupId = 1694898197492666370L;

		private Long viewId = 1694898197446529025L;

	}

	private Site site = new Site();

	@Data
	public static class Site {

		private String tableName = "memm_448208a319fa4d7ab3d77ee54e10c066";

		private Long viewGroupId = 1694897301455114242L;

		private Long viewId = 1694897301383811073L;

		private Long warnViewId = 1711945230104760322L;

	}

	@Data
	public static class SiteItem {

		private String tableName = "memm_e648652640b44b2092c93e1742e6171b";

		private Long viewId = 1698891624814911489L;

		private Long viewGroupId = 1698891625108512770L;

		private Long warnViewId = 1712008310353346561L;

		private Long warnViewGroupId = 1712008310458204162L;

		private Long menuId = 1712009850107510785L;

	}

	private CustomerProject customerProject = new CustomerProject();

	@Data
	public static class CustomerProject {

		private String tableName = "memm_f15b45017dee432daf88693b3d13b60b";

		private Long viewGroupId = 1744599913573912578L;

		private Long viewId = 1744599913485832194L;

	}

	@Data
	public static class IncomeExpenditure {

		private String tableName = "memm_c58db11c81d3403ab9e59ce72b815ade";

		private Long viewId = 1703664043260973058L;

		private Long viewGroupId = 1703664043554574338L;

	}

	private WarningThreshold warningThreshold = new WarningThreshold();

	@Data
	public static class WarningThreshold {

		private String tableName = "memm_7345607a202c4e0eb52ffef451faa3aa";

		private Long viewId = 1699601040117915650L;

		private Long viewGroupId = 1699601040222773249L;

	}

	private WarningMessage warningMessage = new WarningMessage();

	@Data
	public static class WarningMessage {

		private String tableName = "memm_70848da039e44392bc6e066b5963ba1d";

		private Long viewId = 1699601040117915650L;

		private Long viewGroupId = 1699601040222773249L;

	}

	private SiteDelivery siteDelivery = new SiteDelivery();

	@Data
	public static class SiteDelivery {

		private String tableName = "memm_e45cb01fc742457a85ed8243aff1aa28";

		private Long viewId = 1694897953770049537L;

		private Long viewGroupId = 1694897953807798274L;

		private Long warnViewId = 1711945599459364865L;

		private Long warnViewGroupId = 1711945599757160450L;

		private Long menuId = 1712009947536998401L;

	}

	private PO po = new PO();

	@Data
	public static class PO {

		private String tableName = "memm_ed87f18383f04a8f836cea32a1628fc9";

		private Long viewId = 1694904826505641986L;

		private Long warnViewId = 1711945411713929218L;

		private Long viewGroupId = 1694904826560167937L;

	}

	private PoItem poItem = new PoItem();

	@Data
	public static class PoItem {

		private String tableName = "memm_f37920ed96f942fb8f4b1bf16f79e39c";

		private Long viewId = 1694905080818876417L;

		private Long viewGroupId = 1694905080885985282L;

		private Long warnViewId = 1712007063973969922L;

		private Long warnViewGroupId = 1712007064137547777L;

		private Long menuId = 1712010218023469058L;

	}

	private Subscon subcon = new Subscon();

	@Data
	public static class Subscon {

		private String tableName = "memm_134d9474dc244b26bfd7f013a0534710";

		private Long viewId = 1694909596956016642L;

		private Long viewGroupId = 1694909597002153986L;

	}

	private SubconPO subconPO = new SubconPO();

	@Data
	public static class SubconPO {

		private String tableName = "memm_ff802d120a12430db18a68deb783b9c6";

		private Long viewGroupId = 1694904612952653826L;

		private Long viewId = 1694904612826824706L;

		private Long warnViewId = 1712007359936643073L;

	}

	private SubconPOItem subconPOItem = new SubconPOItem();

	@Data
	public static class SubconPOItem {

		private String tableName = "memm_157ac31323c34d46920918117cb577ad";

		private Long viewGroupId = 1698617960417579010L;

		private Long viewId = 1698617960300138498L;

		private Long warnViewId = 1712007852461178882L;

	}

	private YPTTSettlement ypttSettlement = new YPTTSettlement();

	@Data
	public static class YPTTSettlement {

		private String tableName = "memm_4bf72c9a610c4b05a007f0f215b424a6";

		private Long viewId = 1694896902224482306L;

		private Long viewGroupId = 1694896902341922818L;

		private Long warnViewId = 1711942996210061314L;

		private Long warnViewGroupId = 1711942996310724610L;

		private Long menuId = 1712011362815680513L;

	}

	private ReadyForSettlement readyForSettlement = new ReadyForSettlement();

	@Data
	public static class ReadyForSettlement {

		private String tableName = "memm_abdf4191a91e436a9b7e04351042f757";

		private Long viewId = 1694897671652773890L;

		private Long viewGroupId = 1694897671694716930L;

	}

	private ProductivityReport productivityReport = new ProductivityReport();

	@Data
	public static class ProductivityReport {

		private String tableName = "memm_5c8c376451894fdfb7e751c91da66f16";

		private Long viewId = 1694897528232742914L;

		private Long viewGroupId = 1694897528270491650L;

	}

	private SubconPayment subconPayment = new SubconPayment();

	@Data
	public static class SubconPayment {

		private String tableName = "memm_f562b5dbd2be42d99c4992dd2668ed74";

		private Long viewId = 1694897013600030721L;

		private Long viewGroupId = 1694897013671333889L;

		private Long warnViewId = 1711944841544437761L;

		private Long warnViewGroupId = 1711944841934508033L;

		private Long menuId = 1712010613814771714L;

	}

	private SubconSettlement subconSettlement = new SubconSettlement();

	@Data
	public static class SubconSettlement {

		private String tableName = "memm_218a6ab9959842099fd074c2b0ef685b";

		private Long viewId = 1694897216541429761L;

		private Long viewGroupId = 1694897216583372801L;

	}

	private ProjectMember projectMember = new ProjectMember();

	@Data
	public static class ProjectMember {

		private String tableName = "memm_7abc0f7fd9d84f67b4cd9b32575a6933";

		private Long viewId = 1698893239999438849L;

		private Long viewGroupId = 1714817163182866433L;

	}

	@Data
	public static class RoleWarn {

		private String tableName = "memm_505e4855883045729256ad60b745f17e";

		private Long viewId = 1706935725262053378L;

		private Long viewGroupId = 1706935725295607809L;

	}

	@Data
	public static class RoleBi {

		private String tableName = "memm_5ea417b221054a1fbbec0f82d2d77326";

		private Long viewId = 1706935681993613314L;

		private Long viewGroupId = 1706935682035556354L;

	}

	@Data
	public static class RoleData {

		private String tableName = "memm_1ebcbae9c9a648abb5df95540846ff1e";

		private Long viewId = 1706938838815547394L;

		private Long viewGroupId = 1706938839033651201L;

	}

	private ProjectModulePer projectModulePer = new ProjectModulePer();

	@Data
	public static class ProjectModulePer {

		private String tableName = "memm_439131c30ad445e6810ba53e13fd9cfb";

		private Long viewId = 1710907959707082753L;

		private Long viewGroupId = 1710907959761608705L;

	}

	private SubconPoItemExCost subconPoItemExCost = new SubconPoItemExCost();

	@Data
	public static class SubconPoItemExCost {

		private String tableName = "memm_040cb23251f740f5afa2dc9e4b66e5e3";

		private Long viewId = 1714111836730482690L;

	}

}
