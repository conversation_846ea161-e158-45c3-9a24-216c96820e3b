package com.pig4cloud.pig.yptt.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
@ConfigurationProperties(prefix = "yptt")
public class YpttPersonalizedApiProperties {

	private String taskToken = "e2ff13fc7f0c4a20a618fe08877f9c8d";

	private Map<String, String> schedule = new HashMap<>();

}
