package com.pig4cloud.pig.yptt.task;

import com.pig4cloud.pig.common.core.exception.LccAssert;
import com.pig4cloud.pig.yptt.service.AdjustService;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.WarningInfoService;
import com.pig4cloud.pig.yptt.service.standingbook.StandingBookUpdateTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024/01/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ScheduleRegistrar implements ApplicationContextAware {

	private ApplicationContext applicationContext;

	@Scheduled(cron = "${yptt.schedule.warningInfoUpdateTask:-}")
	public void warningInfoUpdateTask() {
		run(WarningInfoService.class, WarningInfoService::updateStatus);
	}

	@Scheduled(cron = "${yptt.schedule.standingBookUpdateTask:-}")
	public void standingBookUpdateTask() {
		run(StandingBookUpdateTaskService.class, StandingBookUpdateTaskService::update);
	}

	@Scheduled(cron = "${yptt.schedule.importTask:-}")
	public void importTask() {
		run(DataMangeService.class, DataMangeService::checkImportTask);
	}

	@Scheduled(cron = "${yptt.schedule.incomeCorrectionTask:-}")
	public void incomeCorrectionTask() {
		run(DataMangeService.class, DataMangeService::incomeCorrection);
	}

	@Scheduled(cron = "${yptt.schedule.updateSiteState:-}")
	public void updateSiteState() {
		run(DataMangeService.class, DataMangeService::updateSiteState);
	}

	@Scheduled(cron = "${yptt.schedule.clearWarningInfo:-}")
	public void clearWarningInfo() {
		run(WarningInfoService.class, WarningInfoService::clearWarningInfo);
	}

	/**
	 * <AUTHOR>
	 * @Description 临时全表更改数据 调整金额 SiteItem
	 * @Date 18:50 2025/1/13
	 * @Param []
	 * @return void
	 **/
//	@Scheduled(cron = "0 0 1 * * ?")
//	@PostConstruct
	public void adjustAmountSiteItem() {
//		run(AdjustService.class, AdjustService::updateAmountSiteItme);
	}

	/**
	 * <AUTHOR>
	 * @Description 临时全表更改数据 调整金额 POItem
	 * @Date 18:50 2025/1/13
	 * @Param []
	 * @return void
	 **/
//	@Scheduled(cron = "0 0 2 * * ?")
//	@PostConstruct
	public void adjustAmountPOItem() {
//		run(AdjustService.class, AdjustService::updateAmountPOItem);
	}

	/**
	 * <AUTHOR>
	 * @Description 1:Y611:决算日期（KPI Archive date），这个日期等于Y315的日期
	 * 				2：Y612：决算金额（KPI Archive amount），如果Y315有日期，则金额自动等于 Y215 PO value
	 * 			每天凌晨1：00 自动执行任务
	 * @Date 14:26 2025/3/24
	 * @Param []
	 * @return void
	 **/
//	@Scheduled(cron = "0 0 1 * * ?")
//	@PostConstruct
	public void updateY6KPI() {
		run(AdjustService.class, AdjustService::updateY6KPI);
	}


	protected <T> Object run(Class<T> singletonBeanClass, Function<T, ?> function) {
		LccAssert.notNull(singletonBeanClass, "singletonBeanClass不能为空");
		String name = singletonBeanClass.getSimpleName();
		long start = System.currentTimeMillis();
		try {
			T bean = this.applicationContext.getBean(singletonBeanClass);
			Object result = function.apply(bean);
			long duration = System.currentTimeMillis() - start;
			log.info("ScheduleTask[{}] duration: {} ms, result: {}", name, duration, result);
			return result;
		}
		catch (Exception e) {
			long duration = System.currentTimeMillis() - start;
			log.error("ScheduleTask[{}] duration: {} ms, error: {}", name, duration, e.getMessage(), e);
			throw e;
		}
	}

	@Override
	public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}

}
