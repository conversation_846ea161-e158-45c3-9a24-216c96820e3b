package com.pig4cloud.pig.yptt.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.base.fms.entity.FmFile;
import com.pig4cloud.pig.base.fms.feign.RemoteFileService;
import com.pig4cloud.pig.common.core.constant.SecurityConstants;
import com.pig4cloud.pig.common.security.component.ProxyAuthentication;
import com.pig4cloud.pig.common.security.service.PigUser;
import com.pig4cloud.pig.common.security.util.ProxyAuthenticateContext;
import com.pig4cloud.pig.common.security.util.ProxyAuthenticateContextHolder;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.StatusEntity;
import com.pig4cloud.pig.yptt.entity.dto.YPTTBatchImportDTO;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.service.DataPermissionsService;
import com.pig4cloud.pig.yptt.task.event.ImportTaskEvent;
import com.pig4cloud.pig.yptt.mapper.ImportMapper;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.TransformManager;
import com.pig4cloud.pig.yptt.service.Transformer;
import com.pig4cloud.pig.yptt.utils.MetaDataUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/10/24
 */
@Slf4j
@AllArgsConstructor
public class ImportTask implements Runnable {

	/**
	 * 标识离线任务
	 */
	@Getter
	private final Integer form;

	@Getter
	private final ProxyAuthenticateContext proxyAuthenticateContext;

	private final DataMangeService dataMangeService;

	private final TransformManager transformManager;

	private final DataPermissionsService dataPermissionsService;

	private final ImportMapper importMapper;

	private final ViewConfProperties viewConfProperties;

	private final RemoteFileService remoteFileService;

	@Getter
	private final Long taskId;

	private final ImportTaskEvent event;

	private final static Integer BATCH_SIZE = 50;

	private final static String TITLE = "IMPORT TASK";

	private final static String MSG_IMPORT_TASK = "The import task [{fileName}] that you uploaded [{uploadTime}] has been completed,"
			+ " with success: [{successes}] and failure: [{failures}].\n"
			+ "<a href= \"{link}\">CLICK TO VIEW DETAILS</a>";

	@Override
	public void run() {
		// 设置代理用户
		setProxyUser();

		YPTTBatchImportDTO param = event.getDto();
		Long mainDataId = event.getMainDataId();
		String moduleType = param.getModuleType();
		log.info("========={}导入任务-id[{}]开始执行=========", moduleType, mainDataId);
		// 获取子数据进行导入操作
		List<Map<String, Object>> maps = importMapper.selectRelData(mainDataId, moduleType);
		// 更新主数据状态进行中
		Map<String, Object> updateMap = new HashMap<>(4);
		updateMap.put("Status",
				CollUtil.isEmpty(maps) ? ImportResultVO.STATUS_COMPLETE : ImportResultVO.STATUS_RUNNING);
		updateMap.put("start_time", LocalDateTime.now());
		importMapper.updateImport(updateMap, moduleType, mainDataId);
		// 分批导入
		for (int start = 0; start < maps.size(); start += BATCH_SIZE) {
			List<ImportResultVO> res = new ArrayList<>();
			// 初始化状态->进行中
			initImportResult(res, maps, start, moduleType);
			Transformer.TransformContext context = new Transformer.TransformContext(
					proxyAuthenticateContext.getPigUser().getId(), param, maps, GlobalConstants.Import.IMPORT_DATA);
			for (ImportResultVO importResultVO : res) {
				try {
					final int index = importResultVO.getIndex();
					Map<String, Object> map = importResultVO.getImportData();
					// 数据转换->返回状态更新
					ImportResultVO resultVO = transformManager.transform(context, index, map);
					importResultVO.setStatus(resultVO.getStatus());
					importResultVO.setWrongReason(resultVO.getWrongReason());
				}
				catch (Exception e) {
					log.error("saving {}-importing-item failed, cause: {}", moduleType, e.getMessage(), e);
					importResultVO
						.addWrongReason("The import of data was successful, but saving the import history failed.");
				}
			}
			// 更新子数据
			updateImportItemStatus(res, moduleType);
			// 导入完成后更新主视图数据
			dataMangeService.updateMainViewData(res, mainDataId, moduleType);
		}
		log.info("========={}导入任务-id[{}]执行完毕=========", moduleType, mainDataId);
		// 发布通知消息
		dataPermissionsService.sendMsg(msgInfo(mainDataId, moduleType), TITLE,
				Collections.singletonList(SecurityUtils.getUser().getId()));
	}

	/**
	 * 设置代理用户
	 */
	private void setProxyUser() {
		// 携带代理用户信息
		ProxyAuthenticateContextHolder.set(this.getProxyAuthenticateContext());
		log.debug("set ProxyAuthenticationContext, taskId: {}, user: {}", this.getTaskId(),
				Optional.of(this)
					.map(ImportTask::getProxyAuthenticateContext)
					.map(ProxyAuthenticateContext::getPigUser)
					.map(PigUser::getId)
					.orElse(null));
		SecurityContext context = SecurityContextHolder.createEmptyContext();
		context.setAuthentication(new ProxyAuthentication(ProxyAuthenticateContextHolder.getUser()));
		SecurityContextHolder.setContext(context);
	}

	/**
	 * 任务通知信息
	 * @param id id
	 * @param type 类型
	 * @return
	 */
	private String msgInfo(Long id, String type) {
		Map<String, Object> selectImport = importMapper.selectImport(id, type);
		Dict msgDict = new Dict();
		Object uploadedFile = selectImport.get("Uploaded_File");
		if (Objects.nonNull(uploadedFile)) {
			Long fileId = MetaDataUtil.handleDataIdJson2Long(uploadedFile.toString());
			FmFile fmFile = remoteFileService.get(String.valueOf(fileId), SecurityConstants.FROM_IN);
			msgDict.put("fileName", fmFile.getName());
		}
		else {
			msgDict.put("fileName", "File not found");
		}
		msgDict.put("uploadTime", DateUtil.parse(selectImport.get("Confirmed_Time").toString()).toString());
		msgDict.put("successes", selectImport.get("Success_Count"));
		msgDict.put("failures", selectImport.get("Failed_Count"));
		// 构建详情链接
		String link = viewConfProperties.getMsgLink();
		Dict linkDict = new Dict();
		linkDict.put("dataId", id);
		linkDict.put("viewId", getViewIdOrMenuId(type, 0));
		linkDict.put("menuId", getViewIdOrMenuId(type, 1));
		msgDict.put("link", StrUtil.format(link, linkDict));
		return StrUtil.format(MSG_IMPORT_TASK, msgDict);
	}

	/**
	 * 获取链接信息
	 * @param type 导入类型
	 * @param num 1 or 0
	 * @return
	 */
	private String getViewIdOrMenuId(String type, Integer num) {
		if (Objects.equals(type, GlobalConstants.Y1.NAME)) {
			return Objects.equals(num, 0) ? viewConfProperties.getY1Import().getViewId()
					: viewConfProperties.getY1Import().getMenuId();
		}
		else if (Objects.equals(type, GlobalConstants.Y2.NAME)) {
			return Objects.equals(num, 0) ? viewConfProperties.getY2Import().getViewId()
					: viewConfProperties.getY2Import().getMenuId();
		}
		else if (Objects.equals(type, GlobalConstants.Y3.NAME)) {
			return Objects.equals(num, 0) ? viewConfProperties.getY3Import().getViewId()
					: viewConfProperties.getY3Import().getMenuId();
		}
		else if (Objects.equals(type, GlobalConstants.Y4.NAME)) {
			return Objects.equals(num, 0) ? viewConfProperties.getY4Import().getViewId()
					: viewConfProperties.getY4Import().getMenuId();
		}
		else if (Objects.equals(type, GlobalConstants.Y8.NAME)) {
			return Objects.equals(num, 0) ? viewConfProperties.getY8Import().getViewId()
					: viewConfProperties.getY8Import().getMenuId();
		}
		else if (Objects.equals(type, GlobalConstants.Y9.NAME)) {
			return Objects.equals(num, 0) ? viewConfProperties.getY9Import().getViewId()
					: viewConfProperties.getY9Import().getMenuId();
		}
		return null;
	}

	/**
	 * 初始化该批量导入状态
	 * @param res 返回集
	 * @param maps 数据
	 */
	private void initImportResult(List<ImportResultVO> res, List<Map<String, Object>> maps, Integer start,
			String type) {
		int total = Math.min(start + BATCH_SIZE, maps.size());
		for (int i = start; i < total; i++) {
			ImportResultVO importResultVO = new ImportResultVO(i, maps.get(i), ImportResultVO.STATUS_RUNNING, "");
			res.add(importResultVO);
		}
		updateImportItemStatus(res, type);

	}

	/**
	 * 更新子数据状态
	 * @param res 数据
	 * @param type 类型
	 */
	private void updateImportItemStatus(List<ImportResultVO> res, String type) {
		if (CollUtil.isNotEmpty(res)) {
			List<StatusEntity> updateList = new ArrayList<>();
			for (ImportResultVO vo : res) {
				StatusEntity update = new StatusEntity();
				update.setStatus(vo.getStatus());
				update.setMsg(vo.getWrongReason());
				update.setId(vo.getImportData().get("id").toString());
				updateList.add(update);
			}
			importMapper.updateImportItem(updateList, type);
		}
	}

}
