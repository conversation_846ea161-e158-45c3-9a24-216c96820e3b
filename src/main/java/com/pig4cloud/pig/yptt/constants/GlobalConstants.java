package com.pig4cloud.pig.yptt.constants;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/09/13
 */
public interface GlobalConstants {

	interface UpdatePerType {

		String QUERY = "query";

		String INSERT = "insert";

		String UPDATE = "update";

		String DEL = "del";

	}

	interface BiPanel {

		String YEAR = "year";

		String DAY = "day";

		String MONTH = "month";

		String B1 = "BI-1";

		String B2 = "BI-2";

	}

	interface Import {

		String IMPORT_DATA = "IMPORT_DATA";

		String CHECK_DATA = "CHECK_DATA";

	}

	interface ExcelUtil {

		String IMPORT_TEMPLATE = "导入模板";

	}

	@Getter
	@RequiredArgsConstructor
	enum ViewConfEnum {

		/**
		 * Y1模板
		 */
		Y1_ITEM(GlobalConstants.Y1.NAME, GlobalConstants.Y1.FILED_LIST),
		/**
		 * Y2模板
		 */
		Y2_ITEM(GlobalConstants.Y2.NAME, GlobalConstants.Y2.FILED_LIST),
		/**
		 * Y3模板
		 */
		Y3_ITEM(GlobalConstants.Y3.NAME, GlobalConstants.Y3.FILED_LIST),
		/**
		 * Y4模板
		 */
		Y4_ITEM(GlobalConstants.Y4.NAME, GlobalConstants.Y4.FILED_LIST),
		/**
		 * Y8模板
		 */
		Y8_ITEM(GlobalConstants.Y8.NAME, GlobalConstants.Y8.FILED_LIST),
		/**
		 * Y9模板
		 */
		Y9_ITEM(GlobalConstants.Y9.NAME, GlobalConstants.Y9.FILED_LIST);

		private final String module;

		private final List<String> filed;

		public static ViewConfEnum getFiledByModule(String code) {
			ViewConfEnum[] values = values();
			for (ViewConfEnum value : values) {
				if (Objects.equals(code, value.getModule())) {
					return value;
				}
			}
			throw new RuntimeException("非法导入模板类型:[" + code + "]");
		}

	}

	interface Y1 {

		String NAME = "y1";

		List<String> FILED_LIST = Arrays.asList("Department", "YPTT_Project_code", "YPTT_Project_name", "Region",
				"Area", "Site_ID", "Site_Name", "site_allocation_date", "Phase", "Type_of_service", "Site_Model",
				"Item_code", "BOQ_item", "quantity", "Unit_price", "Site_value", "Remark", "re_record",
				"uniqueness_field");

	}

	interface Y2 {

		String NAME = "y2";

		List<String> FILED_LIST = Arrays.asList("PO_Received_date", "PO_Number", "Contract_number",
				"Custom_project_name", "YPTT_Project_code", "Region", "Phase", "Site_ID", "Site_Name", "Item_code",
				"BOQ_item", "quantity", "Unit_price", "PO_Value", "PO_GAP", "Pre_payment", "Milestone_1st",
				"Milestone_2nd", "Milestone_3rd", "Milestone_4th", "Remark", "re_record", "uniqueness_field");

	}

	interface Y3 {

		String NAME = "y3";

		List<String> FILED_LIST = Arrays.asList("YPTT_Project_code", "Region", "Site_ID", "Phase", "Item_code",
				"uniqueness_field", "Site_belong_to", "Team_Leader_DT", "engineer_DTA_SPV", "PLO_PC_Others",
				"PIC_PC_PM", "Start_Working_date", "Completed_work_date", "air_CI_Report_submit", "Site_manager_Report",
				"E_ATP_Pass", "F_PAC_Pass", "G_FAC", "Remark", "re_record");

	}

	interface Y4 {

		String NAME = "y4";

		List<String> FILED_LIST = Arrays.asList("YPTT_Project_code", "Region", "Site_ID", "Phase", "Item_code",
				"uniqueness_field", "Site_name", "Item_code", "BOQ_item", "Quantity", "Unit_price", "Subcon_PO_amount",
				"Subcon_name", "Subcon_PO_number", "release_date", "Milestone_1st", "Milestone_2nd", "Milestone_3rd",
				"Milestone_4th", "additional_cost", "Remark", "re_record");

	}

	interface Y5 {

		String NAME = "y5";

	}

	interface Y6 {

		String NAME = "y6";

	}

	interface Y7 {

		String NAME = "y7";

	}

	interface Y8 {

		String NAME = "y8";

		List<String> FILED_LIST = Arrays.asList("Subcon_name", "Subcon_PO_number", "Site_ID", "Item_code",
				"uniqueness_field", "Payment_time_1st", "payment_amount_1st", "Payment_time_2st", "payment_amount_2st",
				"Payment_time_3st", "payment_amount_3st", "Payment_time_4st", "payment_amount_4st", "Remark",
				"Totally_payment", "re_record", "payment_number_1st", "payment_number_2st", "payment_number_3st",
				"payment_number_4st");

	}

	interface Y9 {

		String NAME = "y9";

		List<String> FILED_LIST = Arrays.asList("PO_number", "Contract_number", "Phase", "Site_ID", "Item_code",
				"uniqueness_field", "Invoice_date_1st", "Invoice_number_1st", "Invoice_Amount_1st", "Invoice_date_2nd",
				"Invoice_number_2nd", "Invoice_Amount_2nd", "Invoice_date_3rd", "Invoice_number_3rd",
				"Invoice_Amount_3rd", "Invoice_date_4st", "Invoice_number_4st", "Invoice_Amount_4st", "Remark",
				"Invoice_amount", "re_record", "Invoice_remark_1st", "Invoice_remark_2nd", "Invoice_remark_3rd",
				"Invoice_remark_4th");

	}

	List<String> ALL_ITEM_REL = Arrays.asList(Y1.NAME, Y2.NAME, Y3.NAME, Y4.NAME, Y8.NAME, Y9.NAME);

	List<String> Y1234_UNIQUENESS_FIELD_LIST = Arrays.asList(Y1.NAME, Y2.NAME, Y3.NAME, Y4.NAME);

	String lockDateTimeRedisKey = "lockDataTime::";

}
