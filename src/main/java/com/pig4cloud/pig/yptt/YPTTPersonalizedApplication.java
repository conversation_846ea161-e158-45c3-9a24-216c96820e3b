package com.pig4cloud.pig.yptt;

import com.pig4cloud.pig.common.feign.annotation.EnablePigFeignClients;
import com.pig4cloud.pig.common.security.annotation.EnablePigResourceServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 海外项目启动类
 *
 * <AUTHOR>
 * @date 2023/08/30
 */
@EnableScheduling
@EnableAsync
@EnableRetry
@EnablePigResourceServer
@EnablePigFeignClients
@EnableDiscoveryClient
@SpringBootApplication
@EnableTransactionManagement
public class YPTTPersonalizedApplication {

	public static void main(String[] args) {
		System.setProperty("spring.security.strategy", "MODE_INHERITABLETHREADLOCAL");
		SpringApplication.run(YPTTPersonalizedApplication.class, args);
	}

}