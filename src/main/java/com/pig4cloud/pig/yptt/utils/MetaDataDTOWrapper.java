package com.pig4cloud.pig.yptt.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataDTO;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataValueDTO;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/09/28
 */
public class MetaDataDTOWrapper extends MetaDataDTO {

	public MetaDataDTOWrapper() {
		this.setData(new ArrayList<>());
	}

	public Map<String, Object> toMap() {
		Map<String, Object> map = new HashMap<>();
		map.put("id", this.getDataId());
		this.getData().forEach(data -> map.put(data.getName(), data.getValue()));
		return map;
	}

	public MetaDataDTOWrapper(MetaDataDTO source) {
		BeanUtil.copyProperties(source, this);
		if (Objects.isNull(getData())) {
			this.setData(new ArrayList<>());
		}
	}

	public MetaDataDTOWrapper(List<Map<String, Object>> map) {
		if (CollUtil.isEmpty(map)) {
			this.setData(new ArrayList<>());
		}
		else {
			Map<String, Object> objectMap = map.get(0);
			List<MetaDataValueDTO> data = new ArrayList<>();
			this.setDataId(Objects.isNull(objectMap.get("id")) ? null : Long.parseLong(objectMap.get("id").toString()));
			objectMap.forEach((k, v) -> {
				MetaDataValueDTO valueDTO = new MetaDataValueDTO();
				valueDTO.setName(k);
				valueDTO.setValue(v);
				data.add(valueDTO);
			});
			this.setData(data);
		}
	}

	public void setValue(String name, Object value) {
		MetaDataValueDTO existingValueDTO = getMetaDataValue(name);
		if (Objects.isNull(existingValueDTO)) {
			existingValueDTO = new MetaDataValueDTO();
			existingValueDTO.setName(name);
			this.getData().add(existingValueDTO);
		}
		existingValueDTO.setValue(value);
	}

	public void removeValue(String name) {
		List<MetaDataValueDTO> data = this.getData();
		if (CollUtil.isNotEmpty(data)) {
			data.removeIf(datum -> Objects.equals(datum.getName(), name));
		}
	}

	public void clearNullValue() {
		this.getData()
			.removeIf(datum -> Objects.isNull(datum.getValue()) || StrUtil.isBlank(datum.getValue().toString()));
	}

}
