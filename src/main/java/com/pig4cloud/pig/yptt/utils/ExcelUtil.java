package com.pig4cloud.pig.yptt.utils;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.pig4cloud.pig.yptt.entity.TestDataModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.CharEncoding;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.http.HttpHeaders;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/09/04
 */
@Slf4j
public class ExcelUtil {

	/**
	 * 字段的行号
	 */
	private static final int ROW_NUMBER_OF_FIELD = 4;

	/**
	 * 数据开始行号，即第一行用户录入数据的行号
	 */
	private static final int ROW_NUMBER_OF_DATA_BEGIN = 7;

	/**
	 * 解析excel2Map
	 * @param file 文件
	 */
	public static List<Map<String, Object>> readExcelToMap(MultipartFile file) throws IOException {
		InputStream fileInputStream = file.getInputStream();
		// 导入的字段
		List<String> filedList = new ArrayList<>();
		List<Map<String, Object>> resMap = new ArrayList<>();
		// 读取excel
		EasyExcel.read(fileInputStream, new AnalysisEventListener<Map<Integer, Object>>() {
			@Override
			public void invoke(Map<Integer, Object> dataMap, AnalysisContext analysisContext) {
				HashMap<String, Object> map = new HashMap<>(dataMap.size());
				dataMap.forEach((k, v) -> {
					if (k < filedList.size()) {
						map.put(filedList.get(k), Objects.nonNull(v) ? v.toString().trim() : "");
					}
				});
				// 数据是否全为NULL
				long count = dataMap.values().stream().filter(Objects::nonNull).count();
				if (count > 0) {
					resMap.add(map);
				}
			}

			@Override
			public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
				if (context.readRowHolder().getRowIndex() == ROW_NUMBER_OF_FIELD) {
					headMap.forEach((k, v) -> {
						if (StrUtil.isNotBlank(v)) {
							filedList.add(v);
						}
					});
				}
			}

			@Override
			public void doAfterAllAnalysed(AnalysisContext analysisContext) {
			}

		}).sheet().headRowNumber(ROW_NUMBER_OF_DATA_BEGIN).doRead();
		return resMap;
	}

	private static List<List<String>> createdHead(List<String> headMap) {
		List<List<String>> headList = new ArrayList<>();
		for (String head : headMap) {
			List<String> list = new ArrayList<>();
			list.add(head);
			headList.add(list);
		}
		return headList;
	}

	private static List<List<String>> testCreatedHead(List<String> headMap) {
		List<List<String>> headList = new ArrayList<>();
		for (String head : headMap) {
			List<String> list = new ArrayList<>();
			list.add("");
			list.add("");
			list.add("");
			list.add("");
			list.add(head);
			headList.add(list);
		}
		return headList;
	}

	/**
	 * 导出抽象对象数据
	 * @param response 返回
	 * @param sheetName sheetName
	 * @param list 数据
	 * @param headMap 表头
	 */
	public static void exportNoModel(HttpServletResponse response, String sheetName, List<List<Object>> list,
			List<String> headMap) throws IOException {
		init(response, sheetName);
		WriteCellStyle headWriteCellStyle = new WriteCellStyle();
		EasyExcel.write(response.getOutputStream())
			.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
			.head(createdHead(headMap))
			.sheet(sheetName)
			.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
			// 表头设置
			.registerWriteHandler(new HorizontalCellStyleStrategy() {
				@Override
				protected void setHeadCellStyle(CellWriteHandlerContext context) {
					WriteFont headWriteFont = new WriteFont();
					headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
					headWriteFont.setFontHeightInPoints((short) 12);
					headWriteFont.setBold(Boolean.FALSE);
					headWriteFont.setFontName("等线 (正文)");
					headWriteCellStyle.setWriteFont(headWriteFont);
					WriteCellData<?> firstCellData = context.getFirstCellData();
					headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
					setHeadWriteCellStyle(headWriteCellStyle);
					WriteCellStyle.merge(headWriteCellStyle, firstCellData.getOrCreateStyle());
				}
			})
			.doWrite(list);
		response.flushBuffer();
	}

	/**
	 * 导出模板对象数据
	 * @param response 返回
	 * @param moduleList 数据
	 */
	public static void exportTemplateList(HttpServletResponse response, String sheetName, ClassPathResource resource,
			List<?> moduleList) throws IOException {
		init(response, sheetName);
		if (Objects.nonNull(resource)) {
			String templateFileName = resource.getFile().getPath();
			EasyExcel.write(response.getOutputStream()).withTemplate(templateFileName).sheet().doFill(moduleList);
			response.flushBuffer();
		}
	}

	/**
	 * 导出抽象对象数据
	 * @param response 返回
	 * @param moduleList 数据
	 */
	public static void exportNoModelList(HttpServletResponse response, List<TestDataModule> moduleList)
			throws IOException {
		init(response, "测试数据");
		WriteCellStyle headWriteCellStyle = new WriteCellStyle();
		ServletOutputStream outputStream = response.getOutputStream();
		ExcelWriter writer = EasyExcel.write(outputStream).build();
		for (TestDataModule testDataModule : moduleList) {
			WriteSheet build = EasyExcel.writerSheet(testDataModule.getSheetName())
				.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
				.head(testCreatedHead(testDataModule.getHeadMap()))
				.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
				// 表头设置
				.registerWriteHandler(new HorizontalCellStyleStrategy() {
					@Override
					protected void setHeadCellStyle(CellWriteHandlerContext context) {
						WriteFont headWriteFont = new WriteFont();
						headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
						headWriteFont.setFontHeightInPoints((short) 12);
						headWriteFont.setBold(Boolean.FALSE);
						headWriteFont.setFontName("等线 (正文)");
						headWriteCellStyle.setWriteFont(headWriteFont);
						WriteCellData<?> firstCellData = context.getFirstCellData();
						headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
						setHeadWriteCellStyle(headWriteCellStyle);
						WriteCellStyle.merge(headWriteCellStyle, firstCellData.getOrCreateStyle());
					}
				})
				.build();
			writer.write(testDataModule.getDataList(), build);
		}
		writer.finish();
		response.flushBuffer();
	}

	private static void init(HttpServletResponse response, String sheetName) throws UnsupportedEncodingException {
		response.setContentType("application/ms-excel");
		response.setCharacterEncoding(CharEncoding.UTF_8);
		String fileName = URLEncoder.encode(sheetName, CharEncoding.UTF_8);
		response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName + ".xlsx");

	}

}
