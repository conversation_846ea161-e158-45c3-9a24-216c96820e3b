package com.pig4cloud.pig.yptt.utils;

import com.fasterxml.jackson.core.util.DefaultPrettyPrinter;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger; // 推荐使用 SLF4j 或其他日志框架
import org.slf4j.LoggerFactory; // 推荐使用 SLF4j

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName JsonIdUpdater
 * @Description 租户配置同步 处理sql中的相关id + 1000000000000000000
 * @date 2025/4/15 (Update date)
 * @Version 1.1 (Optimized)
 */
public class JsonIdUpdater {

    // 使用日志框架记录信息和错误，比 System.out/err 更好
    private static final Logger log = LoggerFactory.getLogger(JsonIdUpdater.class);

    // --- 常量定义 ---
    private static final String DATA_LIST_FIELD = "dataList";
    private static final String ENTITY_CLASS_FIELD = "entityClass";

    //    private static final String DATA_LIST_FIELD = "dataList";
    // 需要更新的ID字段列表
    private static final List<String> ID_FIELDS_TO_UPDATE = Arrays.asList(
            //
            "meta_model_id",
            //
            "view_group_id",
            //me_virtual_model_model_mount
            "parent_id",
            "virtual_model_id",
//            "meta_model_id",
            //me_virtual_model_field
            "model_mount_id",
//            "virtual_model_id",
            "meta_attr_id",
            //me_virtual_model
            "app_id",
            "main_model_id",
            //me_view_custom_operation
//            "app_id",
            "view_id",
//            "view_group_Id",
            "model_data_source_id",
            "relation_view_id",
            "page_design_id",
            "relation_connector_id",
            "relation_app_id",
            //me_view_rel
//            "app_id",
//            "view_group_id",
            "main_view_id",
            "rel_view_id",
            //me_view_group
//            "app_id",
            "root_view_id",
            //me_view_attr
//            "app_id",
//            "view_id",
//            "meta_attr_id",
            "data_source_id",
            //me_view
//            "app_id",
            "model_id",
            "ui_template_id",
            "after_add_connector_id",
            "after_update_connector_id",
            "pre_delete_connector_id",
            //me_fixed_ds_val
//            "app_id",
            "fds_id",
            //me_fixed_data_source
//            "app_id",
            //me_model_ds_display
//            "app_id",
//            "model_data_source_id",
            "view_attr_id",
            //me_model_data_source
//            "app_id",
//            "view_id",
            //me_page_design
            //me_connector
            //me_meta_rel
//            "app_id",
            "left_meta_model_id",
            "right_meta_model_id",
            //me_meta_attr
//            "app_id",
//            "meta_model_id",
            "rel_model_id",
//            "data_source_id", todo
            //me_meta_model
//            "app_id",
//            "after_add_connector_id",
//            "after_update_connector_id",
//            "pre_delete_connector_id",
            "id"
            // 在这里添加其他需要更新的ID字段名
    );
    // ID增加的偏移量
    private static final long ID_OFFSET = 1000000000000000000L;
    private static final String OUTPUT_SUFFIX = "_modified";
    private static final String FILE_EXTENSION = ".lcc";

    // ObjectMapper 实例可以重用，作为静态常量或通过依赖注入
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * Main method to run the update process.
     * Expects one argument: the path to the input LCC file.
     *
     * @param args Command line arguments. args[0] should be the input file path.
     */
    public static void main(String[] args) {
        String inputFilePath = "D:\\cqxk\\yptt\\测试环境yptt数据\\租户配置信息-1694550407300681729.lcc";
        File inputFile = new File(inputFilePath);

        if (!inputFile.exists() || !inputFile.isFile()) {
            log.error("Input file does not exist or is not a valid file: {}", inputFilePath);
            System.err.println("Error: Input file not found or invalid: " + inputFilePath);
            System.exit(1);
        }

        String outputFilePath = generateOutputFilePath(inputFilePath);

        try {
            log.info("Starting processing for file: {}", inputFilePath);
            processFile(inputFile, new File(outputFilePath));
            log.info("Processing completed successfully. Output file: {}", outputFilePath);
            System.out.println("Processing completed. Output file: " + outputFilePath);
        } catch (IOException e) {
            log.error("An I/O error occurred during processing file: {}", inputFilePath, e);
            System.err.println("Error processing file: " + e.getMessage());
            System.exit(1);
        } catch (IllegalArgumentException e) {
            log.error("Invalid JSON structure in file: {}", inputFilePath, e);
            System.err.println("Error: Invalid JSON structure - " + e.getMessage());
            System.exit(1);
        } catch (Exception e) { // Catch unexpected errors
            log.error("An unexpected error occurred during processing file: {}", inputFilePath, e);
            System.err.println("An unexpected error occurred: " + e.getMessage());
            System.exit(1);
        }
    }

    /**
     * Processes the input JSON file, updates IDs, and saves to the output file.
     *
     * @param inputFile  The input JSON file.
     * @param outputFile The file where the modified JSON will be saved.
     * @throws IOException              If an I/O error occurs during reading or writing.
     * @throws IllegalArgumentException If the root JSON structure is not an array.
     */
    public static void processFile(File inputFile, File outputFile) throws IOException, IllegalArgumentException {
        JsonNode root = OBJECT_MAPPER.readTree(inputFile);

        if (!root.isArray()) {
            throw new IllegalArgumentException("Expected root element to be a JSON array.");
        }

        // 遍历顶层数组
        for (JsonNode entityNode : root) {
            // 确保顶层元素是对象
            if (entityNode.isObject()) {
                JsonNode dataListNode = entityNode.get(DATA_LIST_FIELD);
                JsonNode entityClassNode = entityNode.get(ENTITY_CLASS_FIELD);
                // 检查 dataList 是否存在且是数组
                if (dataListNode != null && dataListNode.isArray()) {
                    // 遍历 dataList 中的每个项目
                    for (JsonNode item : (ArrayNode) dataListNode) {
                        // 确保 dataList 中的元素是对象
                        if (item.isObject()) {
                            updateItemIds((ObjectNode) item);
                            if (!"me_meta_attr".equals(entityClassNode.asText())
                                    && !"me_virtual_model".equals(entityClassNode.asText())
                                  ) { //更改模型中相关内容的值
                                updateItem((ObjectNode) item, "name");
                                updateItem((ObjectNode) item, "table_name");
                                updateItem((ObjectNode) item, "value");
                                updateItem((ObjectNode) item, "label");
                                updateItem((ObjectNode) item, "display_name");
                            }

                        } else {
                            log.warn("Skipping non-object item in dataList: {}", item.toString());
                        }
                    }
                } else if (dataListNode != null) {
                    log.warn("Field '{}' exists but is not an array in entity: {}", DATA_LIST_FIELD, entityNode.toString());
                }
                // 如果 dataList 字段不存在，则忽略这个 entityNode (或根据需要记录日志)
            } else {
                log.warn("Skipping non-object element in root array: {}", entityNode.toString());
            }
        }

        // 保存修改后的文件，使用 PrettyPrinter 格式化输出
        OBJECT_MAPPER.writer(new DefaultPrettyPrinter()).writeValue(outputFile, root);
    }

    private static void updateItem(ObjectNode node, String fieldName) {
        if (node.has(fieldName)) {
            JsonNode idNode = node.get(fieldName);
            // 确保 ID 字段是文本类型 (因为原始代码是 asText().parseLong())
            if (idNode.isTextual()) {
                String textS = idNode.asText();
                try {
//                    long id = Long.parseLong(idStr);
//                    long newId = id + ID_OFFSET;
                    String newText = textS + "_IT";
                    node.put(fieldName, String.valueOf(newText)); // 更新为字符串类型的新 ID
                    log.debug("Updated field '{}' from {} to {} in node: {}", fieldName, textS, newText, node.hashCode()); // 使用 debug 级别
                } catch (NumberFormatException e) {
                    // 记录解析错误，但通常不中断整个过程
                    log.warn("Could not parse field '{}' value '{}' as long in node: {}", fieldName, textS, node.toString().substring(0, Math.min(node.toString().length(), 100))); // 限制日志输出长度
                }
            } else if (!idNode.isNull()) { // 允许 null 值，但不处理非文本值
                log.warn("Field '{}' exists but is not textual (type: {}) in node: {}", fieldName, idNode.getNodeType(), node.toString().substring(0, Math.min(node.toString().length(), 100)));
            }
            // 如果 idNode 是 null，则忽略
        }
        // 如果字段不存在，则忽略
    }

    /**
     * Updates the specified ID fields within a single JSON object node.
     *
     * @param item The JSON object node to update.
     */
    private static void updateItemIds(ObjectNode item) {
        for (String fieldName : ID_FIELDS_TO_UPDATE) {
            updateIdField(item, fieldName);
        }
    }

    /**
     * Updates a specific ID field in the given JSON object node if it exists and is a valid long string.
     *
     * @param node      The JSON object node containing the field.
     * @param fieldName The name of the ID field to update (e.g., "id", "meta_model_id").
     */
    private static void updateIdField(ObjectNode node, String fieldName) {
        if (node.has(fieldName)) {
            JsonNode idNode = node.get(fieldName);
            // 确保 ID 字段是文本类型 (因为原始代码是 asText().parseLong())
            if (idNode.isTextual()) {
                String idStr = idNode.asText();
                try {
                    long id = Long.parseLong(idStr);
                    long newId = id + ID_OFFSET;
                    node.put(fieldName, String.valueOf(newId)); // 更新为字符串类型的新 ID
                    log.debug("Updated field '{}' from {} to {} in node: {}", fieldName, idStr, newId, node.hashCode()); // 使用 debug 级别
                } catch (NumberFormatException e) {
                    // 记录解析错误，但通常不中断整个过程
                    log.warn("Could not parse field '{}' value '{}' as long in node: {}", fieldName, idStr, node.toString().substring(0, Math.min(node.toString().length(), 100))); // 限制日志输出长度
                }
            } else if (!idNode.isNull()) { // 允许 null 值，但不处理非文本值
                log.warn("Field '{}' exists but is not textual (type: {}) in node: {}", fieldName, idNode.getNodeType(), node.toString().substring(0, Math.min(node.toString().length(), 100)));
            }
            // 如果 idNode 是 null，则忽略
        }
        // 如果字段不存在，则忽略
    }

    /**
     * Generates the output file path based on the input file path.
     * Appends "_modified" before the extension.
     *
     * @param inputPath The input file path string.
     * @return The generated output file path string.
     */
    private static String generateOutputFilePath(String inputPath) {
        String pathWithoutExtension;
        String extension;

        int lastDotIndex = inputPath.lastIndexOf('.');
        int lastSeparatorIndex = inputPath.lastIndexOf(File.separatorChar);

        // 处理没有扩展名或点在路径分隔符之前的情况
        if (lastDotIndex == -1 || lastDotIndex < lastSeparatorIndex) {
            pathWithoutExtension = inputPath;
            extension = ""; // 没有扩展名
        } else {
            pathWithoutExtension = inputPath.substring(0, lastDotIndex);
            extension = inputPath.substring(lastDotIndex); // 包含点 "."
        }

        // 确保只添加一次后缀，即使原文件已包含 _modified
        if (pathWithoutExtension.endsWith(OUTPUT_SUFFIX)) {
            return inputPath; // 或者可以选择覆盖或生成 _modified_modified
        }


        // 如果我们期望特定扩展名，可以强制使用
        if (!FILE_EXTENSION.equalsIgnoreCase(extension)) {
            log.warn("Input file '{}' does not have the expected '{}' extension.", inputPath, FILE_EXTENSION);
            // 仍然按原扩展名处理，或者可以决定抛出错误或强制使用 .lcc
        }


        return pathWithoutExtension + OUTPUT_SUFFIX + extension;
    }
}