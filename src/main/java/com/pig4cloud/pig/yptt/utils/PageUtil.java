package com.pig4cloud.pig.yptt.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.ArrayList;
import java.util.List;


public class PageUtil {
    public static <T> Page<T> getPageInfo(int currentPage, int pageSize, List<T> list) {
        int total = list.size();
        if (total > pageSize) {
            int toIndex = pageSize * currentPage;
            if (toIndex > total) {
                toIndex = total;
            }
            int totalPage = total % pageSize == 0 ? (total / pageSize) : (total / pageSize) + 1;
            if (totalPage < currentPage) {
                list = new ArrayList<>();
            } else {
                list = list.subList(pageSize * (currentPage - 1), toIndex);
            }
        }
        Page<T> page = new Page<>(currentPage, pageSize);
        page.setRecords(list);
        page.setPages((total + pageSize - 1) / pageSize);
        page.setTotal(total);
        return page;
    }
}
