package com.pig4cloud.pig.yptt.utils;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @ClassName PRUtil
 * @Description PR工具类
 * @date 2025/6/4 13:00
 * @Version 1.0
 */
public class PRUtil {
    public static void main(String[] args) {
        System.out.println(new PRUtil().createPrNo("xxx", "jjjj"));
    }
    //创建PR号码
    public String createPrNo(String projectCode,String userName){
        // 或者自定义格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String customFormat = LocalDateTime.now().format(formatter);
        return customFormat + projectCode + generateSecureRandomCode();
    }

    private static final String CHAR_SET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final SecureRandom random = new SecureRandom();

    /**
     * 生成安全的6位随机码
     */
    public static String generateSecureRandomCode() {
        StringBuilder sb = new StringBuilder(6);
        for (int i = 0; i < 6; i++) {
            sb.append(CHAR_SET.charAt(random.nextInt(CHAR_SET.length())));
        }
        return sb.toString();
    }
}