package com.pig4cloud.pig.yptt.utils;
import org.apache.commons.lang3.ObjectUtils; // 保留使用的库
import org.apache.commons.lang3.StringUtils; // 保留使用的库

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.Collections; // 引入 Collections 用于返回空列表

/**
 * <AUTHOR>
 * @ClassName DataFilterOptimized
 * @Description
 * @date 2025/4/11 16:22
 * @Version 1.0
 */
public class DataFilterOptimized {
    // --- 日期格式化器 ---
    /** 用于解析 'yyyy-MM-dd' 格式的日期格式化器 */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    /** 用于解析 'yyyy-MM-dd HH:mm:ss' 格式的日期时间格式化器 */
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // --- 日期检查核心逻辑 ---

    /**
     * 检查目标日期/时间字符串是否落在指定的开始和结束日期/时间范围内（包含边界）。
     *
     * @param dateStrStart 起始日期/时间字符串 (格式: 'yyyy-MM-dd' 或 'yyyy-MM-dd HH:mm:ss')
     * @param dateStrEnd   结束日期/时间字符串 (格式: 'yyyy-MM-dd' 或 'yyyy-MM-dd HH:mm:ss')
     * @param targetDate   要检查的目标日期/时间字符串
     * @return 如果目标日期在范围内（含边界）则返回 true，否则返回 false（包括解析失败或输入为空的情况）
     */
    public static boolean isCorrectDate(String dateStrStart, String dateStrEnd, String targetDate) {
        // 对输入参数进行基础的非空校验
        if (StringUtils.isAnyBlank(dateStrStart, dateStrEnd, targetDate)) {
            // 注意：这里不再打印错误日志，因为调用方可能期望对无效输入返回 false
            // System.err.println("错误：输入的日期字符串不能为空。");
            return false;
        }

        try {
            // 解析开始、结束和目标日期为 LocalDateTime 对象，以便进行统一比较
            LocalDateTime startDateTime = parseToLocalDateTime(dateStrStart, "开始日期");
            LocalDateTime endDateTime = parseToLocalDateTime(dateStrEnd, "结束日期");
            LocalDateTime targetDateTime = parseToLocalDateTime(targetDate.substring(0,10), "目标日期");

            // 理论上 parseToLocalDateTime 失败会抛异常，这里的 null 检查是双重保险
            if (startDateTime == null || endDateTime == null || targetDateTime == null) {
                return false;
            }

            // 执行比较：target >= start AND target <= end
            // !isBefore 等价于 >=
            // !isAfter 等价于 <=
            boolean isAfterOrEqualsStart = !targetDateTime.isBefore(startDateTime);
            boolean isBeforeOrEqualsEnd = !targetDateTime.isAfter(endDateTime);

            // 目标时间必须同时大于等于开始时间且小于等于结束时间
            return isAfterOrEqualsStart && isBeforeOrEqualsEnd;

        } catch (DateTimeParseException e) {
            // 如果日期字符串格式不正确，解析会失败
            // System.err.println("日期解析错误: " + e.getMessage()); // 日志可以根据需要开启
            return false; // 解析失败意味着不在有效范围内
        } catch (IllegalArgumentException e) {
            // parseToLocalDateTime 可能因 null 或空字符串抛出此异常
            // System.err.println(e.getMessage());
            return false;
        }
    }

    /**
     * 将日期/时间字符串解析为 LocalDateTime 对象。
     * 优先尝试解析 'yyyy-MM-dd HH:mm:ss' 格式，如果失败，再尝试 'yyyy-MM-dd' 格式。
     * 如果只解析为日期（'yyyy-MM-dd'），则自动转换为当天零点（00:00:00）的 LocalDateTime。
     *
     * @param dateStr  要解析的日期/时间字符串
     * @param dateName 日期的描述性名称（用于可能的错误消息）
     * @return 解析得到的 LocalDateTime 对象
     * @throws DateTimeParseException 如果字符串无法使用任一格式解析
     * @throws IllegalArgumentException 如果 dateStr 为 null 或空
     */
    private static LocalDateTime parseToLocalDateTime(String dateStr, String dateName) {
        if (dateStr == null || dateStr.isEmpty()) {
            // 抛出异常而不是打印错误并返回 null，让调用者决定如何处理
            throw new IllegalArgumentException("错误: " + dateName + " 字符串不能为空。");
        }

        try {
            // 1. 尝试按日期时间格式解析
            return LocalDateTime.parse(dateStr, DATETIME_FORMATTER);
        } catch (DateTimeParseException e1) {
            // 2. 如果日期时间格式失败，尝试按仅日期格式解析
            try {
                LocalDate date = LocalDate.parse(dateStr, DATE_FORMATTER);
                // 将 LocalDate 转换为当天的开始时间 (00:00:00)
                return date.atStartOfDay();
            } catch (DateTimeParseException e2) {
                // 3. 如果两种格式都失败，抛出异常
                // 可以选择性地包含原始异常信息 e2
                throw new DateTimeParseException("无法使用 'yyyy-MM-dd HH:mm:ss' 或 'yyyy-MM-dd' 格式解析 " + dateName + ": '" + dateStr + "'", dateStr, 0, e2);
            }
        }
    }

    // --- 数据过滤核心逻辑 ---

    /**
     * 从 Map 中安全地获取指定键的值，并将其转换为字符串。
     *
     * @param map 包含数据的 Map
     * @param key 要获取值的键
     * @return 键对应值的字符串表示；如果键不存在或值为 null，则返回 null
     */
    private String safeGetAsString(Map<String, Object> map, String key) {
        if (map == null || key == null) {
            return null;
        }
        Object value = map.get(key);
        // Objects.toString(obj, default) 在 obj 为 null 时返回 default (这里是 null)
        return Objects.toString(value, null);
    }

    /**
     * 检查给定 Map 中，是否存在任意一个指定键对应的日期值落在时间范围内。
     *
     * @param base         包含数据的 Map 对象
     * @param dateStrStart 开始日期字符串
     * @param dateStrEnd   结束日期字符串
     * @param dateKeys     一个或多个要检查的日期键名 (可变参数)
     * @return 如果至少有一个键对应的日期在时间范围内，则返回 true，否则返回 false
     */
    private boolean checkAnyDateInRange(Map<String, Object> base, String dateStrStart, String dateStrEnd, String... dateKeys) {
        if (base == null || dateKeys == null || dateKeys.length == 0) {
            return false; // 无效输入直接返回 false
        }
        // 使用 Stream API 遍历所有指定的日期键
        return Stream.of(dateKeys)
                // 对于每个键，安全地获取其在 Map 中的字符串值
                .map(key -> safeGetAsString(base, key))
                // 使用 isCorrectDate 检查获取到的日期字符串是否在指定范围内
                // isCorrectDate 内部已处理 null 或无效格式，会返回 false
                .anyMatch(dateValue -> isCorrectDate(dateStrStart, dateStrEnd, dateValue));
    }

    /**
     * 根据指定的时间类型和时间区间，筛选数据列表。
     *
     * @param baseDatas    原始数据列表 (List of Map)
     * @param dateStrStart 筛选区间的开始日期/时间字符串
     * @param dateStrEnd   筛选区间的结束日期/时间字符串
     * @param dateType     用于确定要检查哪个日期字段的类型标识符
     * @return 经过筛选后的新列表；如果原始列表为空或筛选条件不足，可能返回原始列表或空列表
     */
    public List<Map<String, Object>> baseDataFilter(List<Map<String, Object>> baseDatas, String dateStrStart, String dateStrEnd, String dateType) {
        if (baseDatas == null || baseDatas.isEmpty()) {
            // System.out.println("输入数据列表为空，无需筛选。");
            return Collections.emptyList();
        }
        // 检查日期范围和类型参数是否为空白
        if (StringUtils.isAnyBlank(dateStrStart, dateStrEnd, dateType)) {
            // System.out.println("开始日期、结束日期或日期类型为空白，不执行筛选。");
            return baseDatas; // 参数不全，返回原始数据
        }

        // 2. 定义不同 dateType 对应的日期键名常量数组 (提高可读性，易于维护)
        final String[] PRODUCTIVITY_KEYS = {"1stProductivityReportDate", "2ndProductivityReportDate", "3rdProductivityReportDate", "4thProductivityReportDate"}; // 假设需要检查这四个
        final String[] SUBCON_PAYMENT_KEYS = {"Subcon-Payment-time-1st", "Subcon-Payment-time-2nd", "Subcon-Payment-time-3rd", "Subcon-Payment-time-4th"}; // 修正了 "4st" -> "4th"
        final String[] INVOICE_DATE_KEYS = {"Invoice-date-1st", "Invoice-date-2nd", "Invoice-date-3rd", "Invoice-date-4th"};
        final String KPI_ARCHIVE_KEY = "KPI-Archive-date"; // 单个键
        final String[] PRODUCTIVITY_AND_INVOICE_KEYS = {"1stProductivityReportDate", "2ndProductivityReportDate", "3rdProductivityReportDate", "4thProductivityReportDate",
                "Invoice-date-1st", "Invoice-date-2nd", "Invoice-date-3rd", "Invoice-date-4th"};

        // 3. 使用 Stream API 进行过滤
        List<Map<String, Object>> filteredData = baseDatas.stream() // 创建 Stream 流
                .filter(base -> { // 对列表中的每个 Map 对象应用过滤逻辑
                    if (base == null) {
                        return false; // 如果列表中的某个 Map 为 null，则过滤掉
                    }

                    // 根据传入的 dateType 选择不同的检查逻辑
                    switch (dateType) {
                        case "Productivity_report_date":
                            // 检查 PRODUCTIVITY_KEYS 中任意一个日期是否在范围内
                            return checkAnyDateInRange(base, dateStrStart, dateStrEnd, PRODUCTIVITY_KEYS);
                        case "KPI_Archive_date":
                            // 获取单个 KPI 日期值并检查
                            String kpiDate = safeGetAsString(base, KPI_ARCHIVE_KEY);
                            return isCorrectDate(dateStrStart, dateStrEnd, kpiDate);
                        case "Subcon_Payment_Time":
                            // 检查 SUBCON_PAYMENT_KEYS 中任意一个日期是否在范围内
                            return checkAnyDateInRange(base, dateStrStart, dateStrEnd, SUBCON_PAYMENT_KEYS);
                        case "Invoice_Date":
                            // 检查 INVOICE_DATE_KEYS 中任意一个日期是否在范围内
                            return checkAnyDateInRange(base, dateStrStart, dateStrEnd, INVOICE_DATE_KEYS);
//                        case "product_invoice_date":
//                            // 检查 PRODUCTIVITY_KEYS 和 INVOICE_DATE_KEYS 中任意一个日期是否在范围内
//                            return checkAnyDateInRange(base, dateStrStart, dateStrEnd, PRODUCTIVITY_AND_INVOICE_KEYS);
                        default:
                            // 如果 dateType 不匹配任何已知类型
                            // System.err.println("警告：未知的日期类型 '" + dateType + "'，此条目将不被过滤。");
                            return true;
                    }
                })
                .collect(Collectors.toList()); // 将过滤后的结果收集到一个新的 List 中 !!!

        // 4. 返回过滤后的新列表
        return filteredData;
    }
}