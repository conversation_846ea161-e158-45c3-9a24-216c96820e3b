package com.pig4cloud.pig.yptt.utils;

import cn.hutool.core.util.ObjectUtil;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo;
import com.pig4cloud.pig.yptt.service.LockDataTimeService;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName LockTimeUtil
 * @Description 校验时间锁工具
 * @date 2025/3/21 15:36
 * @Version 1.0
 */
public class LockTimeUtil {
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public void checkTimeLock(Map<String, Object> dateTmp, ImportResultVO importResultVO, String module) {
        String projectCode = null;
        try {
            projectCode = dateTmp.get("YPTT_Project_code").toString();
        } catch (Exception e) {
            importResultVO.addWrongReason("This field 【" + "YPTT_Project_code" + "】 is not exist");
            importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
        }

        //校验时间锁 y8
        //y3结算时间
        if ("y8".equals(module)) {
            String[] dateKeysPayment = {"Payment_time_4st", "Payment_time_3st", "Payment_time_2st", "Payment_time_1st"};
            Map<String, Object> m = getLastDate(dateKeysPayment, dateTmp);
            lockDate(importResultVO, m, module, projectCode);
        } else if ("y3".equals(module)) {
            String[] dateKeysSettlement = {"settlement_4th", "settlement_3rd", "settlement_2nd", "settlement_1st"};
            Map<String, Object> m = getLastDate(dateKeysSettlement, dateTmp);
            lockDate(importResultVO, m, "y3-customer", projectCode);

            String[] dateKeysSubSettlement = {"SubconSettlement_4th", "SubconSettlement_3rd", "SubconSettlement_2nd", "SubconSettlement_1st"};
            Map<String, Object> m1 = getLastDate(dateKeysSubSettlement, dateTmp);
            lockDate(importResultVO, m1, "y3-subcon", projectCode);
        } else if ("y9".equals(module)) {
            String[] dateKeysInvoice = {"Invoice_date_4st", "Invoice_date_3rd", "Invoice_date_2nd", "Invoice_date_1st"};
            Map<String, Object> m = getLastDate(dateKeysInvoice, dateTmp);
            lockDate(importResultVO, m, module, projectCode);
        }

    }

    private void lockDate(ImportResultVO importResultVO, Map<String, Object> m, String module, String projectCode) {
        if (m != null) {
            m.forEach((k, v) -> {
                try {
                    //增加日期校验 （当前模块日期被锁定，不能正常导入）
                    if (StringUtils.isNotBlank(v.toString())) {

                        if (checkDate(LocalDate.parse(v.toString(), formatter), module, projectCode)) {
                            importResultVO.addWrongReason("This field 【" + k + "】Value 【" + v + "】 date has been locked;");
                            importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                        }
                    }
                } catch (Exception e) {
                    importResultVO.addWrongReason("This field 【" + k + "】Incorrect date format Value 【" + v + "】;");
                    importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
                }
            });
        }
    }

    boolean checkDate(LocalDate v, String moduleType, String projectCode) {
        LockDataTimeService lockDataTimeService = ApplicationContextUtil.getBean(LockDataTimeService.class);
        LockDataTimeVo redisValueByModule = lockDataTimeService.getRedisValueByModule(moduleType, projectCode);
        if (ObjectUtil.isEmpty(redisValueByModule)) {
            return false;
        }

        LocalDate startTime = redisValueByModule.getStartTime();
        LocalDate endTime = redisValueByModule.getEndTime();
        if (startTime == null || endTime == null) {
            return false;
        }
        return v.isAfter(startTime) && v.isBefore(endTime);
    }

    //获取最新存在的时间
    public Map<String, Object> getLastDate(String[] dateKeys, Map<String, Object> dateTmp) {
        Map<String, Object> result = new HashMap<>();
        for (String key : dateKeys) {
            Object value = dateTmp.get(key);
            if (value == null) {
                continue; // 跳过不存在的键
            }

            String dateStr = value.toString();
            if (StringUtils.isNotBlank(dateStr)) {
                try {
                    result.put(key, LocalDate.parse(dateStr, formatter));
                    return result;
                } catch (DateTimeParseException e) {
                    // 日期格式错误处理（如记录日志）
                    System.out.println("Invalid date format for key '" + key + "': " + dateStr);
                }
            }
        }
        return result; // 所有日期为空或解析失败
    }
    public boolean checkDateErr(Object od1, Object od2, Object od3, Object od4,
                                String nd1, String nd2, String nd3, String nd4) {
        Object[] oldDates = {od1, od2, od3, od4};
        String[] newDates = {nd1, nd2, nd3, nd4};

        int lastNonEmptyIndex = -1;

        // 找出最后一个非空的新值索引
        for (int i = newDates.length - 1; i >= 0; i--) {
            if (StringUtils.isNotBlank(newDates[i])) {
                lastNonEmptyIndex = i-1;
                break;
            }
        }

        // 如果全部为 null/空，则不报错
        if (lastNonEmptyIndex == -1) {
            return false;
        }

        for (int i = 0; i <= lastNonEmptyIndex; i++) {
            String ndStr = newDates[i];
            Object odObj = oldDates[i];

            if (StringUtils.isBlank(ndStr) && odObj == null) {
                continue; // 都为空，跳过
            }

            if (StringUtils.isBlank(ndStr) || odObj == null) {
                return true; // 一个空一个不空，错误
            }

            try {
                LocalDate ndDate = parseToLocalDate(ndStr.trim());
                LocalDate odDate = (odObj instanceof LocalDate)
                        ? (LocalDate) odObj
                        : parseToLocalDate(odObj.toString().trim());

                if (ndDate == null || odDate == null || !ndDate.equals(odDate)) {
                    return true; // 日期不一致或无法解析
                }

            } catch (Exception e) {
                e.printStackTrace();
                return true; // 出现异常，认为错误
            }
        }

        return false; // 所有对比项都一致
    }

    // 封装统一日期解析逻辑，兼容 yyyy-MM-dd 和 yyyy-MM-ddTHH:mm
    private LocalDate parseToLocalDate(String input) {
        if (StringUtils.isBlank(input)) return null;
        input = input.trim();
        try {
            if (input.contains("T")) {
                return LocalDateTime.parse(input).toLocalDate(); // eg. 2025-01-30T00:00
            } else {
                return LocalDate.parse(input, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
        } catch (DateTimeParseException e) {
            return null;
        }
    }



}