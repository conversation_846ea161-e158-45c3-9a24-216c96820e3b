package com.pig4cloud.pig.yptt.utils;

import cn.hutool.core.util.ObjectUtil;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo;
import com.pig4cloud.pig.yptt.service.LockDataTimeService;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName 锁定功能 第三版本
 * @Description
 * @date 2025/5/25 12:07
 * @Version 1.0
 */
public class LockTimeV3Util {
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public String getRedisKey(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            return "";
        }
        return "releaseRedisKey" + projectCode;
    }

    //检查当前时间是否存在在时间范围内 返回true:当前时间不存在在时间锁定范围
    public boolean checkTimeLock(ImportResultVO importResultVO, String projectCode, LocalDate parmDate) {
        String redisKey = getRedisKey(projectCode);
        if (parmDate == null){
            parmDate = LocalDate.now();
        }
        boolean isAllowed = checkDate(importResultVO, parmDate, projectCode);
//        if (!isAllowed) {
//            importResultVO.setStatus(ImportResultVO.STATUS_FAILED);
//        }
        return !isAllowed;
    }

    /**
     * 检查当前日期是否在允许的范围内
     * @param importResultVO 返回结果
     * @param v 当前日期
     * @param projectCode 项目编号
     * @return true=允许操作, false=不允许
     */
    boolean checkDate(ImportResultVO importResultVO, LocalDate v, String projectCode) {
        LockDataTimeService lockDataTimeService = ApplicationContextUtil.getBean(LockDataTimeService.class);
        LockDataTimeVo redisValueByModule = lockDataTimeService.getRedisValueByModuleV2(projectCode, getRedisKey(projectCode));

        // todo 锁定可能是缓存问题 无缓存 返回允许了；无缓存，应该查询数据库验证
        // 如果 Redis 无数据，默认允许操作
        if (ObjectUtil.isEmpty(redisValueByModule)) {
            return true;
        }

        LocalDate startTime = redisValueByModule.getStartTime();
        LocalDate endTime = redisValueByModule.getEndTime();

        // 如果 startTime 或 endTime 为空，说明未正确配置锁定时间，默认允许操作
        if (startTime == null || endTime == null) {
            return true;
        }

        // 检查当前日期是否在 [startTime, endTime] 范围内
        if (!v.isBefore(startTime) && !v.isAfter(endTime)) {
            return true;
        } else {
//            importResultVO.addWrongReason("The current project:" + projectCode + " is locked (Valid: " + startTime + " to " + endTime + ")");
            return false;
        }
    }

    public boolean checkDateErr(Object od1, Object od2, Object od3, Object od4,
                                String nd1, String nd2, String nd3, String nd4) {
        Object[] oldDates = {od1, od2, od3, od4};
        String[] newDates = {nd1, nd2, nd3, nd4};

        // 检查每个对应位置是否匹配
        for (int i = 0; i < oldDates.length; i++) {
            String ndStr = newDates[i];
            Object odObj = oldDates[i];

            // 情况1: 两者都为空，继续检查下一个
            if (StringUtils.isBlank(ndStr) && odObj == null) {
                continue;
            }

            // 情况2: 一个空一个不空，返回错误
            if (StringUtils.isBlank(ndStr) || odObj == null) {
                return true;
            }

            // 情况3: 两者都不空，比较日期值
            try {
                LocalDate ndDate = parseToLocalDate(ndStr.trim());
                LocalDate odDate = (odObj instanceof LocalDate)
                        ? (LocalDate) odObj
                        : parseToLocalDate(odObj.toString().trim());

                // 如果任一日期无法解析或日期不相等，返回错误
                if (ndDate == null || odDate == null || !ndDate.equals(odDate)) {
                    return true;
                }
            } catch (Exception e) {
                e.printStackTrace();
                return true; // 出现异常，认为错误
            }
        }

        return false; // 所有对应位置都匹配
    }

    // 封装统一日期解析逻辑，兼容 yyyy-MM-dd 和 yyyy-MM-ddTHH:mm
    private LocalDate parseToLocalDate(String input) {
        if (StringUtils.isBlank(input)) return null;
        input = input.trim();
        try {
            if (input.contains("T")) {
                return LocalDateTime.parse(input).toLocalDate(); // eg. 2025-01-30T00:00
            } else {
                return LocalDate.parse(input, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    //获取日期转换返回LocalDate格式日期
    public static LocalDate toLocalDate(Object raw) {
        if (ObjectUtil.isEmpty(raw)) return null;
        if (raw instanceof LocalDateTime) {
            return  ((LocalDateTime) raw).toLocalDate();
        }
        if (raw instanceof LocalDate) return (LocalDate) raw;
        if (raw instanceof Date) {
            return ((Date) raw).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        }
        if (raw instanceof String) {
            try {
                return LocalDate.parse((String) raw); // 默认 yyyy-MM-dd
            } catch (DateTimeParseException e) {
                throw new RuntimeException("字符串不能解析为 LocalDate: " + raw);
            }
        }
        throw new RuntimeException("不支持的日期类型: " + raw.getClass());
    }


}
