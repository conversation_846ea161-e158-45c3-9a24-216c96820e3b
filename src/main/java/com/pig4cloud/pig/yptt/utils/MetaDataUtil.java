package com.pig4cloud.pig.yptt.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataDTO;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataValueDTO;
import com.pig4cloud.pig.me.api.dto.operation.QueryDTO;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/09/28
 */
public class MetaDataUtil {

	private final static String EMPTY_JSON_ARRAY = "[]";

	private final static BigDecimal BigDecimal100 = new BigDecimal(100);

	/**
	 * Long型数据ID 转为 数据ID Json串
	 * @param id Long型数据ID
	 * @return 数据ID Json串
	 */
	public static String handleDataId2Json(Long id) {
		if (Objects.isNull(id)) {
			return EMPTY_JSON_ARRAY;
		}
		return String.format("[\"%d\"]", id);
	}

	/**
	 * 数据ID Json串 转为 Long型数据ID
	 * @param dataIdJsonStr 数据ID Json串
	 * @return Long型数据ID
	 */
	public static Long handleDataIdJson2Long(String dataIdJsonStr) {
		if (StrUtil.isBlank(dataIdJsonStr)) {
			return null;
		}
		JSONArray jsonArray = JSONArray.parseArray(dataIdJsonStr);
		if (CollUtil.size(jsonArray) != 1) {
			throw new IllegalArgumentException("dataId should be one, but got " + CollUtil.size(jsonArray));
		}
		return jsonArray.getLong(0);
	}

	public static String handleValues2Json(String... values) {
		return handleValues2Json(true, true, values);
	}

	public static String handleValues2Json(boolean ignoreBlank, boolean trim, String... values) {
		if (ArrayUtil.isEmpty(values)) {
			return EMPTY_JSON_ARRAY;
		}
		Stream<String> stringStream = Arrays.stream(values).filter(Objects::nonNull);
		if (ignoreBlank) {
			stringStream = stringStream.filter(StrUtil::isNotBlank);
		}
		if (trim) {
			stringStream = stringStream.map(StrUtil::trim);
		}
		String handledStrings = stringStream.map(s -> String.format("\"%s\"", s)).collect(Collectors.joining(","));
		return String.format("[%s]", handledStrings);
	}

	public static <T> List<T> handleJson2Values(String jsonArrayStr, Class<T> clz) {
		if (StrUtil.isBlank(jsonArrayStr)) {
			return new ArrayList<>(0);
		}
		try {
			return JSONArray.parseArray(jsonArrayStr, clz);
		}
		catch (Exception e) {
			throw new IllegalArgumentException("Parsing jsonarray failed, " + jsonArrayStr);
		}
	}

	public static String handleObject2String(Object value) {
		return Objects.isNull(value) ? null : StrUtil.toString(value);
	}

	public static BigDecimal handleObject2BigDecimal(Object value, Boolean blank2Zero) {
		if (Objects.isNull(value)) {
			return blank2Zero ? BigDecimal.ZERO : null;
		}
		return new BigDecimal(value.toString());
	}

	public static BigDecimal percentageStr2BigDecimal(String percentageStr) {
		return percentageStr2BigDecimal(percentageStr, 4, false);
	}

	public static BigDecimal percentageStr2BigDecimal(String percentageStr, int scale, boolean blank2Zero) {
		try {
			percentageStr = StrUtil.trim(percentageStr);
			if (StrUtil.isBlank(percentageStr)) {
				return blank2Zero ? BigDecimal.ZERO : null;
			}
			return new BigDecimal(percentageStr.replace("%", "").trim()).divide(BigDecimal100, scale,
					RoundingMode.HALF_UP);
		}
		catch (Exception e) {
			throw new IllegalArgumentException("Illegal Percentage Format: " + percentageStr, e);
		}
	}

	public static BigDecimal numberStr2BigDecimal(String numberStr) {
		return numberStr2BigDecimal(numberStr, 4, false);
	}

	public static BigDecimal numberStr2BigDecimal(String numberStr, int scale, boolean blank2Zero) {
		try {
			numberStr = StrUtil.trim(numberStr);
			if (StrUtil.isBlank(numberStr)) {
				return blank2Zero ? BigDecimal.ZERO : null;
			}
			return new BigDecimal(numberStr.replace(",", "")).setScale(scale, RoundingMode.HALF_UP);
		}
		catch (Exception e) {
			throw new IllegalArgumentException("Illegal Number Format: " + numberStr, e);
		}
	}

	/**
	 * 解析 日期字符串 为 {@link LocalDateTime}, 时间使用 {@link LocalDate#atStartOfDay()} 设置为当天开始时间，即
	 * 00:00:00
	 * @param dateStr 日期字符串
	 * @return {@link LocalDateTime}
	 */
	public static LocalDateTime dateStr2LocalDateTime(String dateStr) {
		if (StrUtil.isBlank(dateStr)) {
			return null;
		}
		return LocalDateTimeUtil.parseDate(dateStr).atStartOfDay();
	}

	public static String toPrettyString(MetaDataDTO metaDataDTO) {
		if (Objects.isNull(metaDataDTO)) {
			return null;
		}
		Map<String, Object> json = new JSONObject(
				IterUtil.toMap(metaDataDTO.getData(), MetaDataValueDTO::getName, MetaDataValueDTO::getValue));
		return String.format("{\"dataId\":%d, \"data\": %s", metaDataDTO.getDataId(), json);
	}

	/**
	 * 获取唯一标识字符串
	 * @param dict map
	 * @param type 导入模块
	 * @return uniquenessField
	 */
	public static String getUniquenessField(Dict dict, String type) {
		StringJoiner uniqueField = new StringJoiner(StrUtil.UNDERLINE);
		if (GlobalConstants.Y1234_UNIQUENESS_FIELD_LIST.contains(type)) {
			uniqueField.add(dict.getStr("YPTT_Project_code"))
				.add(dict.getStr("Region"))
				.add(dict.getStr("Site_ID"))
				.add(dict.getStr("Phase"))
				.add(dict.getStr("Item_code"));
		}
		else if (Objects.equals(type, GlobalConstants.Y8.NAME)) {
			uniqueField.add(dict.getStr("Subcon_name"))
				.add(dict.getStr("Subcon_PO_number"))
				.add(dict.getStr("Site_ID"))
				.add(dict.getStr("Phase"))
				.add(dict.getStr("Item_code"));
		}
		else if (Objects.equals(type, GlobalConstants.Y9.NAME)) {
			uniqueField.add(dict.getStr("PO_number"))
				.add(dict.getStr("Contract_number"))
				.add(dict.getStr("Phase"))
				.add(dict.getStr("Site_ID"))
				.add(dict.getStr("Item_code"));
		}
		return uniqueField.toString();
	}

	/**
	 * 追加字符串；若原始字符串为{@code null}或空串，则直接返回追加字符串{@code append}，否则返回
	 * {@code originalStr + separator + append}
	 * @param originalStr 原始字符串
	 * @param append 追加字符串
	 * @param separator 分割字符串
	 * @return 追加后字符串
	 */
	public static String appendStr(Object originalStr, String append, String separator) {
		if (StrUtil.isBlank(StrUtil.trim(append))) {
			return (String) originalStr;
		}
		if (Objects.isNull(originalStr)) {
			return append;
		}
		if (StrUtil.isBlank(StrUtil.trim(originalStr.toString()))) {
			return append;
		}
		return StrUtil.trim(originalStr.toString()) + separator + append;
	}

	public static Object conditionsToPrettyString(List<QueryDTO> conditions) {
		if (Objects.isNull(conditions)) {
			return "";
		}
		return conditions.stream()
			.filter(Objects::nonNull)
			.map(q -> String.format("%s/%s/%s", q.getName(), Objects.toString(q.getSymbol(), "default"), q.getValue()))
			.collect(Collectors.joining(","));
	}

}
