package com.pig4cloud.pig.yptt.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 付款申请单
 * @TableName memm_payment_application_1f120kq4jd401
 */
@Data
public class PaymentApplication {
    /**
     * 记录id
     */
    private Long id;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Long updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 记录是否被删除
     */
    private Long isDeleted;

    /**
     * 数据被当前的流程ID使用
     */
    private String currProcInstId;

    /**
     * 一级项目名称
     */
    private String project;

    /**
     * 一级项目编号
     */
    private String projectCode;

    /**
     * 流程编号
     */
    private String code;

    /**
     * 申请人
     */
    private String sqr;

    /**
     * 申请日期
     */
    private Date sqrq;

    /**
     * 申请部门
     */
    private String sqbm;

    /**
     * 事由
     */
    private String sy;

    /**
     * 币种
     */
    private String currency;

    /**
     * 账户信息
     */
    private String account;

    /**
     * 银行账号
     */
    private String bankNum;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 付款金额
     */
    private BigDecimal paymentMoney;

    /**
     * 付款状态
     */
    private String status;
}