package com.pig4cloud.pig.yptt.entity;


import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @ClassName: PendingPaymentBill
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-07-23  14:33
 * @Version: 1.0
 */
@Data
public class PendingPaymentBill {
    private Long id;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    private String currProcInstId;
    private String code;
    private String departmentid;
    private String yjxmmc;
    private String yjxmbh;
    private String account;
    private String bank;
    private String bankNum;
    private BigDecimal payMoney;
    private BigDecimal payMoneyLeave;
    private String bz;
    private String workflowId;
    private String sqr;
    private LocalDateTime sqrq;
    private String company;
    private String fysy;
    private String fybm;
    private String zflx;
    private String invoice;
    private String reimType;
}