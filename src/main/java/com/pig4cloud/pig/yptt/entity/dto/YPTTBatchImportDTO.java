package com.pig4cloud.pig.yptt.entity.dto;

import com.pig4cloud.pig.me.api.dto.operation.OperationBatchImportDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/09/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class YPTTBatchImportDTO extends OperationBatchImportDTO {

	private String moduleType;

	private String appId;

	private Long mainViewId;

	private Long mainViewGroupId;

}
