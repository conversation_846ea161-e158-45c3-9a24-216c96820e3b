package com.pig4cloud.pig.yptt.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName StatisticsUserOperateDTO
 * @Description
 * @date 2025/2/24 13:48
 * @Version 1.0
 */
@Data
public class StatisticsUserOperateDTO {

    @NotNull
    private String startDate;

    @NotNull
    private String endDate;

    private List<String> moduleTypes;

    private Long userId;

    private String projectCode;

}