package com.pig4cloud.pig.yptt.entity.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReportDataFiledVO implements Serializable {

	private Page<Map<String, Object>> page;

	private List<Header> headers;

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Header {

		private String groupName;

		private List<Key> keys;

	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Key {

		private String label;

		private String value;

	}

}
