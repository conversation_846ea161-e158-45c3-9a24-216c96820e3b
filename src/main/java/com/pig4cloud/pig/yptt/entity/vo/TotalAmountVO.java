package com.pig4cloud.pig.yptt.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TotalAmountVO
 * @Description
 * @date 2025/4/8 10:03
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TotalAmountVO {
    //    private AmountY2VO amountY2VOS;
    private BigDecimal totalPOValue;

    //    private AmountY6VO amountY6VOS;
    private BigDecimal totalKPIArchiveAmount;
    private BigDecimal total1stProductivityReportAmount;
    private BigDecimal total2ndProductivityReportAmount;
    private BigDecimal total3rdProductivityReportAmount;
    private BigDecimal total4thProductivityReportAmount;
    private BigDecimal totalProductivityAmount;

    //    private AmountY9VO amountY9VOS;
    private BigDecimal totalInvoiceAmount1st;
    private BigDecimal totalInvoiceAmount2st;
    private BigDecimal totalInvoiceAmount3st;
    private BigDecimal totalInvoiceAmount4st;
    private BigDecimal totalInvoiceAmount;
    private BigDecimal totalCNAmount;
}