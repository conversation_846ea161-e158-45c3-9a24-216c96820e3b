package com.pig4cloud.pig.yptt.entity;

import lombok.Data;
import java.math.BigDecimal;

/**
 * @ClassName: ReimburseInvoiceEntity
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-08-13  15:25
 * @Version: 1.0
 */
@Data
public class ReimburseInvoiceEntity extends BaseEntity {
    /**
     * 发票号
     **/
    private String invoiceNum;
    /**
     * 含税金额
     **/
    private BigDecimal taxMoney;
    /**
     * 不含税金额
     **/
    private BigDecimal noTaxMoney;
    /**
     * 税额
     **/
    private BigDecimal taxes;
    /**
     * 发票单位
     **/
    private String supplier;
}