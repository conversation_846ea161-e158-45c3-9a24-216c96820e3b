package com.pig4cloud.pig.yptt.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @ClassName LockDataTimeVo
 * @Description
 * @date 2025/3/18 14:09
 * @Version 1.0
 */
@Data
public class LockDataTimeVo implements Serializable {
    private static final long serialVersionUID = 1L; // 可选，但推荐添加
    String moduleType;
    LocalDate startTime;
    LocalDate endTime;
    Long id;
    String projectCode;
    private Integer typeLock;
}