package com.pig4cloud.pig.yptt.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/09/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IncomeAndExpenditureVO implements Serializable {

	/**
	 * 站点价值合计
	 */
	private BigDecimal siteItemValueTotal;

	/**
	 * po订单价值合计
	 */
	private BigDecimal poItemValueTotal;

	/**
	 * 分包商po合计
	 */
	private BigDecimal subconTotalAmountPoItem;

	/**
	 * 可结算金额合计
	 */
	private BigDecimal totalSettableAmount;

	/**
	 * 产值申报金额合计
	 */
	private BigDecimal totalOutputValue;

	/**
	 * 分包商可结算合计
	 */
	private BigDecimal subconTotalAmountSettled;

	/**
	 * 分包商支付金额合计
	 */
	private BigDecimal subconTotalAmountPaid;

	/**
	 * 发票总金额合计
	 */
	private BigDecimal totalInvoiceAmount;

	/**
	 * 时间点
	 */
	private String dateLine;

	/**
	 * 项目名称
	 */
	private String projectName;
	/**
	 * y9 结算cn金额
	 */
	private BigDecimal CNAmount;



}
