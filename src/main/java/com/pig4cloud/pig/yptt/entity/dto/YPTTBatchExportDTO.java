package com.pig4cloud.pig.yptt.entity.dto;

import com.pig4cloud.pig.me.api.dto.operation.BatchExportDTO;
import com.pig4cloud.pig.yptt.entity.YPTTExportFiled;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/09/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class YPTTBatchExportDTO extends BatchExportDTO {

	private Map<String, YPTTExportFiled> extHeadMap;

}
