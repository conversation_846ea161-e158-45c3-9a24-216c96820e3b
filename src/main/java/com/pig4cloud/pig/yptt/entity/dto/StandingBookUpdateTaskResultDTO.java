package com.pig4cloud.pig.yptt.entity.dto;

import com.pig4cloud.pig.common.core.constant.enums.CommonBizCode;
import com.pig4cloud.pig.common.core.util.R;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/01/17
 */
public class StandingBookUpdateTaskResultDTO extends ConcurrentHashMap<String, R<List<R<StandingBook>>>> {

	public boolean isAllSuccessful() {
		for (R<List<R<StandingBook>>> value : this.values()) {
			if (!Objects.equals(value.getCode(), CommonBizCode.SUCCESS.name())) {
				return false;
			}
		}
		return true;
	}

}
