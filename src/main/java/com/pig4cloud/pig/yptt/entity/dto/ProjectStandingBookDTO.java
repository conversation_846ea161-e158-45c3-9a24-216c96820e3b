package com.pig4cloud.pig.yptt.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
public class ProjectStandingBookDTO implements StandingBook {

	public final static RowMapper ROW_MAPPER = new RowMapper();

	private Long id;

	private Long projectId;

	private String projectName;

	private Integer countOfSiteItem;

	private Integer countOfPoItem;

	private BigDecimal totalSiteValue;

	private BigDecimal totalPoValue;

	private Integer countOfSubconPoItem;

	private BigDecimal subconPoAmount;

	private BigDecimal subconPoAddiCost;

	private BigDecimal readyForSettleAmount;

	private BigDecimal notReadySettleAmount;

	private BigDecimal reporttedProdAmount;

	private BigDecimal subconSettleAmount;

	private BigDecimal subconSettleGap;

	private BigDecimal subconPayAmount;

	private BigDecimal subconPayAmountGap;

	private BigDecimal invoiceAmount;

	private BigDecimal invoiceAmountGap;

	@NoArgsConstructor
	public final static class RowMapper implements org.springframework.jdbc.core.RowMapper<ProjectStandingBookDTO> {

		@Override
		public ProjectStandingBookDTO mapRow(ResultSet rs, int rowNum) throws SQLException {
			ProjectStandingBookDTO entity = new ProjectStandingBookDTO();
			Long project_id = rs.getLong("ProjectId");
			String project_name = rs.getString("ProjectName");
			Integer count_of_site_item = rs.getInt("CountOfSiteItem");
			Integer count_of_po_item = rs.getInt("CountOfPoItem");
			BigDecimal total_site_value = rs.getBigDecimal("TotalSiteValue");
			BigDecimal total_po_value = rs.getBigDecimal("TotalPoValue");
			Integer count_of_subcon_po_item = rs.getInt("CountOfSubconPoItem");
			BigDecimal subcon_po_amount = rs.getBigDecimal("SubconPoAmount");
			BigDecimal subcon_po_addi_cost = rs.getBigDecimal("SubconPoAddiCost");
			BigDecimal ready_for_settle_amount = rs.getBigDecimal("ReadyForSettleAmount");
			BigDecimal not_ready_settle_amount = rs.getBigDecimal("NotReadySettleAmount");
			BigDecimal reportted_prod_amount = rs.getBigDecimal("ReporttedProdAmount");
			BigDecimal subcon_settle_amount = rs.getBigDecimal("SubconSettleAmount");
			BigDecimal subcon_settle_gap = rs.getBigDecimal("SubconSettleGap");
			BigDecimal subcon_pay_amount = rs.getBigDecimal("SubconPayAmount");
			BigDecimal SubconPayAmountGap = rs.getBigDecimal("SubconPayAmountGap");
			BigDecimal invoice_amount = rs.getBigDecimal("InvoiceAmount");
			BigDecimal invoice_amount_gap = rs.getBigDecimal("InvoiceAmountGap");

			entity.setProjectId(project_id);
			entity.setProjectName(project_name);
			entity.setCountOfSiteItem(count_of_site_item);
			entity.setCountOfPoItem(count_of_po_item);
			entity.setTotalSiteValue(total_site_value);
			entity.setTotalPoValue(total_po_value);
			entity.setCountOfSubconPoItem(count_of_subcon_po_item);
			entity.setSubconPoAmount(subcon_po_amount);
			entity.setSubconPoAddiCost(subcon_po_addi_cost);
			entity.setReadyForSettleAmount(ready_for_settle_amount);
			entity.setNotReadySettleAmount(not_ready_settle_amount);
			entity.setReporttedProdAmount(reportted_prod_amount);
			entity.setSubconSettleAmount(subcon_settle_amount);
			entity.setSubconSettleGap(subcon_settle_gap);
			entity.setSubconPayAmount(subcon_pay_amount);
			entity.setSubconPayAmountGap(SubconPayAmountGap);
			entity.setInvoiceAmount(invoice_amount);
			entity.setInvoiceAmountGap(invoice_amount_gap);

			return entity;
		}

	}

}
