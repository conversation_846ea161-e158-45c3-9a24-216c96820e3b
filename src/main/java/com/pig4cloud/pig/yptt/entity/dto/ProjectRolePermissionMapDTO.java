package com.pig4cloud.pig.yptt.entity.dto;

import com.pig4cloud.pig.me.api.handler.JsonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/13
 */
@Data
public class ProjectRolePermissionMapDTO implements Serializable {

	private Long projectId;

	private String ypttProjectCode;

	private String ypttProjectName;

	private List<Long> projectRole;

	private List<Long> projectMember;

	private List<String> permModel;

	private Integer permQuery;

	private Integer permUpdate;

	private Integer permDel;

	private Integer permAdd;

	public static class ListStringHandler extends JsonTypeHandler<String> {

		public ListStringHandler(Class<String> type) {
			super(type);
		}

	}

}
