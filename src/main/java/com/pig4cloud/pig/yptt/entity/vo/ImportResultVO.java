package com.pig4cloud.pig.yptt.entity.vo;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/09/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportResultVO implements Serializable {

	public static final String STATUS_SUCCEED = "SUCCEED";

	public static final String STATUS_FAILED = "FAILED";

	public static final String STATUS_COMPLETE = "COMPLETE";

	public static final String STATUS_RUNNING = "RUNNING";

	public static final String STATUS_READY = "READY";

	private Integer index;

	private Map<String, Object> importData;

	/**
	 * optional: {@link #STATUS_SUCCEED}, {@link #STATUS_FAILED}, {@link #STATUS_RUNNING},
	 * {@link #STATUS_READY}
	 */
	private String status;

	private String wrongReason;

	public void addWrongReason(String newWrongReason) {
		if (StrUtil.isBlank(wrongReason)) {
			setWrongReason(StrUtil.trim(newWrongReason));
		}
		else {
			setWrongReason(wrongReason + "\n" + StrUtil.trim(newWrongReason));
		}
	}

}
