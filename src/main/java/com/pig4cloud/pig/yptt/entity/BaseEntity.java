package com.pig4cloud.pig.yptt.entity;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName: BaseEntity
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-08-14  16:19
 * @Version: 1.0
 */
@Data
public class BaseEntity {
    private Long id = IdWorker.getId();
    private Long createBy;
    private LocalDateTime createTime = LocalDateTime.now();
    private Long updateBy;
    private LocalDateTime updateTime = LocalDateTime.now();
    private Long isDeleted = 0L;
    private String currProcInstId = "-1";
}