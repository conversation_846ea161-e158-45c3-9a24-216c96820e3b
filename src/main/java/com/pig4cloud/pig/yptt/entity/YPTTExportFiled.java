package com.pig4cloud.pig.yptt.entity;

import com.pig4cloud.pig.me.api.dto.operation.ExportFiled;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/09/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class YPTTExportFiled extends ExportFiled {

	private Integer isRequire;

	private String metaDisplayName;

}
