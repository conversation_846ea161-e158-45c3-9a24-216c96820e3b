package com.pig4cloud.pig.yptt.entity.vo;

import com.pig4cloud.pig.yptt.entity.StatisticsUserOperate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName StatisticsUserOperateVO
 * @Description
 * @date 2025/2/24 13:45
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsUserOperateVO implements Serializable {

    private List<StatisticsUserOperate> statisticsUserOperates;

    private List<String> moduleTypes;
}