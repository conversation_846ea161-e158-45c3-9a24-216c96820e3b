package com.pig4cloud.pig.yptt.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName StatisticsUserOperate
 * @Description
 * @date 2025/2/24 13:58
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatisticsUserOperate {

    /** 创建人id */
    private Long createBy;

    /** 创建人名称 */
    private String fullName;

    /** 创建人日期 */
    private String createTime;

    /** 项目编号 */
    private String YPTTProjectCode;

    /** 项目名称 */
    private String YPTTProjectName;

    /** 有效条目数量 */
    private Integer effectiveQuantity;

    /** 有效条更新天数 */
    private Integer effectiveDays;

    /** 模块类型 */
    private String moduleType;
    /** 唯一标识id */
    private String unId;

    /** 用户 日期-模块 操作列表 */
    private List<StatisticsUserDateOperate> statisticsUserDateOperate;

}