package com.pig4cloud.pig.yptt.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/01/17
 */
@Data
public class CustomerProjectStandingBookDTO implements StandingBook {

	private Long customerProjectId;

	private String contractNumber;

	private String customProjectName;

	private BigDecimal invoiceAmount;

	private BigDecimal invoiceAmountGap;

	private Integer poCount;

	private Integer poItemCount;

}
