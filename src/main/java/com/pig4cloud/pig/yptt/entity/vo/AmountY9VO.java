package com.pig4cloud.pig.yptt.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName AmountY2VO
 * @Description
 * @date 2025/4/8 10:05
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AmountY9VO {

    private BigDecimal totalInvoiceAmount1st;
    private BigDecimal totalInvoiceAmount2st;
    private BigDecimal totalInvoiceAmount3st;
    private BigDecimal totalInvoiceAmount4st;
    private BigDecimal totalInvoiceAmount;
    private BigDecimal totalCNAmount;
}