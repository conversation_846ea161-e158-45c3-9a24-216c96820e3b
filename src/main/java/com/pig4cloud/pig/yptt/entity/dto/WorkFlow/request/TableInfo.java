package com.pig4cloud.pig.yptt.entity.dto.WorkFlow.request;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlowDTO;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlowDataDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: TableInfo
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-07-24  16:10
 * @Version: 1.0
 */
@Data
public class TableInfo {
    private String dataId;
    private Map<String, WorkFlowDataDTO> data = new HashMap<>();
    private List<TableInfo> sub = new ArrayList<>();
    private String modelId;
    private String modelName;
    private String viewId;
    private String viewName;
    private String viewGroupId;
    private String viewGroupName;

    /**
     * 将字符串数组类型的表单字段值转换成List
     **/
    public List<String> getJsonArrayValue(String WorkFlowDataDTOName) {
        ObjectMapper mapper = new ObjectMapper();

        List<String> values = new ArrayList<>();
        try {
            // 解析 json 数组字符串
            WorkFlowDataDTO fieldInfo = data.get(WorkFlowDataDTOName);
            if(fieldInfo == null){
                return values;
            }

            String jsonValue = fieldInfo.getValue();
            if (jsonValue == null) {
                return values;
            }
            values = mapper.readValue(
                    jsonValue,
                    new TypeReference<List<String>>() {}
            );
        } catch (Exception e) {
            e.printStackTrace();
            return values;
        }

        return values;
    }

    public String getValue(String WorkFlowDataDTOName) {
        try {
            return data.get(WorkFlowDataDTOName).getValue();
        }catch (Exception e){
            return null;
        }
    }

    public Double getDoubleValue(String WorkFlowDataDTOName) {
        try {
            return Double.parseDouble(getValue(WorkFlowDataDTOName));
        }catch (Exception e){
            return null;
        }
    }

    public String getExtValue(String WorkFlowDataDTOName) {
        try {
            return data.get(WorkFlowDataDTOName).getExtValue();
        }catch (Exception e){
            return null;
        }
    }

    public Long getLongValue(String WorkFlowDataDTOName) {
        try {
            return Long.parseLong(getValue(WorkFlowDataDTOName));
        }catch (Exception e){
            return null;
        }
    }
}