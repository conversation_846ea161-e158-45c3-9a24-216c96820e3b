package com.pig4cloud.pig.yptt.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName BaseReportDataQueryParam
 * @Description
 * @date 2025/4/10 10:24
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseReportDataQueryParam {
    private List<String> projectIds;
    private String dateStrStart;
    private String dateStrEnd;
    private String dateType;
    private String area;
    private String nation;
}