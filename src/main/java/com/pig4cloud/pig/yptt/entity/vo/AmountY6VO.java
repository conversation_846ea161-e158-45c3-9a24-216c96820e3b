package com.pig4cloud.pig.yptt.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName AmountY2VO
 * @Description
 * @date 2025/4/8 10:05
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AmountY6VO {
    private BigDecimal totalKPIArchiveAmount;
    private BigDecimal total1stProductivityReportAmount;
    private BigDecimal total2ndProductivityReportAmount;
    private BigDecimal total3rdProductivityReportAmount;
    private BigDecimal total4thProductivityReportAmount;
    private BigDecimal totalProductivityAmount;
}