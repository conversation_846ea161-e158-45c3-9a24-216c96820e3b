package com.pig4cloud.pig.yptt.controller.workflow;

import com.pig4cloud.pig.yptt.controller.WorkFlowApiRes;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlowDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: WorkFlowEndController
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-08-15  16:28
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("workflow/end")
public class WorkFlowEndController {
    /**
     * 借预付款申请
     **/
    @PostMapping("advance-payments-and-personal-loans")
    public WorkFlowApiRes advancePaymentsAndPersonalLoans(WorkFlowDTO request){
        return WorkFlowApiRes.error("失败");
    }
}