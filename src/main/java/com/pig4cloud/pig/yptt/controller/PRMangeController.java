package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.io.resource.ClassPathResource;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.me.api.dto.operation.OperationPageDTO;
import com.pig4cloud.pig.me.api.exception.ModelEngineBizCode;
import com.pig4cloud.pig.yptt.entity.dto.PRDataDTO;
import com.pig4cloud.pig.yptt.service.PRService;
import com.pig4cloud.pig.yptt.utils.ExcelUtil;
import com.pig4cloud.pig.yptt.utils.PRUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName PRMangeController
 * @Description
 * @date 2025/5/30 11:21
 * @Version 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/PR")
public class PRMangeController {

    private final PRService prService;

    /**
     * @return
     * <AUTHOR>
     * @Description PR下载
     * @Date 11:22 2025/5/30
     **/
    @PostMapping("/export/pr-data")
    public void exportPRData(HttpServletResponse response, @RequestBody @Validated PRDataDTO prDataDTO) {
        try {
            String projectCode = prDataDTO.getProjectCode();
            if (StringUtils.isBlank(projectCode)){
                throw new BizException(ModelEngineBizCode.EXPORT_ERROR, "have no projectCode");
            }
            PRUtil prUtil = new PRUtil();
            String prNo = prUtil.createPrNo(projectCode, "");

            ClassPathResource resource = new ClassPathResource("import-template/PRList.xlsx");
            response.setHeader("filename",prNo);
            ExcelUtil.exportTemplateList(response, prNo, resource, prService.exportPRData(prDataDTO, prNo));
        } catch (IOException e) {
            throw new BizException(ModelEngineBizCode.EXPORT_ERROR, e);
        }
    }

    /**
     * <AUTHOR>
     * @Description PR搜索功能
     * @Date 11:35 2025/5/30
     **/
    @GetMapping("/search")
    public R<Page<Map<String, Object>>> search(@RequestParam(value = "projectCode") String projectCode,
                                               @RequestParam(value = "region", required = false) String region,
                                               @RequestParam(value = "siteId", required = false) String siteId,
                                               @RequestParam(value = "itemCode", required = false) String itemCode,
                                               @RequestParam(value = "phase", required = false) String phase,
                                               @RequestParam(value = "unId", required = false) String unId,
                                               @RequestParam( defaultValue = "1") Integer page,
                                               @RequestParam(defaultValue = "10") Integer size) {
        return prService.search(projectCode, region, siteId, itemCode, phase, page, size, unId);
    }

    /**
     * <AUTHOR>
     * @Description PR查询条目下载详情
     * @Date 11:35 2025/5/30
     **/
    @GetMapping("/search-detail")
    public R<Page<Map<String, Object>>> searchDeatil(@RequestParam(value = "ufId") String ufId,
                                               @RequestParam( defaultValue = "1") Integer page,
                                               @RequestParam(defaultValue = "10") Integer size) {
        return prService.searchDeatil(ufId, page, size);
    }

    @GetMapping("/search-total")
    public R<Page<Map<String, Object>>> searchTotal(@RequestParam(value = "projectCode",required = false) String projectCode,
                                                    @RequestParam(value = "userId" ,required = false) Long userId,
                                                     @RequestParam( defaultValue = "1") Integer page,
                                                     @RequestParam(defaultValue = "10") Integer size) {
        return prService.searchTotal(projectCode, userId, page, size);
    }

}