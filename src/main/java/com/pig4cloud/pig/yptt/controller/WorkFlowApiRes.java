package com.pig4cloud.pig.yptt.controller;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: WorkFlowApiRes
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-06-20  11:00
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkFlowApiRes {
    private Boolean success;

    private String msg;

    private Object data;

    public static WorkFlowApiRes ok(Object data) {
        return new WorkFlowApiRes(true, null, data);
    }

    public static WorkFlowApiRes ok() {
        return new WorkFlowApiRes(true, null, null);
    }

    public static WorkFlowApiRes error(String msg) {
        return new WorkFlowApiRes(false, msg, null);
    }
}