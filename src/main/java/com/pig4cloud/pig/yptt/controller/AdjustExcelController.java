package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.lang.Assert;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.annotation.Inner;
import com.pig4cloud.pig.me.api.dto.operation.OperationPageDTO;
import com.pig4cloud.pig.me.api.dto.operation.OperationUpdateDTO;
import com.pig4cloud.pig.me.api.exception.ModelEngineBizCode;
import com.pig4cloud.pig.yptt.config.YpttPersonalizedApiProperties;
import com.pig4cloud.pig.yptt.entity.vo.*;
import com.pig4cloud.pig.yptt.service.AdjustExcelService;
import com.pig4cloud.pig.yptt.service.AdjustService;
import com.pig4cloud.pig.yptt.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName AdjustController
 * @Description excel文件操作
 * @date 2025/1/11 16:43
 * @Version 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/adjust-excel")
public class AdjustExcelController {
    private final AdjustExcelService adjustService;
    private final YpttPersonalizedApiProperties props;

    /**
     * @return com.pig4cloud.pig.common.core.util.R<java.lang.Object>
     * <AUTHOR>
     * @Description 导出y2的批量修改excel模板
     * @Date 16:49 2025/1/11
     * @Param []
     **/
    @PostMapping("/export/update-y2")
    public void exportY2(HttpServletResponse response, @RequestBody @Validated OperationPageDTO pageDTO) {
        try {
            ClassPathResource resource = new ClassPathResource("import-template/y2-update-batch.xlsx");
            ExcelUtil.exportTemplateList(response, "y2-update-batch", resource, adjustService.exportY2(pageDTO));
        }
        catch (IOException e) {
            throw new BizException(ModelEngineBizCode.EXPORT_ERROR, e);
        }
    }

    /**
     * <AUTHOR>
     * @Description y2导入数据
     * @Date 9:23 2025/1/15
     * @Param [file, key]
     * @return com.pig4cloud.pig.common.core.util.R<java.util.List < com.pig4cloud.pig.yptt.entity.vo.ImportResultVO>>
     **/
    @PostMapping("/import/update-y2")
    public R<List<ImportResultVO>> updateY2(MultipartFile file, String key) {
        return R.ok(adjustService.updateY2(file, key));
    }

    /**
     * <AUTHOR>
     * @Description 查询导入进度
     * @Date 11:06 2025/1/15
     * @Param [key]
     * @return com.pig4cloud.pig.common.core.util.R<com.pig4cloud.pig.yptt.entity.vo.ProgressY2VO>
     **/
    @GetMapping("/import/query-progress-y2")
    public R<ProgressY2VO> queryProgressY2(String key) {
        return R.ok(adjustService.queryProgressY2(key));
    }


    /**
     * @return com.pig4cloud.pig.common.core.util.R<java.lang.Object>
     * <AUTHOR>
     * @Description 导出y2的批量修改excel模板
     * @Date 16:49 2025/1/11
     * @Param []
     **/
    @PostMapping("/export/update-y1")
    public void exportY1(HttpServletResponse response, @RequestBody @Validated OperationPageDTO pageDTO) {
        try {
            ClassPathResource resource = new ClassPathResource("import-template/y1-update-batch.xlsx");
            ExcelUtil.exportTemplateList(response, "y1-update-batch", resource, adjustService.exportY1(pageDTO));
        }
        catch (IOException e) {
            throw new BizException(ModelEngineBizCode.EXPORT_ERROR, e);
        }
    }

    /**
     * <AUTHOR>
     * @Description y2导入数据
     * @Date 9:23 2025/1/15
     * @Param [file, key]
     * @return com.pig4cloud.pig.common.core.util.R<java.util.List < com.pig4cloud.pig.yptt.entity.vo.ImportResultVO>>
     **/
    @PostMapping("/import/update-y1")
    public R<List<ImportResultVO>> updateY1(MultipartFile file, String key) {
        return R.ok(adjustService.updateY1New(file, key));
//        return R.ok(adjustService.updateY1(file, key));
    }

    /**
     * <AUTHOR>
     * @Description 查询导入进度
     * @Date 11:06 2025/1/15
     * @Param [key]
     * @return com.pig4cloud.pig.common.core.util.R<com.pig4cloud.pig.yptt.entity.vo.ProgressY1VO>
     **/
    @GetMapping("/import/query-progress-y1")
    public R<ProgressY1VO> queryProgressY1(String key) {
        return R.ok(adjustService.queryProgressY1(key));
    }

    /**
     * @return com.pig4cloud.pig.common.core.util.R<java.lang.Object>
     * <AUTHOR>
     * @Description 导出y4的批量修改excel模板
     * @Date 16:49 2025/1/11
     * @Param []
     **/
    @PostMapping("/export/update-y4")
    public void exportY4(HttpServletResponse response, @RequestBody @Validated OperationPageDTO pageDTO) {
        try {
            ClassPathResource resource = new ClassPathResource("import-template/y4-update-batch.xlsx");
            ExcelUtil.exportTemplateList(response, "y4-update-batch", resource, adjustService.exportY4(pageDTO));
        }
        catch (IOException e) {
            throw new BizException(ModelEngineBizCode.EXPORT_ERROR, e);
        }
    }

    /**
     * <AUTHOR>
     * @Description y4导入数据
     * @Date 9:23 2025/1/15
     * @Param [file, key]
     * @return com.pig4cloud.pig.common.core.util.R<java.util.List < com.pig4cloud.pig.yptt.entity.vo.ImportResultVO>>
     **/
    @PostMapping("/import/update-y4")
    public R<List<ImportResultVO>> updateY4(MultipartFile file, String key) {
        return R.ok(adjustService.updateY4(file, key));
    }

    /**
     * <AUTHOR>
     * @Description 查询导入进度
     * @Date 11:06 2025/1/15
     * @Param [key]
     * @return com.pig4cloud.pig.common.core.util.R<com.pig4cloud.pig.yptt.entity.vo.ProgressY1VO>
     **/
    @GetMapping("/import/query-progress-y4")
    public R<ProgressY4VO> queryProgressY4(String key) {
        return R.ok(adjustService.queryProgressY4(key));
    }


}