package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.lang.Assert;
import com.pig4cloud.pig.common.security.annotation.Inner;
import com.pig4cloud.pig.me.api.dto.operation.OperationUpdateDTO;
import com.pig4cloud.pig.yptt.config.ViewConfProperties;
import com.pig4cloud.pig.yptt.config.YpttPersonalizedApiProperties;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.service.Connector2codeService;
import com.pig4cloud.pig.yptt.service.transform.Y1Transformer;
import com.pig4cloud.pig.yptt.utils.MetaDataDTOWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/connector2code")
public class Connector2codeController {

	private final Connector2codeService connector2codeService;

	private final YpttPersonalizedApiProperties props;

	private final ViewConfProperties viewConfProperties;

	private final Y1Transformer y1Transformer;

	private final BasicMapper basicMapper;

	@Inner(value = false)
	@RequestMapping("/y3")
	public ApiRes connector2codeY3(@RequestBody OperationUpdateDTO o,
			@RequestParam("yptt-task-token") String ypttTaskToken) {
		Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
		return connector2codeService.y3Connector2code(o);
	}

	@Inner(value = false)
	@RequestMapping("/y1")
	public ApiRes connector2codeY1(@RequestBody OperationUpdateDTO o,
			@RequestParam("yptt-task-token") String ypttTaskToken) {
		CompletableFuture.runAsync(() -> {
			Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
			MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(o);
			y1Transformer.y1connector(wrapper);
			// 移除虚拟属性
			wrapper.removeValue("Currency");
			basicMapper.updateItemData(wrapper.toMap(), viewConfProperties.getSiteItem().getTableName());
		});
		return ApiRes.ok(Boolean.TRUE);
	}

	@Inner(value = false)
	@RequestMapping("/y2")
	public ApiRes connector2codeY2(@RequestBody OperationUpdateDTO o,
			@RequestParam("yptt-task-token") String ypttTaskToken) {
		CompletableFuture.runAsync(() -> {
			Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
			MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(o);
			y1Transformer.y2connector(wrapper);
			// 移除虚拟属性
			wrapper.removeValue("Currency");
			basicMapper.updateItemData(wrapper.toMap(), viewConfProperties.getPoItem().getTableName());
		});
		return ApiRes.ok(Boolean.TRUE);
	}

	@Inner(value = false)
	@RequestMapping("/y4")
	public ApiRes connector2codeY4(@RequestBody OperationUpdateDTO o,
			@RequestParam("yptt-task-token") String ypttTaskToken) {
		CompletableFuture.runAsync(() -> {
			Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
			MetaDataDTOWrapper wrapper = new MetaDataDTOWrapper(o);
			y1Transformer.y4connector(wrapper);
			// 移除虚拟属性
			wrapper.removeValue("Currency");
			basicMapper.updateItemData(wrapper.toMap(), viewConfProperties.getSubconPOItem().getTableName());
		});
		return ApiRes.ok(Boolean.TRUE);
	}

}
