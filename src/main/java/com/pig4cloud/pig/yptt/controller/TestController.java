package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.util.IdUtil;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.security.annotation.Inner;
import com.pig4cloud.pig.me.api.exception.ModelEngineBizCode;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.TestDataModule;
import com.pig4cloud.pig.yptt.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/11/02
 */
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
@PreAuthorize("@pms.hasRole('SYSTEM_ADMIN')")
public class TestController {

	private final static ThreadLocal<SimpleDateFormat> DATE_FORMAT_THREAD_LOCAL = ThreadLocal
		.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

	private final static String SHEET_NAME = "test-data";

	private final static List<String> SUBCON_LIST = Arrays.asList("YPTT-1", "TTT-2", "CV FAJAR INTI SENTOSA-3",
			"FBI-4");

	private final static List<String> MEMBER_LIST = Arrays.asList("Faturahman", "Juremi", "Febri alam nuari", "Wita");

	private final static ThreadLocal<List<String>> SITE_ID_LOCAL = ThreadLocal.withInitial(ArrayList::new);

	private final static ThreadLocal<List<String>> ITEM_CODE_LOCAL = ThreadLocal.withInitial(ArrayList::new);

	private final static ThreadLocal<List<String>> PO_CODE_LOCAL = ThreadLocal.withInitial(ArrayList::new);

	private final static ThreadLocal<List<String>> SUBCON_NAME_LOCAL = ThreadLocal.withInitial(ArrayList::new);

	private final static ThreadLocal<List<String>> SUBCON_NUMBER_LOCAL = ThreadLocal.withInitial(ArrayList::new);

	@GetMapping("/data-export")
	@Inner(value = false)
	public void testDataExport(HttpServletResponse response, Integer subconPoNum, Integer poNum, String projectCode,
			String cusContract) {
		List<TestDataModule> moduleList = new ArrayList<>();
		for (GlobalConstants.ViewConfEnum value : GlobalConstants.ViewConfEnum.values()) {
			List<String> exportFiled = value.getFiled();
			TestDataModule testDataModule = new TestDataModule();
			testDataModule.setSheetName(value.getModule());
			testDataModule.setHeadMap(exportFiled);
			List<List<Object>> dataList = new ArrayList<>();
			dataList.add(new ArrayList<>());
			dataList.add(new ArrayList<>());
			// 构建数据
			buildData(exportFiled, dataList, value.getModule(), subconPoNum, poNum, projectCode, cusContract);
			testDataModule.setDataList(dataList);
			moduleList.add(testDataModule);
		}
		try {
			ExcelUtil.exportNoModelList(response, moduleList);
		}
		catch (IOException e) {
			throw new BizException(ModelEngineBizCode.EXPORT_ERROR, e);
		}
		finally {
			remove();
		}
	}

	private void remove() {
		SITE_ID_LOCAL.remove();
		DATE_FORMAT_THREAD_LOCAL.remove();
		PO_CODE_LOCAL.remove();
		ITEM_CODE_LOCAL.remove();
		SUBCON_NAME_LOCAL.remove();
		SUBCON_NUMBER_LOCAL.remove();
	}

	private void buildData(List<String> exportFiled, List<List<Object>> dataList, String type, Integer subconPoNum,
			Integer poNum, String projectCode, String cusContract) {
		if (Objects.equals(type, GlobalConstants.Y1.NAME)) {
			for (int i = 0; i < poNum; i++) {
				List<Object> list = new ArrayList<>();
				int num = ((int) (Math.random() * 10));
				int price = ((int) (Math.random() * 1000000));
				for (String s : exportFiled) {
					switch (s) {
						case "Department":
							list.add("信亚");
							break;
						case "YPTT_Project_code":
							list.add(projectCode);
							break;
						case "YPTT_Project_name":
							list.add("项目-" + projectCode);
							break;
						case "Region":
							list.add("重庆");
							break;
						case "Area":
							list.add("中国");
							break;
						case "Site_ID":
							List<String> set = SITE_ID_LOCAL.get();
							String siteId = IdUtil.getSnowflakeNextIdStr() + ((int) (Math.random() * 10));
							set.add(siteId);
							SITE_ID_LOCAL.set(set);
							list.add(siteId);
							break;
						case "Site_Name":
							list.add("站点-" + SITE_ID_LOCAL.get().get(i));
							break;
						case "site_allocation_date":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(new Date()));
							break;
						case "Phase":
							list.add("Phase1");
							break;
						case "Type_of_service":
							list.add("业务类型" + ((int) (Math.random() * 10)));
							break;
						case "Site_Model":
							list.add("站点模型" + ((int) (Math.random() * 10)));
							break;
						case "Item_code":
							List<String> strings = ITEM_CODE_LOCAL.get();
							String itemCode = IdUtil.getSnowflakeNextIdStr() + ((int) (Math.random() * 10));
							strings.add(itemCode);
							ITEM_CODE_LOCAL.set(strings);
							list.add("条目-" + itemCode);
							break;
						case "BOQ_item":
							list.add("条目名称" + ITEM_CODE_LOCAL.get().get(i));
							break;
						case "quantity":
							list.add(num);
							break;
						case "Unit_price":
							list.add(price);
							break;
						case "Site_value":
							list.add(num * price);
							break;
						case "Remark":
							list.add("无");
							break;
						case "re_record":
							list.add("0");
							break;
						case "uniqueness_field":
							list.add(projectCode + "_" + "重庆" + "_" + SITE_ID_LOCAL.get().get(i) + "_Phase1_" + "条目-"
									+ ITEM_CODE_LOCAL.get().get(i));
							break;
						default:
							throw new IllegalArgumentException("Unexpected type encountered");
					}
				}
				dataList.add(list);
			}
		}
		else if (Objects.equals(type, GlobalConstants.Y2.NAME)) {
			for (int i = 0; i < poNum; i++) {
				List<Object> list = new ArrayList<>();
				int num = ((int) (Math.random() * 10));
				int price = ((int) (Math.random() * 10000));
				for (String s : exportFiled) {
					switch (s) {
						case "PO_Received_date":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(new Date()));
							break;
						case "PO_Number":
							List<String> set = PO_CODE_LOCAL.get();
							String poCode = IdUtil.getSnowflakeNextIdStr() + ((int) (Math.random() * 10));
							set.add(poCode);
							PO_CODE_LOCAL.set(set);
							list.add(poCode);
							break;
						case "Contract_number":
							list.add(cusContract);
							break;
						case "Custom_project_name":
							list.add("客户合同-" + projectCode);
							break;
						case "YPTT_Project_code":
							list.add(projectCode);
							break;
						case "Region":
							list.add("重庆");
							break;
						case "Phase":
							list.add("Phase1");
							break;
						case "Site_ID":
							list.add(SITE_ID_LOCAL.get().get(i));
							break;
						case "Site_Name":
							list.add("站点-" + SITE_ID_LOCAL.get().get(i));
							break;
						case "Item_code":
							list.add("条目-" + ITEM_CODE_LOCAL.get().get(i));
							break;
						case "BOQ_item":
							list.add("条目名称" + ITEM_CODE_LOCAL.get().get(i));
							break;
						case "quantity":
							list.add(num);
							break;
						case "Unit_price":
							list.add(price);
							break;
						case "PO_Value":
							list.add(num * price);
							break;
						case "PO_GAP":
							list.add("0");
							break;
						case "Pre_payment":
							list.add("5%");
							break;
						case "Milestone_1st":
							list.add("10%");
							break;
						case "Milestone_2nd":
							list.add("20%");
							break;
						case "Milestone_3rd":
							list.add("50%");
							break;
						case "Milestone_4th":
							list.add("20%");
							break;
						case "Remark":
							list.add("无");
							break;
						case "re_record":
							list.add("0");
							break;
						case "uniqueness_field":
							list.add(projectCode + "_" + "重庆" + "_" + SITE_ID_LOCAL.get().get(i) + "_Phase1_" + "条目-"
									+ ITEM_CODE_LOCAL.get().get(i));
							break;
						default:
							throw new IllegalArgumentException("Unexpected type encountered");
					}
				}
				dataList.add(list);
			}
		}
		else if (Objects.equals(type, GlobalConstants.Y3.NAME)) {
			for (int i = 0; i < poNum; i++) {
				List<Object> list = new ArrayList<>();
				for (String s : exportFiled) {
					switch (s) {
						case "YPTT_Project_code":
							list.add(projectCode);
							break;
						case "Region":
							list.add("重庆");
							break;
						case "Site_ID":
							list.add(SITE_ID_LOCAL.get().get(i));
							break;
						case "Phase":
							list.add("Phase1");
							break;
						case "Item_code":
							list.add("条目-" + ITEM_CODE_LOCAL.get().get(i));
							break;
						case "uniqueness_field":
							list.add(projectCode + "_" + "重庆" + "_" + SITE_ID_LOCAL.get().get(i) + "_Phase1_" + "条目-"
									+ ITEM_CODE_LOCAL.get().get(i));
							break;
						case "Site_belong_to":
							list.add(SUBCON_LIST.get((int) (Math.random() * 3)).split("-")[0]);
							break;
						case "Team_Leader_DT":
							list.add(MEMBER_LIST.get((int) (Math.random() * 3)));
							break;
						case "engineer_DTA_SPV":
							list.add(MEMBER_LIST.get((int) (Math.random() * 3)));
							break;
						case "PLO_PC_Others":
							list.add(MEMBER_LIST.get((int) (Math.random() * 3)));
							break;
						case "PIC_PC_PM":
							list.add(MEMBER_LIST.get((int) (Math.random() * 3)));
							break;
						case "Start_Working_date":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 1)));
							break;
						case "Completed_work_date":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 2)));
							break;
						case "air_CI_Report_submit":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 3)));
							break;
						case "Site_manager_Report":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 4)));
							break;
						case "E_ATP_Pass":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 5)));
							break;
						case "F_PAC_Pass":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 6)));
							break;
						case "G_FAC":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 7)));
							break;
						case "Remark":
							list.add("无");
							break;
						case "re_record":
							list.add("0");
							break;
						default:
							throw new IllegalArgumentException("Unexpected type encountered");
					}
				}
				dataList.add(list);
			}
		}
		else if (Objects.equals(type, GlobalConstants.Y4.NAME)) {
			for (int i = 0; i < subconPoNum; i++) {
				List<Object> list = new ArrayList<>();
				int num = ((int) (Math.random() * 10));
				int price = ((int) (Math.random() * 10000));
				for (String s : exportFiled) {
					switch (s) {
						case "YPTT_Project_code":
							list.add(projectCode);
							break;
						case "Region":
							list.add("重庆");
							break;
						case "Site_ID":
							list.add(SITE_ID_LOCAL.get().get(Math.min(i, SITE_ID_LOCAL.get().size() - 1)));
							break;
						case "Phase":
							list.add("Phase1");
							break;
						case "Item_code":
							list.add("条目-" + ITEM_CODE_LOCAL.get().get(Math.min(i, ITEM_CODE_LOCAL.get().size() - 1)));
							break;
						case "uniqueness_field":
							list.add(projectCode + "_" + "重庆" + "_"
									+ SITE_ID_LOCAL.get().get(Math.min(i, SITE_ID_LOCAL.get().size() - 1)) + "_Phase1_"
									+ "条目-" + ITEM_CODE_LOCAL.get().get(Math.min(i, ITEM_CODE_LOCAL.get().size() - 1)));
							break;
						case "Subcon_name":
							String name = SUBCON_LIST.get((int) (Math.random() * 3)).split("-")[0];
							SUBCON_NAME_LOCAL.get().add(name);
							list.add(name);
							break;
						case "Subcon_PO_number":
							String code = IdUtil.getSnowflakeNextIdStr();
							SUBCON_NUMBER_LOCAL.get().add(code);
							list.add(code);
							break;
						case "release_date":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(new Date()));
							break;
						case "Site_name":
							list.add("站点-" + SITE_ID_LOCAL.get().get(Math.min(i, SITE_ID_LOCAL.get().size() - 1)));
							break;
						case "BOQ_item":
							list.add("条目名称" + ITEM_CODE_LOCAL.get().get(Math.min(i, ITEM_CODE_LOCAL.get().size() - 1)));
							break;
						case "Quantity":
							list.add(num);
							break;
						case "Unit_price":
							list.add(price);
							break;
						case "Subcon_PO_amount":
							list.add(num * price);
							break;
						case "Milestone_1st":
							list.add("60%");
							break;
						case "Milestone_2nd":
							list.add("20%");
							break;
						case "Milestone_3rd":
							list.add("10%");
							break;
						case "Milestone_4th":
							list.add("10%");
							break;
						case "additional_cost":
							list.add("0");
							break;
						case "Remark":
							list.add("无");
							break;
						case "re_record":
							list.add("0");
							break;
						default:
							throw new IllegalArgumentException("Unexpected type encountered");
					}
				}
				dataList.add(list);
			}
		}
		else if (Objects.equals(type, GlobalConstants.Y8.NAME)) {
			for (int i = 0; i < SUBCON_NUMBER_LOCAL.get().size(); i++) {
				List<Object> list = new ArrayList<>();
				int price1 = ((int) (Math.random() * 10000));
				int price2 = ((int) (Math.random() * 10000));
				int price3 = ((int) (Math.random() * 10000));
				int price4 = ((int) (Math.random() * 10000));
				String name = SUBCON_NAME_LOCAL.get().get(i);
				String number = SUBCON_NUMBER_LOCAL.get().get(i);
				for (String s : exportFiled) {
					switch (s) {
						case "Subcon_name":
							list.add(name);
							break;
						case "Subcon_PO_number":
							list.add(number);
							break;
						case "Site_ID":
							list.add(SITE_ID_LOCAL.get().get(Math.min(i, SITE_ID_LOCAL.get().size() - 1)));
							break;
						case "Item_code":
							list.add("条目-" + ITEM_CODE_LOCAL.get().get(Math.min(i, ITEM_CODE_LOCAL.get().size() - 1)));
							break;
						case "uniqueness_field":
							list.add(name + "_" + number + "_"
									+ SITE_ID_LOCAL.get().get(Math.min(i, SITE_ID_LOCAL.get().size() - 1)) + "_" + "条目-"
									+ ITEM_CODE_LOCAL.get().get(Math.min(i, ITEM_CODE_LOCAL.get().size() - 1)));
							break;
						case "Payment_time_1st":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 1)));
							break;
						case "payment_amount_1st":
							list.add(price1);
							break;
						case "payment_number_1st":
							list.add(IdUtil.getSnowflakeNextIdStr());
							break;
						case "Payment_time_2st":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 2)));
							break;
						case "payment_amount_2st":
							list.add(price2);
							break;
						case "payment_number_2st":
							list.add(IdUtil.getSnowflakeNextIdStr());
							break;
						case "Payment_time_3st":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 3)));
							break;
						case "payment_number_3st":
							list.add(IdUtil.getSnowflakeNextIdStr());
							break;
						case "payment_amount_3st":
							list.add(price3);
							break;
						case "Payment_time_4st":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 4)));
							break;
						case "payment_amount_4st":
							list.add(price4);
							break;
						case "payment_number_4st":
							list.add(IdUtil.getSnowflakeNextIdStr());
							break;
						case "Remark":
							list.add("无");
							break;
						case "Totally_payment":
							list.add(price1 + price2 + price3 + price4);
							break;
						case "re_record":
							list.add("0");
							break;
						default:
							throw new IllegalArgumentException("Unexpected type encountered");
					}
				}
				dataList.add(list);
			}
		}
		else if (Objects.equals(type, GlobalConstants.Y9.NAME)) {
			int i = 0;
			for (String po : PO_CODE_LOCAL.get()) {
				List<Object> list = new ArrayList<>();
				int price1 = ((int) (Math.random() * 10000));
				int price2 = ((int) (Math.random() * 10000));
				int price3 = ((int) (Math.random() * 10000));
				int price4 = ((int) (Math.random() * 10000));
				for (String s : exportFiled) {
					switch (s) {
						case "PO_number":
							list.add(po);
							break;
						case "Contract_number":
							list.add(cusContract);
							break;
						case "Phase":
							list.add("Phase1");
							break;
						case "Site_ID":
							list.add(SITE_ID_LOCAL.get().get(Math.min(i, SITE_ID_LOCAL.get().size() - 1)));
							break;
						case "Item_code":
							list.add("条目-" + ITEM_CODE_LOCAL.get().get(Math.min(i, ITEM_CODE_LOCAL.get().size() - 1)));
							break;
						case "uniqueness_field":
							list.add(po + "_" + cusContract + "_Phase1_"
									+ SITE_ID_LOCAL.get().get(Math.min(i, SITE_ID_LOCAL.get().size() - 1)) + "_条目-"
									+ ITEM_CODE_LOCAL.get().get(Math.min(i, ITEM_CODE_LOCAL.get().size() - 1)));
							break;
						case "Invoice_date_1st":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(new Date()));
							break;
						case "Invoice_number_1st":
							list.add(IdUtil.getSnowflakeNextIdStr());
							break;
						case "Invoice_Amount_1st":
							list.add(price1);
							break;
						case "Invoice_remark_1st":
							list.add("无");
							break;
						case "Invoice_date_2nd":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 1)));
							break;
						case "Invoice_number_2nd":
							list.add(IdUtil.getSnowflakeNextIdStr());
							break;
						case "Invoice_Amount_2nd":
							list.add(price2);
							break;
						case "Invoice_remark_2nd":
							list.add("无");
							break;
						case "Invoice_date_3rd":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 2)));
							break;
						case "Invoice_number_3rd":
							list.add(IdUtil.getSnowflakeNextIdStr());
							break;
						case "Invoice_Amount_3rd":
							list.add(price3);
							break;
						case "Invoice_remark_3rd":
							list.add("无");
							break;
						case "Invoice_date_4st":
							list.add(DATE_FORMAT_THREAD_LOCAL.get().format(addMonths(new Date(), 3)));
							break;
						case "Invoice_number_4st":
							list.add(IdUtil.getSnowflakeNextIdStr());
							break;
						case "Invoice_Amount_4st":
							list.add(price4);
							break;
						case "Invoice_remark_4th":
							list.add("无");
							break;
						case "Remark":
							list.add("无");
							break;
						case "Invoice_amount":
							list.add(price1 + price2 + price3 + price4);
							break;
						case "re_record":
							list.add("0");
							break;
						default:
							throw new IllegalArgumentException("Unexpected type encountered");
					}
				}
				dataList.add(list);
				i++;
			}
		}
	}

	public Date addMonths(Date date, int months) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, months);
		return calendar.getTime();
	}

}
