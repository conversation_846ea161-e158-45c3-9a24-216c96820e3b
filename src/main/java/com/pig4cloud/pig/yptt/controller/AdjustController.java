package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.annotation.Inner;
import com.pig4cloud.pig.me.api.dto.operation.OperationUpdateDTO;
import com.pig4cloud.pig.yptt.config.YpttPersonalizedApiProperties;
import com.pig4cloud.pig.yptt.service.AdjustService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName AdjustController
 * @Description TODO
 * @date 2025/1/11 16:43
 * @Version 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/adjust")
public class AdjustController {
    private final AdjustService adjustService;
    private final YpttPersonalizedApiProperties props;

    /**
     * @return com.pig4cloud.pig.common.core.util.R<java.lang.Object>
     * <AUTHOR>
     * @Description 金额变化 y1
     * @Date 16:49 2025/1/11
     * @Param []
     **/
    @PostMapping("amount-siteItem")
    @Inner(value = false)
    public ApiRes adjustAmountSite(@RequestBody OperationUpdateDTO obj,
                                      @RequestParam("yptt-task-token") String ypttTaskToken) {
        Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
        return adjustService.adjustAmountSite(obj);
    }

    /**
     * @return com.pig4cloud.pig.common.core.util.R<java.lang.Object>
     * <AUTHOR>
     * @Description 金额变化 y2
     * @Date 16:49 2025/1/11
     * @Param []
     **/
    @PostMapping("amount-poItem")
    @Inner(value = false)
    public ApiRes adjustAmountPo(@RequestBody OperationUpdateDTO obj,
                                    @RequestParam("yptt-task-token") String ypttTaskToken) {
        Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
        return adjustService.adjustAmountPo(obj);
    }

    /**
     * @return com.pig4cloud.pig.common.core.util.R<java.lang.Object>
     * <AUTHOR>
     * @Description 金额变化 y4
     * @Date 16:49 2025/1/11
     * @Param []
     **/
    @PostMapping("amount-subPoItem")
    @Inner(value = false)
    public ApiRes adjustAmountSubPo(@RequestBody OperationUpdateDTO obj,
                                 @RequestParam("yptt-task-token") String ypttTaskToken) {
        Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
        return adjustService.adjustAmountSubPo(obj);
    }

}