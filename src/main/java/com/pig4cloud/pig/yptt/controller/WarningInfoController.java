package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.annotation.Inner;
import com.pig4cloud.pig.me.api.exception.ModelEngineBizCode;
import com.pig4cloud.pig.yptt.config.YpttPersonalizedApiProperties;
import com.pig4cloud.pig.yptt.entity.WarningMessage;
import com.pig4cloud.pig.yptt.entity.vo.WarningStatisticsVO;
import com.pig4cloud.pig.yptt.service.WarningInfoService;
import com.pig4cloud.pig.yptt.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/09/19
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/warning")
public class WarningInfoController {

	private final WarningInfoService warningInfoService;

	private final YpttPersonalizedApiProperties props;

	/**
	 * 警告信息更新
	 * @param ypttTaskToken 标识
	 * @return Boolean
	 */
	@GetMapping("/update")
	@Inner(value = false)
	public R<Boolean> updateStatus(@RequestParam(value = "listCode", required = false) List<String> codes,
			@RequestParam("yptt-task-token") String ypttTaskToken) {
		Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
		CompletableFuture.runAsync(() -> warningInfoService.updateByProjectCode(codes));
		return R.ok(Boolean.TRUE);
	}

	/**
	 * 警告信息导出
	 * @return Boolean
	 */
	@PostMapping("/export")
	public void export(HttpServletResponse response,
			@RequestParam(value = "projectCode", required = false) String projectCode,
			@RequestParam(value = "projectName", required = false) String projectName,
			@RequestParam(value = "uniquenessField", required = false) String uniquenessField,
			@RequestParam(value = "warnType", required = false) List<String> warnType,
			@RequestParam(value = "startTime",
					required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
			@RequestParam(value = "endTime",
					required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
		List<Map<String, Object>> maps = warningInfoService.listMap(projectCode, projectName, uniquenessField, warnType,
				startTime, endTime);
		if (CollUtil.isEmpty(maps)) {
			return;
		}
		try {
			ClassPathResource resource = new ClassPathResource("import-template/warn-msg.xlsx");
			ExcelUtil.exportTemplateList(response, "warn_msg", resource, maps);
		}
		catch (IOException e) {
			throw new BizException(ModelEngineBizCode.EXPORT_ERROR, e);
		}
	}

	/**
	 * 分页查询警告信息
	 * @param size 大小
	 * @param cur 当前页
	 * @param projectCode 项目id
	 * @param projectName 项目名称
	 * @param warnType 警告类型
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return IPage
	 */
	@GetMapping("/page")
	public R<IPage<WarningMessage>> page(Integer size, Integer cur,
			@RequestParam(value = "projectCode", required = false) String projectCode,
			@RequestParam(value = "projectName", required = false) String projectName,
			@RequestParam(value = "uniquenessField", required = false) String uniquenessField,
			@RequestParam(value = "warnType", required = false) List<String> warnType,
			@RequestParam(value = "startTime",
					required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
			@RequestParam(value = "endTime",
					required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
		return R.ok(warningInfoService.page(size, cur, projectCode, projectName, uniquenessField, warnType, startTime,
				endTime));
	}

	@GetMapping("/statistics")
	public R<List<WarningStatisticsVO>> statistics(
			@RequestParam(value = "projectCode", required = false) String projectCode,
			@RequestParam(value = "projectName", required = false) String projectName,
			@RequestParam(value = "uniquenessField", required = false) String uniquenessField,
			@RequestParam(value = "warnType", required = false) List<String> warnType,
			@RequestParam(value = "startTime",
					required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
			@RequestParam(value = "endTime",
					required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
		return R
			.ok(warningInfoService.statistics(projectCode, projectName, uniquenessField, warnType, startTime, endTime));
	}

}
