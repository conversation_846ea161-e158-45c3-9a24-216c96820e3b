package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.lang.Assert;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.annotation.Inner;
import com.pig4cloud.pig.yptt.config.YpttPersonalizedApiProperties;
import com.pig4cloud.pig.yptt.entity.dto.StandingBookUpdateTaskResultDTO;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.standingbook.StandingBookUpdateTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Slf4j
@RestController
@RequestMapping("/task")
@RequiredArgsConstructor
public class TaskController {

	private final YpttPersonalizedApiProperties props;

	private final DataMangeService dataMangeService;

	private final StandingBookUpdateTaskService standingBookUpdateTaskService;

	@GetMapping(value = { "/update-project-standing-book", "update-standing-books" })
	@Inner(value = false)
	public R<StandingBookUpdateTaskResultDTO> updateProjectStandingBook(
			@RequestParam("yptt-task-token") String ypttTaskToken) {
		Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
		StandingBookUpdateTaskResultDTO updateResult = standingBookUpdateTaskService.update();
		return updateResult.isAllSuccessful() ? R.ok(updateResult) : R.failed(updateResult, "Partial failure");
	}

	@GetMapping("/check-import-task")
	@Inner(value = false)
	public void checkImportTask(@RequestParam("yptt-task-token") String ypttTaskToken) {
		Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
		dataMangeService.checkImportTask();
	}

	@GetMapping("/income-correction-task")
	@Inner(value = false)
	public R<Boolean> incomeCorrectionTask(@RequestParam("yptt-task-token") String ypttTaskToken) {
		Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
		dataMangeService.incomeCorrection();
		return R.ok(Boolean.TRUE);
	}

	@GetMapping("/update-site-state")
	@Inner(value = false)
	public R<Boolean> updateSiteState(@RequestParam("yptt-task-token") String ypttTaskToken) {
		Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
		dataMangeService.updateSiteState();
		return R.ok(Boolean.TRUE);
	}

}
