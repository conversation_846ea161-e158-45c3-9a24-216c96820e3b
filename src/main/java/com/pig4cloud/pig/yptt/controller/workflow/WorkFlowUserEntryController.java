package com.pig4cloud.pig.yptt.controller.workflow;

import com.pig4cloud.pig.yptt.controller.WorkFlowApiRes;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlowDTO;
import com.pig4cloud.pig.yptt.service.workflow.WorkFlowServiceCommon;
import com.pig4cloud.pig.yptt.service.workflow.WorkFlowUserEntryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: WorkFlowUserEntryController
 * @Description: 用户入职信息
 * @Author: lijianpan
 * @CreateTime: 2025-06-19  19:30
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/workflow-user-entry")
public class WorkFlowUserEntryController {
    private final WorkFlowUserEntryService userEntryService;

    private final WorkFlowServiceCommon workFlowServiceCommon;
    /**
     * @description: 新增用户
     * @author: lijianpan
     **/
    @PostMapping("/add")
    public WorkFlowApiRes add(@RequestBody WorkFlowDTO request){
        try {
            return userEntryService.add(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            e.printStackTrace();
            return WorkFlowApiRes.error(e.getMessage());
        }
    }
}