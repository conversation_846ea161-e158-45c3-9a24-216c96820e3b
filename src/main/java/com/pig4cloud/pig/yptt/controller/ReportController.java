package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.io.resource.ClassPathResource;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.me.api.dto.operation.OperationPageDTO;
import com.pig4cloud.pig.me.api.exception.ModelEngineBizCode;
import com.pig4cloud.pig.yptt.entity.dto.StatisticsUserOperateDTO;
import com.pig4cloud.pig.yptt.entity.vo.ProgressY1VO;
import com.pig4cloud.pig.yptt.entity.vo.StatisticsUserOperateVO;
import com.pig4cloud.pig.yptt.service.ReportService;
import com.pig4cloud.pig.yptt.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ReportController
 * @Description
 * @date 2025/2/24 13:36
 * @Version 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/report")
public class ReportController {
    private final ReportService reportService;

    /**
     * @return com.pig4cloud.pig.common.core.util.R<com.pig4cloud.pig.yptt.entity.vo.ProgressY1VO>
     * <AUTHOR>
     * @Description 统计用户Y1-Y9的操作数据
     * @Date 13:44 2025/2/24
     * @Param [key]
     **/
    @PostMapping("/statistics/user-operate")
    public R<StatisticsUserOperateVO> statisticsUserOperate(@RequestBody @Validated StatisticsUserOperateDTO statisticsUserOperateDTO) {
        return R.ok(reportService.statisticsUserOperate(statisticsUserOperateDTO));
    }

    /**
     * @return com.pig4cloud.pig.common.core.util.R<?>
     * <AUTHOR>
     * @Description 查询用户信息
     * @Date 10:02 2025/2/28
     * @Param []
     **/
    @GetMapping("/statistics/users")
    public R<?> getUserInfo(@RequestParam(required = false) String name,
                            @RequestParam(defaultValue = "1") Integer current,
                            @RequestParam(defaultValue = "10") Integer size) {
        return R.ok(reportService.getUserInfo(name, current, size));
    }

    /**
     * <AUTHOR>
     * @Description 导出用户操作
     * @Date 13:28 2025/2/28
     * @Param [response, statisticsUserOperateDTO]
     * @return void
     **/
    @PostMapping("/export/user-operate")
    public void exportY1(HttpServletResponse response, @RequestBody @Validated StatisticsUserOperateDTO statisticsUserOperateDTO) {
        try {
            ClassPathResource resource = new ClassPathResource("import-template/user-operate.xlsx");
            ExcelUtil.exportTemplateList(response, "user-operate", resource, reportService.exportUserOperate(statisticsUserOperateDTO));
        }
        catch (IOException e) {
            throw new BizException(ModelEngineBizCode.EXPORT_ERROR, e);
        }
    }

    /**
     * <AUTHOR>
     * @Description 获取项目数据
     * @Date 13:30 2025/2/28
     * @Param [name, current, size]
     * @return com.pig4cloud.pig.common.core.util.R<?>
     **/
    @GetMapping("/get/project")
    public R<?> getProject(@RequestParam(required = false) String name,
                           @RequestParam(defaultValue = "1") Integer current,
                           @RequestParam(defaultValue = "10") Integer size){
        return R.ok(reportService.getProject(name, current, size));
    }
}