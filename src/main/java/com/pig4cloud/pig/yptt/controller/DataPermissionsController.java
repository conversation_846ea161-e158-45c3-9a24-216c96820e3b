package com.pig4cloud.pig.yptt.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pig4cloud.pig.me.api.dto.meta.data.MetaDataValueDTO;
import com.pig4cloud.pig.me.api.dto.modelOperation.BatchOperationParams;
import com.pig4cloud.pig.me.api.dto.operation.OperationDeleteDTO;
import com.pig4cloud.pig.me.api.dto.operation.OperationInsertDTO;
import com.pig4cloud.pig.me.api.dto.operation.OperationUpdateDTO;
import com.pig4cloud.pig.yptt.config.OperationLogger;
import com.pig4cloud.pig.yptt.service.DataPermissionsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/07
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/data-permissions")
public class DataPermissionsController {

	private final DataPermissionsService dataPermissionsService;

	@PostMapping("/member/save")
	@OperationLogger
	public ApiRes saveMember(@RequestBody JSONObject o) {
//		OperationInsertDTO operationInsertDTO = new OperationInsertDTO();
//		operationInsertDTO.setDataId(o.getLong("dataId"));
//		Map<String, Object> map = jsonObjectToMap(o.getJSONObject("data"));
//		List<MetaDataValueDTO> metaDataValueDTOS = new ArrayList<>();
//
//		for (String key : map.keySet()) {
//			MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
//			metaDataValueDTO.setName(key);
//			metaDataValueDTO.setValue(map.get(key));
//			metaDataValueDTOS.add(metaDataValueDTO);
//		}
//
//		operationInsertDTO.setData(metaDataValueDTOS);
		System.out.println("========================json saveMember"+o);
		return ApiRes.ok(dataPermissionsService.saveMember(o));
	}

	@PostMapping("/member/save-check")
	@OperationLogger
	public ApiRes saveMemberCheck(@RequestBody JSONObject o) {
//		OperationInsertDTO o
		OperationInsertDTO operationInsertDTO = new OperationInsertDTO();
		System.out.println("========================json save-check"+o);
		return dataPermissionsService.saveMemberCheck(operationInsertDTO);
	}

	@PostMapping("/member/update")
	@OperationLogger
	public ApiRes updateMember(@RequestBody BatchOperationParams.Payload o) {
//		OperationUpdateDTO operationUpdateDTO = new OperationUpdateDTO();
////		operationUpdateDTO.setDataId(o.getLong("dataId"));
//		operationUpdateDTO.setDataId(o.getDataId());
////		Map<String, Object> map = jsonObjectToMap(o.getJSONObject("data"));
//		Map<String, Object> map = o.getData();
//		List<MetaDataValueDTO> metaDataValueDTOS = new ArrayList<>();
//
//		for (String key : map.keySet()) {
//			MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
//			metaDataValueDTO.setName(key);
//			metaDataValueDTO.setValue(map.get(key));
//			metaDataValueDTOS.add(metaDataValueDTO);
//		}
//
//		operationUpdateDTO.setData(metaDataValueDTOS);
		System.out.println("========================json updateMember"+o);
		return ApiRes.ok(dataPermissionsService.updateMember(o));
	}

	@PostMapping("/member/update-check")
	@OperationLogger
	public ApiRes updateCheck(@RequestBody JSONObject o) {
        OperationUpdateDTO operationUpdateDTO = new OperationUpdateDTO();
        System.out.println("========================json updateCheck"+o);
        return dataPermissionsService.updatePerCheck(operationUpdateDTO);
	}

	@PostMapping("/member/del")
	@OperationLogger
	public ApiRes delMember(@RequestBody  BatchOperationParams.Payload o) {
        System.out.println("========================json delMember" + o);
		return ApiRes.ok(dataPermissionsService.delMember(o));
	}


	public static Map<String, Object> jsonObjectToMap(JSONObject jsonObject) {
		return (Map<String, Object>) JSONObject.toJavaObject(jsonObject, Map.class);
	}

//	public static void main(String[] args) {
//		JSONObject o = JSONObject.parseObject("{\"data\":{\"Project_Member\":{\"extValue\":\"[\\\"Yanny\\\"]\",\"value\":\"[\\\"1902651317909524481\\\"]\"},\"Project_Role\":{\"extValue\":\"[\\\"系统管理员\\\"]\",\"value\":\"[\\\"1694550407313264642, 4444\\\"]\"},\"country\":{\"value\":\"Malaysia\"}},\"dataId\":\"1907369651918393344\",\"perm\":{\"delete\":1,\"query\":1,\"update\":1},\"model\":\"YPTT_Project_Member\",\"audits\":{\"createBy\":\"1902651317909524481\",\"createByExtValue\":\"Yanny\",\"createTime\":\"2025-04-02 17:48:57\",\"currProcInstId\":\"-1\",\"updateBy\":\"1875102831650467841\",\"updateByExtValue\":\"谢坪\",\"updateTime\":\"2025-05-08 18:17:16\"}}");
//
//		OperationUpdateDTO operationUpdateDTO = new OperationUpdateDTO();
//		operationUpdateDTO.setDataId(o.getLong("dataId"));
//		Map<String, Object> map = jsonObjectToMap(o.getJSONObject("data"));
//		List<MetaDataValueDTO> metaDataValueDTOS = new ArrayList<>();
//
//		for (String key : map.keySet()) {
//			MetaDataValueDTO metaDataValueDTO = new MetaDataValueDTO();
//			metaDataValueDTO.setName(key);
//			metaDataValueDTO.setValue(map.get(key));
//			metaDataValueDTOS.add(metaDataValueDTO);
//		}
//
//		operationUpdateDTO.setData(metaDataValueDTOS);
//
//
//
//		System.out.println("dto.getDataId()"+operationUpdateDTO.getDataId());
//
//		JSONObject project_member = (JSONObject)operationUpdateDTO.getValue("Project_Member");
//		long userId = Long.parseLong((String) project_member.getJSONArray("value").get(0));
//		System.out.println("operationUpdateDTO.getValue(\"Project_Member\")"+userId);
//
//		JSONObject Project_Role = (JSONObject)operationUpdateDTO.getValue("Project_Role");
//		JSONArray value = Project_Role.getJSONArray("value");
//		String roleIdStr = null;
//		for (Object o1 : value) {
//			roleIdStr = "," + o1.toString();
//		}
//		String projectRole = roleIdStr.replaceFirst(",", "");
//		System.out.println();
////		System.out.println(userId);
//
//
//	}
}
