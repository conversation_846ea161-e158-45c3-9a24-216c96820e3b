package com.pig4cloud.pig.yptt.controller.fs;

import cn.hutool.core.lang.Assert;
import com.pig4cloud.pig.common.security.annotation.Inner;
import com.pig4cloud.pig.me.api.dto.modelOperation.BatchOperationParams;
import com.pig4cloud.pig.me.api.vo.modelOperation.ModelDataVO;
import com.pig4cloud.pig.yptt.config.YpttPersonalizedApiProperties;
import com.pig4cloud.pig.yptt.controller.ApiRes;
import com.pig4cloud.pig.yptt.service.fs.PaymentApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * @ClassName: paymentApplicationController
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-07-29  14:21
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("fs/payment-application")
public class PaymentApplicationController {
    private final YpttPersonalizedApiProperties props;

    private final PaymentApplicationService paymentApplicationService;

    /**
     * @description: 完成付款并修改状态(新增后)
     * @author: lijianpan
     **/
    @PostMapping("payment-end-by-after-add")
    @Inner(value = false)
    public ApiRes paymentEndByAfterAdd(@RequestBody BatchOperationParams.Payload params, @RequestParam("yptt-task-token") String ypttTaskToken) {
        try {
            Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
            log.info("completePaymentAndModifyStatus params:{}", params);
            return paymentApplicationService.paymentEndByMainDataId(params.getMainDataId(), params.getDataId(),new BigDecimal((params.getData().get("payment_money")).toString()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return ApiRes.failed(e.getMessage());
        }
    }

    /**
     * @description: 完成付款并修改状态(删除后)
     * @author: lijianpan
     **/
    @PostMapping("payment-end-by-after-del")
    @Inner(value = false)
    public ApiRes paymentEndByAfterDel(@RequestBody BatchOperationParams.Payload params, @RequestParam("yptt-task-token") String ypttTaskToken) {
        try {
            Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
            log.info("completePaymentAndModifyStatus params:{}", params);
            return paymentApplicationService.paymentEndByMainDataId(params.getMainDataId(), params.getDataId(), new BigDecimal(0));
        } catch (Exception e) {
            log.error(e.getMessage());
            return ApiRes.failed(e.getMessage());
        }
    }

    /**
     * @description: 完成付款并修改状态(更新后)
     * @author: lijianpan
     **/
    @PostMapping("payment-end-by-after-update")
    @Inner(value = false)
    public ApiRes paymentEndByAfterUpdate(@RequestBody BatchOperationParams.Payload params, @RequestParam("yptt-task-token") String ypttTaskToken) {
        try {
            Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
            log.info("completePaymentAndModifyStatus params:{}", params);
            return paymentApplicationService.paymentEndByMainDataId(params.getMainDataId(), params.getDataId(),new BigDecimal((params.getData().get("payment_money")).toString()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return ApiRes.failed(e.getMessage());
        }
    }
}