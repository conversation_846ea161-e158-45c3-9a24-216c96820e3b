package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiRes implements Serializable {

	private Boolean success;

	private Object msg;

	public static ApiRes ok(Object msg) {
		return new ApiRes(Boolean.TRUE, Objects.nonNull(msg) ? msg.toString() : StrUtil.EMPTY);
	}

	public static ApiRes failed(Object msg) {
		return new ApiRes(Boolean.FALSE, Objects.nonNull(msg) ? msg.toString() : StrUtil.EMPTY);
	}

}
