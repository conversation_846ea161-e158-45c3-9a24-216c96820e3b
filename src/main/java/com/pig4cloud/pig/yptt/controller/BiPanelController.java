package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.ResourceUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.service.PigUser;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import com.pig4cloud.pig.me.api.exception.ModelEngineBizCode;
import com.pig4cloud.pig.yptt.bizcode.YpttBizCode;
import com.pig4cloud.pig.yptt.constants.GlobalConstants;
import com.pig4cloud.pig.yptt.entity.vo.IncomeAndExpenditureVO;
import com.pig4cloud.pig.yptt.entity.vo.ReportDataFiledVO;
import com.pig4cloud.pig.yptt.entity.vo.SiteItemStatisticsVO;
import com.pig4cloud.pig.yptt.entity.vo.TotalAmountVO;
import com.pig4cloud.pig.yptt.mapper.BasicMapper;
import com.pig4cloud.pig.yptt.service.BiPanelService;
import com.pig4cloud.pig.yptt.service.OptimizeBiPanelService;
import com.pig4cloud.pig.yptt.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/08/31
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/Bi-panel")
public class BiPanelController {

    private final BiPanelService biPanelService;

    private final BasicMapper basicMapper;

    private final OptimizeBiPanelService optimizeBiPanelService;

    private final RedisTemplate<String, Object> redisTemplate;

    private String redisKey = "BI::3::queryDataWay";
    private String redisKeyExport = "BI::3::exportDataWay";


    /**
     * 收支统计
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param projectId 项目id
     * @param type      类型
     * @return List<IncomeAndExpenditureVO>
     */
    @GetMapping("/income-expenditure-status")
    public R<List<IncomeAndExpenditureVO>> incomeExpenditureStatus(
            @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
            @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
            @RequestParam(value = "projectId") Long projectId, @RequestParam(value = "type") String type,
            @RequestParam(value = "projectCycle") Integer projectCycle) {

        if (!biPanelService.authorityCheck(GlobalConstants.BiPanel.B2, projectId)) {
            log.info("[{}]无权限访问BI-2面板信息",
                    Objects.nonNull(SecurityUtils.getUser()) ? SecurityUtils.getUser().getName() : null);
            return R.ok(Collections.emptyList());
        }
        List<IncomeAndExpenditureVO> voList = biPanelService.incomeExpenditureStatus(startTime, endTime, projectId,
                type, projectCycle);
        if (Objects.equals(0, projectCycle) && CollUtil.isNotEmpty(voList)) {
            return R.ok(projectCycleBuild(voList));
        }
        return R.ok(voList);
    }

    private List<IncomeAndExpenditureVO> projectCycleBuild(List<IncomeAndExpenditureVO> voList) {
        List<IncomeAndExpenditureVO> res = new ArrayList<>();
        int size = voList.size();
        IncomeAndExpenditureVO vo = new IncomeAndExpenditureVO();
        IncomeAndExpenditureVO start = voList.get(0);
        IncomeAndExpenditureVO end = voList.get(size - 1);
        vo.setDateLine(start.getDateLine() + " ~ " + end.getDateLine());
        vo.setProjectName(start.getProjectName());
        BigDecimal siteItemValueTotal = new BigDecimal(0);
        BigDecimal poItemValueTotal = new BigDecimal(0);
        BigDecimal subconTotalAmountPoItem = new BigDecimal(0);
        BigDecimal totalSettableAmount = new BigDecimal(0);
        BigDecimal totalOutputValue = new BigDecimal(0);
        BigDecimal subconTotalAmountSettled = new BigDecimal(0);
        BigDecimal subconTotalAmountPaid = new BigDecimal(0);
        BigDecimal totalInvoiceAmount = new BigDecimal(0);
        for (IncomeAndExpenditureVO expenditureVO : voList) {
            siteItemValueTotal = siteItemValueTotal.add(expenditureVO.getSiteItemValueTotal());
            poItemValueTotal = poItemValueTotal.add(expenditureVO.getPoItemValueTotal());
            subconTotalAmountPoItem = subconTotalAmountPoItem.add(expenditureVO.getSubconTotalAmountPoItem());
            totalSettableAmount = totalSettableAmount.add(expenditureVO.getTotalSettableAmount());
            totalOutputValue = totalOutputValue.add(expenditureVO.getTotalOutputValue());
            subconTotalAmountSettled = subconTotalAmountSettled.add(expenditureVO.getSubconTotalAmountSettled());
            subconTotalAmountPaid = subconTotalAmountPaid.add(expenditureVO.getSubconTotalAmountPaid());
            totalInvoiceAmount = totalInvoiceAmount.add(expenditureVO.getTotalInvoiceAmount());
        }
        vo.setSiteItemValueTotal(siteItemValueTotal);
        vo.setPoItemValueTotal(poItemValueTotal);
        vo.setSubconTotalAmountPoItem(subconTotalAmountPoItem);
        vo.setTotalOutputValue(totalOutputValue);
        vo.setTotalInvoiceAmount(totalInvoiceAmount);
        vo.setTotalSettableAmount(totalSettableAmount);
        vo.setSubconTotalAmountPaid(subconTotalAmountPaid);
        vo.setSubconTotalAmountSettled(subconTotalAmountSettled);
        res.add(vo);
        return res;
    }

    /**
     * 收支统计导出
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param projectId 项目id
     * @param type      类型
     * @return List<IncomeAndExpenditureVO>
     */
    @PostMapping("/income-expenditure-export")
    public R<Object> incomeExpenditureStatusExport(HttpServletResponse response,
                                                   @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                                   @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                                   @RequestParam(value = "projectId", required = false) Long projectId,
                                                   @RequestParam(value = "type", required = false, defaultValue = "year") String type,
                                                   @RequestParam(value = "projectCycle", required = false, defaultValue = "0") Integer projectCycle) {
        List<IncomeAndExpenditureVO> dataList = new ArrayList<>();
        PigUser pigUser = SecurityUtils.getUser();
        List<Long> roles = SecurityUtils.getRoles();
        if (Objects.isNull(projectId)) {
            List<Long> ids = basicMapper.getProjectIdList(pigUser.getId(), roles);
            for (Long id : ids) {
                dataList.addAll(biPanelService.incomeExpenditureStatus(startTime, endTime, id, type, projectCycle));
            }
        } else {
            if (!biPanelService.authorityCheck(GlobalConstants.BiPanel.B2, projectId)) {
                log.info("[{}]无权限访问BI-2面板信息", Objects.nonNull(pigUser) ? pigUser.getName() : null);
                return R.failed("Unauthorized access！");
            }
            if (Objects.equals(1, projectCycle)) {
                if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
                    throw new BizException(YpttBizCode.DATE_IS_NULL_ERROR);
                }
            }
            dataList = biPanelService.incomeExpenditureStatus(startTime, endTime, projectId, type, projectCycle);
        }
        try {
            ClassPathResource resource = new ClassPathResource("import-template/BI-2-template.xlsx");
            ExcelUtil.exportTemplateList(response, "BI-2", resource, dataList);
        } catch (IOException e) {
            throw new BizException(ModelEngineBizCode.EXPORT_ERROR, e);
        }
        return R.ok(Boolean.TRUE);
    }

    /**
     * 站点关闭率
     *
     * @param appId 应用id
     * @return List<SiteItemStatisticsVO>
     */
    @GetMapping("/site-item-statistics")
    public R<List<SiteItemStatisticsVO>> siteItemStatistics(@RequestParam(value = "appId") String appId,
                                                            @RequestParam(value = "projectId", required = false) String projectId) {
        return R.ok(biPanelService.siteItemStatistics(appId, projectId));
    }

    /**
     * 报表导出
     *
     * @param response  response
     * @param projectId 项目id
     */
    @PostMapping("/report-export")
    public void reportExport(HttpServletResponse response, String projectId,
                             @RequestParam(name = "dateStrStart", required = false) String dateStrStart,
                             @RequestParam(name = "dateStrEnd", required = false) String dateStrEnd,
                             @RequestParam(name = "dateType", defaultValue = "product_invoice_date") String dateType,
                             @RequestParam(name = "area", required = false) String area,
                             @RequestParam(name = "projectIds", required = false) String projectIds,
                             @RequestParam(name = "moduleTypes", required = false) String moduleTypes,
                             @RequestParam(name = "nation", required = false) String nation,
                             @RequestParam(name = "unId", required = false) String unId) {
        try {
            ClassPathResource resource = new ClassPathResource(getPath(moduleTypes));
            Object o = redisTemplate.opsForValue().get(redisKeyExport);
            String queryDataWay = null;
            if (o != null) {
                queryDataWay = o.toString();
            }
            System.out.println("queryDataWay" + queryDataWay);
            if (queryDataWay != null && !"".equals(queryDataWay) && "OLD".equals(queryDataWay)) {
                ExcelUtil.exportTemplateList(response, "report_form", resource,
                        biPanelService.reportForm(projectId, dateStrStart, dateStrEnd, dateType, area, projectIds, moduleTypes, nation));
            } else {
                ExcelUtil.exportTemplateList(response, "report_form", resource,
                        optimizeBiPanelService.reportForm( projectId, dateStrStart, dateStrEnd, dateType, area, projectIds, moduleTypes, nation, unId));
//                ExcelUtil.exportTemplateList(response, "report_form", resource,
//                        biPanelService.reportForm(projectId, dateStrStart, dateStrEnd, dateType, area, projectIds, moduleTypes, nation));
            }

        } catch (IOException e) {
            throw new BizException(ModelEngineBizCode.EXPORT_ERROR, e);
        }
    }

    private String getPath(String moduleTypes) {
        String BASE_PATH = "import-template/bi-3/report-template-";
        String DEFAULT_PATH = "import-template/report-template.xlsx";

        if (StringUtils.isBlank(moduleTypes)) {
            return DEFAULT_PATH;
        }

        // 定义特殊模块类型
        String[] specialModules = {"Y2,Y9", "Y8,Y9", "Y1,Y3", "Y4,Y8"};
        for (String module : specialModules) {
            if (module.equals(moduleTypes)) {
                return BASE_PATH + module.replace(",", "") + ".xlsx";
            }
        }

        // 默认逻辑：取第一个模块类型
        String firstModule = moduleTypes.split(",")[0];
        return BASE_PATH + firstModule + ".xlsx";
    }


    /**
     * 获取报表
     *
     * @param projectId 项目id
     * @return ReportDataFiledVO
     */
    @GetMapping("/report-form")
    public R<ReportDataFiledVO> reportForm(Page<Map<String, Object>> page,
                                           @RequestParam(value = "projectId", required = false) String projectId,
                                           @RequestParam(name = "dateStrStart", required = false) String dateStrStart,
                                           @RequestParam(name = "dateStrEnd", required = false) String dateStrEnd,
                                           @RequestParam(name = "dateType", defaultValue = "product_invoice_date") String dateType,
                                           @RequestParam(name = "area", required = false) String area,
                                           @RequestParam(name = "projectIds", required = false) String projectIds,
                                           @RequestParam(name = "moduleTypes", required = false) String moduleTypes,
                                           @RequestParam(name = "nation", required = false) String nation,
                                           @RequestParam(name = "unId", required = false) String unId) {

        Object o = redisTemplate.opsForValue().get(redisKey);
        String queryDataWay = null;
        if (o != null) {
            queryDataWay = o.toString();
        }
        System.out.println("queryDataWay" + queryDataWay);
        if (queryDataWay != null && !"".equals(queryDataWay) && "OLD".equals(queryDataWay)) {
            return R.ok(biPanelService.getReportFiled(page, projectId, dateStrStart, dateStrEnd, dateType, area, projectIds, moduleTypes, nation));
        } else {
            return R.ok(optimizeBiPanelService.getReportFiled(page, projectId, dateStrStart, dateStrEnd, dateType, area, projectIds, moduleTypes, nation, unId));
        }
    }

    /**
     * 统计金额一段时间内的 产值及决算金额y6、开票y9、PO金额y2
     *
     * @param projectId 项目id
     * @return ReportDataFiledVO
     */
    @GetMapping("total-amount")
    public R<TotalAmountVO> totalAmount(@RequestParam(value = "projectId", required = false) String projectId,
                                        @RequestParam(name = "dateStrStart", required = false) String dateStrStart,
                                        @RequestParam(name = "dateStrEnd", required = false) String dateStrEnd,
                                        @RequestParam(name = "dateType", required = false) String dateType) {
        return biPanelService.totalAmount(projectId, dateStrStart, dateStrEnd, dateType);
    }

    /**
     * 切换bi-3的查询方式
     */
    @GetMapping("switchBi3Query")
    public R<?> switchBi3Query() {
        try {
            if (redisTemplate.hasKey(redisKey)) {
                redisTemplate.delete(redisKey);
            } else {
                redisTemplate.opsForValue().set(redisKey, "OLD");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.ok("切换成功");
    }

    /**
     * 切换bi-3的导出方式
     */
    @GetMapping("switchBi3Export")
    public R<?> switchBi3Export() {
        try {
            if (redisTemplate.hasKey(redisKeyExport)) {
                redisTemplate.delete(redisKeyExport);
            } else {
                redisTemplate.opsForValue().set(redisKeyExport, "OLD");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.ok("切换成功");
    }

}
