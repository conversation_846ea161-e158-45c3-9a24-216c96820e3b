package com.pig4cloud.pig.yptt.controller.workflow;

import com.pig4cloud.pig.yptt.controller.WorkFlowApiRes;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlowDTO;
import com.pig4cloud.pig.yptt.service.workflow.WorkFlowServiceCommon;
import com.pig4cloud.pig.yptt.service.workflow.WorkFlowSubmitInterceptService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: WorkFlowSubmitInterceptController
 * @Description: 流程提交拦截
 * @Author: lijianpan
 * @CreateTime: 2025-07-08  14:13
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("workflow/fs/submit-intercept")
public class WorkFlowSubmitInterceptController {
    private final WorkFlowSubmitInterceptService workFlowSubmitInterceptService;

    private final WorkFlowServiceCommon workFlowServiceCommon;

    /**
     * @description: 综合报销申请流程提交拦截
     * @author: lijianpan
     **/
    @PostMapping("other-reimbursement")
    public WorkFlowApiRes otherReimbursement(@RequestBody WorkFlowDTO request){
        try {
            return workFlowSubmitInterceptService.otherReimbursement(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            return WorkFlowApiRes.error(e.getMessage());
        }
    }

    /**
     * @description: 劳务/材料报销申请提交拦截
     * @author: lijianpan
     **/
    @PostMapping("sub-con-and-material-Reimbursement")
    public WorkFlowApiRes SubConAndMaterialReimbursement(@RequestBody WorkFlowDTO request){
        try {
            return workFlowSubmitInterceptService.SubConAndMaterialReimbursement(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            return WorkFlowApiRes.error(e.getMessage());
        }
    }

    /**
     * @description: 差旅报销申请提交拦截
     * @author: lijianpan
     **/
    @PostMapping("travel-Reimbursement")
    public WorkFlowApiRes travelReimbursement(@RequestBody WorkFlowDTO request){
        try {
            return workFlowSubmitInterceptService.travelReimbursement(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            return WorkFlowApiRes.error(e.getMessage());
        }
    }

    /**
     * @description: 财务付款申请提交拦截
     * @author: lijianpan
     **/
    @PostMapping("financial-payments")
    public WorkFlowApiRes financialPayments(@RequestBody WorkFlowDTO request){
        try {
            return workFlowSubmitInterceptService.financePayment(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            return WorkFlowApiRes.error(e.getMessage());
        }
    }

    /**
     * @description: 客户/供应商登记提交拦截
     * @author: lijianpan
     **/
    @PostMapping("customer-and-supplier-registration")
    public WorkFlowApiRes customerAndSupplierRegistration(@RequestBody WorkFlowDTO request){
        try {
            return workFlowSubmitInterceptService.customerAndSupplierRegistration(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            return WorkFlowApiRes.error(e.getMessage());
        }
    }
}