package com.pig4cloud.pig.yptt.controller.fs;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.pig4cloud.pig.me.api.dto.modelOperation.BatchOperationParams;
import com.pig4cloud.pig.yptt.config.YpttPersonalizedApiProperties;
import com.pig4cloud.pig.yptt.constants.CompanyCodeConstants;
import com.pig4cloud.pig.yptt.controller.ApiRes;
import com.pig4cloud.pig.yptt.service.fs.CustomerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName: ProjectCustomerCodeController
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-08-27  10:23
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("fs/project-customer-code")
public class ProjectCustomerCodeController {
    private final YpttPersonalizedApiProperties props;
    private final CustomerService customerService;

    /**
     * @description: 客户编码使用后改变状态 YPTT
     * @author: lijianpan
     **/
    @PostMapping("change-status-by-yptt")
    public ApiRes changeStatusByYPTT(@RequestBody BatchOperationParams.Payload params, @RequestParam("yptt-task-token") String ypttTaskToken) {
        try {
            Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
            log.info("completePaymentAndModifyStatus params:{}", params);
            customerService.updateProjectCustomerCodeStatus(JSONUtil.parseArray(params.getData().get("code")).get(0).toString(), CompanyCodeConstants.YPTT.getCode());
            return ApiRes.ok("");
        } catch (Exception e) {
            log.error(e.getMessage());
            return ApiRes.failed(e.getMessage());
        }
    }

    /**
     * @description: 客户编码使用后改变状态 YPIT
     * @author: lijianpan
     **/
    @PostMapping("change-status-by-ypit")
    public ApiRes changeStatusByYPIT(@RequestBody BatchOperationParams.Payload params, @RequestParam("yptt-task-token") String ypttTaskToken) {
        try {
            Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
            log.info("completePaymentAndModifyStatus params:{}", params);
            customerService.updateProjectCustomerCodeStatus(JSONUtil.parseArray(params.getData().get("code")).get(0).toString(),CompanyCodeConstants.YPIT.getCode());
            return ApiRes.ok("");
        } catch (Exception e) {
            log.error(e.getMessage());
            return ApiRes.failed(e.getMessage());
        }
    }
}