package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.lang.Assert;
import com.pig4cloud.pig.common.security.annotation.Inner;
//import com.pig4cloud.pig.me.api.dto.modelOperation.BatchOperationParams;
import com.pig4cloud.pig.yptt.config.YpttPersonalizedApiProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName: FinancialPaymentController
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-07-18  14:05
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/financial-payment")
public class FinancialPaymentController {
    private final YpttPersonalizedApiProperties props;

//    @PostMapping("/payment-status-changes")
//    @Inner(value = false)
//    public ApiRes paymentStatusChanges(@RequestBody BatchOperationParams.Payload params, @RequestParam("yptt-task-token") String ypttTaskToken){
//        Assert.equals(ypttTaskToken, props.getTaskToken(), "illegal task-token");
//        log.info("paymentStatusChanges params:{}",params);
//        return ApiRes.ok("成功");
//    }
}