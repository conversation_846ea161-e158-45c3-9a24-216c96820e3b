package com.pig4cloud.pig.yptt.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.yptt.entity.dto.AbilityOperateDateDTO;
import com.pig4cloud.pig.yptt.entity.dto.LockDataDTO;
import com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo;
import com.pig4cloud.pig.yptt.service.AdjustExcelService;
import com.pig4cloud.pig.yptt.service.LockDataTimeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName LockDataTimeController
 * @Description 时间锁定 （y1-y9根据时间，能否导入）
 * @date 2025/3/18 9:30
 * @Version 1.0
 */

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/lock-time")
public class LockDataTimeController {
    private final LockDataTimeService lockDataTimeService;

    /**
     * <AUTHOR>
     * @Description 添加时间锁 （第一版本内容）
     * @Date 14:40 2025/3/18
     * @Param [lockDataDTO]
     * @return com.pig4cloud.pig.yptt.controller.ApiRes
     **/
    @PostMapping("exec-data")
    public ApiRes execData(@RequestBody LockDataDTO lockDataDTO){
        return lockDataTimeService.execData(lockDataDTO);
    }

    /**
     * <AUTHOR>
     * @Description 锁列表 （第一版本内容）
     * @Date 14:40 2025/3/18
     * @Param []
     * @return com.pig4cloud.pig.common.core.util.R<java.lang.Object>
     **/
    @GetMapping("list")
    public R<Page<LockDataTimeVo>> list(@RequestParam(defaultValue = "1") Integer page,
                                            @RequestParam(defaultValue = "10") Integer size,
                                            @RequestParam(required = false) String module,
                                            @RequestParam(required = false) String projectCode
                              ){
        return lockDataTimeService.list(module, projectCode, page, size);
    }

    @PostMapping("del")
    public ApiRes del(@RequestBody List<String> idList) {
        return lockDataTimeService.del(idList);
    }

    /*
     * <AUTHOR>
     * @Description 添加能够操作的时间段 (第二版本内容)
     * @Date 11:49 2025/5/25
     * @Param
     * @return
     **/
    @PostMapping("abilityOperateDate")
    public ApiRes abilityOperateDate(@RequestBody AbilityOperateDateDTO abilityOperateDateDTO) {
        return lockDataTimeService.abilityOperateDate(abilityOperateDateDTO);
    }

    @GetMapping("listV2")
    public R<Page<LockDataTimeVo>> listV2(@RequestParam(defaultValue = "1") Integer page,
                                        @RequestParam(defaultValue = "10") Integer size,
                                        @RequestParam(required = false) String projectCode
    ){
        return lockDataTimeService.listV2( projectCode, page, size);
    }


}