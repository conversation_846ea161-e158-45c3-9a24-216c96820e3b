package com.pig4cloud.pig.yptt.controller.workflow;

import com.pig4cloud.pig.yptt.controller.WorkFlowApiRes;
import com.pig4cloud.pig.yptt.entity.dto.WorkFlowDTO;
import com.pig4cloud.pig.yptt.service.workflow.WorkFlowEndInterceptService;
import com.pig4cloud.pig.yptt.service.workflow.WorkFlowServiceCommon;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: 流程结束拦截
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-07-22  15:17
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("workflow/end-intercept")
public class WorkFlowEndInterceptController {
    private final WorkFlowEndInterceptService workFlowEndInterceptService;

    private final WorkFlowServiceCommon workFlowServiceCommon;

    /**
     * @description: 综合报销
     * @author: lijianpan
     **/
    @PostMapping("other-reimbursement")
    public WorkFlowApiRes otherReimbursement(@RequestBody WorkFlowDTO request){
        try {
            return workFlowEndInterceptService.otherReimbursement(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            log.error(e.getMessage());
            return WorkFlowApiRes.error(e.getMessage());
        }
    }

    /**
     * @description: 劳务/材料报销
     * @author: lijianpan
     **/
    @PostMapping("sub-con-and-material-reimbursement")
    public WorkFlowApiRes SubConAndMaterialReimbursement(@RequestBody WorkFlowDTO request){
        try {
            return workFlowEndInterceptService.SubConAndMaterialReimbursement(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            log.error(e.getMessage());
            return WorkFlowApiRes.error(e.getMessage());
        }
    }

    /**
     * @description: 差旅报销
     * @author: lijianpan
     **/
    @PostMapping("travel-reimbursement")
    public WorkFlowApiRes travelReimbursement(@RequestBody WorkFlowDTO request){
        try {
            return workFlowEndInterceptService.travelReimbursement(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            log.error(e.getMessage());
            return WorkFlowApiRes.error(e.getMessage());
        }
    }

    /**
     * @description: 预借付款申请
     * @author: lijianpan
     **/
    @PostMapping("advance-payments-and-personal-loans")
    public WorkFlowApiRes advancePaymentsAndPersonalLoans(@RequestBody WorkFlowDTO request){
        try {
            return workFlowEndInterceptService.advancePaymentsAndPersonalLoans(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            log.error(e.getMessage());
            return WorkFlowApiRes.error(e.getMessage());
        }
    }

    /**
     * @description: 财务付款申请
     * @author: lijianpan
     **/
    @PostMapping("financial-payments")
    public WorkFlowApiRes financialPayments(@RequestBody WorkFlowDTO request){
        try {
            return workFlowEndInterceptService.financialPayments(workFlowServiceCommon.getRequestInfo(request));
        }catch (Exception e){
            log.error(e.getMessage());
            return WorkFlowApiRes.error(e.getMessage());
        }
    }
}