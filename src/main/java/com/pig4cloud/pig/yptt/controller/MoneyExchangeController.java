package com.pig4cloud.pig.yptt.controller;

import com.pig4cloud.pig.yptt.service.MoneyExchangeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName MoneyExchangeController
 * @Description 货币
 * @date 2025/6/9 10:51
 * @Version 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/money-exchange")
public class MoneyExchangeController {
    private final MoneyExchangeService moneyExchangeService;

    /*
     * <AUTHOR>
     * @Description 金额转换
     * @Date 10:57 2025/6/9
     * @Param [moneyInput, moneyOutput, Amount]
     * @return com.pig4cloud.pig.yptt.controller.ApiRes
     **/
    @GetMapping("transfer")
    public ApiRes transfer(@RequestParam String moneyInput,
                          @RequestParam String moneyOutput,
                          @RequestParam BigDecimal Amount
    ) {
        return moneyExchangeService.transfer(moneyInput,moneyOutput,Amount);
    }
}