package com.pig4cloud.pig.yptt.controller;

import cn.hutool.core.io.resource.ClassPathResource;
import com.pig4cloud.pig.common.core.exception.biz.BizException;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.annotation.Inner;
import com.pig4cloud.pig.me.api.dto.operation.OperationInsertDTO;
import com.pig4cloud.pig.me.api.dto.operation.OperationPageDTO;
import com.pig4cloud.pig.me.api.dto.operation.OperationUpdateDTO;
import com.pig4cloud.pig.me.api.exception.ModelEngineBizCode;
import com.pig4cloud.pig.yptt.config.OperationLogger;
import com.pig4cloud.pig.yptt.entity.dto.YPTTBatchImportDTO;
import com.pig4cloud.pig.yptt.entity.vo.ImportResultVO;
import com.pig4cloud.pig.yptt.entity.vo.ProgressDelVO;
import com.pig4cloud.pig.yptt.entity.vo.ProgressY3VO;
import com.pig4cloud.pig.yptt.service.DataMangeService;
import com.pig4cloud.pig.yptt.service.DeleteService;
import com.pig4cloud.pig.yptt.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/09/04
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/data-mange")
public class DataMangeController {

	private final DataMangeService dataMangeService;
	private final DeleteService deleteService;

	@PostMapping("/import/upload-data-table")
	public R<Object> importDataTable(YPTTBatchImportDTO param) {
		return dataMangeService.importDataTable(param);
	}

	@PostMapping("/import/check-upload-data")
	public R<?> checkImportData(YPTTBatchImportDTO param) {
		return dataMangeService.checkImportData(param);
	}

	@PostMapping("/import/download-fail-data")
	public void downloadFailData(HttpServletResponse response,@RequestParam(name = "key") String key,@RequestParam(name = "module") String module) {
		ClassPathResource resource = new ClassPathResource("import-template/import-fail/" + module + ".xlsx");
		try {
			ExcelUtil.exportTemplateList(response, "fail-"+module, resource,
					dataMangeService.downloadFailData(key));
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@GetMapping("/import/download-template")
	public ResponseEntity<byte[]> importTemplate(@RequestParam("moduleType") String moduleType) {
		return dataMangeService.downloadTemplate(moduleType);
	}

	@PostMapping("/subcon/external-cost/add")
	@OperationLogger
	@Inner(value = false)
	public ApiRes addExternalCost(@RequestBody OperationInsertDTO o) {
		return ApiRes.ok(dataMangeService.addExternalCost(o));
	}

	@PostMapping("/subcon/external-cost/update")
	@OperationLogger
	@Inner(value = false)
	public ApiRes updateExternalCost(@RequestBody OperationUpdateDTO o) {
		return ApiRes.ok(dataMangeService.updateExternalCost(o));
	}

	@PostMapping("/import/update-y3")
	public R<List<ImportResultVO>> updateY3(MultipartFile file, String key) {
		return R.ok(dataMangeService.updateY3(file, key));
	}

	@GetMapping("/import/query-progress-y3")
	public R<ProgressY3VO> queryProgressY3(String key) {
		return R.ok(dataMangeService.queryProgressY3(key));
	}

	@PostMapping("/export/update-y3")
	public void exportY3(HttpServletResponse response, @RequestBody @Validated OperationPageDTO pageDTO) {
		try {
			ClassPathResource resource = new ClassPathResource("import-template/y3-update.xlsx");
			ExcelUtil.exportTemplateList(response, "y3", resource, dataMangeService.exportY3(pageDTO));
		}
		catch (IOException e) {
			throw new BizException(ModelEngineBizCode.EXPORT_ERROR, e);
		}
	}

	@GetMapping("/del-preview")
	public R<List<Map<String, Object>>> deletePreview(@RequestParam(value = "projectCode") String projectCode,
			@RequestParam(value = "type") String type, @RequestParam(value = "region", required = false) String region,
			@RequestParam(value = "siteId", required = false) String siteId,
			@RequestParam(value = "itemCode", required = false) String itemCode,
			@RequestParam(value = "phase", required = false) String phase,
			@RequestParam(value = "PONumber", required = false) String PONumber,
			@RequestParam(value = "unId", required = false) String unId) {
		return R.ok(dataMangeService.deletePreview(projectCode, type, region, siteId, itemCode, phase, PONumber, unId));
	}

	@GetMapping("/del-data")
	public R<Boolean> deleteData(@RequestParam(value = "projectCode") String projectCode,
			@RequestParam(value = "projectId") String projectId,
			@RequestParam(value = "type", required = false) String type,
			@RequestParam(value = "region", required = false) String region,
			@RequestParam(value = "siteId", required = false) String siteId,
			@RequestParam(value = "itemCode", required = false) String itemCode,
			@RequestParam(value = "phase", required = false) String phase,
			@RequestParam(value = "PONumber", required = false) String PONumber,
			@RequestParam(value = "unId", required = false) String unId) {
		return R.ok(dataMangeService.deleteData(projectId, projectCode, type, region, siteId, itemCode, phase, PONumber, unId));
	}

	@PostMapping("/delete/batch")
	public R<List<ImportResultVO>> deleteBatch(MultipartFile file, String key) {
//		return R.ok(dataMangeService.deleteBatch(file, key));
		return R.ok(deleteService.deleteBatch(file,key));
	}

	@GetMapping("/import/query-progress-delete")
	public R<ProgressDelVO> queryProgressDel(String key) {
		return R.ok(deleteService.queryProgressDel(key));
	}

	/**
	 * <AUTHOR>
	 * @Description 下载删除模板
	 * @Date 14:34 2025/5/26
	 * @Param [moduleType]
	 * @return org.springframework.http.ResponseEntity<byte [ ]>
	 **/
	@GetMapping("/delete/download-template")
	public ResponseEntity<byte[]> downloadDelete() {
		return dataMangeService.downloadDelete();
	}

	/**
	 * <AUTHOR>
	 * @Description PM删除权限
	 * @Date 11:42 2025/5/27
	 * @Param [projectCode, projectId, type, region, siteId, itemCode, phase, PONumber, unId]
	 * @return com.pig4cloud.pig.common.core.util.R<java.lang.Boolean>
	 **/
	@GetMapping("/del-data-pm")
	public R<Boolean> deleteDataPM(@RequestParam(value = "projectCode") String projectCode,
								 @RequestParam(value = "projectId") String projectId,
								 @RequestParam(value = "type") String type,
								 @RequestParam(value = "region", required = false) String region,
								 @RequestParam(value = "siteId", required = false) String siteId,
								 @RequestParam(value = "itemCode", required = false) String itemCode,
								 @RequestParam(value = "phase", required = false) String phase,
								 @RequestParam(value = "PONumber", required = false) String PONumber,
								 @RequestParam(value = "unId") String unId) {
		return R.ok(dataMangeService.deleteDataPM(projectId, projectCode, type, region, siteId, itemCode, phase, PONumber, unId));
	}


}
