package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.yptt.entity.ProjectRole;
import com.pig4cloud.pig.yptt.entity.WarnTemp;
import com.pig4cloud.pig.yptt.entity.WarningMessage;
import com.pig4cloud.pig.yptt.entity.dto.WarningPageDTO;
import com.pig4cloud.pig.yptt.entity.vo.WarningStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Mapper
//@InterceptorIgnore(tenantLine = "true")
public interface WarningMapper {

	/**
	 * 需要更新警告状态的站点信息
	 * @param cur
	 * @param size
	 * @return
	 */
	List<WarnTemp> selectSiteWarnTemp(@Param("cur") int cur, @Param("size") int size,
			@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 查询移除站点警告信息
	 * @return
	 */
	List<Long> selectRemoveSiteId(@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 更新站点警告信息状态
	 * @param type
	 * @param ids
	 */
	void updateSiteWarning(@Param("type") String type, @Param("ids") List<Long> ids);

	/**
	 * 需要更新警告状态的po信息
	 * @param cur
	 * @param size
	 * @return
	 */
	List<WarnTemp> selectPoWarnTemp(@Param("cur") int cur, @Param("size") int size,
			@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 查询移除po警告信息
	 * @return
	 */
	List<Long> selectRemovePoId(@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 更新po警告信息状态
	 * @param type
	 * @param ids
	 */
	void updatePoWarning(@Param("type") String type, @Param("ids") List<Long> ids);

	/**
	 * 需要更新警告状态的po金额信息
	 * @param cur
	 * @param size
	 * @return
	 */
	List<WarnTemp> selectPoAmountWarnTemp(@Param("cur") int cur, @Param("size") int size,
			@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 查询移除po金额警告信息
	 * @return
	 */
	List<Long> selectRemovePoAmountWarnTemp(@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 更新po警告信息状态
	 * @param type
	 * @param ids
	 */
	void updateSiteDeliveryWarning(@Param("type") String type, @Param("ids") List<Long> ids);

	/**
	 * 开工警告信息查询
	 * @param cur
	 * @param size
	 * @return
	 */
	List<WarnTemp> selectStartWorkWarnTemp(@Param("cur") int cur, @Param("size") int size,
			@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 移除开工警告信息查询
	 * @return
	 */
	List<Long> selectRemoveStartWorkWarnTemp(@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 验收警告信息查询
	 * @param cur
	 * @param size
	 * @return
	 */
	List<WarnTemp> selectCheckWorkWarnTemp(@Param("cur") int cur, @Param("size") int size,
			@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 移除验收警告信息查询
	 * @return
	 */
	List<Long> selectRemoveCheckWorkWarnTemp(@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 分包商po警告信息查询
	 * @param cur
	 * @param size
	 * @return
	 */
	List<WarnTemp> selectSubconPoWarnTemp(@Param("cur") int cur, @Param("size") int size,
			@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 移除分包商破po警告信息查询
	 * @return
	 */
	List<Long> selectRemoveSubconPoWarnTemp(@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * yptt结算警告信息查询
	 * @param cur
	 * @param size
	 * @return
	 */
	List<WarnTemp> selectYpttSettlementWarnTemp(@Param("cur") int cur, @Param("size") int size,
			@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 移除yptt结算警告信息查询
	 * @return
	 */
	List<Long> selectRemoveYpttSettlementWarnTemp(@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 更新yptt结算警告信息状态
	 * @param type
	 * @param ids
	 */
	void updateYpttSettlementWarning(@Param("type") String type, @Param("ids") List<Long> ids);

	/**
	 * 分包商支付警告信息查询
	 * @param cur
	 * @param size
	 * @return
	 */
	List<WarnTemp> selectSubconPayWarnTemp(@Param("cur") int cur, @Param("size") int size,
			@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 移除分包商结算警告信息查询
	 * @return
	 */
	List<Long> selectRemoveSubconPayWarnTemp(@Param("projectCode") String projectCode, @Param("warn") String warn);

	/**
	 * 更新分包商警告信息状态
	 * @param type
	 * @param ids
	 */
	void updateSubconPayWarning(@Param("type") String type, @Param("ids") List<Long> ids);

	/**
	 * 查找存在的警告信息
	 * @param dataId
	 * @param type
	 * @return
	 */
	List<Integer> findWarnMsg(@Param("id") Long dataId, @Param("type") String type);

	/**
	 * 批量保存警告信息
	 * @param warningMessageList
	 */
	void saveMsg(@Param("list") List<WarningMessage> warningMessageList);

	/**
	 * 批量保存警告信息
	 * @param warningMessageList
	 */
	void deleteMsgByType(@Param("list") List<WarningMessage> warningMessageList, @Param("type") String warningType);

	/**
	 * 警告信息分页查询
	 * @param page
	 * @param dtoList
	 * @param projectCode 项目id
	 * @param projectName 项目名称
	 * @param uniquenessField 唯一标识
	 * @param typeList 警告类型
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 */
	IPage<WarningMessage> warnPage(Page<WarningMessage> page, @Param("list") List<WarningPageDTO> dtoList,
			@Param("projectCode") String projectCode, @Param("projectName") String projectName,
			@Param("uniquenessField") String uniquenessField, @Param("typeList") List<String> typeList,
			@Param("startTime") Date startTime, @Param("endTime") Date endTime);

	/**
	 * 警告信息统计
	 * @param dtoList
	 * @param projectCode 项目id
	 * @param projectName 项目名称
	 * @param uniquenessField 唯一标识
	 * @param typeList 警告类型
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 */
	List<WarningStatisticsVO> warningStatistics(@Param("list") List<WarningPageDTO> dtoList,
			@Param("projectCode") String projectCode, @Param("projectName") String projectName,
			@Param("uniquenessField") String uniquenessField, @Param("typeList") List<String> typeList,
			@Param("startTime") Date startTime, @Param("endTime") Date endTime);

	/**
	 * 用户id获取项目角色
	 * @param userId
	 * @return
	 */
	List<ProjectRole> getProjectRole(@Param("id") Long userId);

	/**
	 * 查询角色警告权限
	 * @param roleList
	 * @return
	 */
	List<String> warnRoleList(@Param("list") List<String> roleList);

	/**
	 * 警告信息list
	 * @param dtoList
	 * @param projectCode 项目id
	 * @param projectName 项目名称
	 * @param uniquenessField 唯一标识
	 * @param typeList 警告类型
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 */
	List<Map<String, Object>> listMap(@Param("list") List<WarningPageDTO> dtoList,
			@Param("projectCode") String projectCode, @Param("projectName") String projectName,
			@Param("uniquenessField") String uniquenessField, @Param("typeList") List<String> typeList,
			@Param("startTime") Date startTime, @Param("endTime") Date endTime);

	void clearWarningInfo();

	String getThresholdByCode(@Param("code") String code, @Param("type") String type);

	List<Long> getWarnDataIdByCode(@Param("code") String code, @Param("type") String type);

}
