package com.pig4cloud.pig.yptt.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface LockDataTimeMapper {

    Integer execData(@Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime,
                     @Param("module") String module, @Param("id") String id, @Param("projectCode") String projectCode);

    List<LockDataTimeVo> list(@Param("module") String module, @Param("projectCode") String projectCode);

    Integer del(@Param("idList") List<String> idList);

    List<LockDataTimeVo> selectByIds(@Param("idList") List<String> idList);

    Page<LockDataTimeVo> selectPage(@Param("pageMap") Page<LockDataTimeVo> pageMap,
                                    @Param("module") String module, @Param("projectCodes") List<String> projectCodes);

    Integer updateTime(@Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime,
                       @Param("module") String module, @Param("projectCode") String projectCode);

    Integer addData(@Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime,
                    @Param("projectCode") String projectCode, @Param("typeLock") Integer type_lock,
                    @Param("id") String id);

    List<LockDataTimeVo> selectAllV2(@Param("projectCodes") List<String> projectCodes);

    Integer updateTimeV2(@Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime,
                        @Param("projectCode") String projectCode);

    Page<LockDataTimeVo> selectPageV2(@Param("pageMap") Page<LockDataTimeVo> pageMap,
                                      @Param("projectCodes") List<String> projectCodes);
}
