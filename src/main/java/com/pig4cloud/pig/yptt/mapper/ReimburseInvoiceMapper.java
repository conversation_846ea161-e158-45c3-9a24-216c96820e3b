package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.pig4cloud.pig.yptt.entity.ReimburseInvoiceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface ReimburseInvoiceMapper {
    int addBatch(@Param("modelTable") String modelTable,@Param("list") List<ReimburseInvoiceEntity> reimburseInvoices);

    boolean isDuplicateInvoiceNums(@Param("modelTable") String modelTable,@Param("invoiceNums") List<String> invoiceNums);
}
