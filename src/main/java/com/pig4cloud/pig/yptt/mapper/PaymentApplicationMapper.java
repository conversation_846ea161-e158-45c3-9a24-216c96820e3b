package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pig4cloud.pig.yptt.entity.PaymentApplication;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【memm_payment_application_1f120kq4jd401(付款申请单)】的数据库操作Mapper
* @createDate 2025-07-28 10:27:53
*/
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface PaymentApplicationMapper extends BaseMapper<PaymentApplication> {
    int add(@Param("modelTable") String modelTable,@Param("list") List<PaymentApplication> paymentApplications);

    BigDecimal getRealPaymentMoney(@Param("modelTable") String modelTable,@Param("dataIds") List<Long> dataIds,@Param("excludeDataId") Long excludeDataId);

    BigDecimal getPaymentMoney(@Param("modelTable") String modelTable,@Param("dataId") Long dataId);

    boolean updateStatus(@Param("modelTable") String modelTable,@Param("status") String status,@Param("dataId") Long dataId);

    BigDecimal getRealPaymentMoneyByList(@Param("modelTable") String modelTable,@Param("dataIds") List<Long> dataIds,@Param("excludeDataIds") List<Long> excludeDataIds);
}
