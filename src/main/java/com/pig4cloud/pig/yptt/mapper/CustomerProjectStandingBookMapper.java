package com.pig4cloud.pig.yptt.mapper;

import com.pig4cloud.pig.yptt.entity.dto.CustomerProjectStandingBookDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/17
 */
@Mapper
public interface CustomerProjectStandingBookMapper {

	/**
	 * 生成客户项目台账
	 * @param cur 当前页
	 * @param size 页容量
	 * @return 生成客户项目台账
	 */
	List<CustomerProjectStandingBookDTO> generateCustomerProjectStandingBookList(@Param("cur") int cur,
			@Param("size") int size);

	/**
	 * 更新客户项目台账
	 * @param generatedCustomerProjectStandingBookDTO 客户项目台账
	 * @return 更新数量
	 */
	int update(@Param("dto") CustomerProjectStandingBookDTO generatedCustomerProjectStandingBookDTO);

}
