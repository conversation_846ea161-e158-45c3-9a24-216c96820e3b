package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.me.api.dto.operation.QueryDTO;
import com.pig4cloud.pig.me.api.entity.DataLog;
import com.pig4cloud.pig.yptt.entity.UserRoleInfo;
import com.pig4cloud.pig.yptt.entity.vo.AmountY2VO;
import com.pig4cloud.pig.yptt.entity.vo.AmountY6VO;
import com.pig4cloud.pig.yptt.entity.vo.AmountY9VO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface BasicMapper {

	/**
	 * 根据id查询项目权限表
	 * @param id
	 * @return
	 */
	Map<String, Object> findProjectPerById(@Param("id") Long id);

	/**
	 * 更新项目权限表
	 * @param map
	 * @return
	 */
	int updateProjectPerByProjectId(@Param("map") Map<String, Object> map);

	/**
	 * 根据code查询yptt项目
	 * @param code
	 * @return
	 */
	List<Map<String, Object>> findYpttProjectByCode(@Param("code") String code);

	/**
	 * 根据siteId查询site
	 * @param siteId
	 * @return
	 */
	List<Map<String, Object>> findSiteBySiteID(@Param("siteId") String siteId);

	/**
	 * findSiteItemByUniquenessField
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findSiteItemByUniquenessField(@Param("unField") Long unField);

	/**
	 * findUniquenessByUniquenessField
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findUniquenessByUniquenessField(@Param("unField") String unField);

	/**
	 * findIncomeExpenditureByUniquenessField
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findIncomeExpenditureByUniquenessField(@Param("unField") Long unField);

	/**
	 * findPOItemByUniquenessId
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findPOItemByUniquenessId(@Param("unField") Long unField);

	/**
	 * findSubconPOItemByUniquenessId
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findSubconPOItemByUniquenessId(@Param("unField") Long unField);

	/**
	 * findSiteDeliveryInfoByUniquenessId
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findSiteDeliveryInfoByUniquenessId(@Param("unField") Long unField);

	/**
	 * findSubconPaymentByUniquenessIdJson
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findSubconPaymentByUniquenessIdJson(@Param("unField") Long unField);

	/**
	 * findYpttSettlementByUniquenessIdJson
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findYpttSettlementByUniquenessIdJson(@Param("unField") Long unField);

	/**
	 * findReadySettlementByUniquenessId
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findReadySettlementByUniquenessId(@Param("unField") Long unField);

	/**
	 * findProductivityReportByUniquenessId
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findProductivityReportByUniquenessId(@Param("unField") Long unField);

	/**
	 * findSubconSettlementByUniquenessId
	 * @param unField
	 * @return
	 */
	List<Map<String, Object>> findSubconSettlementByUniquenessId(@Param("unField") Long unField);

	/**
	 * findCustomerProjectByContractNumber
	 * @param number
	 * @return
	 */
	List<Map<String, Object>> findCustomerProjectByContractNumber(@Param("number") String number,
			@Param("name") String name);

	/**
	 * findPoByPoNumber
	 * @param number
	 * @return
	 */
	List<Map<String, Object>> findPoByPoNumber(@Param("number") String number);

	/**
	 * findSubconBySubconName
	 * @param name
	 * @return
	 */
	List<Map<String, Object>> findSubconBySubconName(@Param("name") String name);

	/**
	 * findSubconPOBySubconPONumber
	 * @param number
	 * @return
	 */
	List<Map<String, Object>> findSubconPOBySubconPONumber(@Param("number") String number);

	/**
	 * 报表
	 * @param projectId
	 * @return
	 */
	List<Map<String, Object>> reportForm(@Param("id") String projectId,
										 @Param("dateStrStart") String dateStrStart, @Param("dateStrEnd") String dateStrEnd,
										 @Param("dateType") String dateType, @Param("area") String area, @Param("list") List<String> list,
										 @Param("moduleTypes") String moduleTypes, @Param("nation") String nation);

	/**
	 * 报表分页
	 * @param page
	 * @param projectId
	 * @return
	 */
	Page<Map<String, Object>> reportFormPage(Page<Map<String, Object>> page, @Param("id") String projectId,
											 @Param("dateStrStart") String dateStrStart, @Param("dateStrEnd") String dateStrEnd,
											 @Param("dateType") String dateType, @Param("area") String area,
											 @Param("list") List<String> list,
											 @Param("moduleTypes") String moduleTypes, @Param("nation") String nation);

	/**
	 * 查询po与poItem的关系是否以存在
	 * @param poId
	 * @param poItemId
	 * @return
	 */
	List<Long> findPoRelPoItem(@Param("poId") Long poId, @Param("poItemId") Long poItemId);

	/**
	 * 更新关系
	 * @param id
	 * @param userId
	 * @param poId
	 * @param poItemId
	 * @return
	 */
	void addPoRelPoItem(@Param("id") Long id, @Param("userId") Long userId, @Param("poId") Long poId,
			@Param("poItemId") Long poItemId);

	/**
	 * 查询po与poItem的关系是否以存在
	 * @param left
	 * @param right
	 * @return
	 */
	List<Long> findSubconPoRelPoItem(@Param("left") Long left, @Param("right") Long right);

	/**
	 * 更新关系
	 * @param id
	 * @param userId
	 * @param left
	 * @param right
	 * @return
	 */
	void addSubconPoRelPoItem(@Param("id") Long id, @Param("userId") Long userId, @Param("left") Long left,
			@Param("right") Long right);

	/**
	 * 后期收支统计
	 * @param projectId 项目id
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 */
	List<Map<String, Object>> getIncomeExpenditure(@Param("projectId") Long projectId,
			@Param("startTime") Date startTime, @Param("endTime") Date endTime);

	/**
	 * 根据项目id查询siteItem
	 * @param projectId 项目id
	 * @return
	 */
	List<Long> getSiteItemIdByProjectId(@Param("projectId") Long projectId);

	/**
	 * 根据项目id查询siteId
	 * @param projectId
	 * @return
	 */
	List<Long> getSiteIdByProjectId(@Param("projectId") Long projectId);

	/**
	 * 根据项目id查询poId
	 * @param projectId
	 * @return
	 */
	List<Long> getPoIdByProjectId(@Param("projectId") Long projectId);

	/**
	 * 根据项目id查询SubconPoId
	 * @param projectId
	 * @return
	 */
	List<Long> getSubconPoIdByProjectId(@Param("projectId") Long projectId);

	/**
	 * 根据项目id查询各个模块id
	 * @param projectId
	 * @param type
	 * @return
	 */
	List<Long> getIdsByProjectId(@Param("projectId") Long projectId, @Param("type") String type);

	/**
	 * 唯一标识
	 * @param id
	 * @return
	 */
	List<Map<String, Object>> findUniquenessById(@Param("id") Long id);

	/**
	 * 唯一标识id
	 * @param str
	 * @return
	 */
	Long findUniquenessIdByStrField(@Param("str") String str);

	/**
	 * 唯一标识id
	 * @param id
	 * @return
	 */
	Long findUniquenessIdByY3Id(@Param("id") String id);

	/**
	 * 批量更新y3
	 * @param map
	 * @return
	 */
	int batchUpdateY3(@Param("list") List<Map<String, Object>> map);

	/**
	 * 用户id
	 * @param list
	 * @return
	 */
	List<UserRoleInfo> getUserIdByMemberId(@Param("list") List<Long> list);

	/**
	 * 用户角色信息
	 * @param projectId
	 * @return
	 */
	List<UserRoleInfo> getUserRoleInfo(@Param("projectId") Long projectId);

	/**
	 * 批量更新数据日志
	 * @param list
	 * @return
	 */
	Integer batchSaveDataLog(@Param("list") List<DataLog> list);

	/**
	 * 站点交付查询
	 * @param id
	 * @return 站点交付
	 */
	Map<String, Object> findY3ById(@Param("id") Long id);

	/**
	 * 删除预览
	 * @param projectCode 项目编号
	 * @param type type
	 * @param region 地域
	 * @param siteId 站点id
	 * @param itemCode 项目编号
	 * @param phase 阶段
	 * @return Map
	 */
	List<Map<String, Object>> deletePreview(@Param("projectCode") String projectCode, @Param("type") String type,
											@Param("region") String region, @Param("siteId") String siteId, @Param("itemCode") String itemCode,
											@Param("phase") String phase, @Param("unId") String unId);

	/**
	 * 删除数据
	 * @param projectCode 项目编号
	 * @param type type
	 * @param region 地域
	 * @param siteId 站点id
	 * @param itemCode 项目编号
	 * @param phase 阶段
	 * @return int
	 */
	int deleteData(@Param("projectCode") String projectCode, @Param("type") String type, @Param("region") String region,
			@Param("siteId") String siteId, @Param("itemCode") String itemCode, @Param("phase") String phase,
			@Param("unId") String unId);

	/**
	 * 删除唯一标识
	 * @param projectCode 项目编号
	 * @return int
	 */
	int deleteUniqueness(@Param("projectCode") String projectCode, @Param("region") String region,
			@Param("siteId") String siteId, @Param("itemCode") String itemCode, @Param("phase") String phase,
						 @Param("unId") String unId);

	/**
	 * 删除收支统计
	 * @param projectCode 项目编号
	 * @return int
	 */
	int deleteIncomeExpenditure(@Param("projectCode") String projectCode, @Param("region") String region,
			@Param("siteId") String siteId, @Param("itemCode") String itemCode, @Param("phase") String phase,
								@Param("unId") String unId);

	/**
	 * 根据项目编号查询数据
	 * @param projectCode 项目编号
	 * @param type 类型
	 * @return int
	 */
	Integer getDataByProjectCode(@Param("projectCode") String projectCode, @Param("type") String type,
			@Param("region") String region, @Param("siteId") String siteId, @Param("itemCode") String itemCode,
			@Param("phase") String phase, @Param("unId") String unId);

	int deleteAllWarnInfo(@Param("projectId") String projectId);

	void deleteIncomeExpenditureByUniquenessId(@Param("list") List<String> uniquenessList, @Param("type") String type);

	List<Map<String, Object>> findPOItem(@Param("poDataId") Long poDataIdJson, @Param("phase") String phase,
			@Param("siteDataId") Long siteDataIdJson, @Param("itemCode") String itemCode);

	List<Map<String, Object>> findSubconPOItem(@Param("subconPoDataId") Long subconPoDataId,
			@Param("subconDataId") Long subconDataId, @Param("siteDataId") Long siteDataId,
			@Param("itemCode") String itemCode, @Param("phase") String phase);

	void saveItemData(@Param("map") Map<String, Object> map, @Param("table") String table);

	void updateItemData(@Param("map") Map<String, Object> map, @Param("table") String table);

	Object findWarningThreshold(@Param("projectId") Long projectId);

	void saveRelProject(@Param("id") Long id, @Param("dataId") Long dataId, @Param("projectId") Long projectId);

	void saveRelSite(@Param("id") Long id, @Param("siteDataId") Long siteDataId, @Param("dataId") Long dataId);

	void saveRelPo(@Param("id") Long id, @Param("poDataId") Long poDataId, @Param("dataId") Long dataId);

	void saveRelSubconPoItemExCost(@Param("id") Long id, @Param("subconPoDataId") Long poDataId,
			@Param("dataId") Long dataId);

	List<Map<String, Object>> exportY3(@Param("map") HashMap<String, Object> mapCondition,
			@Param("roleList") List<Long> roleList, @Param("userId") Long userId);

	Map<String, Object> findPoById(@Param("poId") Long poId);

	void incomeCorrection(@Param("maps") List<Map<String, Object>> maps);

	List<Map<String, Object>> incomeTotal(@Param("page") Integer page, @Param("size") Integer size);

	List<Map<String, Object>> selectYPTTSiteStateList(@Param("page") Integer page, @Param("size") Integer size);

	void updateSiteState(@Param("list") List<String> list);

	List<Map<String, Object>> selectSiteStateList(@Param("page") Integer page, @Param("size") Integer size);

	List<Long> getProjectIdList(@Param("userId") Long userId, @Param("list") List<Long> roleList);

	List<String> getProjectCodes();

	AmountY2VO totalY2(@Param("projectId") String projectId, @Param("dateStrStart") String dateStrStart,
							 @Param("dateStrEnd") String dateStrEnd, @Param("dateType") String dateType);

	AmountY6VO totalY6(@Param("projectId") String projectId, @Param("dateStrStart") String dateStrStart,
							 @Param("dateStrEnd") String dateStrEnd, @Param("dateType") String dateType);

	AmountY9VO totalY9(@Param("projectId") String projectId, @Param("dateStrStart") String dateStrStart,
							 @Param("dateStrEnd") String dateStrEnd, @Param("dateType") String dateType);

    Integer selectPermissByProjectId(@Param("projectId") Long projectId);

	Integer addPermissPro(@Param("projectId")String projectId, @Param("id") String snowflakeNextIdStr);

	List<Map<String, Object>> exportY3V2(@Param("conditions")List<QueryDTO> conditions,
										 @Param("roleList") List<Long> roles,@Param("userId") Long userId,
										 @Param("DATE_FILED_LIST") List<String> DATE_FILED_LIST);
}
