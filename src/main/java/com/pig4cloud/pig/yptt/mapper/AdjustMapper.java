package com.pig4cloud.pig.yptt.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface AdjustMapper {

    /**
     * 查询y1-y9的所有相关数据
     */
    List<Map<String, Object>> selectReport(@Param("projectCode") String projectCode);

    /**
     * 调整站点条目的数据 y1
     */
    boolean updateSiteItemData(@Param("map") Map<String, Object> siteItem);

    /**
     * 调整po条目的数据 y2
     */
    boolean updatePoItemData(@Param("map") Map<String, Object> poItem);

    /**
     * 批量调整po条目的数据 y2
     */
    boolean updatePoItemDatas(@Param("poItems") List<Map<String, Object>> poItems);

    /**
     * 调整分包商po条目的数据 y4
     */
    boolean updateSubPoItemData(@Param("map") Map<String, Object> subPoItem);

    /**
     * 调整可结算参考的数据 y5
     */
    boolean updateSettlementData(@Param("map") Map<String, Object> settlementData);

    /**
     * 批量调整可结算参考的数据 y5
     */
    boolean updateSettlementDatas(@Param("settlementDatas") List<Map<String, Object>> settlementDatas);

    /**
     * 调整产值管理的数据 y6
     */
    boolean updateProductivityData(@Param("map") Map<String, Object> productivityData);

    /**
     * 批量调整产值管理的数据 y6
     */
    boolean updateProductivityDatas(@Param("productivityDatas") List<Map<String, Object>> productivityDatas);

    /**
     * 调整分包商结算数据 y7
     */
    boolean updateSubSettlementData(@Param("map") Map<String, Object> subSettlement);

    /**
     * 调整产值管理的数据 y9
     */
    boolean updateYPTTSettlement(@Param("map") Map<String, Object> ypttSettlement);
    /**
     * 批量调整产值管理的数据 y9
     */
    boolean updateYPTTSettlements(@Param("YPTTSettlements") List<Map<String, Object>> YPTTSettlements);

    List<Map<String, Object>> selectSiteItem();

    List<Map<String, Object>> selectReportNoCondition();

    List<Map<String, Object>> selectPOItem();

    /**
     * 调整站点状态
     */
    boolean changeStatusSiteItem(@Param("id") Long id, @Param("status") String status);

    /**
     * 批量调整站点条目的数据 y1
     */
    boolean updateSiteItemDataes(@Param("siteItemList") List<Map<String, Object>> siteItemList);

    /**
     * 批量调整po条目的数据 y2
     */
    boolean updateSitePoDataes(@Param("poItemList") List<Map<String, Object>> poItemList);

    /**
     * 查询y2-PO_value ； y3 - E_ATP_Pass ； y6 - id
     */
    List<Map<String, Object>> selectKPIY2AndY3();

    Map<String, Object> selectSiteItemById(@Param("id") String id);

    Map<String, Object> selectSiteItemByPOItemId(@Param("id") String toString);

    Map<String, Object> selectSiteItemBySubItemId(@Param("id") String toString);

    Map<String, Object> selectPoItemById(@Param("id") String toString);
}
