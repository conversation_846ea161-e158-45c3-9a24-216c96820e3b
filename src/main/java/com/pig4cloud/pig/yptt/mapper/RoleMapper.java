package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.pig4cloud.pig.yptt.entity.ModuleRolePer;
import com.pig4cloud.pig.yptt.entity.SiteItem;
import com.pig4cloud.pig.yptt.entity.dto.ProjectRolePermissionMapDTO;
import com.pig4cloud.pig.yptt.entity.dto.ProjectStandingBookDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/31
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface RoleMapper {

	/**
	 * 获取成员id
	 * @param id
	 * @return
	 */
	Long getProjectIdByMemberId(@Param("id") Long id);

	/**
	 * 获取bi角色权限
	 * @param roleList
	 * @return
	 */
	List<String> getBiRole(@Param("list") List<String> roleList);

	/**
	 * 获取模型角色权限
	 * @param roleList
	 * @return
	 */
	List<ModuleRolePer> getRoleModuleList(@Param("list") List<String> roleList);

	/**
	 * 获取项目权限根据code
	 * @param ypttProjectCode
	 * @param userId
	 * @return
	 */
	List<ProjectRolePermissionMapDTO> queryProjectRolesByProjectCode(@Param("code") String ypttProjectCode,
			@Param("userId") Long userId);

	/**
	 * 获取项目权限根据id
	 * @param ypttProjectCode
	 * @param userId
	 * @return
	 */
	List<ProjectRolePermissionMapDTO> queryProjectRolesByProjectId(@Param("projectId") Long ypttProjectCode,
			@Param("userId") Long userId);

	/**
	 * 获取当前用户所有站点信息
	 * @param userId 用户id
	 * @return
	 */
	List<SiteItem> getSiteItem(@Param("userId") Long userId);

	/**
	 * 获取所有站点信息
	 * @return
	 */
	List<SiteItem> getAllSiteItems();

	/**
	 * 获取当前用户所有项目id
	 * @param userId 用户id
	 * @return
	 */
	List<ProjectStandingBookDTO> getProject(@Param("userId") Long userId, @Param("projectId") String projectId);

	/**
	 * 获取所有项目id
	 * @return
	 */
	List<ProjectStandingBookDTO> getAllProjectsById(@Param("id") String id);

	/**
	 * 根据id获取项目信息
	 * @param id
	 * @return
	 */
	Map<String, Object> getProjectById(@Param("id") Long id);

	/**
	 * 根据权限获取用户id
	 * @param perType
	 * @param code
	 * @return
	 */
	String getUserIdListByPerType(@Param("perType") String perType, @Param("code") String code);

	/**
	 * siteID获取projectCode
	 * @param siteId
	 * @return
	 */
	String getProjectCodeBySiteId(@Param("siteId") String siteId);

}
