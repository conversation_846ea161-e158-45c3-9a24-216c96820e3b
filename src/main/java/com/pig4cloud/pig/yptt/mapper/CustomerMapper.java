package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @ClassName: CustomerMapper
 * @Description: TODO
 * @Author: lijianpan
 * @CreateTime: 2025-08-26  15:12
 * @Version: 1.0
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface CustomerMapper {
    int isExistsCustomer(@Param("modelTable") String modelTable,@Param("name") String name);

    int updateProjectCustomerCodeStatus(@Param("modelTable") String modelTable,@Param("id") String id,@Param("company") String company);
}