package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.pig4cloud.pig.yptt.entity.MetaModeInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 视图模型关联
 * @author: lijianpan
 **/
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface ViewModelRel {
    /**
     * 获取模型名称
     * @param viewId 模型id
     * @return 模型名称
     **/
    String getModelNameByViewId(@Param("viewId") Long viewId);

    /**
     * 获取模型基本信息
     * @param modelName 模型名称
     * @return MetaModeInfo
     **/
    MetaModeInfo getModelTableNameByModelName(@Param("modelName") String modelName);
    
    /**
     * 获取模型关系物理表
     * @param leftModelId 左模型id
     * @param rightModelId 右模型id
     * @return 模型关系物理表
     **/
    String getModelRelTableNameByModelName(@Param("leftModelId") Long leftModelId, @Param("rightModelId") Long rightModelId);

    /**
     * 获取右模型表数据id
     * @param leftModelDataId 由左模型数据id
     * @param modelRelTableName 模型关系物理表
     * @return 右模型表数据id
     **/
    List<Long> getRightModelDataIdByLeftModelDataId(@Param("leftModelDataId") Long leftModelDataId, @Param("modelRelTableName") String modelRelTableName);

    /**
     * 获取左模型表数据id
     * @param rightModelDataId 由右模型数据id
     * @param modelRelTableName 模型关系物理表
     * @return 右模型表数据id
     **/
    List<Long> getLeftModelDataIdByRightModelDataId(@Param("rightModelDataId") Long rightModelDataId, @Param("modelRelTableName") String modelRelTableName);
}
