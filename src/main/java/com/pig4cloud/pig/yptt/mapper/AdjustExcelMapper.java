package com.pig4cloud.pig.yptt.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.me.api.dto.operation.QueryDTO;
import com.pig4cloud.pig.yptt.entity.PRDownloadEntity;
import com.pig4cloud.pig.yptt.entity.PRTotalEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface AdjustExcelMapper {


    List<Map<String, Object>> exportY2(@Param("map") HashMap<String, Object> mapCondition,
                                       @Param("roleList") List<Long> roleList, @Param("userId") Long userId, @Param("flag")  String flag);

    List<Map<String, Object>> exportY1(@Param("map") HashMap<String, Object> mapCondition,
                                       @Param("roleList") List<Long> roleList, @Param("userId") Long userId, @Param("flag")  String flag);

    List<Map<String, Object>> exportY4(@Param("map") HashMap<String, Object> mapCondition,
                                       @Param("roleList") List<Long> roleList, @Param("userId") Long userId, @Param("flag")  String flag);

    List<Map<String, Object>> exportPR(@Param("ids") List<String> ids, @Param("itemCode") String itemCode,
                                       @Param("phase") String phase, @Param("projectCode") String projectCode,
                                       @Param("region") String region, @Param("siteId") String siteId,
                                       @Param("unId") String unId);

    Page<Map<String, Object>> searchPR(Page<Map<String, Object>> pageMap, @Param("projectCode") String projectCode,
                                       @Param("region") String region, @Param("siteId") String siteId,
                                       @Param("itemCode") String itemCode, @Param("phase") String phase,
                                       @Param("unId") String unId);

    void insertPRDownLoad(@Param("pRDownLoadRecords") List<PRDownloadEntity> pRDownLoadRecords);

    Page<Map<String, Object>> searchDeatil(Page<Map<String, Object>> pageMap, @Param("ufId") String ufId);

    void insertPRTotal(@Param("prTotalEntity") PRTotalEntity prTotalEntity);

    String selectUserFullNameById(@Param("userId") Long userId);

    Page<Map<String, Object>> searchTotal(Page<Map<String, Object>> pageMap,@Param("projectCode") String projectCode,@Param("userId") Long userId);

    Page<Map<String, Object>> searchPRBaseData(Page<Map<String, Object>> pageMap,@Param("projectCode") String projectCode, @Param("region") String region,
                                               @Param("listSiteIds") List<String> listSiteIds, @Param("itemCode") String itemCode,
                                               @Param("phase") String phase, @Param("unId") String unId);

    List<Map<String, Object>> selectOtherInfoByUnIds(@Param("ufIds") List<String> ufIds);

    List<Map<String, Object>> searchPRBaseData2(@Param("ids") List<String> ids, @Param("itemCode") String itemCode,
                                                @Param("phase") String phase, @Param("projectCode") String projectCode,
                                                @Param("region") String region, @Param("listSiteIds") List<String> listSiteIds,
                                                @Param("unId") String unId);

    List<Map<String, Object>> selectDeptInfo(@Param("tenantId") Long tenantId);

    List<Map<String, Object>> exportY1V2(@Param("conditions")List<QueryDTO> conditions,
                                         @Param("roleList") List<Long> roleList, @Param("userId") Long userId, @Param("flag")  String flag,
                                         @Param("DATE_FILED_LIST") List<String> DATE_FILED_LIST);

    List<Map<String, Object>> exportY2V2(@Param("conditions") List<QueryDTO> conditions,
                                         @Param("roleList") List<Long> roleList, @Param("userId") Long userId,
                                         @Param("flag") String flag, @Param("DATE_FILED_LIST") List<String>  DATE_FILED_LIST);
}
