package com.pig4cloud.pig.yptt.mapper;

import com.pig4cloud.pig.yptt.entity.dto.PoStandingBookDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/18
 */
@Mapper
public interface PoStandingBookMapper {

	/**
	 * 更新
	 * @param dto
	 * @return
	 */
	int update(@Param("dto") PoStandingBookDTO dto);

	/**
	 * 获取信息
	 * @param cur
	 * @param size
	 * @return
	 */
	List<PoStandingBookDTO> generatePoStandingBookList(@Param("cur") int cur, @Param("size") int size);

}
