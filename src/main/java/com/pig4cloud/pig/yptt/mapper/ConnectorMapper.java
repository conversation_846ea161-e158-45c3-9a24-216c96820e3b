package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface ConnectorMapper {

	/**
	 * 收支统计更新
	 * @param incomeExpenditure 条件
	 * @param uniquenessId id
	 */
	void updateIncomeExpenditure(@Param("map") HashMap<String, Object> incomeExpenditure,
			@Param("uniquenessId") Long uniquenessId);

	/**
	 * 可结算更新
	 * @param readySettlement 条件
	 * @param uniquenessId id
	 */
	void updateReadySettlement(@Param("map") HashMap<String, Object> readySettlement,
			@Param("uniquenessId") Long uniquenessId);

	/**
	 * 根据唯一标识查询poItem
	 * @param uniquenessId
	 * @return
	 */
	Map<String, Object> getPoItemByUniquenessId(@Param("uniquenessId") Long uniquenessId);

	/**
	 * 根据唯一标识查询subconPoItem
	 * @param uniquenessId
	 * @return
	 */
	Map<String, Object> getSubconPoItemByUniquenessId(@Param("uniquenessId") Long uniquenessId);

	/**
	 * 根据唯一标识查询权限表
	 * @param uniquenessId
	 * @return
	 */
	Map<String, Object> getPermByUniquenessId(@Param("uniquenessId") Long uniquenessId);

	/**
	 * 产值报告更新
	 * @param productivityReport
	 * @param uniquenessId
	 */
	void updateProductivityReport(@Param("map") HashMap<String, Object> productivityReport,
			@Param("uniquenessId") Long uniquenessId);

	/**
	 * 分包商结算更新
	 * @param subconSettlement
	 * @param uniquenessId
	 */
	void updateSubconSettlement(@Param("map") HashMap<String, Object> subconSettlement,
			@Param("uniquenessId") Long uniquenessId);

	/**
	 * 根据唯一标识查询yptt
	 * @param unField
	 * @return
	 */
	Map<String, Object> getYpttSettlementByUniquenessId(@Param("uniquenessId") Long unField);

	/**
	 * 根据唯一标识查询subconPayment
	 * @param unField
	 * @return
	 */
	Map<String, Object> getSubconPaymentByUniquenessId(@Param("uniquenessId") Long unField);

	/**
	 * 更新yptt
	 * @param ypttSettlement
	 * @param uniquenessId
	 */
	void updateYpttSettlement(@Param("map") HashMap<String, Object> ypttSettlement,
			@Param("uniquenessId") Long uniquenessId);

	/**
	 * 更新subconPayment
	 * @param subconPayment
	 * @param uniquenessId
	 */
	void updateSubconPayment(@Param("map") HashMap<String, Object> subconPayment,
			@Param("uniquenessId") Long uniquenessId);

	/**
	 * 站点是否关闭
	 * @param uniquenessId
	 * @return
	 */
	Integer siteItemClosed(@Param("uniquenessId") Long uniquenessId);

	/**
	 * 关闭站点
	 * @param uniquenessId
	 * @return
	 */
	void closeSite(@Param("uniquenessId") Long uniquenessId);

}
