package com.pig4cloud.pig.yptt.mapper;

import com.pig4cloud.pig.yptt.entity.dto.PoStandingBookDTO;
import com.pig4cloud.pig.yptt.entity.dto.SiteStandingBookDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/18
 */
@Mapper
public interface SiteStandingBookMapper {

	/**
	 * 更新
	 * @param dto
	 * @return
	 */
	int update(@Param("dto") SiteStandingBookDTO dto);

	/**
	 * 获取信息
	 * @param cur
	 * @param size
	 * @return
	 */
	List<SiteStandingBookDTO> generateSiteStandingBookList(@Param("cur") int cur, @Param("size") int size);

}
