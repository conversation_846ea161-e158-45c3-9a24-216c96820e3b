package com.pig4cloud.pig.yptt.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.yptt.entity.PRDownloadEntity;
import com.pig4cloud.pig.yptt.entity.PRTotalEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface MoneyExchangeMapper {

    List<Map<String, Object>> selectByParis(@Param("moneyInput") String moneyInput, @Param("moneyOutput") String moneyOutput);
}
