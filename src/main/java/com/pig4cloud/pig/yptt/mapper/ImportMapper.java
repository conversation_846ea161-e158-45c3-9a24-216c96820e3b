package com.pig4cloud.pig.yptt.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.pig4cloud.pig.yptt.entity.ImportRecord;
import com.pig4cloud.pig.yptt.entity.StatusEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/24
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface ImportMapper {

	/**
	 * 批量新增
	 * @param dataList 数据
	 * @param type 类型
	 */
	void batchInsert(@Param("dataList") List<Map<String, Object>> dataList, @Param("type") String type);

	/**
	 * 批量更新与主表的关系
	 * @param ids ids
	 * @param mainId 主数据
	 * @param type 类型
	 */
	void batchInsertRel(@Param("ids") List<Long> ids, @Param("mainId") Long mainId, @Param("type") String type);

	/**
	 * 查询子数据
	 * @param mainId 主数据
	 * @param type 类型
	 * @return map
	 */
	List<Map<String, Object>> selectRelData(@Param("mainId") Long mainId, @Param("type") String type);

	/**
	 * 查询导入主数据
	 * @param id id
	 * @param type type
	 * @return map
	 */
	Map<String, Object> selectImport(@Param("id") Long id, @Param("type") String type);

	/**
	 * 更新导入主数据
	 * @param map 数据
	 * @param type 类型
	 * @param id id
	 */
	void updateImport(@Param("map") Map<String, Object> map, @Param("type") String type, @Param("id") Long id);

	/**
	 * 更新导入item数据
	 * @param maps 数据
	 * @param type 类型
	 */
	void updateImportItem(@Param("maps") List<StatusEntity> maps, @Param("type") String type);

	/**
	 * 更新额外成本金额
	 * @param resTotal 额外金额
	 * @param id id
	 * @return int
	 */
	int updateSubconPoExternalCost(@Param("total") BigDecimal resTotal, @Param("id") Long id);

	/**
	 * 查询未完成对导入任务
	 * @param type
	 * @return
	 */
	List<Long> selectUnfinishedImportDataId(@Param("type") String type);

	/**
	 * 查询未完成对导入任务的用户id
	 * @param type
	 * @param id
	 * @return userid
	 */
	Long selectUnfinishedImportUserId(@Param("type") String type, @Param("id") Long id);

	/**
	 * 根据mainId查询统计条数
	 * @param type
	 * @param id
	 * @return
	 */
	ImportRecord selectItemNumByMainId(@Param("type") String type, @Param("mainId") Long id);

	/**
	 * 查询额外成本
	 * @param rootId
	 * @return
	 */
	List<Map<String, Object>> selectSubconPoExternalCost(@Param("rootId") Long rootId);


	/**
	 * 查询y2的金额
	 * @param
	 * @return
	 */
	Map<String, Object> selectY2Amount(@Param("unId") String unId);

	/**
	 * 更新y6 数据
	 * @param
	 * @return
	 */
	void updateY6(@Param("eATPPass") DateTime eATPPass, @Param("po_value") BigDecimal po_value, @Param("id")Long id);

	/**
	 * 批量更新y6 数据
	 * @param
	 * @return
	 */
//	void updateY6(@Param("mapsY6") Map<String, String> mapsY6);
}
