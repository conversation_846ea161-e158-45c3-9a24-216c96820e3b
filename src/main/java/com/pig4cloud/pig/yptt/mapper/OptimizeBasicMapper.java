package com.pig4cloud.pig.yptt.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.yptt.entity.StatisticsUserOperate;
import com.pig4cloud.pig.yptt.entity.vo.GetProject;
import com.pig4cloud.pig.yptt.entity.vo.GetUserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface OptimizeBasicMapper {
    /**
     * 查询报表所需的基础数据 (uf, YPTTProject, site, dept 等)。
     *
     * @param projectIds   需要查询的项目 ID 集合。
     * @param dateStrStart 查询起始日期。
     * @param dateStrEnd   查询结束日期。
     * @param dateType     日期过滤类型，用于 SQL 判断具体过滤哪个日期字段。
     * @param area         区域过滤条件。
     * @param nation       国家/地区过滤条件。
     * @return 包含基础数据的 Map 列表，每个 Map 包含 ufId, siteUn 及其他基础字段。
     */
    List<Map<String, Object>> getBaseReportData(
                                                @Param("dateStrStart") String dateStrStart,
                                                @Param("dateStrEnd") String dateStrEnd,
                                                @Param("dateType") String dateType,
                                                @Param("area") String area,
                                                @Param("nation") String nation,
                                                @Param("projectIds") List<String> projectIds,
                                                @Param("listUnIds") List<String> listUnIds);

    /**
     * 根据 uf ID 列表查询 SiteItem (Y1) 相关数据。
     *
     * @param ufIds uf 表的主键 ID 列表。
     * @return 包含 SiteItem 数据的 Map 列表，每个 Map 必须包含能关联回基础数据的键 (如 siteUn)。
     */
    List<Map<String, Object>> getSiteItemDataForReport(@Param("ufIds") List<String> ufIds);

    /**
     * 根据 uf ID 列表查询 PO (Y2) 相关数据 (包括 poItem, po, cusProject)。
     *
     * @param ufIds uf 表的主键 ID 列表。
     * @return 包含 PO 相关数据的 Map 列表，每个 Map 必须包含能关联回基础数据的键 (如 siteUn)。
     */
    List<Map<String, Object>> getPODataForReport(@Param("ufIds") List<String> ufIds);

    /**
     * 根据 uniqueness_field 列表查询 SiteDelivery (Y3) 相关数据。
     *
     * @param uniqueFields uniqueness_field 列表 (通常来自 uf.uniqueness_field)。
     * @return 包含 SiteDelivery 数据的 Map 列表，每个 Map 必须包含 'siteUn' 键。
     */
    List<Map<String, Object>> getSiteDeliveryDataForReport(@Param("uniqueFields") List<String> uniqueFields);

    /**
     * 根据 uniqueness_field 列表查询 Subcontractor (Y4) 相关数据 (包括 subItem, subPo, sub)。
     *
     * @param uniqueFields uniqueness_field 列表。
     * @return 包含 Subcontractor 数据的 Map 列表，每个 Map 必须包含 'siteUn' 键。
     */
    List<Map<String, Object>> getSubcontractorDataForReport(@Param("uniqueFields") List<String> uniqueFields);

    /**
     * 根据 uniqueness_field 列表查询 ReadySettlement (Y5) 相关数据。
     *
     * @param uniqueFields uniqueness_field 列表。
     * @return 包含 ReadySettlement 数据的 Map 列表，每个 Map 必须包含 'siteUn' 键。
     */
    List<Map<String, Object>> getReadySettlementDataForReport(@Param("uniqueFields") List<String> uniqueFields);

    /**
     * 根据 uniqueness_field 列表查询 ProductivityReport (Y6) 相关数据。
     *
     * @param uniqueFields uniqueness_field 列表。
     * @return 包含 ProductivityReport 数据的 Map 列表，每个 Map 必须包含 'siteUn' 键。
     */
    List<Map<String, Object>> getProductivityDataForReport(@Param("uniqueFields") List<String> uniqueFields);

    /**
     * 根据 uniqueness_field 列表查询 SubconSettlement (Y7) 相关数据。
     *
     * @param uniqueFields uniqueness_field 列表。
     * @return 包含 SubconSettlement 数据的 Map 列表，每个 Map 必须包含 'siteUn' 键。
     */
    List<Map<String, Object>> getSubconSettlementDataForReport(@Param("uniqueFields") List<String> uniqueFields);

    /**
     * 根据 uniqueness_field 列表查询 SubconPayment (Y8) 相关数据。
     *
     * @param uniqueFields uniqueness_field 列表。
     * @return 包含 SubconPayment 数据的 Map 列表，每个 Map 必须包含 'siteUn' 键。
     */
    List<Map<String, Object>> getSubconPaymentDataForReport(@Param("uniqueFields") List<String> uniqueFields);

    /**
     * 根据 uniqueness_field 列表查询 YPTTSettlement (Y9) 相关数据。
     *
     * @param uniqueFields uniqueness_field 列表。
     * @return 包含 YPTTSettlement 数据的 Map 列表，每个 Map 必须包含 'siteUn' 键。
     */
    List<Map<String, Object>> getYPTTSettlementDataForReport(@Param("uniqueFields") List<String> uniqueFields);

    List<Map<String, Object>> getBaseReportDataReportForm(@Param("dateStrStart") String dateStrStart,
                                                          @Param("dateStrEnd") String dateStrEnd,
                                                          @Param("dateType") String dateType,
                                                          @Param("area") String area,
                                                          @Param("nation") String nation,
                                                          @Param("projectIds") List<String> projectIds);
}
