package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.pig4cloud.pig.yptt.entity.PendingPaymentBill;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: PendingPaymentBillMapper
 * @author: lijianpan
 **/
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface PendingPaymentBillMapper {
    /**
     * 新增
     **/
    Integer add(@Param("modelTable") String modelTable,@Param("list") List<PendingPaymentBill> PendingPaymentBills);
}
