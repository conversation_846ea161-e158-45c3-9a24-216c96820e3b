package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface AccountsSuspenseMpper {
    BigDecimal selectUnpaidMoney(@Param("modelTable") String modelTable, @Param("dataId") Long dataId);

    boolean updateUnpaidMoney(@Param("modelTable") String modelTable, @Param("dataId") Long dataId, @Param("payMoney") BigDecimal payMoney);
}
