package com.pig4cloud.pig.yptt.mapper;

import com.pig4cloud.pig.yptt.entity.dto.SubconStandingBookDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/17
 */
@Mapper
public interface SubconStandingBookMapper {

	/**
	 * 生成分包商台账
	 * @param cur 当前页
	 * @param size 页容量
	 * @return 生成分包商台账列表
	 */
	List<SubconStandingBookDTO> generateSubconStandingBookList(@Param("cur") int cur, @Param("size") int size);

	/**
	 * 更新分包商台账
	 * @param generatedSubconStandingBookDTO 分包商台账
	 * @return 更新数量
	 */
	int update(@Param("dto") SubconStandingBookDTO generatedSubconStandingBookDTO);

}
