package com.pig4cloud.pig.yptt.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pig.admin.api.dto.UserInfo;
import com.pig4cloud.pig.yptt.entity.StatisticsUserOperate;
import com.pig4cloud.pig.yptt.entity.WarningMessage;
import com.pig4cloud.pig.yptt.entity.vo.GetProject;
import com.pig4cloud.pig.yptt.entity.vo.GetUserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface ReportMapper {

    //统计用户y1操作数量
    List<StatisticsUserOperate> statisticsUserOperateY1(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                        @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<StatisticsUserOperate> statisticsUserOperateY2(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                        @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<StatisticsUserOperate> statisticsUserOperateY3(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                        @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<StatisticsUserOperate> statisticsUserOperateY4(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                        @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<StatisticsUserOperate> statisticsUserOperateY8(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                        @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<StatisticsUserOperate> statisticsUserOperateY9(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                        @Param("projectCode") String projectCode, @Param("userId") Long userId);

    IPage<GetUserInfo> getUserInfo(@Param("page") Page<GetUserInfo> page, @Param("name") String name, @Param("tenantId") Long tenantId);

    IPage<GetProject> getProject(@Param("page") Page<GetProject> of, @Param("name") String name);

    List<Map<String, Object>> statisticsUserOperateY1V2(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                          @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<Map<String, Object>> statisticsUserOperateY2V2(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                    @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<Map<String, Object>> statisticsUserOperateY4V2(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                    @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<Map<String, Object>> statisticsUserOperateY3V2(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                                        @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<Map<String, Object>> statisticsUserOperateY8V2(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                      @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<Map<String, Object>> statisticsUserOperateY9V2(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                      @Param("projectCode") String projectCode, @Param("userId") Long userId);

    List<Map<String, Object>> getAllUserInfo();

    List<Map<String, Object>> selectProjects();
}
