package com.pig4cloud.pig.yptt.mapper;

import com.pig4cloud.pig.yptt.entity.dto.ProjectStandingBookDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/31
 */
@Mapper
public interface ProjectStandingBookMapper {

	/**
	 * generateProjectStandingBookList
	 * @param cur
	 * @param size
	 * @return
	 */
	List<ProjectStandingBookDTO> generateProjectStandingBookList(@Param("cur") int cur, @Param("size") int size);

	/**
	 * 通过项目id查询
	 * @param projectId
	 * @return
	 */
	List<ProjectStandingBookDTO> queryByProjectId(@Param("projectId") Long projectId);

	/**
	 * 更新
	 * @param insertDTO
	 * @return
	 */
	Integer update(@Param("dto") ProjectStandingBookDTO insertDTO);

	/**
	 * 保存
	 * @param insertDTO
	 * @return
	 */
	Integer save(@Param("dto") ProjectStandingBookDTO insertDTO);

}
