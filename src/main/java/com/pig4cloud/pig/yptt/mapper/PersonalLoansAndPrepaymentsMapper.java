package com.pig4cloud.pig.yptt.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface PersonalLoansAndPrepaymentsMapper {
    boolean updateSYWH(@Param("modelTable") String modelTable,@Param("dataId") Long dataId,@Param("money") BigDecimal money);
}
