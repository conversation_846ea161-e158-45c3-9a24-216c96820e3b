#server:
#  port: 20001
#
#spring:
#  servlet:
#    multipart:
#      max-file-size: 200MB
#  application:
#    name: @artifactId@
#  cloud:
#    nacos:
#      username: ${NACOS_USERNAME:nacos}
#      password: ${NACOS_PASSWORD:nacos}
#      discovery:
#        server-addr: ${NACOS_HOST:pig-register}:${NACOS_PORT:8848}
#      config:
#        server-addr: ${spring.cloud.nacos.discovery.server-addr}
#  config:
#    import:
#    - nacos:<EMAIL>@.yml
#    - nacos:${spring.application.name}-@profiles.active@.yml
#
#logging:
#  level:
#    root: info
#
#
#mybatis-plus:
#  configuration:
#    call-setters-on-nulls: true
#
#me-app:
#  ignore-tables:
#    - me_app
#    - sys_user
#    - sys_dept
#    - prefix:me_
#    - prefix:memm_
#
#tenant-id:
#  ignore-tables:
#    - sys_user
#    - prefix:ACT_
#    - prefix:memm_
#    -
#feign:
#  client:
#    config:
#      default:
#        connectTimeout: 300000
#        readTimeout: 300000
#
#yptt:
#  schedule:
#    warningInfoUpdateTask: "-"
#    standingBookUpdateTask: "-"
#    importTask: "-"
#    updateSiteState: "-"
#    incomeCorrectionTask: "-"
#    clearWarningInfo: "-"

#
#server:
#  port: 20001
#
#spring:
#  servlet:
#    multipart:
#      max-file-size: 200MB
#  application:
#    name: @artifactId@
#  cloud:
#    nacos:
#      username: ${NACOS_USERNAME:nacos}
#      password: ${NACOS_PASSWORD:nacos}
#      discovery:
#        server-addr: ${NACOS_HOST:pig-register}:${NACOS_PORT:8848}
#      config:
#        server-addr: ${spring.cloud.nacos.discovery.server-addr}
#  config:
#    import:
#    - nacos:<EMAIL>@.yml
#    - nacos:${spring.application.name}-@profiles.active@.yml
#
#logging:
#  level:
#    root: info
#
#
#mybatis-plus:
#  configuration:
#    call-setters-on-nulls: true
#
#me-app:
#  ignore-tables:
#    - me_app
#    - sys_user
#    - sys_dept
#    - prefix:me_
#    - prefix:memm_
#
#tenant-id:
#  ignore-tables:
#    - sys_user
#    - prefix:ACT_
#    - prefix:memm_
#    -
#feign:
#  client:
#    config:
#      default:
#        connectTimeout: 300000
#        readTimeout: 300000
#
#yptt:
#  schedule:
#    warningInfoUpdateTask: "-"
#    standingBookUpdateTask: "-"
#    importTask: "-"
#    updateSiteState: "-"
#    incomeCorrectionTask: "-"
#    clearWarningInfo: "-"

server:
  port: 20001

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
      discovery:
        server-addr: 127.0.0.1:8848
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        import-check:
          enabled: false
  #    service-registry:
  #      auto-registration:
  #        enabled: false
  config:
    import:
  #    - nacos:<EMAIL>@.yml
  #    - nacos:${spring.application.name}-@profiles.active@.yml
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    #    url: ******************************************************************************************************************************************************************************************************************************
    url: ******************************************************************************************************************************************************************************************************************************
    username: root
    password: root
  redis:
    host: *************
    port: 20115
  #    port: 20330
  cache:
    type: redis
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

seata:
  enabled: false

logging:
  level:
    com.pig4cloud.pig.yptt.service.dataconverter: trace

#logging:
#  level:
#    root: info
#    com.pig4cloud.pig.yptt.service.transform: trace
#    com.pig4cloud.pig.yptt.service.DataMangeService: trace
#
#
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    call-setters-on-nulls: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
