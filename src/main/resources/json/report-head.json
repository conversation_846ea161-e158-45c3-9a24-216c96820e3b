[{"groupName": "Y1", "keys": [{"label": "Site register date", "value": "Site_register_date"}, {"label": "Site Status", "value": "Site_item_status"}, {"label": "Department", "value": "name"}, {"label": "YPTT Project name", "value": "YPTT_Project_name"}, {"label": "Region", "value": "Region"}, {"label": "Area", "value": "Area"}, {"label": "Site ID", "value": "Site_Serial_number"}, {"label": "Site Name", "value": "site_name"}, {"label": "site allocation date", "value": "site_allocation_date"}, {"label": "Phase", "value": "Phase"}, {"label": "type of service", "value": "Type_of_service"}, {"label": "site model", "value": "Site_model"}, {"label": "item code", "value": "Item_code"}, {"label": "BOQ item", "value": "BOQ_item"}, {"label": "Quantity", "value": "Quantity"}, {"label": "item value", "value": "Site_value"}, {"label": "Remark", "value": "siteItemRemark"}, {"label": "Identification field of uniqueness", "value": "siteUn"}]}, {"groupName": "Y2", "keys": [{"label": "<PERSON><PERSON> Received-date", "value": "PO_Received_date"}, {"label": "PO Number", "value": "PO_number"}, {"label": "Contract number", "value": "Contract_number"}, {"label": "Customer project name", "value": "Custom_project_name"}, {"label": "YPTT Project code", "value": "poItemProjectcode"}, {"label": "Region", "value": "poItemRegion"}, {"label": "Phase", "value": "poItemPhase"}, {"label": "Site ID", "value": "poItemSiteID"}, {"label": "Site Name", "value": "poItemSiteName"}, {"label": "item code", "value": "poItemcode"}, {"label": "BOQ item", "value": "po_BOQ_item"}, {"label": "Quantity", "value": "poItemQuantity"}, {"label": "Quantity Adjust", "value": "poItemQuantityReduce"}, {"label": "Unit price", "value": "poUp"}, {"label": "PO value", "value": "PO_value"}, {"label": "PO gap", "value": "PO_gap"}, {"label": "Pre payment milestone", "value": "Pre_payment"}, {"label": "Site settlement Milestone 1st %", "value": "SiteSettlementMilestone1st%"}, {"label": "Site settlement Milestone 2nd %", "value": "SiteSettlementMilestone2nd%"}, {"label": "Site settlement Milestone 3rd %", "value": "SiteSettlementMilestone3rd%"}, {"label": "Site settlement Milestone 4th %", "value": "SiteSettlementMilestone4th%"}, {"label": "Remark", "value": "poItemRemark"}, {"label": "Identification field of uniqueness", "value": "poItemUn"}]}, {"groupName": "Y3", "keys": [{"label": "Site belong to", "value": "Site_belong_to"}, {"label": "Team leader/DT", "value": "Team_Leader_DT"}, {"label": "Site engineer/DTA/SPV", "value": "engineer_DTA_SPV"}, {"label": "PLO/PC/其他", "value": "PLO_PC_Others"}, {"label": "PIC/PC/PM", "value": "PIC_PC_PM"}, {"label": "A-Start Working date（Mos/DT/HI）", "value": "Start_Working_date"}, {"label": "B-Completed work date（ HI done/DT done/Delivered）", "value": "Completed_work_date"}, {"label": "C-On air/CI/Report submit", "value": "air_CI_Report_submit"}, {"label": "D-L1/Site manager/Report approved by 1st level", "value": "Site_manager_Report"}, {"label": "E-ATP Pass(Approval by customer）", "value": "E_ATP_Pass"}, {"label": "F-PAC pass", "value": "F_PAC_Pass"}, {"label": "G-FAC", "value": "G_FAC"}, {"label": "Remark", "value": "deliveryRemark"}, {"label": "Ready for settlement- 1st", "value": "site-ReadyForSettlement-1st"}, {"label": "Ready for settlement- 2nd", "value": "site-ReadyForSettlement-2nd"}, {"label": "Ready for settlement- 3rd", "value": "site-ReadyForSettlement-3rd"}, {"label": "Ready for settlement- 4th", "value": "site-ReadyForSettlement-4th"}, {"label": "Subcon-settlement date-1st", "value": "site-Subcon-settlementDate-1st"}, {"label": "Subcon-settlement date-2nd", "value": "site-Subcon-settlementDate-2nd"}, {"label": "Subcon-settlement date-3rd", "value": "site-Subcon-settlementDate-3rd"}, {"label": "Subcon-settlement date-4th ", "value": "site-Subcon-settlementDate-4th"}]}, {"groupName": "Y4", "keys": [{"label": "subcon name", "value": "Subcon_name"}, {"label": "Subcon-PO number", "value": "Subcon_PO_number"}, {"label": "Subcon-PO release date", "value": "release_date"}, {"label": "Site ID", "value": "subSiteID"}, {"label": "Site Name", "value": "subSiteName"}, {"label": "Item code", "value": "subItemcode"}, {"label": "BOQ Item", "value": "subBOQitem"}, {"label": "quantity", "value": "subQuantity"}, {"label": "Quantity Adjust", "value": "subQuantityReduce"}, {"label": "Unit price", "value": "subUp"}, {"label": "Subcon-PO amount", "value": "Subcon_PO_amount"}, {"label": "Subcon settlement Milestone 1st %", "value": "SubconSettlementMilestone1st"}, {"label": "Subcon settlement Milestone 2nd %", "value": "SubconSettlementMilestone2nd"}, {"label": "Subcon settlement Milestone 3rd %", "value": "SubconSettlementMilestone3rd"}, {"label": "Subcon settlement Milestone 4th %", "value": "SubconSettlementMilestone4th"}, {"label": "Subcon Additional cost", "value": "additional_cost"}, {"label": "Remark", "value": "subRemark"}, {"label": "Identification field of uniqueness", "value": "subconUniquenessField"}]}, {"groupName": "Y5", "keys": [{"label": "Pre payment", "value": "Pre_Settlement_date"}, {"label": "Pre payment %", "value": "Pre_payment_ratio"}, {"label": "Pre payment amount", "value": "Pre_payment_amount"}, {"label": "Ready for settlement- 1st", "value": "ReadyForSettlement-1st"}, {"label": "settlement %- 1st", "value": "settlement%-1st"}, {"label": "settlement amount- 1st", "value": "settlementAmount-1st"}, {"label": "Ready for settlement- 2nd", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-2nd"}, {"label": "settlement %- 2nd", "value": "settlement%-2nd"}, {"label": "settlement amount- 2nd", "value": "settlementAmount-2nd"}, {"label": "Ready for settlement- 3rd", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-3rd"}, {"label": "settlement %- 3rd", "value": "settlement%-3rd"}, {"label": "settlement amount- 3rd", "value": "settlementAmount-3rd"}, {"label": "Ready for settlement- 4th", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-4th"}, {"label": "settlement %- 4th", "value": "settlement%-4th"}, {"label": "settlement amount- 4th", "value": "settlementAmount-4th"}, {"label": "Ready for settlement Amount", "value": "ReadySettlementAmount"}, {"label": "NY ready for settlement amount（Deducation or Job OG)", "value": "readySettlementAmountGap"}]}, {"groupName": "Y6", "keys": [{"label": "1st Productivity report date", "value": "1stProductivityReportDate"}, {"label": "1st Productivity report amount", "value": "1stProductivityReportAmount"}, {"label": "2nd Productivity report date", "value": "2ndProductivityReportDate"}, {"label": "2nd Productivity report amount", "value": "2ndProductivityReportAmount"}, {"label": "3rd Productivity report date", "value": "3rdProductivityReportDate"}, {"label": "3rd Productivity report amount", "value": "3rdProductivityReportAmount"}, {"label": "4th Productivity report date", "value": "4thProductivityReportDate"}, {"label": "4th Productivity report amount", "value": "4thProductivityReportAmount"}, {"label": "Productivity Amount of already reportted", "value": "ProductivityAmount"}, {"label": "Productivity declaration ratio%", "value": "ProductivityDeclarationRatio%"}, {"label": "KPI Archive date", "value": "KPI-Archive-date"}, {"label": "KPI Archive amount", "value": "KPI-Archive-amount"}]}, {"groupName": "Y7", "keys": [{"label": "Subcon-settlement time-1st ", "value": "Subcon-settlement-time-1st"}, {"label": "Subcon-settlement %-1st ", "value": "Subcon-settlement%-1st"}, {"label": "Subcon-settlement amount-1st ", "value": "Subcon-settlement-amount-1st"}, {"label": "Subcon-settlement time-2nd", "value": "Subcon-settlement-time-2nd"}, {"label": "Subcon-settlement %-2nd", "value": "Subcon-settlement%-2nd"}, {"label": "Subcon-settlement amount-2nd ", "value": "Subcon-settlement-amount-2nd"}, {"label": "Subcon-settlement time-3rd", "value": "Subcon-settlement-time-3rd"}, {"label": "Subcon-settlement %-3rd", "value": "Subcon-settlement%-3rd"}, {"label": "Subcon-settlement amount-3rd", "value": "Subcon-settlement-amount-3rd"}, {"label": "Subcon-settlement time-4th", "value": "Subcon-settlement-time-4th"}, {"label": "Subcon-settlement %-4st", "value": "Subcon-settlement%-4st"}, {"label": "Subcon-settlement amount-4th", "value": "Subcon-settlement-amount-4th"}, {"label": "Subcon-Totally settlement amount", "value": "SubconTotallySettlementAmount"}, {"label": "Subcon-Totally settlement Gap", "value": "SubconTotallySettlementGap"}]}, {"groupName": "Y8", "keys": [{"label": "Subcon Payment Time-1st", "value": "Subcon-Payment-time-1st"}, {"label": "Subcon Payment Amount-1st", "value": "Subcon-payment-amount-1st"}, {"label": "Subcon Invoice Number-1st", "value": "Subcon-payment-number-1st"}, {"label": "Subcon Payment Time-2nd", "value": "Subcon-Payment-time-2nd"}, {"label": "Subcon Payment Amount-2nd", "value": "Subcon-payment-amount-2nd"}, {"label": "Subcon Invoice Number-2nd", "value": "Subcon-payment-number-2st"}, {"label": "Subcon Payment Time-3rd", "value": "Subcon-Payment-time-3rd"}, {"label": "Subcon Payment Amount-3rd", "value": "Subcon-payment-amount-3rd"}, {"label": "Subcon Invoice Number-3rd", "value": "Subcon-payment-number-3st"}, {"label": "Subcon Payment Time-4th", "value": "Subcon-Payment-time-4st"}, {"label": "Subcon Payment Amount-4th", "value": "Subcon-payment-amount-4st"}, {"label": "Subcon Invoice Number-4th", "value": "Subcon-payment-number-4st"}, {"label": "Remark", "value": "subPayRemark"}, {"label": "Subcon-Totally payment amount", "value": "SubconTotallyPaymentAmount"}, {"label": "Subcon-Totally payment amount-gap", "value": "SubconTotallyPaymentAmountGap"}]}, {"groupName": "Y9", "keys": [{"label": "Invoice date-1st", "value": "Invoice-date-1st"}, {"label": "Invoice number-1st", "value": "Invoice-number-1st"}, {"label": "Invoice Amount-1st", "value": "Invoice-Amount-1st"}, {"label": "Invoice Remark-1st", "value": "Invoice-remark-1st"}, {"label": "Invoice Amount-Diff-1st", "value": "Invoice-Amount-Diff-1st"}, {"label": "Invoice date-2nd", "value": "Invoice-date-2nd"}, {"label": "Invoice number-2nd", "value": "Invoice-number-2nd"}, {"label": "Invoice Amount-2nd", "value": "Invoice-Amount-2nd"}, {"label": "Invoice Remark-2nd", "value": "Invoice-remark-2nd"}, {"label": "Invoice Amount-Diff-2nd", "value": "Invoice-Amount-Diff-2nd"}, {"label": "Invoice date-3rd", "value": "Invoice-date-3rd"}, {"label": "Invoice number-3rd", "value": "Invoice-number-3rd"}, {"label": "Invoice Amount-3rd", "value": "Invoice-Amount-3rd"}, {"label": "Invoice Remark-3rd", "value": "Invoice-remark-3rd"}, {"label": "Invoice Amount-Diff-3srd", "value": "Invoice-Amount-Diff-3rd"}, {"label": "Invoice date-4th", "value": "Invoice-date-4th"}, {"label": "Invoice number-4th", "value": "Invoice-number-4th"}, {"label": "Invoice Amount-4th", "value": "Invoice-Amount-4th"}, {"label": "Invoice Remark-4th", "value": "Invoice-remark-4th"}, {"label": "Invoice Amount-Diff-4th", "value": "Invoice-Amount-Diff-4th"}, {"label": "Remark", "value": "ypttRemark"}, {"label": "Totally Invoice amount", "value": "TotallyInvoiceAmount"}, {"label": "Invoice amount-GAP", "value": "InvoiceAmountGAP"}]}]