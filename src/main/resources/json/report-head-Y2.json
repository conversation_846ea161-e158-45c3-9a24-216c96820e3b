[{"groupName": "Y2", "keys": [{"label": "<PERSON><PERSON> Received-date", "value": "PO_Received_date"}, {"label": "PO Number", "value": "PO_number"}, {"label": "Contract number", "value": "Contract_number"}, {"label": "Customer project name", "value": "Custom_project_name"}, {"label": "YPTT Project code", "value": "poItemProjectcode"}, {"label": "Region", "value": "poItemRegion"}, {"label": "Phase", "value": "poItemPhase"}, {"label": "Site ID", "value": "poItemSiteID"}, {"label": "Site Name", "value": "poItemSiteName"}, {"label": "item code", "value": "poItemcode"}, {"label": "BOQ item", "value": "po_BOQ_item"}, {"label": "Quantity", "value": "poItemQuantity"}, {"label": "Quantity Adjust", "value": "poItemQuantityReduce"}, {"label": "Unit price", "value": "poUp"}, {"label": "PO value", "value": "PO_value"}, {"label": "PO gap", "value": "PO_gap"}, {"label": "Pre payment milestone", "value": "Pre_payment"}, {"label": "Site settlement Milestone 1st %", "value": "SiteSettlementMilestone1st%"}, {"label": "Site settlement Milestone 2nd %", "value": "SiteSettlementMilestone2nd%"}, {"label": "Site settlement Milestone 3rd %", "value": "SiteSettlementMilestone3rd%"}, {"label": "Site settlement Milestone 4th %", "value": "SiteSettlementMilestone4th%"}, {"label": "Remark", "value": "poItemRemark"}, {"label": "Identification field of uniqueness", "value": "poItemUn"}]}]