<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.CustomerProjectStandingBookMapper">
    <select id="generateCustomerProjectStandingBookList" resultType="com.pig4cloud.pig.yptt.entity.dto.CustomerProjectStandingBookDTO">
        SELECT customer_project.id                                AS customer_project_id,
               customer_project.Contract_number                   AS contract_number,
               customer_project.Custom_project_name               AS custom_project_name,
               SUM(IFNULL(yptt_settlement.Invoice_amount, 0))     AS invoice_amount,
               SUM(IFNULL(yptt_settlement.Invoice_amount_gap, 0)) AS invoice_amount_gap,
               COUNT(DISTINCT (po.id))                            AS po_count,
               COUNT(po_item.id)                                  AS po_item_count
        FROM memm_f15b45017dee432daf88693b3d13b60b AS customer_project
                 LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c po_item
                           ON po_item.is_deleted = 0
                               AND customer_project.id = JSON_UNQUOTE(po_item.Customer_project -> '$[0]')
                 LEFT JOIN memm_PO_PO_Item_mdr_l096d po2poItem
                           ON po2poItem.is_deleted = 0 AND po_item.id = po2poItem.right_data_id
                 LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
                           ON po.is_deleted = 0. AND po2poItem.left_data_id = po.id
                 LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 yptt_settlement
                           ON yptt_settlement.is_deleted = 0
                               AND po_item.uniqueness_field = yptt_settlement.uniqueness_field
        WHERE customer_project.is_deleted = 0
        GROUP BY customer_project.id
        LIMIT #{cur}, #{size}
    </select>


    <update id="update">
        UPDATE
            memm_f15b45017dee432daf88693b3d13b60b AS customer_project
        SET invoice_amount     = #{dto.invoiceAmount, jdbcType=DECIMAL},
            invoice_amount_gap = #{dto.invoiceAmountGap, jdbcType=DECIMAL},
            po_count           = #{dto.poCount, jdbcType=INTEGER},
            po_item_count      = #{dto.poItemCount, jdbcType=INTEGER}
        WHERE id = #{dto.customerProjectId, jdbcType=BIGINT}
    </update>

</mapper>
