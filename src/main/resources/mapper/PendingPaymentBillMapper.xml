<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.PendingPaymentBillMapper">

    <insert id="add">
        insert into ${modelTable} (
        id, code, departmentid, yjxmmc, yjxmbh, account, bank, bank_num, pay_money,
        pay_money_leave, bz, sqr, sqrq, company, fysy, fybm,zflx
        , create_by, create_time, update_by, update_time, is_deleted,invoice,reim_type
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.code},
            #{item.departmentid},
            #{item.yjxmmc},
            #{item.yjxmbh},
            #{item.account},
            #{item.bank},
            #{item.bankNum},
            #{item.payMoney},
            #{item.payMoneyLeave},
            #{item.bz},
            #{item.sqr},
            #{item.sqrq},
            #{item.company},
            #{item.fysy},
            #{item.fybm},
            #{item.zflx},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            0,
            #{item.invoice},
            #{item.reimType}
            )
        </foreach>
    </insert>
</mapper>