<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.SiteStandingBookMapper">
    <select id="generateSiteStandingBookList" resultType="com.pig4cloud.pig.yptt.entity.dto.SiteStandingBookDTO">
        SELECT site.id                             AS siteId,
               SUM(IFNULL(siteItem.Site_value, 0)) AS totalPrice,
               COUNT(DISTINCT siteItem.id)                  AS itemQuantity
        FROM memm_448208a319fa4d7ab3d77ee54e10c066 AS site
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b AS siteItem
                           ON siteItem.is_deleted = 0. AND site.id = JSON_UNQUOTE(siteItem.site -> '$[0]')
        WHERE site.is_deleted = 0
        GROUP BY site.id
        LIMIT #{cur}, #{size}
    </select>


    <update id="update">
        UPDATE
            memm_448208a319fa4d7ab3d77ee54e10c066
        SET Total_Price   = #{dto.totalPrice,jdbcType=DECIMAL},
            Item_Quantity = #{dto.itemQuantity,jdbcType=DECIMAL}
        WHERE id = #{dto.siteId,jdbcType=BIGINT}
    </update>
</mapper>
