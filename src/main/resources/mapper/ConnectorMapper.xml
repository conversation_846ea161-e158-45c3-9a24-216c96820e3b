<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.ConnectorMapper">
    <update id="updateIncomeExpenditure">
        update memm_c58db11c81d3403ab9e59ce72b815ade
        set
        <foreach collection="map" separator="," item="item" index="key">
            `${key}` = #{item}
        </foreach>
        where JSON_CONTAINS(uniqueness_field, CONCAT('"',#{uniquenessId,jdbcType=BIGINT},'"'))
    </update>

    <select id="getPoItemByUniquenessId" resultType="java.util.Map">
        select poItem.*, po.PO_Received_date
        from memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
        left join memm_ed87f18383f04a8f836cea32a1628fc9 po
        on JSON_UNQUOTE(poItem.PO -> '$[0]') = po.id and po.is_deleted = 0
        where JSON_CONTAINS(uniqueness_field, CONCAT('"',#{uniquenessId,jdbcType=BIGINT},'"')) and poItem.is_deleted = 0
        limit 1
    </select>

    <select id="getSubconPoItemByUniquenessId" resultType="java.util.Map">
        select *
        from memm_157ac31323c34d46920918117cb577ad
        where JSON_CONTAINS(uniqueness_field, CONCAT('"',#{uniquenessId,jdbcType=BIGINT},'"')) and is_deleted = 0
        limit 1
    </select>

    <select id="getPermByUniquenessId" resultType="java.util.Map">
        SELECT per.*
        FROM memm_72a2450126dd41708a07374eff08b982 p
        LEFT JOIN memm_439131c30ad445e6810ba53e13fd9cfb per
        on JSON_CONTAINS(per.project, CONCAT('"', p.id, '"'))
        LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un on un.Project_code = p.YPTT_Project_code
        where un.id = #{uniquenessId,jdbcType=BIGINT}  and is_deleted = 0
        limit 1
    </select>

    <update id="updateReadySettlement">
        update memm_abdf4191a91e436a9b7e04351042f757
        set
        <foreach collection="map" separator="," item="item" index="key">
            `${key}` = #{item}
        </foreach>
        where JSON_CONTAINS(uniqueness_field, CONCAT('"',#{uniquenessId,jdbcType=BIGINT},'"'))
    </update>

    <update id="updateProductivityReport">
        update memm_5c8c376451894fdfb7e751c91da66f16
        set
        <foreach collection="map" separator="," item="item" index="key">
            `${key}` = #{item}
        </foreach>
        where JSON_CONTAINS(uniqueness_field, CONCAT('"',#{uniquenessId,jdbcType=BIGINT},'"'))
    </update>

    <update id="updateSubconSettlement">
        update memm_218a6ab9959842099fd074c2b0ef685b
        set
        <foreach collection="map" separator="," item="item" index="key">
            `${key}` = #{item}
        </foreach>
        where JSON_CONTAINS(uniqueness_field, CONCAT('"',#{uniquenessId,jdbcType=BIGINT},'"'))
    </update>

    <select id="getYpttSettlementByUniquenessId" resultType="java.util.Map">
        select *
        from memm_4bf72c9a610c4b05a007f0f215b424a6
        where JSON_CONTAINS(uniqueness_field, CONCAT('"',#{uniquenessId,jdbcType=BIGINT},'"')) and is_deleted = 0
        limit 1
    </select>

    <select id="getSubconPaymentByUniquenessId" resultType="java.util.Map">
        select *
        from memm_f562b5dbd2be42d99c4992dd2668ed74
        where JSON_UNQUOTE(uniqueness_field->'$[0]') = #{uniquenessId} and is_deleted = 0
        limit 1
    </select>

    <update id="updateYpttSettlement">
        update memm_4bf72c9a610c4b05a007f0f215b424a6
        set
        <foreach collection="map" separator="," item="item" index="key">
            `${key}` = #{item}
        </foreach>
        where JSON_CONTAINS(uniqueness_field, CONCAT('"',#{uniquenessId,jdbcType=BIGINT},'"'))
    </update>

    <update id="updateSubconPayment">
        update memm_f562b5dbd2be42d99c4992dd2668ed74
        set
        <foreach collection="map" separator="," item="item" index="key">
            `${key}` = #{item}
        </foreach>
        where JSON_CONTAINS(uniqueness_field, CONCAT('"',#{uniquenessId,jdbcType=BIGINT},'"'))
    </update>

    <select id="siteItemClosed" resultType="java.lang.Integer">
        select 1
        from memm_e648652640b44b2092c93e1742e6171b
        where JSON_CONTAINS(uniqueness_field, CONCAT('"',#{uniquenessId,jdbcType=BIGINT},'"'))
        and 'close' = JSON_UNQUOTE(Site_item_status -> '$[0]') and is_deleted = 0
        limit 1
    </select>

    <update id="closeSite">
        update memm_e648652640b44b2092c93e1742e6171b
        set Site_item_status = JSON_SET(Site_item_status, '$[0]', 'close')
        where JSON_CONTAINS(uniqueness_field, CONCAT(#{uniquenessId,jdbcType=BIGINT})) and is_deleted = 0
    </update>
</mapper>
