<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.PoStandingBookMapper">
    <select id="generatePoStandingBookList" resultType="com.pig4cloud.pig.yptt.entity.dto.PoStandingBookDTO">
        SELECT po.id                           AS poId,
               SUM(IFNULL(poItem.PO_value, 0)) AS totalPrice,
               COUNT(DISTINCT poItem.id)                AS itemQuantity
        FROM memm_ed87f18383f04a8f836cea32a1628fc9 AS po
                 LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c AS poItem
                           ON poItem.is_deleted = 0. AND po.id = JSON_UNQUOTE(poItem.PO -> '$[0]')
        WHERE po.is_deleted = 0
        GROUP BY po.id
        LIMIT #{cur}, #{size}
    </select>


    <update id="update">
        UPDATE
            memm_ed87f18383f04a8f836cea32a1628fc9
        SET Total_Price   = #{dto.totalPrice,jdbcType=DECIMAL},
            Item_Quantity = #{dto.itemQuantity,jdbcType=DECIMAL}
        WHERE id = #{dto.poId,jdbcType=BIGINT}
    </update>
</mapper>
