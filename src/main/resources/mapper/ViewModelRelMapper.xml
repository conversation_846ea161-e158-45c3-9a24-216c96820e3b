<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.ViewModelRel">

    <select id="getModelNameByViewId" resultType="java.lang.String">
        SELECT a.name
        FROM me_meta_model a
                 inner join me_view b on a.id = b.model_id
        WHERE b.id = #{viewId}
        and a.is_deleted = 0 and b.is_deleted = 0
    </select>

    <select id="getModelTableNameByModelName" resultType="com.pig4cloud.pig.yptt.entity.MetaModeInfo">
        SELECT id,table_name,name
        FROM me_meta_model
        WHERE name = #{modelName}
        and is_deleted = 0
    </select>

    <select id="getModelRelTableNameByModelName" resultType="java.lang.String">
        SELECT meta_data_rel_table_name
        FROM me_meta_rel
        WHERE left_meta_model_id = #{leftModelId} and right_meta_model_id = #{rightModelId}
        and is_deleted = 0
    </select>

    <select id="getRightModelDataIdByLeftModelDataId" resultType="java.lang.Long">
         SELECT right_data_id
         FROM ${modelRelTableName}
         WHERE left_data_id = #{leftModelDataId}
         and is_deleted = 0
    </select>

    <select id="getLeftModelDataIdByRightModelDataId" resultType="java.lang.Long">
        SELECT left_data_id
        FROM ${modelRelTableName}
        WHERE right_data_id = #{rightModelDataId}
        and is_deleted = 0
    </select>
</mapper>