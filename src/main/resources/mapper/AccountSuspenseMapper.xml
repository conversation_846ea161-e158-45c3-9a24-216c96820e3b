<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.AccountsSuspenseMpper">

    <sql id="Base_Column_List">
        id,create_by,create_time,update_by,update_time,is_deleted,
        curr_proc_inst_id,code,departmentid,yjxmmc,yjxmbh,
        account,bank,bank_num,pay_money,pay_money_leave,
        bz,workflow_id,sqr,sqrq,company,
        fysy,fybm,zflx
    </sql>
    <update id="updateUnpaidMoney">
        update ${modelTable} set pay_money_leave = pay_money_leave - #{payMoney} where id = #{dataId}
    </update>
    <select id="selectUnpaidMoney" resultType="java.math.BigDecimal">
        select pay_money_leave from ${modelTable} where id = #{dataId}
    </select>

</mapper>
