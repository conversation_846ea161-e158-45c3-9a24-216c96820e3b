<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.ReimburseInvoiceMapper">

    <sql id="Base_Column_List">
        id,create_by,create_time,update_by,update_time,is_deleted,curr_proc_inst_id,invoice_num, tax_money, no_tax_money, taxes,supplier
    </sql>

    <insert id="addBatch">
        insert into ${modelTable} (
        <include refid="Base_Column_List"/>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
             #{item.id},
             #{item.createBy},
             #{item.createTime},
             #{item.updateBy},
             #{item.updateTime},
             #{item.isDeleted},
             #{item.currProcInstId},
             #{item.invoiceNum},
             #{item.taxMoney},
             #{item.noTaxMoney},
             #{item.taxes},
             #{item.supplier}
            )
        </foreach>
    </insert>
    <select id="isDuplicateInvoiceNums" resultType="java.lang.Boolean">
        select exists(
        select 1 from ${modelTable} where invoice_num in
        <foreach item="item" collection="invoiceNums" separator="," close=")" open="(">
            #{item}
        </foreach>
        )
    </select>
</mapper>
