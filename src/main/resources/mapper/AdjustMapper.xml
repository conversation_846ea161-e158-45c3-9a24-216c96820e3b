<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.AdjustMapper">



    <update id="updateSiteItemData">
        update memm_e648652640b44b2092c93e1742e6171b
        set
        <foreach collection="map" index="key" item="value" separator=",">
            `${key}` = #{value}
        </foreach>
        where id = #{map.id} and is_deleted = 0
    </update>

    <update id="updatePoItemData">
        update memm_f37920ed96f942fb8f4b1bf16f79e39c
        set
        <foreach collection="map" index="key" item="value" separator=",">
            `${key}` = #{value}
        </foreach>
        where id = #{map.id} and is_deleted = 0
    </update>
    <update id="updateSettlementData">
        update memm_abdf4191a91e436a9b7e04351042f757
        set
        <foreach collection="map" index="key" item="value" separator=",">
            `${key}` = #{value}
        </foreach>
        where id = #{map.id} and is_deleted = 0
    </update>
    <update id="updateProductivityData">
        update memm_5c8c376451894fdfb7e751c91da66f16
        set
        <foreach collection="map" index="key" item="value" separator=",">
            `${key}` = #{value}
        </foreach>
        where id = #{map.id} and is_deleted = 0
    </update>
    <update id="updateYPTTSettlement">
        update memm_4bf72c9a610c4b05a007f0f215b424a6
        set
        <foreach collection="map" index="key" item="value" separator=",">
            `${key}` = #{value}
        </foreach>
        where id = #{map.id} and is_deleted = 0
    </update>
    <update id="updateSubPoItemData">
        update memm_157ac31323c34d46920918117cb577ad
        set
        <foreach collection="map" index="key" item="value" separator=",">
            `${key}` = #{value}
        </foreach>
        where id = #{map.id} and is_deleted = 0
    </update>
    <update id="updateSubSettlementData">
        update memm_218a6ab9959842099fd074c2b0ef685b
        set
        <foreach collection="map" index="key" item="value" separator=",">
            `${key}` = #{value}
        </foreach>
        where id = #{map.id} and is_deleted = 0
    </update>
    <update id="changeStatusSiteItem">
        update memm_e648652640b44b2092c93e1742e6171b
        SET Site_item_status = JSON_ARRAY(CONCAT(#{status}))
        where id = #{id} and is_deleted = 0
    </update>
    <update id="updateSiteItemDataes">
        <foreach collection="siteItemList" item="map" separator=";">
            update memm_e648652640b44b2092c93e1742e6171b
            set
            <foreach collection="map" index="key" item="value" separator=",">
                <choose>
                    <when test="key == 'Site_item_status'">
                        Site_item_status = JSON_ARRAY(CONCAT(#{value}))
                    </when>
                    <otherwise>
                        `${key}` = #{value}
                    </otherwise>
                </choose>

            </foreach>
            where id = #{map.id} and is_deleted = 0
        </foreach>
    </update>
    <update id="updateSitePoDataes">

        <foreach collection="poItemList" item="map" separator=";">
             update memm_f37920ed96f942fb8f4b1bf16f79e39c
             set
             <foreach collection="map" index="key" item="value" separator=",">
                 `${key}` = #{value}
             </foreach>
             where id = #{map.id} and is_deleted = 0
         </foreach>
    </update>
    <update id="updatePoItemDatas">
        <foreach collection="poItems" item="poItem" separator=";">
            update memm_f37920ed96f942fb8f4b1bf16f79e39c
            set
            <foreach collection="poItem" index="key" item="value" separator=",">
                `${key}` = #{value}
            </foreach>
            where id = #{poItem.id} and is_deleted = 0
        </foreach>
    </update>
    <update id="updateSettlementDatas">
        <foreach collection="settlementDatas" item="settlementData" separator=";">
            update memm_abdf4191a91e436a9b7e04351042f757
            set
            <foreach collection="settlementData" index="key" item="value" separator=",">
                `${key}` = #{value}
            </foreach>
            where id = #{settlementData.id} and is_deleted = 0
        </foreach>
    </update>
    <update id="updateProductivityDatas">
        <foreach collection="productivityDatas" item="productivityData" separator=";">
            update memm_5c8c376451894fdfb7e751c91da66f16
            set
            <foreach collection="productivityData" index="key" item="value" separator=",">
                `${key}` = #{value}
            </foreach>
            where id = #{productivityData.id} and is_deleted = 0
        </foreach>
    </update>
    <update id="updateYPTTSettlements">
        <foreach collection="YPTTSettlements" item="YPTTSettlement" separator=";">
            update memm_4bf72c9a610c4b05a007f0f215b424a6
            set
            <foreach collection="YPTTSettlement" index="key" item="value" separator=",">
                `${key}` = #{value}
            </foreach>
            where id = #{YPTTSettlement.id} and is_deleted = 0
        </foreach>
    </update>

    <select id="selectReport" resultType="java.util.Map">
        SELECT DISTINCT
        IFNULL(
        DATE_FORMAT( uf.create_time, '%Y-%m-%d' ),
        DATE_FORMAT( NOW(), '%Y-%m-%d' )) AS Site_register_date,
        site.Site_Serial_number,
        site.site_name,
        DATE_FORMAT( siteItem.site_allocation_date, '%Y-%m-%d' ) AS site_allocation_date,
        site.Area,
        uf.Region,
        siteItem.Site_model,
        siteItem.BOQ_item,
        CASE
        siteItem.Site_item_status
        WHEN '["unclose"]' THEN
        '未关闭'
        WHEN '["close"]' THEN
        '已关闭'
        WHEN '["invalid"]' THEN
        '无效' ELSE '未知'
        END AS Site_item_status,
        siteItem.Item_code,
        siteItem.Phase,
        dept.`name`,
        siteItem.Type_of_service,
        YPTTProject.YPTT_Project_name,
        YPTTProject.YPTT_Project_code,
        siteItem.Quantity,
        uf.uniqueness_field 'siteUn',
        siteItem.Unit_price,
        siteItem.Site_value,
        siteItem.Remark siteItemRemark,
        DATE_FORMAT( po.PO_Received_date, '%Y-%m-%d' ) AS PO_Received_date,
        po.PO_number,
        poItem.Quantity poItemQuantity,
        poItem.quantity_reduce poItemQuantityReduce,
        cusProject.Contract_number,
        cusProject.Custom_project_name,
        poItem.Unit_price poUp,
        poItem.PO_value,
        poItem.PO_gap,
        poItem.BOQ_item po_BOQ_item,
        poItem.Pre_payment,
        poItem.Milestone_1st 'SiteSettlementMilestone1st%',
        poItem.Milestone_2nd 'SiteSettlementMilestone2nd%',
        poItem.Milestone_3rd 'SiteSettlementMilestone3rd%',
        poItem.Milestone_4th 'SiteSettlementMilestone4th%',
        poItem.Remark poItemRemark,
        IF
        ( poItem.PO IS NULL, NULL, poItem.Item_code ) poItemcode,
        IF
        ( poItem.PO IS NULL, NULL, poItem.Site_ID ) poItemSiteID,
        IF
        ( poItem.PO IS NULL, NULL, poItem.Project_code ) poItemProjectcode,
        IF
        ( poItem.PO IS NULL, NULL, poItem.Phase ) poItemPhase,
        IF
        ( poItem.PO IS NULL, NULL, site.site_name ) poItemSiteName,
        IF
        ( poItem.PO IS NULL, NULL, poItem.Region ) poItemRegion,
        IF
        ( poItem.PO IS NULL, NULL, uf.uniqueness_field ) 'poItemUn',
        siteDelivery.Site_belong_to,
        siteDelivery.Team_Leader_DT,
        siteDelivery.engineer_DTA_SPV,
        siteDelivery.PLO_PC_Others,
        siteDelivery.PIC_PC_PM,
        DATE_FORMAT( siteDelivery.Start_Working_date, '%Y-%m-%d' ) 'Start_Working_date',
        DATE_FORMAT( siteDelivery.Completed_work_date, '%Y-%m-%d' ) 'Completed_work_date',
        DATE_FORMAT( siteDelivery.air_CI_Report_submit, '%Y-%m-%d' ) 'air_CI_Report_submit',
        DATE_FORMAT( siteDelivery.Site_manager_Report, '%Y-%m-%d' ) 'Site_manager_Report',
        DATE_FORMAT( siteDelivery.E_ATP_Pass, '%Y-%m-%d' ) 'E_ATP_Pass',
        DATE_FORMAT( siteDelivery.F_PAC_Pass, '%Y-%m-%d' ) 'F_PAC_Pass',
        DATE_FORMAT( siteDelivery.G_FAC, '%Y-%m-%d' ) 'G_FAC',
        siteDelivery.remark deliveryRemark,
        DATE_FORMAT( siteDelivery.settlement_1st, '%Y-%m-%d' ) 'site-ReadyForSettlement-1st',
        DATE_FORMAT( siteDelivery.settlement_2nd, '%Y-%m-%d' ) 'site-ReadyForSettlement-2nd',
        DATE_FORMAT( siteDelivery.settlement_3rd, '%Y-%m-%d' ) 'site-ReadyForSettlement-3rd',
        DATE_FORMAT( siteDelivery.settlement_4th, '%Y-%m-%d' ) 'site-ReadyForSettlement-4th',
        DATE_FORMAT( siteDelivery.SubconSettlement_1st, '%Y-%m-%d' ) 'site-Subcon-settlementDate-1st',
        DATE_FORMAT( siteDelivery.SubconSettlement_2nd, '%Y-%m-%d' ) 'site-Subcon-settlementDate-2nd',
        DATE_FORMAT( siteDelivery.SubconSettlement_3rd, '%Y-%m-%d' ) 'site-Subcon-settlementDate-3rd',
        DATE_FORMAT( siteDelivery.SubconSettlement_4th, '%Y-%m-%d' ) 'site-Subcon-settlementDate-4th',
        sub.Subcon_name,
        subPo.Subcon_PO_number,
        DATE_FORMAT( subPo.release_date, '%Y-%m-%d' ) 'release_date',
        subItem.Subcon_PO_amount,
        subItem.Quantity subQuantity,
        subItem.Unit_price subUp,
        subItem.quantity_reduce subQuantityReduce,
        subItem.Milestone_1st 'SubconSettlementMilestone1st',
        subItem.Milestone_2nd 'SubconSettlementMilestone2nd',
        subItem.Milestone_3rd 'SubconSettlementMilestone3rd',
        subItem.Milestone_4th 'SubconSettlementMilestone4th',
        subItem.remark subRemark,
        IF
        ( subItem.Subcon_PO IS NULL, NULL, subItem.Site_ID ) subSiteID,
        IF
        ( subItem.Subcon_PO IS NULL, NULL, subItem.BOQ_item ) subBOQitem,
        IF
        ( subItem.Subcon_PO IS NULL, NULL, subItem.Item_code ) subItemcode,
        IF
        ( subItem.Subcon_PO IS NULL, NULL, site.site_name ) subSiteName,
        subItem.additional_cost,
        CONCAT( sub.Subcon_name, '_', subPo.Subcon_PO_number, '_', site.Site_Serial_number, '_', subItem.BOQ_item, '_', subItem.Subcon_PO_amount ) subconUniquenessField,
        DATE_FORMAT( ReadySettlement.Pre_Settlement_date, '%Y-%m-%d' ) 'Pre_Settlement_date',
        ReadySettlement.Pre_payment_ratio,
        ReadySettlement.Pre_payment_amount,
        DATE_FORMAT( ReadySettlement.settlement_1st, '%Y-%m-%d' ) 'ReadyForSettlement-1st',
        ReadySettlement.Settlement_ratio_1st 'settlement%-1st',
        ReadySettlement.amount_1st 'settlementAmount-1st',
        DATE_FORMAT( ReadySettlement.settlement_2nd, '%Y-%m-%d' ) 'ReadyForSettlement-2nd',
        ReadySettlement.Settlement_ratio_2nd 'settlement%-2nd',
        ReadySettlement.amount_2nd 'settlementAmount-2nd',
        DATE_FORMAT( ReadySettlement.settlement_3rd, '%Y-%m-%d' ) 'ReadyForSettlement-3rd',
        ReadySettlement.Settlement_ratio_3rd 'settlement%-3rd',
        ReadySettlement.amount_3rd 'settlementAmount-3rd',
        DATE_FORMAT( ReadySettlement.settlement_4th, '%Y-%m-%d' ) 'ReadyForSettlement-4th',
        ReadySettlement.Settlement_ratio_4th 'settlement%-4th',
        ReadySettlement.amount_4th 'settlementAmount-4th',
        ReadySettlement.settlement_Amount 'ReadySettlementAmount',
        ReadySettlement.settlement_amountGap 'readySettlementAmountGap',
        DATE_FORMAT( ProductivityReport.report_date_1st, '%Y-%m-%d' ) '1stProductivityReportDate',
        ProductivityReport.report_amount_1st '1stProductivityReportAmount',
        DATE_FORMAT( ProductivityReport.report_date_2nd, '%Y-%m-%d' ) '2ndProductivityReportDate',
        ProductivityReport.report_amount_2nd '2ndProductivityReportAmount',
        DATE_FORMAT( ProductivityReport.report_date_3rd, '%Y-%m-%d' ) '3rdProductivityReportDate',
        ProductivityReport.report_amount_3rd '3rdProductivityReportAmount',
        DATE_FORMAT( ProductivityReport.report_date_4th, '%Y-%m-%d' ) '4thProductivityReportDate',
        ProductivityReport.report_amount_4th '4thProductivityReportAmount',
        ProductivityReport.Productivity_Amount 'ProductivityAmount',
        ProductivityReport.declaration_ratio 'ProductivityDeclarationRatio%',
        DATE_FORMAT( SubconSettlement.settlement_time_1st, '%Y-%m-%d' ) 'Subcon-settlement-time-1st',
        SubconSettlement.settlement_ratio_1st 'Subcon-settlement%-1st',
        SubconSettlement.settlementAmount_1st 'Subcon-settlement-amount-1st',
        DATE_FORMAT( SubconSettlement.settlement_time_2nd, '%Y-%m-%d' ) 'Subcon-settlement-time-2nd',
        SubconSettlement.settlement_ratio_2nd 'Subcon-settlement%-2nd',
        SubconSettlement.settlementAmount_2nd 'Subcon-settlement-amount-2nd',
        DATE_FORMAT( SubconSettlement.settlement_time_3rd, '%Y-%m-%d' ) 'Subcon-settlement-time-3rd',
        SubconSettlement.settlement_ratio_3rd 'Subcon-settlement%-3rd',
        SubconSettlement.settlementAmount_3rd 'Subcon-settlement-amount-3rd',
        DATE_FORMAT( SubconSettlement.settlement_time_4th, '%Y-%m-%d' ) 'Subcon-settlement-time-4th',
        SubconSettlement.settlement_ratio_4th 'Subcon-settlement%-4st',
        SubconSettlement.settlementAmount_4th 'Subcon-settlement-amount-4th',
        SubconSettlement.Totally_Amount 'SubconTotallySettlementAmount',
        SubconSettlement.Totally_amount_Gap 'SubconTotallySettlementGap',
        DATE_FORMAT( SubconPayment.Payment_time_1st, '%Y-%m-%d' ) 'Subcon-Payment-time-1st',
        SubconPayment.payment_amount_1st 'Subcon-payment-amount-1st',
        DATE_FORMAT( SubconPayment.Payment_time_2st, '%Y-%m-%d' ) 'Subcon-Payment-time-2nd',
        SubconPayment.payment_amount_2st 'Subcon-payment-amount-2nd',
        DATE_FORMAT( SubconPayment.Payment_time_3st, '%Y-%m-%d' ) 'Subcon-Payment-time-3rd',
        SubconPayment.payment_amount_3st 'Subcon-payment-amount-3rd',
        DATE_FORMAT( SubconPayment.Payment_time_4st, '%Y-%m-%d' ) 'Subcon-Payment-time-4st',
        SubconPayment.payment_amount_4st 'Subcon-payment-amount-4st',
        SubconPayment.payment_number_1st 'Subcon-payment-number-1st',
        SubconPayment.payment_number_2st 'Subcon-payment-number-2st',
        SubconPayment.payment_number_3st 'Subcon-payment-number-3st',
        SubconPayment.payment_number_4st 'Subcon-payment-number-4st',
        SubconPayment.remark subPayRemark,
        SubconPayment.Totally_payment 'SubconTotallyPaymentAmount',
        SubconPayment.Totally_payment_gap 'SubconTotallyPaymentAmountGap',
        DATE_FORMAT( YPTTSettlement.Invoice_date_1st, '%Y-%m-%d' ) 'Invoice-date-1st',
        YPTTSettlement.Invoice_number_1st 'Invoice-number-1st',
        YPTTSettlement.Invoice_Amount_1st 'Invoice-Amount-1st',
        YPTTSettlement.Invoice_Amount_diff_1st 'Invoice-Amount-Diff-1st',
        YPTTSettlement.Invoice_remark_1st 'Invoice-remark-1st',
        DATE_FORMAT( YPTTSettlement.Invoice_date_2st, '%Y-%m-%d' ) 'Invoice-date-2nd',
        YPTTSettlement.Invoice_number_2st 'Invoice-number-2nd',
        YPTTSettlement.Invoice_Amount_2st 'Invoice-Amount-2nd',
        YPTTSettlement.Invoice_Amount_diff_2st 'Invoice-Amount-Diff-2nd',
        YPTTSettlement.Invoice_remark_2st 'Invoice-remark-2nd',
        DATE_FORMAT( YPTTSettlement.Invoice_date_3st, '%Y-%m-%d' ) 'Invoice-date-3rd',
        YPTTSettlement.Invoice_number_3st 'Invoice-number-3rd',
        YPTTSettlement.Invoice_Amount_3st 'Invoice-Amount-3rd',
        YPTTSettlement.Invoice_Amount_diff_3st 'Invoice-Amount-Diff-3rd',
        YPTTSettlement.Invoice_remark_3st 'Invoice-remark-3rd',
        DATE_FORMAT( YPTTSettlement.Invoice_date_4st, '%Y-%m-%d' ) 'Invoice-date-4th',
        YPTTSettlement.Invoice_number_4st 'Invoice-number-4th',
        YPTTSettlement.Invoice_Amount_4st 'Invoice-Amount-4th',
        YPTTSettlement.Invoice_Amount_diff_4st 'Invoice-Amount-Diff-4th',
        YPTTSettlement.Invoice_remark_4st 'Invoice-remark-4th',
        YPTTSettlement.remark ypttRemark,
        YPTTSettlement.Invoice_amount 'TotallyInvoiceAmount',
        YPTTSettlement.Invoice_amount_gap 'InvoiceAmountGAP',
        siteItem.id 'siteItemId',
        uf.id 'ufId',
        site.id 'siteId',
        poItem.id 'poItemId',
        cusProject.id 'cusProjectId',
        po.id 'poId',
        siteDelivery.id 'siteDeliveryId',
        subItem.id 'subItemId',
        subPo.id 'subPoId',
        sub.id 'subId',
        ReadySettlement.id 'ReadySettlementId',
        ProductivityReport.id 'ProductivityReportId',
        SubconSettlement.id 'SubconSettlementId',
        SubconPayment.id 'SubconPaymentId',
        YPTTSettlement.id 'YPTTSettlementId',
        YPTTProject.id 'YPTTProjectId',
        dept.dept_id 'deptId'

        FROM
        memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem
        on JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field and siteItem.is_deleted = 0
        LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site
        on uf.Site_ID = site.Site_Serial_number and site.is_deleted = 0
        LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
        on poItem.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and poItem.is_deleted = 0
        LEFT JOIN memm_f15b45017dee432daf88693b3d13b60b cusProject
        on JSON_ARRAY(CONCAT(cusProject.id)) = poItem.Customer_project and cusProject.is_deleted = 0
        LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
        on JSON_ARRAY(CONCAT(po.id)) = poItem.PO and po.is_deleted = 0
        LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 siteDelivery
        on siteDelivery.uniqueness_field = poItem.uniqueness_field and siteDelivery.is_deleted = 0
        LEFT JOIN memm_157ac31323c34d46920918117cb577ad subItem
        on subItem.uniqueness_field = siteItem.uniqueness_field and subItem.Subcon_PO IS NOT NULL and
        subItem.is_deleted = 0
        LEFT JOIN memm_ff802d120a12430db18a68deb783b9c6 subPo
        on subItem.Subcon_PO = JSON_ARRAY(CONCAT(subPo.id)) and subPo.is_deleted = 0
        LEFT JOIN memm_134d9474dc244b26bfd7f013a0534710 sub
        on subItem.Subcon = JSON_ARRAY(CONCAT(sub.id)) and sub.is_deleted = 0
        LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 ReadySettlement
        on ReadySettlement.uniqueness_field = siteItem.uniqueness_field and
        ReadySettlement.is_deleted = 0
        LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
        on ProductivityReport.uniqueness_field = siteItem.uniqueness_field and
        ProductivityReport.is_deleted = 0
        LEFT JOIN memm_218a6ab9959842099fd074c2b0ef685b SubconSettlement
        on SubconSettlement.uniqueness_field = siteItem.uniqueness_field and
        SubconSettlement.is_deleted = 0
        LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
        on SubconPayment.uniqueness_field = siteItem.uniqueness_field and
        SubconPayment.is_deleted = 0
        LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 YPTTSettlement
        on YPTTSettlement.uniqueness_field = siteItem.uniqueness_field and
        YPTTSettlement.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        on uf.Project_code = YPTTProject.YPTT_Project_code and YPTTProject.is_deleted = 0
        LEFT JOIN sys_dept dept
        on JSON_ARRAY(CONCAT(dept.dept_id)) = YPTTProject.Department and dept.is_deleted = 0
        where uf.is_deleted = 0
        and YPTTProject.YPTT_Project_code = #{projectCode}
    </select>
    <select id="selectSiteItem" resultType="java.util.Map">
        select *,uniq.uniqueness_field 'uniField' from memm_e648652640b44b2092c93e1742e6171b as siteItem
        left join memm_562ace74337e462289972ce20939e9a7 as uniq on JSON_ARRAY(CONCAT(uniq.id)) = siteItem.uniqueness_field
        where siteItem.is_deleted = 0 and uniq.is_deleted = 0
    </select>
    <select id="selectReportNoCondition" resultType="java.util.Map">
        SELECT DISTINCT
        IFNULL(
        DATE_FORMAT( uf.create_time, '%Y-%m-%d' ),
        DATE_FORMAT( NOW(), '%Y-%m-%d' )) AS Site_register_date,
        site.Site_Serial_number,
        site.site_name,
        DATE_FORMAT( siteItem.site_allocation_date, '%Y-%m-%d' ) AS site_allocation_date,
        site.Area,
        uf.Region,
        siteItem.Site_model,
        siteItem.BOQ_item,
        CASE
        siteItem.Site_item_status
        WHEN '["unclose"]' THEN
        '未关闭'
        WHEN '["close"]' THEN
        '已关闭'
        WHEN '["invalid"]' THEN
        '无效' ELSE '未知'
        END AS Site_item_status,
        siteItem.Item_code,
        siteItem.Phase,
        dept.`name`,
        siteItem.Type_of_service,
        YPTTProject.YPTT_Project_name,
        YPTTProject.YPTT_Project_code,
        siteItem.Quantity,
        uf.uniqueness_field 'siteUn',
        siteItem.Unit_price,
        siteItem.Site_value,
        siteItem.Remark siteItemRemark,
        DATE_FORMAT( po.PO_Received_date, '%Y-%m-%d' ) AS PO_Received_date,
        po.PO_number,
        poItem.Quantity poItemQuantity,
        poItem.quantity_reduce poItemQuantityReduce,
        cusProject.Contract_number,
        cusProject.Custom_project_name,
        poItem.Unit_price poUp,
        poItem.PO_value,
        poItem.PO_gap,
        poItem.BOQ_item po_BOQ_item,
        poItem.Pre_payment,
        poItem.Milestone_1st 'SiteSettlementMilestone1st%',
        poItem.Milestone_2nd 'SiteSettlementMilestone2nd%',
        poItem.Milestone_3rd 'SiteSettlementMilestone3rd%',
        poItem.Milestone_4th 'SiteSettlementMilestone4th%',
        poItem.Remark poItemRemark,
        IF
        ( poItem.PO IS NULL, NULL, poItem.Item_code ) poItemcode,
        IF
        ( poItem.PO IS NULL, NULL, poItem.Site_ID ) poItemSiteID,
        IF
        ( poItem.PO IS NULL, NULL, poItem.Project_code ) poItemProjectcode,
        IF
        ( poItem.PO IS NULL, NULL, poItem.Phase ) poItemPhase,
        IF
        ( poItem.PO IS NULL, NULL, site.site_name ) poItemSiteName,
        IF
        ( poItem.PO IS NULL, NULL, poItem.Region ) poItemRegion,
        IF
        ( poItem.PO IS NULL, NULL, uf.uniqueness_field ) 'poItemUn',
        siteDelivery.Site_belong_to,
        siteDelivery.Team_Leader_DT,
        siteDelivery.engineer_DTA_SPV,
        siteDelivery.PLO_PC_Others,
        siteDelivery.PIC_PC_PM,
        DATE_FORMAT( siteDelivery.Start_Working_date, '%Y-%m-%d' ) 'Start_Working_date',
        DATE_FORMAT( siteDelivery.Completed_work_date, '%Y-%m-%d' ) 'Completed_work_date',
        DATE_FORMAT( siteDelivery.air_CI_Report_submit, '%Y-%m-%d' ) 'air_CI_Report_submit',
        DATE_FORMAT( siteDelivery.Site_manager_Report, '%Y-%m-%d' ) 'Site_manager_Report',
        DATE_FORMAT( siteDelivery.E_ATP_Pass, '%Y-%m-%d' ) 'E_ATP_Pass',
        DATE_FORMAT( siteDelivery.F_PAC_Pass, '%Y-%m-%d' ) 'F_PAC_Pass',
        DATE_FORMAT( siteDelivery.G_FAC, '%Y-%m-%d' ) 'G_FAC',
        siteDelivery.remark deliveryRemark,
        DATE_FORMAT( siteDelivery.settlement_1st, '%Y-%m-%d' ) 'site-ReadyForSettlement-1st',
        DATE_FORMAT( siteDelivery.settlement_2nd, '%Y-%m-%d' ) 'site-ReadyForSettlement-2nd',
        DATE_FORMAT( siteDelivery.settlement_3rd, '%Y-%m-%d' ) 'site-ReadyForSettlement-3rd',
        DATE_FORMAT( siteDelivery.settlement_4th, '%Y-%m-%d' ) 'site-ReadyForSettlement-4th',
        DATE_FORMAT( siteDelivery.SubconSettlement_1st, '%Y-%m-%d' ) 'site-Subcon-settlementDate-1st',
        DATE_FORMAT( siteDelivery.SubconSettlement_2nd, '%Y-%m-%d' ) 'site-Subcon-settlementDate-2nd',
        DATE_FORMAT( siteDelivery.SubconSettlement_3rd, '%Y-%m-%d' ) 'site-Subcon-settlementDate-3rd',
        DATE_FORMAT( siteDelivery.SubconSettlement_4th, '%Y-%m-%d' ) 'site-Subcon-settlementDate-4th',
        sub.Subcon_name,
        subPo.Subcon_PO_number,
        DATE_FORMAT( subPo.release_date, '%Y-%m-%d' ) 'release_date',
        subItem.Subcon_PO_amount,
        subItem.Quantity subQuantity,
        subItem.Unit_price subUp,
        subItem.quantity_reduce subQuantityReduce,
        subItem.Milestone_1st 'SubconSettlementMilestone1st',
        subItem.Milestone_2nd 'SubconSettlementMilestone2nd',
        subItem.Milestone_3rd 'SubconSettlementMilestone3rd',
        subItem.Milestone_4th 'SubconSettlementMilestone4th',
        subItem.remark subRemark,
        IF
        ( subItem.Subcon_PO IS NULL, NULL, subItem.Site_ID ) subSiteID,
        IF
        ( subItem.Subcon_PO IS NULL, NULL, subItem.BOQ_item ) subBOQitem,
        IF
        ( subItem.Subcon_PO IS NULL, NULL, subItem.Item_code ) subItemcode,
        IF
        ( subItem.Subcon_PO IS NULL, NULL, site.site_name ) subSiteName,
        subItem.additional_cost,
        CONCAT( sub.Subcon_name, '_', subPo.Subcon_PO_number, '_', site.Site_Serial_number, '_', subItem.BOQ_item, '_', subItem.Subcon_PO_amount ) subconUniquenessField,
        DATE_FORMAT( ReadySettlement.Pre_Settlement_date, '%Y-%m-%d' ) 'Pre_Settlement_date',
        ReadySettlement.Pre_payment_ratio,
        ReadySettlement.Pre_payment_amount,
        DATE_FORMAT( ReadySettlement.settlement_1st, '%Y-%m-%d' ) 'ReadyForSettlement-1st',
        ReadySettlement.Settlement_ratio_1st 'settlement%-1st',
        ReadySettlement.amount_1st 'settlementAmount-1st',
        DATE_FORMAT( ReadySettlement.settlement_2nd, '%Y-%m-%d' ) 'ReadyForSettlement-2nd',
        ReadySettlement.Settlement_ratio_2nd 'settlement%-2nd',
        ReadySettlement.amount_2nd 'settlementAmount-2nd',
        DATE_FORMAT( ReadySettlement.settlement_3rd, '%Y-%m-%d' ) 'ReadyForSettlement-3rd',
        ReadySettlement.Settlement_ratio_3rd 'settlement%-3rd',
        ReadySettlement.amount_3rd 'settlementAmount-3rd',
        DATE_FORMAT( ReadySettlement.settlement_4th, '%Y-%m-%d' ) 'ReadyForSettlement-4th',
        ReadySettlement.Settlement_ratio_4th 'settlement%-4th',
        ReadySettlement.amount_4th 'settlementAmount-4th',
        ReadySettlement.settlement_Amount 'ReadySettlementAmount',
        ReadySettlement.settlement_amountGap 'readySettlementAmountGap',
        DATE_FORMAT( ProductivityReport.report_date_1st, '%Y-%m-%d' ) '1stProductivityReportDate',
        ProductivityReport.report_amount_1st '1stProductivityReportAmount',
        DATE_FORMAT( ProductivityReport.report_date_2nd, '%Y-%m-%d' ) '2ndProductivityReportDate',
        ProductivityReport.report_amount_2nd '2ndProductivityReportAmount',
        DATE_FORMAT( ProductivityReport.report_date_3rd, '%Y-%m-%d' ) '3rdProductivityReportDate',
        ProductivityReport.report_amount_3rd '3rdProductivityReportAmount',
        DATE_FORMAT( ProductivityReport.report_date_4th, '%Y-%m-%d' ) '4thProductivityReportDate',
        ProductivityReport.report_amount_4th '4thProductivityReportAmount',
        ProductivityReport.Productivity_Amount 'ProductivityAmount',
        ProductivityReport.declaration_ratio 'ProductivityDeclarationRatio%',
        DATE_FORMAT( SubconSettlement.settlement_time_1st, '%Y-%m-%d' ) 'Subcon-settlement-time-1st',
        SubconSettlement.settlement_ratio_1st 'Subcon-settlement%-1st',
        SubconSettlement.settlementAmount_1st 'Subcon-settlement-amount-1st',
        DATE_FORMAT( SubconSettlement.settlement_time_2nd, '%Y-%m-%d' ) 'Subcon-settlement-time-2nd',
        SubconSettlement.settlement_ratio_2nd 'Subcon-settlement%-2nd',
        SubconSettlement.settlementAmount_2nd 'Subcon-settlement-amount-2nd',
        DATE_FORMAT( SubconSettlement.settlement_time_3rd, '%Y-%m-%d' ) 'Subcon-settlement-time-3rd',
        SubconSettlement.settlement_ratio_3rd 'Subcon-settlement%-3rd',
        SubconSettlement.settlementAmount_3rd 'Subcon-settlement-amount-3rd',
        DATE_FORMAT( SubconSettlement.settlement_time_4th, '%Y-%m-%d' ) 'Subcon-settlement-time-4th',
        SubconSettlement.settlement_ratio_4th 'Subcon-settlement%-4st',
        SubconSettlement.settlementAmount_4th 'Subcon-settlement-amount-4th',
        SubconSettlement.Totally_Amount 'SubconTotallySettlementAmount',
        SubconSettlement.Totally_amount_Gap 'SubconTotallySettlementGap',
        DATE_FORMAT( SubconPayment.Payment_time_1st, '%Y-%m-%d' ) 'Subcon-Payment-time-1st',
        SubconPayment.payment_amount_1st 'Subcon-payment-amount-1st',
        DATE_FORMAT( SubconPayment.Payment_time_2st, '%Y-%m-%d' ) 'Subcon-Payment-time-2nd',
        SubconPayment.payment_amount_2st 'Subcon-payment-amount-2nd',
        DATE_FORMAT( SubconPayment.Payment_time_3st, '%Y-%m-%d' ) 'Subcon-Payment-time-3rd',
        SubconPayment.payment_amount_3st 'Subcon-payment-amount-3rd',
        DATE_FORMAT( SubconPayment.Payment_time_4st, '%Y-%m-%d' ) 'Subcon-Payment-time-4st',
        SubconPayment.payment_amount_4st 'Subcon-payment-amount-4st',
        SubconPayment.payment_number_1st 'Subcon-payment-number-1st',
        SubconPayment.payment_number_2st 'Subcon-payment-number-2st',
        SubconPayment.payment_number_3st 'Subcon-payment-number-3st',
        SubconPayment.payment_number_4st 'Subcon-payment-number-4st',
        SubconPayment.remark subPayRemark,
        SubconPayment.Totally_payment 'SubconTotallyPaymentAmount',
        SubconPayment.Totally_payment_gap 'SubconTotallyPaymentAmountGap',
        DATE_FORMAT( YPTTSettlement.Invoice_date_1st, '%Y-%m-%d' ) 'Invoice-date-1st',
        YPTTSettlement.Invoice_number_1st 'Invoice-number-1st',
        YPTTSettlement.Invoice_Amount_1st 'Invoice-Amount-1st',
        YPTTSettlement.Invoice_Amount_diff_1st 'Invoice-Amount-Diff-1st',
        YPTTSettlement.Invoice_remark_1st 'Invoice-remark-1st',
        DATE_FORMAT( YPTTSettlement.Invoice_date_2st, '%Y-%m-%d' ) 'Invoice-date-2nd',
        YPTTSettlement.Invoice_number_2st 'Invoice-number-2nd',
        YPTTSettlement.Invoice_Amount_2st 'Invoice-Amount-2nd',
        YPTTSettlement.Invoice_Amount_diff_2st 'Invoice-Amount-Diff-2nd',
        YPTTSettlement.Invoice_remark_2st 'Invoice-remark-2nd',
        DATE_FORMAT( YPTTSettlement.Invoice_date_3st, '%Y-%m-%d' ) 'Invoice-date-3rd',
        YPTTSettlement.Invoice_number_3st 'Invoice-number-3rd',
        YPTTSettlement.Invoice_Amount_3st 'Invoice-Amount-3rd',
        YPTTSettlement.Invoice_Amount_diff_3st 'Invoice-Amount-Diff-3rd',
        YPTTSettlement.Invoice_remark_3st 'Invoice-remark-3rd',
        DATE_FORMAT( YPTTSettlement.Invoice_date_4st, '%Y-%m-%d' ) 'Invoice-date-4th',
        YPTTSettlement.Invoice_number_4st 'Invoice-number-4th',
        YPTTSettlement.Invoice_Amount_4st 'Invoice-Amount-4th',
        YPTTSettlement.Invoice_Amount_diff_4st 'Invoice-Amount-Diff-4th',
        YPTTSettlement.Invoice_remark_4st 'Invoice-remark-4th',
        YPTTSettlement.remark ypttRemark,
        YPTTSettlement.Invoice_amount 'TotallyInvoiceAmount',
        YPTTSettlement.Invoice_amount_gap 'InvoiceAmountGAP',
        siteItem.id 'siteItemId',
        uf.id 'ufId',
        site.id 'siteId',
        poItem.id 'poItemId',
        cusProject.id 'cusProjectId',
        po.id 'poId',
        siteDelivery.id 'siteDeliveryId',
        subItem.id 'subItemId',
        subPo.id 'subPoId',
        sub.id 'subId',
        ReadySettlement.id 'ReadySettlementId',
        ProductivityReport.id 'ProductivityReportId',
        SubconSettlement.id 'SubconSettlementId',
        SubconPayment.id 'SubconPaymentId',
        YPTTSettlement.id 'YPTTSettlementId',
        YPTTProject.id 'YPTTProjectId',
        dept.dept_id 'deptId'

        FROM
        memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem
        on JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field and siteItem.is_deleted = 0
        LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site
        on uf.Site_ID = site.Site_Serial_number and site.is_deleted = 0
        LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
        on poItem.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and poItem.is_deleted = 0
        LEFT JOIN memm_f15b45017dee432daf88693b3d13b60b cusProject
        on JSON_ARRAY(CONCAT(cusProject.id)) = poItem.Customer_project and cusProject.is_deleted = 0
        LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
        on JSON_ARRAY(CONCAT(po.id)) = poItem.PO and po.is_deleted = 0
        LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 siteDelivery
        on siteDelivery.uniqueness_field = poItem.uniqueness_field and siteDelivery.is_deleted = 0
        LEFT JOIN memm_157ac31323c34d46920918117cb577ad subItem
        on subItem.uniqueness_field = siteItem.uniqueness_field and subItem.Subcon_PO IS NOT NULL and
        subItem.is_deleted = 0
        LEFT JOIN memm_ff802d120a12430db18a68deb783b9c6 subPo
        on subItem.Subcon_PO = JSON_ARRAY(CONCAT(subPo.id)) and subPo.is_deleted = 0
        LEFT JOIN memm_134d9474dc244b26bfd7f013a0534710 sub
        on subItem.Subcon = JSON_ARRAY(CONCAT(sub.id)) and sub.is_deleted = 0
        LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 ReadySettlement
        on ReadySettlement.uniqueness_field = siteItem.uniqueness_field and
        ReadySettlement.is_deleted = 0
        LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
        on ProductivityReport.uniqueness_field = siteItem.uniqueness_field and
        ProductivityReport.is_deleted = 0
        LEFT JOIN memm_218a6ab9959842099fd074c2b0ef685b SubconSettlement
        on SubconSettlement.uniqueness_field = siteItem.uniqueness_field and
        SubconSettlement.is_deleted = 0
        LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
        on SubconPayment.uniqueness_field = siteItem.uniqueness_field and
        SubconPayment.is_deleted = 0
        LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 YPTTSettlement
        on YPTTSettlement.uniqueness_field = siteItem.uniqueness_field and
        YPTTSettlement.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        on uf.Project_code = YPTTProject.YPTT_Project_code and YPTTProject.is_deleted = 0
        LEFT JOIN sys_dept dept
        on JSON_ARRAY(CONCAT(dept.dept_id)) = YPTTProject.Department and dept.is_deleted = 0
        where uf.is_deleted = 0
    </select>
    <select id="selectPOItem" resultType="java.util.Map">
        select *,uniq.uniqueness_field 'uniField' from memm_f37920ed96f942fb8f4b1bf16f79e39c as poItem
        left join memm_562ace74337e462289972ce20939e9a7 as uniq on JSON_ARRAY(CONCAT(uniq.id)) = poItem.uniqueness_field
        where poItem.is_deleted = 0 and uniq.is_deleted = 0
    </select>
    <select id="selectKPIY2AndY3" resultType="java.util.Map">
        SELECT DISTINCT
            CAST(poItem.PO_value AS DECIMAL(18,6)) as 'KPI_Archive_amount',
            DATE_FORMAT(siteDelivery.E_ATP_Pass, '%Y-%m-%d') 'KPI_Archive_date',
            ProductivityReport.id
        FROM
            memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem
            ON JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field AND siteItem.is_deleted = 0
        LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
            ON poItem.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) AND poItem.is_deleted = 0
        LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 siteDelivery
            ON siteDelivery.uniqueness_field = poItem.uniqueness_field AND siteDelivery.is_deleted = 0
        LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
            ON ProductivityReport.uniqueness_field = siteItem.uniqueness_field AND
            ProductivityReport.is_deleted = 0
        WHERE uf.is_deleted = 0
    </select>
    <select id="selectSiteItemById" resultType="java.util.Map">
        SELECT
          *
        FROM
          memm_e648652640b44b2092c93e1742e6171b
        WHERE
          is_deleted = 0
          AND id = #{id}
    </select>
    <select id="selectSiteItemByPOItemId" resultType="java.util.Map">
        SELECT
          *
        FROM
          memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
          LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON poItem.uniqueness_field = siteItem.uniqueness_field
        WHERE
          poItem.id = #{id}
          AND poItem.is_deleted = 0
          AND siteItem.is_deleted = 0
    </select>
    <select id="selectSiteItemBySubItemId" resultType="java.util.Map">
        SELECT
      siteItem.*
    FROM
      memm_157ac31323c34d46920918117cb577ad subItem
      LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON subItem.uniqueness_field = siteItem.uniqueness_field
    WHERE
    subItem.is_deleted = 0
      AND siteItem.is_deleted = 0
      and subItem.id = #{id}
    </select>
    <select id="selectPoItemById" resultType="java.util.Map">
        SELECT
          *
        FROM
          memm_f37920ed96f942fb8f4b1bf16f79e39c
        WHERE
          is_deleted = 0
          and id = #{id}
    </select>
</mapper>
