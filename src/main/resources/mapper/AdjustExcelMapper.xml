<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.AdjustExcelMapper">
    <insert id="insertPRDownLoad">
        INSERT INTO memm_pr_download_1f120kq4jd401
        (id, download_user, download_date, uf_id, pr_total_id , is_deleted)
        VALUES
        <foreach collection="pRDownLoadRecords" item="item" separator=",">
            (#{item.id}, #{item.downloadUser}, #{item.downloadDate}, #{item.ufId}, #{item.prTotalId}, 0)
        </foreach>
    </insert>
    <insert id="insertPRTotal">
        INSERT INTO memm_pr_total_1f120kq4jd401
        (id, download_user, download_date, project_code, download_num, download_no, is_deleted)
        VALUES
        (#{prTotalEntity.id}, #{prTotalEntity.downloadUser}, #{prTotalEntity.downloadDate} , #{prTotalEntity.projectCode}, #{prTotalEntity.downloadNum}, #{prTotalEntity.downloadNo},0)
    </insert>

    <select id="selectPOItem" resultType="java.util.Map">
        select *,uniq.uniqueness_field 'uniField' from memm_f37920ed96f942fb8f4b1bf16f79e39c as poItem
        left join memm_562ace74337e462289972ce20939e9a7 as uniq on JSON_ARRAY(CONCAT(uniq.id)) = poItem.uniqueness_field
        where poItem.is_deleted = 0 and uniq.is_deleted = 0
    </select>
    <select id="exportY2" resultType="java.util.Map">
        SELECT
        concat(poItem.id) id,
        DATE_FORMAT(po.PO_Received_date, '%Y-%m-%d') 'PO_Received_date',
        po.PO_Number,
        poItem.Contract_number,
        cus.Custom_project_name as 'Custom_project_name',
        po.Project_code,
        poItem.Region,
        poItem.Phase,
        poItem.Site_ID,
        poItem.Item_code,
        poItem.BOQ_item,
        poItem.quantity,
        poItem.Unit_price,
        poItem.Pre_payment,
        poItem.Milestone_1st,
        poItem.Milestone_2nd,
        poItem.Milestone_3rd,
        poItem.Milestone_4th,
        poItem.Remark,
        poItem.re_record,
        poItem.quantity_reduce,
        un.uniqueness_field as uniqueness_field,
        site.site_name 'Site_Name'
        FROM
        memm_f37920ed96f942fb8f4b1bf16f79e39c as poItem
        left join memm_ed87f18383f04a8f836cea32a1628fc9 as po on po.id = JSON_UNQUOTE(poItem.PO -> '$[0]')
        left join memm_562ace74337e462289972ce20939e9a7 un on un.id = JSON_UNQUOTE(poItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
        left join memm_448208a319fa4d7ab3d77ee54e10c066 as site on site.id = JSON_UNQUOTE(poItem.site -> '$[0]')
        left join memm_f15b45017dee432daf88693b3d13b60b as cus on cus.id =  JSON_UNQUOTE(poItem.Customer_project -> '$[0]') and cus.is_deleted = 0
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON JSON_ARRAY(CONCAT(un.id)) = siteItem.uniqueness_field
        where poItem.is_deleted = 0
        AND po.is_deleted = 0
        AND siteItem.is_deleted = 0
        <if test="flag != null and flag != ''">
            AND siteItem.Site_item_status = JSON_ARRAY('unclose')
        </if>
        AND siteItem.Site_item_status != JSON_ARRAY('invalid')
        AND (
        1694550407313264642 IN
        <foreach collection="roleList" open="(" close=")" separator="," item="roleId">
            #{roleId}
        </foreach>
        OR
        EXISTS (
        SELECT project.id
        FROM memm_72a2450126dd41708a07374eff08b982 project
        LEFT JOIN memm_439131c30ad445e6810ba53e13fd9cfb per on project.id = JSON_UNQUOTE(per.project->'$[0]') and per.is_deleted = 0
        WHERE project.is_deleted = 0
        AND JSON_CONTAINS(per.y2_query, JSON_ARRAY(#{userId}))
        AND project.YPTT_Project_code = poItem.Project_code
        )
        )

        <if test="map != null and map.size != 0 ">
            <foreach collection="map" index="key" item="value" separator="and" open="and" close="">
                <choose>
                    <when test="key == 'PO' || key == 'uniqueness_field' || key == 'site' || key == 'Customer_project'">
                        JSON_UNQUOTE(poItem.${key}->'$[0]') = #{value}
                    </when>
                    <otherwise>
                        poItem.${key} = #{value}
                    </otherwise>
                </choose>
            </foreach>
        </if>

    </select>
    <select id="exportY1" resultType="java.util.Map">
        select
        concat(siteItem.id) id,
        siteItem.Project_code,
        site.Region,
        site.Area,
        siteItem.Site_ID,
        site.Site_Name,
        DATE_FORMAT(siteItem.site_allocation_date, '%Y-%m-%d') 'site_allocation_date',
        siteItem.Phase,
        siteItem.Type_of_service,
        siteItem.Site_model,
        siteItem.Item_code,
        siteItem.BOQ_item,
        siteItem.Quantity,
        siteItem.Unit_price,
        siteItem.Remark,
        un.uniqueness_field as uniqueness_field,
        CASE
        siteItem.Site_item_status
        WHEN '["unclose"]' THEN
        'unclose'
        WHEN '["close"]' THEN
        'close'
        WHEN '["invalid"]' THEN
        'invalid' ELSE 'unknown'
        END AS Site_item_status
        from
        memm_e648652640b44b2092c93e1742e6171b as siteItem
        left join memm_448208a319fa4d7ab3d77ee54e10c066 as site on site.id = JSON_UNQUOTE(siteItem.site -> '$[0]')
        left join memm_562ace74337e462289972ce20939e9a7 un on un.id = JSON_UNQUOTE(siteItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
        where
        siteItem.is_deleted = 0 and site.is_deleted = 0 and un.is_deleted = 0
        <if test="flag != null and flag != ''">
            AND siteItem.Site_item_status = JSON_ARRAY('unclose')
        </if>
        AND siteItem.Site_item_status != JSON_ARRAY('invalid')
        AND (
        1694550407313264642 IN
        <foreach collection="roleList" open="(" close=")" separator="," item="roleId">
            #{roleId}
        </foreach>
        OR
        EXISTS (
        SELECT project.id
        FROM memm_72a2450126dd41708a07374eff08b982 project
        LEFT JOIN memm_439131c30ad445e6810ba53e13fd9cfb per on project.id = JSON_UNQUOTE(per.project->'$[0]') and per.is_deleted = 0
        WHERE project.is_deleted = 0
        AND JSON_CONTAINS(per.y2_query, JSON_ARRAY(#{userId}))
        AND project.YPTT_Project_code = siteItem.Project_code
        )
        )

        <if test="map != null and map.size != 0 ">
            <foreach collection="map" index="key" item="value" separator="and" open="and" close="">
                <choose>
                    <when test="key == 'PO' || key == 'uniqueness_field' || key == 'site' || key == 'Customer_project'">
                        JSON_UNQUOTE(siteItem.${key}->'$[0]') = #{value}
                    </when>
                    <otherwise>
                        siteItem.${key} = #{value}
                    </otherwise>
                </choose>
            </foreach>
        </if>
    </select>
    <select id="exportY4" resultType="java.util.Map">
        select
        concat(subconItem.id) id,
        subcon.Subcon_PO_number,
        DATE_FORMAT(subcon.release_date, '%Y-%m-%d') 'release_date',
        subconItem.Site_ID,
        subconItem.Item_code,
        subconItem.BOQ_item,
        subconItem.Quantity,
        subconItem.Unit_price,
        subconItem.Milestone_1st,
        subconItem.Milestone_2nd,
        subconItem.Milestone_3rd,
        subconItem.Milestone_4th,
        subconItem.additional_cost,
        subconItem.quantity_reduce,
        subconItem.remark,
        un.uniqueness_field as uniqueness_field,
        subcon.Project_code,
        subconItem.Region,
        subconItem.Phase
        from
        memm_157ac31323c34d46920918117cb577ad as subconItem
        left join memm_ff802d120a12430db18a68deb783b9c6 as subcon on JSON_UNQUOTE(subconItem.Subcon_PO -> '$[0]') = subcon.id
        left join memm_562ace74337e462289972ce20939e9a7 un on un.id = JSON_UNQUOTE(subconItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON subconItem.uniqueness_field = siteItem.uniqueness_field
        AND subconItem.Subcon_PO IS NOT NULL
        AND subconItem.is_deleted = 0
        where subconItem.is_deleted = 0 and subcon.is_deleted = 0 and un.is_deleted = 0
        <if test="flag != null and flag != ''">
            AND siteItem.Site_item_status = JSON_ARRAY('unclose')
        </if>
        AND siteItem.Site_item_status != JSON_ARRAY('invalid')
        AND (
        1694550407313264642 IN
        <foreach collection="roleList" open="(" close=")" separator="," item="roleId">
            #{roleId}
        </foreach>
        OR
        EXISTS (
        SELECT project.id
        FROM memm_72a2450126dd41708a07374eff08b982 project
        LEFT JOIN memm_439131c30ad445e6810ba53e13fd9cfb per on project.id = JSON_UNQUOTE(per.project->'$[0]') and per.is_deleted = 0
        WHERE project.is_deleted = 0
        AND JSON_CONTAINS(per.y2_query, JSON_ARRAY(#{userId}))
        AND project.YPTT_Project_code = subconItem.Project_code
        )
        )

        <if test="map != null and map.size != 0 ">
            <foreach collection="map" index="key" item="value" separator="and" open="and" close="">
                <choose>
                    <when test="key == 'PO' || key == 'uniqueness_field' || key == 'site' || key == 'Customer_project'">
                        JSON_UNQUOTE(poItem.${key}->'$[0]') = #{value}
                    </when>
                    <otherwise>
                        subconItem.${key} = #{value}
                    </otherwise>
                </choose>
            </foreach>
        </if>
    </select>
    <select id="exportPR" resultType="java.util.Map">
        SELECT
          uf.id,
          uf.uniqueness_field AS unId,
          YPTTProject.YPTT_Project_code AS projectCode,
          YPTTProject.YPTT_Project_name AS projectName,
          dept.`name` as deptName,
          site.Region,
          site.Site_Serial_number AS siteID,
          site.site_name AS siteName,
          siteItem.site_allocation_date AS siteAllocationDate,
          siteItem.Phase,
          siteItem.Type_of_service,
          siteItem.Site_model,
          siteItem.Item_code,
          siteItem.BOQ_item,
          siteItem.Quantity,
          siteItem.Unit_price,
          siteItem.Site_value,
          siteDelivery.Site_belong_to,
          siteDelivery.PIC_PC_PM,
          siteDelivery.Start_Working_date,
          siteDelivery.Completed_work_date,
          siteDelivery.E_ATP_Pass
        FROM
          memm_562ace74337e462289972ce20939e9a7 uf
          LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field
          AND siteItem.is_deleted = 0
          LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site ON uf.Site_ID = site.Site_Serial_number
          AND site.is_deleted = 0
          LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 siteDelivery ON siteDelivery.uniqueness_field = siteItem.uniqueness_field
          AND siteDelivery.is_deleted = 0
          LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject ON uf.Project_code = YPTTProject.YPTT_Project_code
          AND YPTTProject.is_deleted = 0
          LEFT JOIN sys_dept dept ON JSON_ARRAY(CONCAT(dept.dept_id)) = YPTTProject.Department
          AND dept.is_deleted = 0
          where
        uf.Project_code = #{projectCode,jdbcType=VARCHAR} and uf.is_deleted = 0 and siteItem.is_deleted = 0
        and site.is_deleted = 0 and siteDelivery.is_deleted = 0 and YPTTProject.is_deleted = 0
        AND siteItem.Site_item_status != JSON_ARRAY('invalid')
        <if test="itemCode != null and itemCode != ''">
            AND uf.Item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="phase != null and phase != ''">
            AND uf.Phase = #{phase,jdbcType=VARCHAR}
        </if>
        <if test="region != null and region != ''">
            AND uf.Region = #{region,jdbcType=VARCHAR}
        </if>
        <if test="siteId != null and siteId != ''">
            AND uf.Site_ID = #{siteId,jdbcType=VARCHAR}
        </if>
        <if test="unId != null and unId != ''">
            AND uf.uniqueness_field = #{unId,jdbcType=VARCHAR}
        </if>
        <if test="ids != null and ids.size != 0">
            and uf.id in
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="searchPR" resultType="java.util.Map">
        SELECT
        uf.id,
        uf.uniqueness_field AS unId,
        YPTTProject.YPTT_Project_code AS projectCode,
        YPTTProject.YPTT_Project_name AS projectName,
        dept.`name` as deptName,
        site.Region,
        site.Site_Serial_number AS siteID,
        site.site_name AS siteName,
        siteItem.site_allocation_date AS siteAllocationDate,
        siteItem.Phase,
        siteItem.Type_of_service,
        siteItem.Site_model,
        siteItem.Item_code,
        siteItem.BOQ_item,
        siteItem.Quantity,
        siteItem.Unit_price,
        siteItem.Site_value,
        siteDelivery.Site_belong_to,
        siteDelivery.PIC_PC_PM,
        siteDelivery.Start_Working_date,
        siteDelivery.Completed_work_date,
        siteDelivery.E_ATP_Pass,
        CASE WHEN EXISTS (
        SELECT 1 FROM memm_pr_download_1f120kq4jd401 d
        WHERE d.uf_id = uf.id
        AND d.is_deleted = 0
        ) THEN 1 ELSE 0 END AS isDownloaded
        FROM
        memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field
        AND siteItem.is_deleted = 0
        LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site ON uf.Site_ID = site.Site_Serial_number
        AND site.is_deleted = 0
        LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 siteDelivery ON siteDelivery.uniqueness_field = siteItem.uniqueness_field
        AND siteDelivery.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject ON uf.Project_code = YPTTProject.YPTT_Project_code
        AND YPTTProject.is_deleted = 0
        LEFT JOIN sys_dept dept ON JSON_ARRAY(CONCAT(dept.dept_id)) = YPTTProject.Department
        AND dept.is_deleted = 0
        where
        uf.Project_code = #{projectCode,jdbcType=VARCHAR} and uf.is_deleted = 0 and siteItem.is_deleted = 0
        and site.is_deleted = 0 and siteDelivery.is_deleted = 0 and YPTTProject.is_deleted = 0
        AND siteItem.Site_item_status != JSON_ARRAY('invalid')
        <if test="itemCode != null and itemCode != ''">
            AND uf.Item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="phase != null and phase != ''">
            AND uf.Phase = #{phase,jdbcType=VARCHAR}
        </if>
        <if test="region != null and region != ''">
            AND uf.Region = #{region,jdbcType=VARCHAR}
        </if>
        <if test="siteId != null and siteId != ''">
            AND uf.Site_ID = #{siteId,jdbcType=VARCHAR}
        </if>
        <if test="unId != null and unId != ''">
            AND uf.uniqueness_field = #{unId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="searchDeatil" resultType="java.util.Map">
        SELECT
          id,
          download_date,
          download_user as user_id,
          su.fullname as download_user,
          uf_id
        FROM
          memm_pr_download_1f120kq4jd401 as prd
        left join sys_user as su on su.user_id = prd.download_user
        WHERE
          prd.is_deleted = 0
          AND prd.uf_id = #{ufId}
    </select>
    <select id="selectUserFullNameById" resultType="java.lang.String">
        select fullName from sys_user where is_deleted = 0 and user_id = #{userId}
    </select>
    <select id="searchTotal" resultType="java.util.Map">
        SELECT
        prt.project_code,
        su.fullname AS download_user,
        prt.download_num,
        prt.download_no,
        prt.download_date,
        prt.download_user as user_id
        FROM
        memm_pr_total_1f120kq4jd401 AS prt
        LEFT JOIN sys_user AS su ON su.user_id = prt.download_user
        WHERE
        prt.is_deleted = 0
        <if test="projectCode != null and projectCode != ''">
            AND prt.project_code = #{projectCode,jdbcType=VARCHAR}
        </if>
        <if test="userId != null and userId != ''">
            AND prt.download_user = #{userId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="searchPRBaseData" resultType="java.util.Map">
        SELECT
          uf.id,
          uf.uniqueness_field AS unId,
          site.Region,
          site.Site_Serial_number AS siteID,
          site.site_name AS siteName,
          siteItem.site_allocation_date AS siteAllocationDate,
          siteItem.Phase,
          siteItem.Type_of_service,
          siteItem.Site_model,
          siteItem.Item_code,
          siteItem.BOQ_item,
          siteItem.Quantity,
          siteItem.Unit_price,
          siteItem.Site_value,
          CASE
            WHEN EXISTS (SELECT 1 FROM memm_pr_download_1f120kq4jd401 d WHERE d.uf_id = uf.id AND d.is_deleted = 0) THEN
              1
            ELSE
              0
          END AS isDownloaded
        FROM
          memm_562ace74337e462289972ce20939e9a7 uf
          LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field
          AND siteItem.is_deleted = 0
          LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site ON uf.Site_ID = site.Site_Serial_number
          AND site.is_deleted = 0
        WHERE
        uf.is_deleted = 0
          AND siteItem.is_deleted = 0
          AND site.is_deleted = 0
          AND siteItem.Site_item_status != JSON_ARRAY('invalid')
          and uf.Project_code = #{projectCode}
        <if test="itemCode != null and itemCode != ''">
            AND uf.Item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="phase != null and phase != ''">
            AND uf.Phase = #{phase,jdbcType=VARCHAR}
        </if>
        <if test="region != null and region != ''">
            AND uf.Region = #{region,jdbcType=VARCHAR}
        </if>
        <if test="unId != null and unId != ''">
            AND uf.uniqueness_field = #{unId,jdbcType=VARCHAR}
        </if>
        <if test="listSiteIds != null and !listSiteIds.isEmpty()">
            AND uf.Site_ID IN
            <foreach collection="listSiteIds" open="(" close=")" separator="," item="siteId">
                #{siteId}
            </foreach>
        </if>
    </select>
    <select id="selectOtherInfoByUnIds" resultType="java.util.Map">
        SELECT
          uf.uniqueness_field AS unId,
          YPTTProject.YPTT_Project_code AS projectCode,
          YPTTProject.YPTT_Project_name AS projectName,
        YPTTProject.Department as deptId,
        siteDelivery.Site_belong_to,
        siteDelivery.PIC_PC_PM,
        siteDelivery.Start_Working_date,
        siteDelivery.Completed_work_date,
        siteDelivery.E_ATP_Pass
      FROM
        memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 siteDelivery ON siteDelivery.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        AND siteDelivery.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject ON uf.Project_code = YPTTProject.YPTT_Project_code
        AND YPTTProject.is_deleted = 0
      <!-- 左连接部门表
        LEFT JOIN sys_dept dept ON JSON_ARRAY(CONCAT(dept.dept_id)) = YPTTProject.Department
        AND dept.is_deleted = 0
        -->
          where
          siteDelivery.is_deleted = 0
          and YPTTProject.is_deleted = 0
          <if test="ufIds != null and !ufIds.isEmpty()">
              AND  uf.id IN
              <foreach item="item" index="index" collection="ufIds" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
    </select>
    <select id="searchPRBaseData2" resultType="java.util.Map">
        SELECT
        uf.id,
        uf.uniqueness_field AS unId,
        site.Region,
        site.Site_Serial_number AS siteID,
        site.site_name AS siteName,
        siteItem.site_allocation_date AS siteAllocationDate,
        siteItem.Phase,
        siteItem.Type_of_service,
        siteItem.Site_model,
        siteItem.Item_code,
        siteItem.BOQ_item,
        siteItem.Quantity,
        siteItem.Unit_price,
        siteItem.Site_value,
        CASE
        WHEN EXISTS (SELECT 1 FROM memm_pr_download_1f120kq4jd401 d WHERE d.uf_id = uf.id AND d.is_deleted = 0) THEN
        1
        ELSE
        0
        END AS isDownloaded
        FROM
        memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field
        AND siteItem.is_deleted = 0
        LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site ON uf.Site_ID = site.Site_Serial_number
        AND site.is_deleted = 0
        WHERE
        uf.is_deleted = 0
        AND siteItem.is_deleted = 0
        AND site.is_deleted = 0
        AND siteItem.Site_item_status != JSON_ARRAY('invalid')
        and uf.Project_code = #{projectCode}
        <if test="itemCode != null and itemCode != ''">
            AND uf.Item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="phase != null and phase != ''">
            AND uf.Phase = #{phase,jdbcType=VARCHAR}
        </if>
        <if test="region != null and region != ''">
            AND uf.Region = #{region,jdbcType=VARCHAR}
        </if>
        <if test="unId != null and unId != ''">
            AND uf.uniqueness_field = #{unId,jdbcType=VARCHAR}
        </if>
        <if test="ids != null and !ids.isEmpty()">
            and uf.id in
            <foreach collection="ids" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="listSiteIds != null and !listSiteIds.isEmpty()">
            AND uf.Site_ID IN
            <foreach collection="listSiteIds" open="(" close=")" separator="," item="siteId">
                #{siteId}
            </foreach>
        </if>
    </select>
    <select id="selectDeptInfo" resultType="java.util.Map">
        SELECT
          CONCAT('["', dept_id, '"]') AS deptId,
          NAME AS deptName
        FROM
          sys_dept
        WHERE
          is_deleted = 0
          and tenant_id = #{tenantId}
    </select>
    <select id="exportY1V2" resultType="java.util.Map">
        select
        concat(siteItem.id) id,
        siteItem.Project_code,
        site.Region,
        site.Area,
        siteItem.Site_ID,
        site.Site_Name,
        DATE_FORMAT(siteItem.site_allocation_date, '%Y-%m-%d') 'site_allocation_date',
        siteItem.Phase,
        siteItem.Type_of_service,
        siteItem.Site_model,
        siteItem.Item_code,
        siteItem.BOQ_item,
        siteItem.Quantity,
        siteItem.Unit_price,
        siteItem.Remark,
        un.uniqueness_field as uniqueness_field,
        CASE
        siteItem.Site_item_status
        WHEN '["unclose"]' THEN
        'unclose'
        WHEN '["close"]' THEN
        'close'
        WHEN '["invalid"]' THEN
        'invalid' ELSE 'unknown'
        END AS Site_item_status
        from
        memm_e648652640b44b2092c93e1742e6171b as siteItem
        left join memm_448208a319fa4d7ab3d77ee54e10c066 as site on site.id = JSON_UNQUOTE(siteItem.site -> '$[0]')
        left join memm_562ace74337e462289972ce20939e9a7 un on un.id = JSON_UNQUOTE(siteItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
        where
        siteItem.is_deleted = 0 and site.is_deleted = 0 and un.is_deleted = 0
        <if test="flag != null and flag != ''">
            AND siteItem.Site_item_status = JSON_ARRAY('unclose')
        </if>
        AND siteItem.Site_item_status != JSON_ARRAY('invalid')
        AND (
        1694550407313264642 IN
        <foreach collection="roleList" open="(" close=")" separator="," item="roleId">
            #{roleId}
        </foreach>
        OR
        EXISTS (
        SELECT project.id
        FROM memm_72a2450126dd41708a07374eff08b982 project
        LEFT JOIN memm_439131c30ad445e6810ba53e13fd9cfb per on project.id = JSON_UNQUOTE(per.project->'$[0]') and per.is_deleted = 0
        WHERE project.is_deleted = 0
        AND JSON_CONTAINS(per.y2_query, JSON_ARRAY(#{userId}))
        AND project.YPTT_Project_code = siteItem.Project_code
        )
        )

        <if test="conditions != null and conditions.size != 0 ">
        <foreach collection="conditions" item="item" separator="and" open="and" close="">
            <choose>
                <when test="item.name == 'Subcon' || item.name == 'uniqueness_field'">
                    JSON_UNQUOTE(siteItem.${item.name}->'$[0]') = #{item.value}
                </when>
                <when test="DATE_FILED_LIST.contains(item.name)">
                    <choose>
                        <when test="item.symbol == 'range' and item.value instanceof java.util.List and !item.value.empty">
                            <![CDATA[
                            siteItem.${item.name} >= STR_TO_DATE(#{item.value[0]}, '%Y-%m-%d')
                            AND siteItem.${item.name} <= STR_TO_DATE(#{item.value[1]}, '%Y-%m-%d')
                            ]]>
                        </when>
                        <otherwise>
                            <![CDATA[
                            siteItem.${item.name} ${item.symbol} STR_TO_DATE(#{item.value}, '%Y-%m-%d')
                            ]]>
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    siteItem.${item.name} LIKE CONCAT('%', #{item.value}, '%')
                </otherwise>
            </choose>
        </foreach>
    </if>
    </select>
    <select id="exportY2V2" resultType="java.util.Map">
        SELECT
        concat(poItem.id) id,
        DATE_FORMAT(po.PO_Received_date, '%Y-%m-%d') 'PO_Received_date',
        po.PO_Number,
        poItem.Contract_number,
        cus.Custom_project_name as 'Custom_project_name',
        po.Project_code,
        poItem.Region,
        poItem.Phase,
        poItem.Site_ID,
        poItem.Item_code,
        poItem.BOQ_item,
        poItem.quantity,
        poItem.Unit_price,
        poItem.Pre_payment,
        poItem.Milestone_1st,
        poItem.Milestone_2nd,
        poItem.Milestone_3rd,
        poItem.Milestone_4th,
        poItem.Remark,
        poItem.re_record,
        poItem.quantity_reduce,
        un.uniqueness_field as uniqueness_field,
        site.site_name 'Site_Name'
        FROM
        memm_f37920ed96f942fb8f4b1bf16f79e39c as poItem
        left join memm_ed87f18383f04a8f836cea32a1628fc9 as po on po.id = JSON_UNQUOTE(poItem.PO -> '$[0]')
        left join memm_562ace74337e462289972ce20939e9a7 un on un.id = JSON_UNQUOTE(poItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
        left join memm_448208a319fa4d7ab3d77ee54e10c066 as site on site.id = JSON_UNQUOTE(poItem.site -> '$[0]')
        left join memm_f15b45017dee432daf88693b3d13b60b as cus on cus.id =  JSON_UNQUOTE(poItem.Customer_project -> '$[0]') and cus.is_deleted = 0
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON JSON_ARRAY(CONCAT(un.id)) = siteItem.uniqueness_field
        where poItem.is_deleted = 0
        AND po.is_deleted = 0
        AND siteItem.is_deleted = 0
        <if test="flag != null and flag != ''">
            AND siteItem.Site_item_status = JSON_ARRAY('unclose')
        </if>
        AND siteItem.Site_item_status != JSON_ARRAY('invalid')
        AND (
        1694550407313264642 IN
        <foreach collection="roleList" open="(" close=")" separator="," item="roleId">
            #{roleId}
        </foreach>
        OR
        EXISTS (
        SELECT project.id
        FROM memm_72a2450126dd41708a07374eff08b982 project
        LEFT JOIN memm_439131c30ad445e6810ba53e13fd9cfb per on project.id = JSON_UNQUOTE(per.project->'$[0]') and per.is_deleted = 0
        WHERE project.is_deleted = 0
        AND JSON_CONTAINS(per.y2_query, JSON_ARRAY(#{userId}))
        AND project.YPTT_Project_code = poItem.Project_code
        )
        )

        <if test="conditions != null and conditions.size != 0 ">
        <foreach collection="conditions" item="item" separator="and" open="and" close="">
            <choose>
                <when test="item.name == 'Subcon' || item.name == 'uniqueness_field'">
                    JSON_UNQUOTE(poItem.${item.name}->'$[0]') = #{item.value}
                </when>
                <when test="DATE_FILED_LIST.contains(item.name)">
                    <choose>
                        <when test="item.symbol == 'range' and item.value instanceof java.util.List and !item.value.empty">
                            <![CDATA[
                            poItem.${item.name} >= #{item.value[0]}
                            AND poItem.${item.name} <= #{item.value[1]}
                            ]]>
                        </when>
                        <otherwise>
                            <![CDATA[
                            poItem.${item.name} ${item.symbol} #{item.value}
                            ]]>
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    poItem.${item.name} = #{item.value}
                </otherwise>
            </choose>
        </foreach>
        </if>
    </select>


</mapper>
