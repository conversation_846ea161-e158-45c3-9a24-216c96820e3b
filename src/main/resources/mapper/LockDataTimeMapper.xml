<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.LockDataTimeMapper">
    <resultMap id="listVo" type="com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo">
<!--        <result column="id" property="id"/>-->
        <result column="module" property="moduleType" javaType="java.lang.String"/>
<!--        <result column="lock_time_start" property="startTime"/>-->
<!--        <result column="lock_time_end" property="endTime"/>-->
    </resultMap>

    <insert id="execData">
        insert into memm_lock_data_time_1f120kq4jd401
        (id, module, lock_time_start, lock_time_end, project_code, is_deleted)
        VALUES
        (#{id}, #{module}, #{startTime}, #{endTime}, #{projectCode}, 0)
    </insert>
    <insert id="addData">
        insert into memm_lock_data_time_1f120kq4jd401
        (id, lock_time_start, lock_time_end, project_code, is_deleted, type_lock)
        VALUES
        (#{id}, #{startTime}, #{endTime}, #{projectCode}, 0 , #{typeLock})
    </insert>
    <update id="updateTime">
        update memm_lock_data_time_1f120kq4jd401
        set lock_time_start = #{startTime},lock_time_end = #{endTime}
        where is_deleted = 0 and module = #{module} and project_code = #{projectCode}
    </update>
    <update id="updateTimeV2">
        update memm_lock_data_time_1f120kq4jd401
        set lock_time_start = #{startTime},lock_time_end = #{endTime}
        where is_deleted = 0 and project_code = #{projectCode}
    </update>
    <delete id="del">
        delete from memm_lock_data_time_1f120kq4jd401
        where id in
        <foreach collection="idList" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </delete>


    <select id="list" resultType="com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo">
        select
        id,
        module as moduleType,
        lock_time_start as startTime,
        lock_time_end as endTime,
        project_code as projectCode
        from memm_lock_data_time_1f120kq4jd401
        where is_deleted = 0
        <if test="module != null and module != ''">
            and module = #{module}
        </if>
        <if test="projectCode != null and projectCode != ''">
            and project_code = #{projectCode}
        </if>
    </select>
    <select id="selectByIds" resultType="com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo">
        select
        id,
        module as moduleType,
        lock_time_start as startTime,
        lock_time_end as endTime,
        project_code as projectCode
        from memm_lock_data_time_1f120kq4jd401
        where is_deleted = 0
        and id in
        <foreach collection="idList" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>
    <select id="selectPage" resultType="com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo">
        select
        id,
        module as moduleType,
        lock_time_start as startTime,
        lock_time_end as endTime,
        project_code as projectCode
        from memm_lock_data_time_1f120kq4jd401
        where is_deleted = 0
        <if test="module != null and module != ''">
            and module = #{module}
        </if>
        <if test="projectCodes != null">
            and project_code in
            <foreach collection="projectCodes" open="(" close=")" separator="," item="code">
                #{code}
            </foreach>
        </if>

    </select>
    <select id="selectAllV2" resultType="com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo">
        select
        id,
        module as moduleType,
        lock_time_start as startTime,
        lock_time_end as endTime,
        project_code as projectCode,
        type_lock as typeLock
        from memm_lock_data_time_1f120kq4jd401
        where is_deleted = 0
        and type_lock = 1
        <if test="projectCodes != null">
            and project_code in
            <foreach collection="projectCodes" open="(" close=")" separator="," item="code">
                #{code}
            </foreach>
        </if>
    </select>
    <select id="selectPageV2" resultType="com.pig4cloud.pig.yptt.entity.vo.LockDataTimeVo">
        select
        id,
        lock_time_start as startTime,
        lock_time_end as endTime,
        project_code as projectCode
        from memm_lock_data_time_1f120kq4jd401
        where is_deleted = 0
        and type_lock = 1
        <if test="projectCodes != null">
            and project_code in
            <foreach collection="projectCodes" open="(" close=")" separator="," item="code">
                #{code}
            </foreach>
        </if>
    </select>
</mapper>
