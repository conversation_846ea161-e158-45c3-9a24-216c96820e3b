<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.ProjectStandingBookMapper">
    <select id="generateProjectStandingBookList" resultType="com.pig4cloud.pig.yptt.entity.dto.ProjectStandingBookDTO">
        SELECT project.id                                            AS ProjectId,
        project.YPTT_Project_name                             AS ProjectName,
        COUNT(site_item.id)                                   AS CountOfSiteItem,
        COUNT(po_item.id)                                     AS CountOfPoItem,
        SUM(IFNULL(site_item.Site_value, 0))                  AS TotalSiteValue,
        SUM(IFNULL(po_item.PO_value, 0))                      AS TotalPoValue,
        COUNT(subcon_po_item.id)                              AS CountOfSubconPoItem,
        SUM(IFNULL(subcon_po_item.Subcon_PO_amount, 0))       AS SubconPoAmount,
        SUM(IFNULL(subcon_po_item.additional_cost, 0))        AS SubconPoAddiCost,
        SUM(IFNULL(ready_for_settle.settlement_Amount, 0))    AS ReadyForSettleAmount,
        SUM(IFNULL(ready_for_settle.settlement_amountGap, 0)) AS NotReadySettleAmount,
        SUM(IFNULL(prod_report.Productivity_Amount, 0))       AS ReporttedProdAmount,
        SUM(IFNULL(subcon_settle.Totally_Amount, 0))          AS SubconSettleAmount,
        SUM(IFNULL(subcon_settle.Totally_amount_Gap, 0))      AS SubconSettleGap,
        SUM(IFNULL(subcon_payment.Totally_payment, 0))        AS SubconPayAmount,
        SUM(IFNULL(subcon_payment.Totally_payment_Gap, 0))    AS SubconPayAmountGap,
        SUM(IFNULL(yptt_settlement.Invoice_amount, 0))        AS InvoiceAmount,
        SUM(IFNULL(yptt_settlement.Invoice_amount_gap, 0))    AS InvoiceAmountGap

        FROM memm_72a2450126dd41708a07374eff08b982 AS project
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b AS site_item
        ON site_item.TPTT_Project = JSON_ARRAY(CONCAT(project.id)) and site_item.site is not null
        LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c AS po_item
        ON po_item.is_deleted = 0 AND po_item.uniqueness_field = site_item.uniqueness_field
        and po_item.PO is not null
        LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 As site_item_deli
        ON site_item_deli.is_deleted = 0 AND
        site_item_deli.uniqueness_field = site_item.uniqueness_field
        LEFT JOIN memm_157ac31323c34d46920918117cb577ad AS subcon_po_item
        ON subcon_po_item.is_deleted = 0 AND
        subcon_po_item.Subcon_PO IS NOT NULL AND
        subcon_po_item.uniqueness_field = site_item.uniqueness_field
        LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 AS ready_for_settle
        ON ready_for_settle.is_deleted = 0 AND
        ready_for_settle.uniqueness_field = site_item.uniqueness_field
        LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 AS prod_report
        ON prod_report.is_deleted = 0 AND prod_report.uniqueness_field = site_item.uniqueness_field
        LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 AS subcon_payment
        ON subcon_payment.is_deleted = 0 AND
        subcon_payment.Payment_time_1st IS NOT NULL AND
        subcon_payment.uniqueness_field = site_item.uniqueness_field
        LEFT JOIN memm_218a6ab9959842099fd074c2b0ef685b AS subcon_settle
        ON subcon_settle.is_deleted = 0 AND
        subcon_settle.settlement_ratio_1st is NOT NULL AND
        subcon_settle.uniqueness_field = site_item.uniqueness_field
        LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 AS yptt_settlement
        ON yptt_settlement.is_deleted = 0 AND
        yptt_settlement.uniqueness_field = site_item.uniqueness_field
        WHERE project.is_deleted = 0
        GROUP BY project.id
        LIMIT #{cur}, #{size}
    </select>

    <select id="queryByProjectId" resultType="com.pig4cloud.pig.yptt.entity.dto.ProjectStandingBookDTO">
        SELECT *
        FROM memm_54413ee2d0fe448b90c84fe06bb31ede AS project_standingbook
        WHERE is_deleted = 0
        AND ProjectId = #{projectId,jdbcType=BIGINT}
    </select>

    <update id="update">
        UPDATE
        memm_54413ee2d0fe448b90c84fe06bb31ede AS project_standingbook
        SET CountOfSiteItem      = #{dto.countOfSiteItem,jdbcType=INTEGER},
        CountOfPoItem        = #{dto.countOfPoItem,jdbcType=INTEGER},
        TotalSiteValue       = #{dto.totalSiteValue,jdbcType=DECIMAL},
        TotalPoValue         = #{dto.totalPoValue,jdbcType=DECIMAL},
        CountOfSubconPoItem  = #{dto.countOfSubconPoItem,jdbcType=INTEGER},
        SubconPoAmount       = #{dto.subconPoAmount,jdbcType=DECIMAL},
        SubconPoAddiCost     = #{dto.subconPoAddiCost,jdbcType=DECIMAL},
        ReadyForSettleAmount = #{dto.readyForSettleAmount,jdbcType=DECIMAL},
        NotReadySettleAmount = #{dto.notReadySettleAmount,jdbcType=DECIMAL},
        ReporttedProdAmount  = #{dto.reporttedProdAmount,jdbcType=DECIMAL},
        SubconSettleAmount   = #{dto.subconSettleAmount,jdbcType=DECIMAL},
        SubconSettleGap      = #{dto.subconSettleGap,jdbcType=DECIMAL},
        SubconPayAmount      = #{dto.subconPayAmount,jdbcType=DECIMAL},
        SubconPayAmountGap   = #{dto.subconPayAmountGap,jdbcType=DECIMAL},
        InvoiceAmount        = #{dto.invoiceAmount,jdbcType=DECIMAL},
        InvoiceAmountGap     = #{dto.invoiceAmountGap,jdbcType=DECIMAL}
        WHERE ProjectId = #{dto.projectId,jdbcType=BIGINT}
    </update>

    <insert id="save">
        INSERT INTO memm_54413ee2d0fe448b90c84fe06bb31ede
        (id,
        ProjectId,
        ProjectName,
        CountOfSiteItem,
        CountOfPoItem,
        TotalSiteValue,
        TotalPoValue,
        CountOfSubconPoItem,
        SubconPoAmount,
        SubconPoAddiCost,
        ReadyForSettleAmount,
        NotReadySettleAmount,
        ReporttedProdAmount,
        SubconSettleAmount,
        SubconSettleGap,
        SubconPayAmount,
        SubconPayAmountGap,
        InvoiceAmount,
        InvoiceAmountGap)
        VALUES (#{dto.id,jdbcType=BIGINT}, #{dto.projectId,jdbcType=BIGINT}, #{dto.projectName,jdbcType=VARCHAR},
        #{dto.countOfSiteItem,jdbcType=INTEGER}, #{dto.countOfPoItem,jdbcType=INTEGER},
        #{dto.totalSiteValue,jdbcType=DECIMAL}, #{dto.totalPoValue,jdbcType=DECIMAL},
        #{dto.countOfSubconPoItem,jdbcType=INTEGER},
        #{dto.subconPoAmount,jdbcType=DECIMAL}, #{dto.subconPoAddiCost,jdbcType=DECIMAL},
        #{dto.readyForSettleAmount,jdbcType=DECIMAL}, #{dto.notReadySettleAmount,jdbcType=DECIMAL},
        #{dto.reporttedProdAmount,jdbcType=DECIMAL}, #{dto.subconSettleAmount,jdbcType=DECIMAL},
        #{dto.subconSettleGap,jdbcType=DECIMAL}, #{dto.subconPayAmount,jdbcType=DECIMAL},
        #{dto.subconPayAmountGap,jdbcType=DECIMAL}, #{dto.invoiceAmount,jdbcType=DECIMAL},
        #{dto.invoiceAmountGap,jdbcType=DECIMAL})
    </insert>
</mapper>
