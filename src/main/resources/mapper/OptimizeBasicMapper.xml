<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.OptimizeBasicMapper">
    <!--
        查询基础数据 (uf, YPTTProject, site, dept)
        应用核心过滤条件，并选取后续关联所需的键 (ufId, siteUn)
    -->
    <select id="getBaseReportData" resultType="map">
        SELECT
        uf.id as ufId,
        uf.uniqueness_field as siteUn,
        IFNULL(DATE_FORMAT(uf.create_time, '%Y-%m-%d'), DATE_FORMAT(NOW(), '%Y-%m-%d')) AS Site_register_date,
        uf.Region,
        YPTTProject.YPTT_Project_name,
        YPTTProject.YPTT_Project_code,
        site.Site_Serial_number,
        site.site_name,
        site.Area,
        dept.name as name,
        site.site_name as poItemSiteName
        <if test="dateType != null and dateType != ''">
            <if test="dateType == 'Productivity_report_date' or dateType == 'product_invoice_date'">
                ,
                ProductivityReport.report_date_1st '1stProductivityReportDate',
                ProductivityReport.report_date_2nd '2ndProductivityReportDate',
                ProductivityReport.report_date_3rd '3rdProductivityReportDate',
                ProductivityReport.report_date_4th '4thProductivityReportDate'
            </if>
            <if test="dateType == 'KPI_Archive_date'">
                ,
                ProductivityReport.KPI_Archive_date as 'KPI-Archive-date'
            </if>
            <if test="dateType == 'Subcon_Payment_Time'">
                ,
                SubconPayment.Payment_time_1st 'Subcon-Payment-time-1st',
                SubconPayment.Payment_time_2st 'Subcon-Payment-time-2nd',
                SubconPayment.Payment_time_3st 'Subcon-Payment-time-3rd',
                SubconPayment.Payment_time_4st 'Subcon-Payment-time-4st'
            </if>
            <if test="dateType == 'Invoice_Date' or dateType == 'product_invoice_date'">
                ,
                YPTTSettlement.Invoice_date_1st  'Invoice-date-1st',
                YPTTSettlement.Invoice_date_2st  'Invoice-date-2nd',
                YPTTSettlement.Invoice_date_3st  'Invoice-date-3rd',
                YPTTSettlement.Invoice_date_4st  'Invoice-date-4th'
            </if>

        </if>
        FROM
        memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field
        AND siteItem.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        ON uf.Project_code = YPTTProject.YPTT_Project_code AND YPTTProject.is_deleted = 0
        LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site
        ON uf.Site_ID = site.Site_Serial_number AND site.is_deleted = 0
        <!-- 左连接部门表 -->
        <!-- **警告**: 下面的 JSON 连接非常低效! YPTTProject.Department 存储 JSON 数组，而 dept.dept_id 是普通类型 -->
        <!-- 理想情况是修改表结构，例如 YPTTProject 有一个 department_id 字段直接关联 dept.dept_id -->
        <!-- 临时方案 (性能差): 尝试使用 JSON_CONTAINS 或类似函数 -->
        LEFT JOIN sys_dept dept
        <!-- 检查 YPTTProject.Department (假设是 ["dept_id_1", "dept_id_2"] 格式) 是否包含 dept.dept_id -->
        <!-- 注意: 需要根据 YPTTProject.Department 的实际 JSON 结构调整 JSON 函数 -->
        <!-- ON JSON_CONTAINS(YPTTProject.Department, CAST(dept.dept_id AS JSON), '$') -->
        ON JSON_ARRAY(CONCAT(dept.dept_id)) = YPTTProject.Department
        <!-- 或者如果 Department 存的是 '["1","2"]' 而 dept_id 是数字: JSON_CONTAINS(YPTTProject.Department, CAST(dept.dept_id as char), '$') -->
        AND dept.is_deleted = 0
        <if test="dateType != null and dateType != ''">
            <if test="dateType == 'Productivity_report_date' or dateType == 'product_invoice_date'">
                LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
                on ProductivityReport.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                ProductivityReport.is_deleted = 0
            </if>
            <if test="dateType == 'KPI_Archive_date'">
                LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
                on ProductivityReport.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                ProductivityReport.is_deleted = 0
            </if>
            <if test="dateType == 'Subcon_Payment_Time'">
                LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
                on SubconPayment.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                SubconPayment.is_deleted = 0
            </if>
            <if test="dateType == 'Invoice_Date' or dateType == 'product_invoice_date'">
                LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 YPTTSettlement
                on YPTTSettlement.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                YPTTSettlement.is_deleted = 0
            </if>

        </if>
        WHERE
        uf.is_deleted = 0
        AND siteItem.site IS NOT NULL
        <if test="projectIds != null and !projectIds.isEmpty()">
            AND YPTTProject.id IN
            <foreach collection="projectIds" open="(" close=")" separator="," item="projectId">
                #{projectId}
            </foreach>
        </if>
        <if test="listUnIds != null and !listUnIds.isEmpty()">
            AND uf.uniqueness_field IN
            <foreach collection="listUnIds" open="(" close=")" separator="," item="unId">
                #{unId}
            </foreach>
        </if>

        <if test="area != null and area != ''">
            AND site.Area = #{area}
        </if>
        <if test="nation != null and nation != ''">
            AND YPTTProject.branch = #{nation}
        </if>
        <if test="dateType != null and dateType != '' and dateStrStart != null and dateStrEnd != null">
            AND
            <choose>
                <when test="dateType == 'ufCreateTime'">
                    uf.create_time BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                    STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                </when>
<!--                <when test="dateType == 'Productivity_report_date'">-->
<!--                    (ProductivityReport.report_date_1st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND-->
<!--                    STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')-->
<!--                    or-->
<!--                    ProductivityReport.report_date_2nd BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND-->
<!--                    STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')-->
<!--                    or-->
<!--                    ProductivityReport.report_date_3rd BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND-->
<!--                    STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')-->
<!--                    or-->
<!--                    ProductivityReport.report_date_4th BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND-->
<!--                    STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')-->
<!--                    )-->
<!--                    &lt;!&ndash; 注意: 如果按 siteItem 日期过滤，需要把 siteItem LEFT JOIN 进来 &ndash;&gt;-->
<!--                </when>-->
                <!-- 注意: 其他时间类型影响查询效率，改为业务逻辑筛选数据 -->
                <otherwise>
                    1 = 1 <!-- 如果 dateType 无效或未匹配，则不应用日期过滤 -->
                </otherwise>
            </choose>
        </if>
        <!-- 可以添加其他必要的过滤条件 -->
    </select>

    <!--
        Y1 模块: 查询 SiteItem 相关数据
        关联方式: JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field (低效!)
        使用传入的 ufIds 列表进行过滤
    -->
    <select id="getSiteItemDataForReport" resultType="map">
        SELECT
        <!-- 关键: 返回 siteItem.uniqueness_field 作为与基础数据关联的键 -->
        uf.uniqueness_field as siteUn,
        <!-- Y1 模块所需的其他字段 -->
        DATE_FORMAT( siteItem.site_allocation_date, '%Y-%m-%d' ) AS site_allocation_date,
        siteItem.Site_model,
        siteItem.BOQ_item,
        CASE siteItem.Site_item_status WHEN '["unclose"]' THEN '未关闭' WHEN '["close"]' THEN '已关闭'  WHEN '["invalid"]' THEN '无效' ELSE '未知' END AS
        Site_item_status,
        siteItem.Item_code,
        siteItem.Phase,
        siteItem.Type_of_service,
        siteItem.Quantity,
        siteItem.Unit_price,
        siteItem.Site_value,
        siteItem.Remark as siteItemRemark -- 使用别名避免冲突
        FROM
        memm_e648652640b44b2092c93e1742e6171b siteItem
        left join memm_562ace74337e462289972ce20939e9a7 uf on JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field and siteItem.is_deleted = 0
        WHERE
        siteItem.is_deleted = 0
        and uf.is_deleted = 0
        <if test="ufIds != null and !ufIds.isEmpty()">
            AND  uf.id IN
            <foreach item="item" index="index" collection="ufIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
<!--        <if test="ufIds != null and !ufIds.isEmpty()">-->
<!--            &lt;!&ndash;-->
<!--             **警告**: 下面的连接方式效率极低! 数据库无法有效利用索引。-->
<!--             这是基于原始 SQL 逻辑的模拟。最佳方案是修改表结构，让 siteItem 直接存储 uf_id。-->
<!--             如果不能修改表结构，以下 EXISTS 子查询是一种可能的实现，但性能依然堪忧。-->
<!--            &ndash;&gt;-->
<!--            AND EXISTS (-->
<!--            SELECT 1 FROM memm_562ace74337e462289972ce20939e9a7 temp_uf-->
<!--            WHERE temp_uf.id IN-->
<!--            <foreach item="item" index="index" collection="ufIds" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            &lt;!&ndash; 这里的 JSON 构造方式必须与 siteItem.uniqueness_field 的实际存储格式完全匹配 &ndash;&gt;-->
<!--            AND siteItem.uniqueness_field = JSON_ARRAY(CONCAT(temp_uf.id))-->
<!--            )-->
<!--        </if>-->
    </select>

    <!--
        Y2 模块: 查询 PO 相关数据 (poItem, po, cusProject)
        关联方式: JSON_ARRAY(CONCAT(uf.id)) = poItem.uniqueness_field (低效!)
        使用传入的 ufIds 列表进行过滤
    -->
    <select id="getPODataForReport" resultType="map">
        SELECT
        <!-- 关键: 返回 poItem.uniqueness_field 作为关联键 -->
        uf.uniqueness_field as siteUn,
        <!-- Y2 模块所需的字段 -->
        DATE_FORMAT( po.PO_Received_date, '%Y-%m-%d' ) AS PO_Received_date,
        po.PO_number,
        poItem.Quantity AS poItemQuantity,
        poItem.quantity_reduce AS poItemQuantityReduce,
        cusProject.Contract_number,
        cusProject.Custom_project_name,
        poItem.Unit_price AS poUp,
        poItem.PO_value,
        poItem.PO_gap,
        poItem.BOQ_item AS po_BOQ_item,
        poItem.Pre_payment,
        poItem.Milestone_1st AS 'SiteSettlementMilestone1st%',
        poItem.Milestone_2nd AS 'SiteSettlementMilestone2nd%',
        poItem.Milestone_3rd AS 'SiteSettlementMilestone3rd%',
        poItem.Milestone_4th AS 'SiteSettlementMilestone4th%',
        poItem.Remark AS poItemRemark
        <!-- 原SQL中用于检查 PO 是否存在的 IF 列可以省略，因为这里直接 JOIN -->
        ,poItem.Item_code AS poItemcode
        ,poItem.Site_ID AS poItemSiteID
        ,poItem.Project_code AS poItemProjectcode
        ,poItem.Phase AS poItemPhase
        <!--,site.site_name AS poItemSiteName  如果需要 site_name，需额外 JOIN site 表，会增加复杂度-->
        ,poItem.Region AS poItemRegion
        ,uf.uniqueness_field as poItemUn
        FROM
        memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
        LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
        <!-- **警告**: 再次出现低效的 JSON 连接! poItem.PO 存储 JSON 数组 -->
--         ON JSON_CONTAINS(poItem.PO, CAST(po.id AS JSON), '$') AND po.is_deleted = 0
        ON JSON_ARRAY(CONCAT(po.id)) = poItem.PO AND po.is_deleted = 0
        LEFT JOIN memm_f15b45017dee432daf88693b3d13b60b cusProject
        <!-- **警告**: 同样低效的 JSON 连接! poItem.Customer_project 存储 JSON 数组 -->
--         ON JSON_CONTAINS(poItem.Customer_project, CAST(cusProject.id AS JSON), '$') AND cusProject.is_deleted = 0
        ON JSON_ARRAY(CONCAT(cusProject.id)) = poItem.Customer_project AND cusProject.is_deleted = 0
        <!-- **警告**: 低效连接! -->
        left join memm_562ace74337e462289972ce20939e9a7 uf on poItem.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        WHERE
        poItem.is_deleted = 0
        and uf.is_deleted = 0
        <if test="ufIds != null and !ufIds.isEmpty()">
            AND  uf.id IN
            <foreach item="item" index="index" collection="ufIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
<!--        <if test="ufIds != null and !ufIds.isEmpty()">-->
<!--            &lt;!&ndash; **警告**: 低效连接! &ndash;&gt;-->
<!--            AND EXISTS (-->
<!--            SELECT 1 FROM memm_562ace74337e462289972ce20939e9a7 temp_uf-->
<!--            WHERE temp_uf.id IN-->
<!--            <foreach item="item" index="index" collection="ufIds" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            AND poItem.uniqueness_field = JSON_ARRAY(CONCAT(temp_uf.id))-->
<!--            )-->
<!--        </if>-->
    </select>

    <!--
        Y3 模块: 查询 SiteDelivery 相关数据
        关联方式: 假设 siteDelivery.uniqueness_field = uf.uniqueness_field
        使用传入的 uniqueFields (即 siteUn 列表) 过滤
    -->
    <select id="getSiteDeliveryDataForReport" resultType="map">
        SELECT
        <!-- 关键: 返回 siteDelivery.uniqueness_field 作为关联键 -->
        uf.uniqueness_field as siteUn,
        <!-- Y3 模块所需的其他字段 -->
        siteDelivery.Site_belong_to,
        siteDelivery.Team_Leader_DT,
        siteDelivery.engineer_DTA_SPV,
        siteDelivery.PLO_PC_Others,
        siteDelivery.PIC_PC_PM,
        DATE_FORMAT( siteDelivery.Start_Working_date, '%Y-%m-%d' ) AS Start_Working_date,
        DATE_FORMAT( siteDelivery.Completed_work_date, '%Y-%m-%d' ) AS Completed_work_date,
        DATE_FORMAT( siteDelivery.air_CI_Report_submit, '%Y-%m-%d' ) AS air_CI_Report_submit,
        DATE_FORMAT( siteDelivery.Site_manager_Report, '%Y-%m-%d' ) AS Site_manager_Report,
        DATE_FORMAT( siteDelivery.E_ATP_Pass, '%Y-%m-%d' ) AS E_ATP_Pass,
        DATE_FORMAT( siteDelivery.F_PAC_Pass, '%Y-%m-%d' ) AS F_PAC_Pass,
        DATE_FORMAT( siteDelivery.G_FAC, '%Y-%m-%d' ) AS G_FAC,
        siteDelivery.remark AS deliveryRemark,
        DATE_FORMAT( siteDelivery.settlement_1st, '%Y-%m-%d' ) AS 'site-ReadyForSettlement-1st',
        DATE_FORMAT( siteDelivery.settlement_2nd, '%Y-%m-%d' ) AS 'site-ReadyForSettlement-2nd',
        DATE_FORMAT( siteDelivery.settlement_3rd, '%Y-%m-%d' ) AS 'site-ReadyForSettlement-3rd',
        DATE_FORMAT( siteDelivery.settlement_4th, '%Y-%m-%d' ) AS 'site-ReadyForSettlement-4th',
        DATE_FORMAT( siteDelivery.SubconSettlement_1st, '%Y-%m-%d' ) AS 'site-Subcon-settlementDate-1st',
        DATE_FORMAT( siteDelivery.SubconSettlement_2nd, '%Y-%m-%d' ) AS 'site-Subcon-settlementDate-2nd',
        DATE_FORMAT( siteDelivery.SubconSettlement_3rd, '%Y-%m-%d' ) AS 'site-Subcon-settlementDate-3rd',
        DATE_FORMAT( siteDelivery.SubconSettlement_4th, '%Y-%m-%d' ) AS 'site-Subcon-settlementDate-4th'
        FROM
        memm_e45cb01fc742457a85ed8243aff1aa28 siteDelivery
        left join memm_562ace74337e462289972ce20939e9a7 uf on siteDelivery.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        WHERE
        siteDelivery.is_deleted = 0
        and uf.is_deleted = 0
        <if test="uniqueFields != null and !uniqueFields.isEmpty()">
            AND  uf.id IN
            <foreach item="item" index="index" collection="uniqueFields" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
<!--        <if test="uniqueFields != null and !uniqueFields.isEmpty()">-->
<!--            &lt;!&ndash; 使用传入的 uniqueFields 列表进行过滤 &ndash;&gt;-->
<!--            AND siteDelivery.uniqueness_field IN-->
<!--            <foreach item="item" index="index" collection="uniqueFields" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
    </select>

    <!--
        Y4 模块: 查询 Subcontractor 相关数据 (subItem, subPo, sub)
        关联方式: 假设 subItem.uniqueness_field = uf.uniqueness_field
    -->
    <select id="getSubcontractorDataForReport" resultType="map">
        SELECT
        <!-- 关键: 返回 subItem.uniqueness_field 作为关联键 -->
        uf.uniqueness_field as siteUn,
        <!-- Y4 模块所需字段 -->
        sub.Subcon_name,
        subPo.Subcon_PO_number,
        DATE_FORMAT( subPo.release_date, '%Y-%m-%d' ) AS release_date,
        subItem.Subcon_PO_amount,
        subItem.Quantity AS subQuantity,
        subItem.Unit_price AS subUp,
        subItem.quantity_reduce AS subQuantityReduce,
        subItem.Milestone_1st AS 'SubconSettlementMilestone1st',
        subItem.Milestone_2nd AS 'SubconSettlementMilestone2nd',
        subItem.Milestone_3rd AS 'SubconSettlementMilestone3rd',
        subItem.Milestone_4th AS 'SubconSettlementMilestone4th',
        subItem.remark AS subRemark,
        subItem.Site_ID AS subSiteID,
        subItem.BOQ_item AS subBOQitem,
        subItem.Item_code AS subItemcode,
        <!-- site.site_name AS subSiteName,  需要额外关联 site 表 -->
        subItem.additional_cost
        <!-- CONCAT(...) AS subconUniquenessField  这个字段可以在 Java 层构建，或者在这里构建 -->
        ,CONCAT_WS('_', sub.Subcon_name, subPo.Subcon_PO_number, subItem.Site_ID, subItem.BOQ_item,
        subItem.Subcon_PO_amount) as subconUniquenessField
        FROM
        memm_157ac31323c34d46920918117cb577ad subItem
        <!-- **警告**: 低效的 JSON 连接 -->
        LEFT JOIN memm_ff802d120a12430db18a68deb783b9c6 subPo
        on subItem.Subcon_PO = JSON_ARRAY(CONCAT(subPo.id)) and subPo.is_deleted = 0

        <!-- **警告**: 低效的 JSON 连接 -->
        LEFT JOIN memm_134d9474dc244b26bfd7f013a0534710 sub
        on subItem.Subcon = JSON_ARRAY(CONCAT(sub.id)) and sub.is_deleted = 0

        left join memm_562ace74337e462289972ce20939e9a7 uf on JSON_ARRAY(CONCAT(uf.id)) = subItem.uniqueness_field and subItem.is_deleted = 0
        WHERE
        subItem.is_deleted = 0
        AND subItem.Subcon_PO IS NOT NULL
        and uf.is_deleted = 0
        <!-- 根据原始 SQL 添加的条件-->
        <if test="uniqueFields != null and !uniqueFields.isEmpty()">
            AND uf.id IN
            <foreach item="item" index="index" collection="uniqueFields" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--    &lt;!&ndash;-->
    <!--        Y5 模块: 查询 ReadySettlement 相关数据-->
    <!--        关联方式: 假设 ReadySettlement.uniqueness_field = uf.uniqueness_field-->
    <!--    &ndash;&gt;-->
    <select id="getReadySettlementDataForReport" resultType="map">
        SELECT
        uf.uniqueness_field as siteUn,
        DATE_FORMAT( ReadySettlement.Pre_Settlement_date, '%Y-%m-%d' ) AS Pre_Settlement_date,
        ReadySettlement.Pre_payment_ratio,
        ReadySettlement.Pre_payment_amount,
        DATE_FORMAT( ReadySettlement.settlement_1st, '%Y-%m-%d' ) AS 'ReadyForSettlement-1st',
        ReadySettlement.Settlement_ratio_1st AS 'settlement%-1st',
        ReadySettlement.amount_1st AS 'settlementAmount-1st',
        DATE_FORMAT( ReadySettlement.settlement_2nd, '%Y-%m-%d' ) AS 'ReadyForSettlement-2nd',
        ReadySettlement.Settlement_ratio_2nd AS 'settlement%-2nd',
        ReadySettlement.amount_2nd AS 'settlementAmount-2nd',
        DATE_FORMAT( ReadySettlement.settlement_3rd, '%Y-%m-%d' ) AS 'ReadyForSettlement-3rd',
        ReadySettlement.Settlement_ratio_3rd AS 'settlement%-3rd',
        ReadySettlement.amount_3rd AS 'settlementAmount-3rd',
        DATE_FORMAT( ReadySettlement.settlement_4th, '%Y-%m-%d' ) AS 'ReadyForSettlement-4th',
        ReadySettlement.Settlement_ratio_4th AS 'settlement%-4th',
        ReadySettlement.amount_4th AS 'settlementAmount-4th',
        ReadySettlement.settlement_Amount AS ReadySettlementAmount,
        ReadySettlement.settlement_amountGap AS readySettlementAmountGap
        FROM
        memm_abdf4191a91e436a9b7e04351042f757 ReadySettlement
        left join memm_562ace74337e462289972ce20939e9a7 uf on ReadySettlement.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        WHERE
        ReadySettlement.is_deleted = 0
        and uf.is_deleted = 0
        <if test="uniqueFields != null and !uniqueFields.isEmpty()">
            AND uf.id IN
            <foreach item="item" index="index" collection="uniqueFields" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--    &lt;!&ndash;-->
    <!--        Y6 模块: 查询 ProductivityReport 相关数据-->
    <!--        关联方式: 假设 ProductivityReport.uniqueness_field = uf.uniqueness_field-->
    <!--    &ndash;&gt;-->
    <select id="getProductivityDataForReport" resultType="map">
        SELECT
        uf.uniqueness_field as siteUn,
        DATE_FORMAT( ProductivityReport.report_date_1st, '%Y-%m-%d' ) AS '1stProductivityReportDate',
        ProductivityReport.report_amount_1st AS '1stProductivityReportAmount',
        DATE_FORMAT( ProductivityReport.report_date_2nd, '%Y-%m-%d' ) AS '2ndProductivityReportDate',
        ProductivityReport.report_amount_2nd AS '2ndProductivityReportAmount',
        DATE_FORMAT( ProductivityReport.report_date_3rd, '%Y-%m-%d' ) AS '3rdProductivityReportDate',
        ProductivityReport.report_amount_3rd AS '3rdProductivityReportAmount',
        DATE_FORMAT( ProductivityReport.report_date_4th, '%Y-%m-%d' ) AS '4thProductivityReportDate',
        ProductivityReport.report_amount_4th AS '4thProductivityReportAmount',
        ProductivityReport.Productivity_Amount AS ProductivityAmount,
        ProductivityReport.declaration_ratio AS 'ProductivityDeclarationRatio%',
        ProductivityReport.KPI_Archive_date AS 'KPI-Archive-date',
        ProductivityReport.KPI_Archive_amount AS 'KPI-Archive-amount'
        FROM
        memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
        left join memm_562ace74337e462289972ce20939e9a7 uf on ProductivityReport.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        WHERE
        ProductivityReport.is_deleted = 0
        and uf.is_deleted = 0
        <if test="uniqueFields != null and !uniqueFields.isEmpty()">
            AND uf.id IN
            <foreach item="item" index="index" collection="uniqueFields" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--    &lt;!&ndash;-->
    <!--        Y7 模块: 查询 SubconSettlement 相关数据-->
    <!--        关联方式: 假设 SubconSettlement.uniqueness_field = uf.uniqueness_field-->
    <!--    &ndash;&gt;-->
    <select id="getSubconSettlementDataForReport" resultType="map">
        SELECT
        uf.uniqueness_field as siteUn,
        DATE_FORMAT( SubconSettlement.settlement_time_1st, '%Y-%m-%d' ) AS 'Subcon-settlement-time-1st',
        SubconSettlement.settlement_ratio_1st AS 'Subcon-settlement%-1st',
        SubconSettlement.settlementAmount_1st AS 'Subcon-settlement-amount-1st',
        DATE_FORMAT( SubconSettlement.settlement_time_2nd, '%Y-%m-%d' ) AS 'Subcon-settlement-time-2nd',
        SubconSettlement.settlement_ratio_2nd AS 'Subcon-settlement%-2nd',
        SubconSettlement.settlementAmount_2nd AS 'Subcon-settlement-amount-2nd',
        DATE_FORMAT( SubconSettlement.settlement_time_3rd, '%Y-%m-%d' ) AS 'Subcon-settlement-time-3rd',
        SubconSettlement.settlement_ratio_3rd AS 'Subcon-settlement%-3rd',
        SubconSettlement.settlementAmount_3rd AS 'Subcon-settlement-amount-3rd',
        DATE_FORMAT( SubconSettlement.settlement_time_4th, '%Y-%m-%d' ) AS 'Subcon-settlement-time-4th',
        SubconSettlement.settlement_ratio_4th AS 'Subcon-settlement%-4st', -- 注意原始 SQL 别名是 4st
        SubconSettlement.settlementAmount_4th AS 'Subcon-settlement-amount-4th',
        SubconSettlement.Totally_Amount AS SubconTotallySettlementAmount,
        SubconSettlement.Totally_amount_Gap AS SubconTotallySettlementGap
        FROM
        memm_218a6ab9959842099fd074c2b0ef685b SubconSettlement
        left join memm_562ace74337e462289972ce20939e9a7 uf on SubconSettlement.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        WHERE
        SubconSettlement.is_deleted = 0
        and uf.is_deleted = 0
        <if test="uniqueFields != null and !uniqueFields.isEmpty()">
            AND uf.id IN
            <foreach item="item" index="index" collection="uniqueFields" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--    &lt;!&ndash;-->
    <!--        Y8 模块: 查询 SubconPayment 相关数据-->
    <!--        关联方式: 假设 SubconPayment.uniqueness_field = uf.uniqueness_field-->
    <!--    &ndash;&gt;-->
    <select id="getSubconPaymentDataForReport" resultType="map">
        SELECT
        uf.uniqueness_field as siteUn,
        DATE_FORMAT( SubconPayment.Payment_time_1st, '%Y-%m-%d' ) AS 'Subcon-Payment-time-1st',
        SubconPayment.payment_amount_1st AS 'Subcon-payment-amount-1st',
        DATE_FORMAT( SubconPayment.Payment_time_2st, '%Y-%m-%d' ) AS 'Subcon-Payment-time-2nd', -- 注意原始 SQL 别名是 2st
        SubconPayment.payment_amount_2st AS 'Subcon-payment-amount-2nd',
        DATE_FORMAT( SubconPayment.Payment_time_3st, '%Y-%m-%d' ) AS 'Subcon-Payment-time-3rd',
        SubconPayment.payment_amount_3st AS 'Subcon-payment-amount-3rd',
        DATE_FORMAT( SubconPayment.Payment_time_4st, '%Y-%m-%d' ) AS 'Subcon-Payment-time-4st',
        SubconPayment.payment_amount_4st AS 'Subcon-payment-amount-4st',
        SubconPayment.payment_number_1st AS 'Subcon-payment-number-1st',
        SubconPayment.payment_number_2st AS 'Subcon-payment-number-2st',
        SubconPayment.payment_number_3st AS 'Subcon-payment-number-3st',
        SubconPayment.payment_number_4st AS 'Subcon-payment-number-4st',
        SubconPayment.remark AS subPayRemark,
        SubconPayment.Totally_payment AS SubconTotallyPaymentAmount,
        SubconPayment.Totally_payment_gap AS SubconTotallyPaymentAmountGap,
        SubconPayment.CN_amount_1st AS 'subcon-CN-amount-1st',
        SubconPayment.CN_remark_1st AS 'subcon-CN-remark-1st',
        SubconPayment.CN_date_1st AS 'subcon-CN-date-1st',
        SubconPayment.CN_number_1st AS 'subcon-CN-number-1st',
        SubconPayment.CN_amount_2nd AS 'subcon-CN-amount-2nd',
        SubconPayment.CN_remark_2nd AS 'subcon-CN-remark-2nd',
        SubconPayment.CN_date_2nd AS 'subcon-CN-date-2nd',
        SubconPayment.CN_number_2nd AS 'subcon-CN-number-2nd',
        SubconPayment.CN_amount_3rd AS 'subcon-CN-amount-3rd',
        SubconPayment.CN_remark_3rd AS 'subcon-CN-remark-3rd',
        SubconPayment.CN_date_3rd AS 'subcon-CN-date-3rd',
        SubconPayment.CN_number_3rd AS 'subcon-CN-number-3rd',
        SubconPayment.CN_amount_4st AS 'subcon-CN-amount-4st',
        SubconPayment.CN_remark_4st AS 'subcon-CN-remark-4st',
        SubconPayment.CN_date_4st AS 'subcon-CN-date-4st',
        SubconPayment.CN_number_4st AS 'subcon-CN-number-4st',
        SubconPayment.Totally_CN_amount AS 'subcon-Totally-CN-amount'
        FROM
        memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
        left join memm_562ace74337e462289972ce20939e9a7 uf on SubconPayment.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        WHERE
        SubconPayment.is_deleted = 0
        and uf.is_deleted = 0
        <if test="uniqueFields != null and !uniqueFields.isEmpty()">
            AND uf.id IN
            <foreach item="item" index="index" collection="uniqueFields" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--    &lt;!&ndash;-->
    <!--        Y9 模块: 查询 YPTTSettlement 相关数据-->
    <!--        关联方式: 假设 YPTTSettlement.uniqueness_field = uf.uniqueness_field-->
    <!--    &ndash;&gt;-->
    <select id="getYPTTSettlementDataForReport" resultType="map">
        SELECT
        uf.uniqueness_field as siteUn,
        DATE_FORMAT( YPTTSettlement.Invoice_date_1st, '%Y-%m-%d' ) AS 'Invoice-date-1st',
        YPTTSettlement.Invoice_number_1st AS 'Invoice-number-1st',
        YPTTSettlement.Invoice_Amount_1st AS 'Invoice-Amount-1st',
        YPTTSettlement.Invoice_Amount_diff_1st AS 'Invoice-Amount-Diff-1st',
        YPTTSettlement.Invoice_remark_1st AS 'Invoice-remark-1st',
        DATE_FORMAT( YPTTSettlement.Invoice_date_2st, '%Y-%m-%d' ) AS 'Invoice-date-2nd',
        YPTTSettlement.Invoice_number_2st AS 'Invoice-number-2nd',
        YPTTSettlement.Invoice_Amount_2st AS 'Invoice-Amount-2nd',
        YPTTSettlement.Invoice_Amount_diff_2st AS 'Invoice-Amount-Diff-2nd',
        YPTTSettlement.Invoice_remark_2st AS 'Invoice-remark-2nd',
        DATE_FORMAT( YPTTSettlement.Invoice_date_3st, '%Y-%m-%d' ) AS 'Invoice-date-3rd',
        YPTTSettlement.Invoice_number_3st AS 'Invoice-number-3rd',
        YPTTSettlement.Invoice_Amount_3st AS 'Invoice-Amount-3rd',
        YPTTSettlement.Invoice_Amount_diff_3st AS 'Invoice-Amount-Diff-3rd',
        YPTTSettlement.Invoice_remark_3st AS 'Invoice-remark-3rd',
        DATE_FORMAT( YPTTSettlement.Invoice_date_4st, '%Y-%m-%d' ) AS 'Invoice-date-4th',
        YPTTSettlement.Invoice_number_4st AS 'Invoice-number-4th',
        YPTTSettlement.Invoice_Amount_4st AS 'Invoice-Amount-4th',
        YPTTSettlement.Invoice_Amount_diff_4st AS 'Invoice-Amount-Diff-4th',
        YPTTSettlement.Invoice_remark_4st AS 'Invoice-remark-4th',
        YPTTSettlement.remark AS ypttRemark,
        YPTTSettlement.Invoice_amount AS TotallyInvoiceAmount,
        YPTTSettlement.Invoice_amount_gap AS InvoiceAmountGAP,
        YPTTSettlement.CN_amount_1st AS 'ypttsettlement-CN-amount-1st',
        YPTTSettlement.CN_remark_1st AS 'ypttsettlement-CN-remark-1st',
        YPTTSettlement.CN_date_1st AS 'ypttsettlement-CN-date-1st',
        YPTTSettlement.CN_number_1st AS 'ypttsettlement-CN-number-1st',
        YPTTSettlement.CN_amount_2nd AS 'ypttsettlement-CN-amount-2nd',
        YPTTSettlement.CN_remark_2nd AS 'ypttsettlement-CN-remark-2nd',
        YPTTSettlement.CN_date_2nd AS 'ypttsettlement-CN-date-2nd',
        YPTTSettlement.CN_number_2nd AS 'ypttsettlement-CN-number-2nd',
        YPTTSettlement.CN_amount_3rd AS 'ypttsettlement-CN-amount-3rd',
        YPTTSettlement.CN_remark_3rd AS 'ypttsettlement-CN-remark-3rd',
        YPTTSettlement.CN_date_3rd AS 'ypttsettlement-CN-date-3rd',
        YPTTSettlement.CN_number_3rd AS 'ypttsettlement-CN-number-3rd',
        YPTTSettlement.CN_amount_4st AS 'ypttsettlement-CN-amount-4st',
        YPTTSettlement.CN_remark_4st AS 'ypttsettlement-CN-remark-4st',
        YPTTSettlement.CN_date_4st AS 'ypttsettlement-CN-date-4st',
        YPTTSettlement.CN_number_4st AS 'ypttsettlement-CN-number-4st',
        YPTTSettlement.Totally_CN_amount AS 'ypttsettlement-Totally-CN-amount'
        FROM
        memm_4bf72c9a610c4b05a007f0f215b424a6 YPTTSettlement
        left join memm_562ace74337e462289972ce20939e9a7 uf on YPTTSettlement.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        WHERE
        YPTTSettlement.is_deleted = 0
        and uf.is_deleted = 0
        <if test="uniqueFields != null and !uniqueFields.isEmpty()">
            AND uf.id IN
            <foreach item="item" index="index" collection="uniqueFields" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getBaseReportDataReportForm" resultType="java.util.Map">
        SELECT
        uf.id as ufId,
        uf.uniqueness_field as siteUn,
        IFNULL(DATE_FORMAT(uf.create_time, '%Y-%m-%d'), DATE_FORMAT(NOW(), '%Y-%m-%d')) AS Site_register_date,
        uf.Region,
        YPTTProject.YPTT_Project_name,
        YPTTProject.YPTT_Project_code,
        site.Site_Serial_number,
        site.site_name,
        site.Area,
        dept.name as name,
        site.site_name as poItemSiteName
        FROM
        memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        ON uf.Project_code = YPTTProject.YPTT_Project_code AND YPTTProject.is_deleted = 0
        LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site
        ON uf.Site_ID = site.Site_Serial_number AND site.is_deleted = 0
        <!-- 左连接部门表 -->
        <!-- **警告**: 下面的 JSON 连接非常低效! YPTTProject.Department 存储 JSON 数组，而 dept.dept_id 是普通类型 -->
        <!-- 理想情况是修改表结构，例如 YPTTProject 有一个 department_id 字段直接关联 dept.dept_id -->
        <!-- 临时方案 (性能差): 尝试使用 JSON_CONTAINS 或类似函数 -->
        LEFT JOIN sys_dept dept
        <!-- 检查 YPTTProject.Department (假设是 ["dept_id_1", "dept_id_2"] 格式) 是否包含 dept.dept_id -->
        <!-- 注意: 需要根据 YPTTProject.Department 的实际 JSON 结构调整 JSON 函数 -->
        <!-- ON JSON_CONTAINS(YPTTProject.Department, CAST(dept.dept_id AS JSON), '$') -->
        ON JSON_ARRAY(CONCAT(dept.dept_id)) = YPTTProject.Department
        <!-- 或者如果 Department 存的是 '["1","2"]' 而 dept_id 是数字: JSON_CONTAINS(YPTTProject.Department, CAST(dept.dept_id as char), '$') -->
        AND dept.is_deleted = 0
        WHERE
        uf.is_deleted = 0
        <if test="projectIds != null and !projectIds.isEmpty()">
            AND YPTTProject.id IN
            <foreach collection="projectIds" open="(" close=")" separator="," item="projectId">
                #{projectId}
            </foreach>
        </if>
        <if test="area != null and area != ''">
            AND site.Area = #{area}
        </if>
        <if test="nation != null and nation != ''">
            AND YPTTProject.branch = #{nation}
        </if>
        <if test="dateType != null and dateType != '' and dateStrStart != null and dateStrEnd != null">
            AND
            <choose>
                <when test="dateType == 'ufCreateTime'">
                    uf.create_time BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                    STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                </when>
                <!--                <when test="dateType == 'site_allocation_date'">-->
                <!--                    siteItem.site_allocation_date BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND-->
                <!--                    STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')-->
                <!--                    &lt;!&ndash; 注意: 如果按 siteItem 日期过滤，需要把 siteItem LEFT JOIN 进来 &ndash;&gt;-->
                <!--                </when>-->
                <otherwise>
                    1 = 1 <!-- 如果 dateType 无效或未匹配，则不应用日期过滤 -->
                </otherwise>
            </choose>
        </if>
        <!-- 可以添加其他必要的过滤条件 -->
    </select>
</mapper>