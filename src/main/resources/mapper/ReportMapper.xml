<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.ReportMapper">

    <select id="selectPOItem" resultType="java.util.Map">
        select *,uniq.uniqueness_field 'uniField' from memm_f37920ed96f942fb8f4b1bf16f79e39c as poItem
        left join memm_562ace74337e462289972ce20939e9a7 as uniq on JSON_ARRAY(CONCAT(uniq.id)) = poItem.uniqueness_field
        where poItem.is_deleted = 0 and uniq.is_deleted = 0
    </select>
    <select id="statisticsUserOperateY1" resultType="com.pig4cloud.pig.yptt.entity.StatisticsUserOperate">
        SELECT
            y1.create_by,
            DATE_FORMAT(y1.create_time, '%Y-%m-%d') as createTime,
            su.fullname,
            y1i.YPTT_Project_code,
            YPTTProject.YPTT_Project_name,
            COUNT(DISTINCT y1i.uniqueness_field) AS effectiveQuantity,
            y1i.uniqueness_field as unId,
            'Y1' as moduleType
        FROM
            memm_e61f1146eb994bf19c1425ae0378e20a y1
        LEFT JOIN
            sys_user AS su ON y1.create_by = su.user_id
        LEFT JOIN
            memm_Importing_Y1_Importing_Y1_Item_mdr_8rq9u AS y1r
            ON y1.id = y1r.left_data_id
        LEFT JOIN
            memm_336275a450394d9f8ce3f7e6f121aa3b AS y1i
            ON y1i.id = y1r.right_data_id
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        on y1i.YPTT_Project_code = YPTTProject.YPTT_Project_code and YPTTProject.is_deleted = 0
        WHERE
            y1i.is_deleted = 0
            and y1.is_deleted = 0
            AND y1i.Import_Status = 'SUCCEED'
            and DATE_FORMAT(y1.create_time, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        <if test="projectCode != null and projectCode != ''">
            and y1i.YPTT_Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and y1.create_by = #{userId}
        </if>
        GROUP BY
            y1.create_by,
            su.fullname,
            y1i.YPTT_Project_code,
            y1i.uniqueness_field,
            DATE_FORMAT(y1.create_time, '%Y-%m-%d');
    </select>
    <select id="statisticsUserOperateY2" resultType="com.pig4cloud.pig.yptt.entity.StatisticsUserOperate">
        SELECT
        y2.create_by,
        DATE_FORMAT(y2.create_time, '%Y-%m-%d') as createTime,
        su.fullname,
        y2i.YPTT_Project_code,
        YPTTProject.YPTT_Project_name,
        COUNT(DISTINCT y2i.uniqueness_field) AS effectiveQuantity,
        y2i.uniqueness_field as unId,
        'Y2' as moduleType
    FROM
        memm_315465c31eba471fa1d8e40ba7263d91 y2
    LEFT JOIN
        sys_user AS su ON y2.create_by = su.user_id
    LEFT JOIN
        memm_Importing_Y2_Importing_Y2_Item_mdr_jrk2n AS y2r
        ON y2.id = y2r.left_data_id
    LEFT JOIN
        memm_c9428c09f1914bd68df4bde20bb6fc0a AS y2i
        ON y2i.id = y2r.right_data_id
    LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
    on y2i.YPTT_Project_code = YPTTProject.YPTT_Project_code and YPTTProject.is_deleted = 0
    WHERE
        y2i.is_deleted = 0
        and y2.is_deleted = 0
        AND y2i.Import_Status = 'SUCCEED'
        and DATE_FORMAT(y2.create_time, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        <if test="projectCode != null and projectCode != ''">
            and y2i.YPTT_Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and y2.create_by = #{userId}
        </if>
    GROUP BY
        y2.create_by,
        su.fullname,
        y2i.YPTT_Project_code,
        y2i.uniqueness_field,
        DATE_FORMAT(y2.create_time, '%Y-%m-%d');
    </select>
    <select id="statisticsUserOperateY3" resultType="com.pig4cloud.pig.yptt.entity.StatisticsUserOperate">
        SELECT
            y3.create_by,
            DATE_FORMAT(y3.create_time, '%Y-%m-%d') as createTime,
            su.fullname,
            y3i.YPTT_Project_code,
            YPTTProject.YPTT_Project_name,
            COUNT(DISTINCT y3i.uniqueness_field) AS effectiveQuantity,
            y3i.uniqueness_field as unId,
            'Y3' as moduleType
        FROM
            memm_f65ca48677d34bf8a0fbdeda4988554e y3
        LEFT JOIN
            sys_user AS su ON y3.create_by = su.user_id
        LEFT JOIN
            memm_Importing_Y3_Importing_Y3_Item_mdr_b9t0i AS y3r
            ON y3.id = y3r.left_data_id
        LEFT JOIN
            memm_940817f9e4d842f5a7a089b37f96b176 AS y3i
            ON y3i.id = y3r.right_data_id
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        on y3i.YPTT_Project_code = YPTTProject.YPTT_Project_code and YPTTProject.is_deleted = 0
        WHERE
            y3i.is_deleted = 0
            and y3.is_deleted = 0
            AND y3i.Import_Status = 'SUCCEED'
            and DATE_FORMAT(y3.create_time, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        <if test="projectCode != null and projectCode != ''">
            and y3i.YPTT_Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and y3.create_by = #{userId}
        </if>
        GROUP BY
            y3.create_by,
            su.fullname,
            y3i.YPTT_Project_code,
            y3i.uniqueness_field,
            DATE_FORMAT(y3.create_time, '%Y-%m-%d')

    </select>
    <select id="statisticsUserOperateY4" resultType="com.pig4cloud.pig.yptt.entity.StatisticsUserOperate">
        SELECT
            y4.create_by,
            DATE_FORMAT(y4.create_time, '%Y-%m-%d') as createTime,
            su.fullname,
            y4i.YPTT_Project_code,
            YPTTProject.YPTT_Project_name,
            COUNT(DISTINCT y4i.uniqueness_field) AS effectiveQuantity,
            y4i.uniqueness_field as unId,
            'Y4' as moduleType
        FROM
            memm_42548c91d07842478da94f235d87f601 y4
        LEFT JOIN
            sys_user AS su ON y4.create_by = su.user_id
        LEFT JOIN
            memm_Importing_Y4_Importing_Y4_Item_mdr_ypurk AS y4r
            ON y4.id = y4r.left_data_id
        LEFT JOIN
            memm_9f1f8dfbea1a4308bc385560228b6665 AS y4i
            ON y4i.id = y4r.right_data_id
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        on y4i.YPTT_Project_code = YPTTProject.YPTT_Project_code and YPTTProject.is_deleted = 0
        WHERE
            y4i.is_deleted = 0
            and y4.is_deleted = 0
            AND y4i.Import_Status = 'SUCCEED'
            and DATE_FORMAT(y4.create_time, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        <if test="projectCode != null and projectCode != ''">
            and y4i.YPTT_Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and y4.create_by = #{userId}
        </if>
        GROUP BY
            y4.create_by,
            su.fullname,
            y4i.YPTT_Project_code,
            y4i.uniqueness_field,
            DATE_FORMAT(y4.create_time, '%Y-%m-%d');

    </select>
    <select id="statisticsUserOperateY8" resultType="com.pig4cloud.pig.yptt.entity.StatisticsUserOperate">
        SELECT
            y8.create_by,
            DATE_FORMAT(y8.create_time, '%Y-%m-%d') as createTime,
            su.fullname,
            subPo.Project_code as YPTTProjectCode,
            YPTTProject.YPTT_Project_name,
            COUNT(DISTINCT y8i.uniqueness_field) AS effectiveQuantity,
            y8i.uniqueness_field as unId,
            'Y8' as moduleType
        FROM
            memm_e55281091b9f42789cc873ccd40c043c as y8
        LEFT JOIN
            sys_user AS su ON y8.create_by = su.user_id
        LEFT JOIN
            memm_Importing_Y8_Importing_Y8_Item_mdr_ljy7q AS y8r
            ON y8.id = y8r.left_data_id
        LEFT JOIN
            memm_de9f0d4541bf48009c8d527e26410b1c AS y8i
            ON y8i.id = y8r.right_data_id
        left join memm_ff802d120a12430db18a68deb783b9c6 as subPo
        on y8i.Subcon_PO_number = subPo.Subcon_PO_number
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        on subPo.Project_code = YPTTProject.YPTT_Project_code and YPTTProject.is_deleted = 0
        WHERE
            y8i.is_deleted = 0
            and y8.is_deleted = 0
            AND y8i.Import_Status = 'SUCCEED'
            and DATE_FORMAT(y8.create_time, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        <if test="projectCode != null and projectCode != ''">
            and YPTTProject.YPTT_Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and y8.create_by = #{userId}
        </if>
        GROUP BY
            y8.create_by,
            su.fullname,
            subPo.Project_code,
            y8i.uniqueness_field,
            DATE_FORMAT(y8.create_time, '%Y-%m-%d')

    </select>
    <select id="statisticsUserOperateY9" resultType="com.pig4cloud.pig.yptt.entity.StatisticsUserOperate">
        SELECT
            y9.create_by,
            DATE_FORMAT(y9.create_time, '%Y-%m-%d') as createTime,
            su.fullname,
            YPTTProject.YPTT_Project_code,
            YPTTProject.YPTT_Project_name,
            COUNT(DISTINCT y9i.uniqueness_field) AS effectiveQuantity,
            y9i.uniqueness_field as unId,
            'Y9' as moduleType
        FROM
            memm_5e6512ba1d5e4c83a1fac1ea682d6c8c y9
        LEFT JOIN
            sys_user AS su ON y9.create_by = su.user_id
        LEFT JOIN
            memm_Importing_Y9_Importing_Y9_Item_mdr_bqvgx AS y9r
            ON y9.id = y9r.left_data_id
        LEFT JOIN
            memm_19f2793e1d8a480bb7fd2e32eaa1eaea AS y9i
            ON y9i.id = y9r.right_data_id
        LEFT join memm_ed87f18383f04a8f836cea32a1628fc9 PO
        on PO.PO_number = y9i.PO_number
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        on PO.Project_code = YPTTProject.YPTT_Project_code and YPTTProject.is_deleted = 0
        WHERE
            y9i.is_deleted = 0
            and y9.is_deleted = 0
            AND y9i.Import_Status = 'SUCCEED'
            and DATE_FORMAT(y9.create_time, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        <if test="projectCode != null and projectCode != ''">
            and YPTTProject.YPTT_Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and y9.create_by = #{userId}
        </if>
        GROUP BY
            y9.create_by,
            su.fullname,
            y9i.uniqueness_field,
            YPTTProject.YPTT_Project_code,
            YPTTProject.YPTT_Project_name,
            y9i.uniqueness_field,
            DATE_FORMAT(y9.create_time, '%Y-%m-%d')

    </select>
    <select id="getUserInfo" resultType="com.pig4cloud.pig.yptt.entity.vo.GetUserInfo">
        select user_id,fullname as fullName from sys_user
         where is_deleted = 0
        <if test="name != null and name != ''">
            and fullname like CONCAT('%', #{name,jdbcType=VARCHAR},'%')
        </if>
    </select>
    <select id="getProject" resultType="com.pig4cloud.pig.yptt.entity.vo.GetProject">
        select
        YPTT_Project_code as projectCode,
        YPTT_Project_name as projectName
        from
        memm_72a2450126dd41708a07374eff08b982
        where is_deleted = 0
        <if test="name != null and name != ''">
            and (YPTT_Project_code like CONCAT('%', #{name,jdbcType=VARCHAR},'%')
                or YPTT_Project_name like CONCAT('%', #{name,jdbcType=VARCHAR},'%')
            )
        </if>
    </select>
    <select id="statisticsUserOperateY1V2" resultType="java.util.Map">
        SELECT
        ifnull(COUNT(DISTINCT siteItem.id), 0) AS effectiveQuantity,
        siteItem.create_by,
        uf.Project_code
        FROM
          memm_562ace74337e462289972ce20939e9a7 uf
          LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field
        WHERE
          uf.is_deleted = 0
          AND siteItem.is_deleted = 0
        and DATE_FORMAT(siteItem.site_allocation_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        <if test="projectCode != null and projectCode != ''">
            and uf.Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and siteItem.create_by = #{userId}
        </if>
        GROUP BY
        siteItem.create_by,
        uf.Project_code
    </select>
    <select id="statisticsUserOperateY2V2" resultType="java.util.Map">
        SELECT
        ifnull(COUNT(DISTINCT poItem.id),0) AS effectiveQuantity,
        poItem.create_by,
        uf.Project_code
        FROM
          memm_562ace74337e462289972ce20939e9a7 uf
          LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem ON poItem.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
          AND poItem.is_deleted = 0
          LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po ON JSON_ARRAY(CONCAT(po.id)) = poItem.PO
          AND po.is_deleted = 0
        WHERE
          uf.is_deleted = 0
          AND poItem.is_deleted = 0
        and DATE_FORMAT(po.PO_Received_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        <if test="projectCode != null and projectCode != ''">
            and uf.Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and poItem.create_by = #{userId}
        </if>
        GROUP BY
        poItem.create_by,
        uf.Project_code
    </select>
    <select id="statisticsUserOperateY4V2" resultType="java.util.Map">
        SELECT
        ifnull(COUNT(DISTINCT subItem.id),0) AS effectiveQuantity,
        subItem.create_by,
        uf.Project_code
        FROM
          memm_562ace74337e462289972ce20939e9a7 uf
          LEFT JOIN memm_157ac31323c34d46920918117cb577ad subItem ON subItem.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
          AND subItem.Subcon_PO IS NOT NULL
          AND subItem.is_deleted = 0
          LEFT JOIN memm_ff802d120a12430db18a68deb783b9c6 subPo ON subItem.Subcon_PO = JSON_ARRAY(CONCAT(subPo.id))
          AND subPo.is_deleted = 0
        WHERE
          uf.is_deleted = 0
          AND subItem.is_deleted = 0
        and DATE_FORMAT(subPo.release_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        <if test="projectCode != null and projectCode != ''">
            and uf.Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and subItem.create_by = #{userId}
        </if>
        GROUP BY
        subItem.create_by,
        uf.Project_code
    </select>
    <select id="statisticsUserOperateY3V2" resultType="java.util.Map">
        SELECT
        ifnull(COUNT(DISTINCT siteDelivery.id),0) AS effectiveQuantity,
        siteDelivery.create_by,
        uf.Project_code
        FROM
          memm_562ace74337e462289972ce20939e9a7 uf
          LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 siteDelivery ON siteDelivery.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
          AND siteDelivery.is_deleted = 0
        WHERE
          uf.is_deleted = 0
          AND siteDelivery.is_deleted = 0
        <if test="projectCode != null and projectCode != ''">
            and uf.Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and siteDelivery.create_by = #{userId}
        </if>
        and (
            DATE_FORMAT(siteDelivery.Start_Working_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
            or DATE_FORMAT(siteDelivery.Completed_work_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
            or DATE_FORMAT(siteDelivery.air_CI_Report_submit, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
            or DATE_FORMAT(siteDelivery.Site_manager_Report, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
            or DATE_FORMAT(siteDelivery.E_ATP_Pass, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
            or DATE_FORMAT(siteDelivery.F_PAC_Pass, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
            or DATE_FORMAT(siteDelivery.G_FAC, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        )
        GROUP BY
        siteDelivery.create_by,
        uf.Project_code
    </select>
    <select id="statisticsUserOperateY8V2" resultType="java.util.Map">
        SELECT
        SubconPayment.Payment_time_1st,
        SubconPayment.Payment_time_2st,
        SubconPayment.Payment_time_3st,
        SubconPayment.Payment_time_4st,
        SubconPayment.create_by,
        uf.Project_code
        FROM
        memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment ON SubconPayment.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        AND SubconPayment.is_deleted = 0
        WHERE
        uf.is_deleted = 0
        AND SubconPayment.is_deleted = 0
        <if test="projectCode != null and projectCode != ''">
            and uf.Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and SubconPayment.create_by = #{userId}
        </if>
        and (
        DATE_FORMAT(SubconPayment.Payment_time_1st, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        or DATE_FORMAT(SubconPayment.Payment_time_2st, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        or DATE_FORMAT(SubconPayment.Payment_time_3st, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        or DATE_FORMAT(SubconPayment.Payment_time_4st, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        )
    </select>
    <select id="statisticsUserOperateY9V2" resultType="java.util.Map">
        SELECT
          YPTTSettlement.Invoice_date_1st,
          YPTTSettlement.Invoice_date_2st,
          YPTTSettlement.Invoice_date_3st,
          YPTTSettlement.Invoice_date_4st,
        YPTTSettlement.create_by,
        uf.Project_code
        FROM
          memm_562ace74337e462289972ce20939e9a7 uf
          LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 YPTTSettlement ON YPTTSettlement.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        WHERE
          uf.is_deleted = 0
          AND YPTTSettlement.is_deleted = 0
        <if test="projectCode != null and projectCode != ''">
            and uf.Project_code = #{projectCode}
        </if>
        <if test="userId != null and userId != ''">
            and YPTTSettlement.create_by = #{userId}
        </if>
        and (
        DATE_FORMAT(YPTTSettlement.Invoice_date_1st, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        or DATE_FORMAT(YPTTSettlement.Invoice_date_2st, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        or DATE_FORMAT(YPTTSettlement.Invoice_date_3st, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        or DATE_FORMAT(YPTTSettlement.Invoice_date_4st, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
        )
    </select>
    <select id="getAllUserInfo" resultType="java.util.Map">
        SELECT
          user_id as id,fullname
        FROM
          sys_user
        WHERE
          is_deleted = 0
    </select>
    <select id="selectProjects" resultType="java.util.Map">
        SELECT
          YPTT_Project_code,
          YPTT_Project_name
        FROM
          memm_72a2450126dd41708a07374eff08b982
        WHERE
          is_deleted = 0
    </select>
</mapper>
