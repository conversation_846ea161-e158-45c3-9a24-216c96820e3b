<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.WarningMapper">
    <select id="selectSiteWarnTemp" resultType="com.pig4cloud.pig.yptt.entity.WarnTemp">
        SELECT siteItem.id,
               un.uniqueness_field   'uniquenessField',
               siteItem.Project_code   'projectCode',
               siteItem.TPTT_Project 'projectName'
        from memm_e648652640b44b2092c93e1742e6171b siteItem
                 LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
                           on siteItem.uniqueness_field = poItem.uniqueness_field and poItem.is_deleted = 0
                                and poItem.Project_code = #{projectCode}
                 LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
                           on po.id = JSON_UNQUOTE(poItem.PO -> '$[0]') and po.is_deleted = 0
                 LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un
                           on un.id = JSON_UNQUOTE(siteItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
        where siteItem.Site_item_status = JSON_ARRAY('unclose') and siteItem.is_deleted = 0
            and siteItem.Project_code = #{projectCode}
          and siteItem.site_allocation_date &lt; CURDATE() - INTERVAL (#{warn}) DAY
          and poItem.PO is null
          and ABS(poItem.PO_gap) > 1
        ORDER BY siteItem.id
        limit #{cur}, #{size}
    </select>

    <select id="selectRemoveSiteId" resultType="java.lang.Long">
        SELECT siteItem.id
        from memm_e648652640b44b2092c93e1742e6171b siteItem
                 LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
                           on siteItem.uniqueness_field = poItem.uniqueness_field and poItem.is_deleted = 0
                                and poItem.Project_code = #{projectCode}
                 LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
                           on po.id = JSON_UNQUOTE(poItem.PO -> '$[0]') and po.is_deleted = 0
        where po.is_deleted = 0  and siteItem.is_deleted = 0 and siteItem.Project_code = #{projectCode}
          and JSON_UNQUOTE(siteItem.warning -> '$[0]') = 'Site_PO_Delay'
          and (siteItem.site_allocation_date &gt; CURDATE() - INTERVAL (#{warn}) DAY
            OR poItem.PO is not null
            OR siteItem.Site_item_status != JSON_ARRAY('unclose')
            OR 1 >= ABS(poItem.PO_gap))
    </select>

    <update id="updateSiteWarning">
        update memm_e648652640b44b2092c93e1742e6171b
        set warning = JSON_ARRAY(CONCAT('',#{type,jdbcType=VARCHAR},''))
        where
        id in
        <foreach collection="ids" close=")" open="(" item="id" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="selectPoWarnTemp" resultType="com.pig4cloud.pig.yptt.entity.WarnTemp">
        SELECT poItem.id,
               un.uniqueness_field   'uniquenessField',
                poItem.Project_code   'projectCode',
               JSON_ARRAY(CONCAT(p.id)) 'projectName'
        from memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
                 LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
                           on po.id = JSON_UNQUOTE(poItem.PO -> '$[0]')
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem
                           on siteItem.uniqueness_field = poItem.uniqueness_field and siteItem.is_deleted = 0
                                and siteItem.Project_code = #{projectCode}
                 LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un
                           on un.id = JSON_UNQUOTE(siteItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
                LEFT JOIN memm_72a2450126dd41708a07374eff08b982 p on p.YPTT_Project_code = un.Project_code and p.is_deleted = 0
        where po.is_deleted = 0  and poItem.is_deleted = 0 and poItem.Project_code = #{projectCode}
        and po.PO_Received_date &lt; CURDATE() - INTERVAL (#{warn}) DAY
          and siteItem.site is null
          and siteItem.Site_item_status = JSON_ARRAY('unclose')
          and ABS(poItem.PO_gap) > 1
        ORDER BY poItem.id
        limit #{cur},#{size}
    </select>

    <select id="selectRemovePoId" resultType="java.lang.Long">
        SELECT poItem.id
        from memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
                 LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
                           on po.id = JSON_UNQUOTE(poItem.PO -> '$[0]') and poItem.is_deleted = 0
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem
                           on siteItem.uniqueness_field = poItem.uniqueness_field and siteItem.is_deleted = 0
                                and siteItem.Project_code = #{projectCode}
        where po.is_deleted = 0  and poItem.is_deleted = 0 and poItem.Project_code = #{projectCode}
          and JSON_UNQUOTE(poItem.siteInfo_Warning -> '$[0]') = 'Site_Delay'
          and (po.PO_Received_date &gt; CURDATE() - INTERVAL (#{warn}) DAY
            or siteItem.site is not null or siteItem.Site_item_status != JSON_ARRAY('unclose'))
    </select>

    <update id="updatePoWarning">
        update memm_f37920ed96f942fb8f4b1bf16f79e39c
        <choose>
            <when test="type == 'Site_Delay'">
                set siteInfo_Warning = JSON_ARRAY(CONCAT('',#{type,jdbcType=VARCHAR},''))
            </when>
            <when test="type == 'Amount_Error'">
                set PoAmount_Warning = JSON_ARRAY(CONCAT('',#{type,jdbcType=VARCHAR},''))
            </when>
            <when test="type == 'Site_Delay_Normal'">
                set siteInfo_Warning = JSON_ARRAY(CONCAT('','Normal',''))
            </when>
            <when test="type == 'Amount_Error_Normal'">
                set PoAmount_Warning = JSON_ARRAY(CONCAT('','Normal',''))
            </when>
        </choose>
        where
        id in
        <foreach collection="ids" close=")" open="(" item="id" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="selectPoAmountWarnTemp" resultType="com.pig4cloud.pig.yptt.entity.WarnTemp">
        SELECT poItem.id,
               un.uniqueness_field   'uniquenessField',
                poItem.Project_code   'projectCode',
               siteItem.TPTT_Project 'projectName'
        from memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem
                           on siteItem.uniqueness_field = poItem.uniqueness_field and siteItem.is_deleted = 0
                            and siteItem.Project_code = #{projectCode}
                 LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un
                           on un.id = JSON_UNQUOTE(siteItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
        where abs(poItem.PO_gap) >= #{warn}
         AND siteItem.Site_item_status = JSON_ARRAY('unclose') and poItem.Project_code = #{projectCode}
        and poItem.is_deleted = 0
        ORDER BY poItem.id
        limit #{cur},#{size}
    </select>

    <select id="selectRemovePoAmountWarnTemp" resultType="java.lang.Long">
        SELECT poItem.id
        from memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem
                           on siteItem.uniqueness_field = poItem.uniqueness_field and siteItem.is_deleted = 0
                                and siteItem.Project_code = #{projectCode}
        where #{warn} >= abs(poItem.PO_gap)
          and poItem.is_deleted = 0 and poItem.Project_code = #{projectCode}
          and (JSON_UNQUOTE(poItem.PoAmount_Warning -> '$[0]') = 'Amount_Error'
          or siteItem.Site_item_status != JSON_ARRAY('unclose')
          )
    </select>

    <update id="updateSiteDeliveryWarning">
        update memm_e45cb01fc742457a85ed8243aff1aa28
        <choose>
            <when test="type == 'Start_Working_Delay'">
                set Start_Warning = JSON_ARRAY(CONCAT('',#{type,jdbcType=VARCHAR},''))
            </when>
            <when test="type == 'Acceptance_Delay'">
                set Check_Warning = JSON_ARRAY(CONCAT('',#{type,jdbcType=VARCHAR},''))
            </when>
            <when test="type == 'Subcon_PO_Delay'">
                set SubconPo_Warning = JSON_ARRAY(CONCAT('',#{type,jdbcType=VARCHAR},''))
            </when>
            <when test="type == 'Start_Working_Delay_Normal'">
                set Start_Warning = JSON_ARRAY(CONCAT('','Normal',''))
            </when>
            <when test="type == 'Acceptance_Delay_Normal'">
                set Check_Warning = JSON_ARRAY(CONCAT('','Normal',''))
            </when>
            <when test="type == 'Subcon_PO_Delay_Normal'">
                set SubconPo_Warning = JSON_ARRAY(CONCAT('','Normal',''))
            </when>
        </choose>
        where
        id in
        <foreach collection="ids" close=")" open="(" item="id" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="selectStartWorkWarnTemp" resultType="com.pig4cloud.pig.yptt.entity.WarnTemp">
        SELECT SiteDeliveryInfo.id,
               un.uniqueness_field   'uniquenessField',
               SiteDeliveryInfo.Project_code   'projectCode',
               SiteItem.TPTT_Project 'projectName'
        FROM memm_e45cb01fc742457a85ed8243aff1aa28 SiteDeliveryInfo
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b SiteItem
                           ON SiteDeliveryInfo.uniqueness_field = SiteItem.uniqueness_field and SiteItem.is_deleted = 0
                            and SiteItem.Project_code = #{projectCode}
                 LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un
                           on un.id = JSON_UNQUOTE(SiteItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
        WHERE SiteDeliveryInfo.is_deleted = 0 and SiteDeliveryInfo.Project_code = #{projectCode}
        and SiteItem.site_allocation_date &lt; CURDATE() - INTERVAL(#{warn}) DAY
          and SiteDeliveryInfo.Start_Working_date is null
          and SiteItem.Site_item_status = JSON_ARRAY('unclose')
        ORDER BY SiteDeliveryInfo.id
        limit #{cur},#{size}
    </select>

    <select id="selectRemoveStartWorkWarnTemp" resultType="java.lang.Long">
        SELECT SiteDeliveryInfo.id
        FROM memm_e45cb01fc742457a85ed8243aff1aa28 SiteDeliveryInfo
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b SiteItem
                           ON SiteDeliveryInfo.uniqueness_field = SiteItem.uniqueness_field and SiteItem.is_deleted = 0
                                and SiteItem.Project_code = #{projectCode}
        WHERE SiteDeliveryInfo.is_deleted = 0 and SiteDeliveryInfo.Project_code = #{projectCode}
          and JSON_UNQUOTE(SiteDeliveryInfo.Start_Warning -> '$[0]') = 'Start_Working_Delay'
          and ((SiteItem.site_allocation_date &gt; CURDATE() - INTERVAL(#{warn}) DAY
            or SiteDeliveryInfo.Start_Working_date is not null)
            or SiteItem.Site_item_status != JSON_ARRAY('unclose'))
    </select>

    <select id="selectCheckWorkWarnTemp" resultType="com.pig4cloud.pig.yptt.entity.WarnTemp">
        SELECT SiteDeliveryInfo.id,
               un.uniqueness_field   'uniquenessField',
                SiteDeliveryInfo.Project_code   'projectCode',
               SiteItem.TPTT_Project 'projectName'
        FROM memm_e45cb01fc742457a85ed8243aff1aa28 SiteDeliveryInfo
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b SiteItem
                           ON SiteDeliveryInfo.uniqueness_field = SiteItem.uniqueness_field and SiteItem.is_deleted = 0
                                and SiteItem.Project_code = #{projectCode}
                 LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un
                           on un.id = JSON_UNQUOTE(SiteItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
        where SiteDeliveryInfo.is_deleted = 0 and SiteDeliveryInfo.Project_code = #{projectCode}
          AND JSON_CONTAINS(SiteItem.Site_item_status, '"unclose"')
        and SiteDeliveryInfo.Start_Working_date &lt; CURDATE() - INTERVAL(#{warn}) DAY
          and SiteDeliveryInfo.E_ATP_Pass is null
          and SiteItem.Site_item_status = JSON_ARRAY('unclose')
        ORDER BY SiteDeliveryInfo.id
        limit #{cur},#{size}
    </select>

    <select id="selectRemoveCheckWorkWarnTemp" resultType="java.lang.Long">
        SELECT SiteDeliveryInfo.id
        FROM memm_e45cb01fc742457a85ed8243aff1aa28 SiteDeliveryInfo
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b SiteItem
                           ON SiteDeliveryInfo.uniqueness_field = SiteItem.uniqueness_field and SiteItem.is_deleted = 0
                                and SiteItem.Project_code = #{projectCode}
        where SiteDeliveryInfo.is_deleted = 0 and SiteDeliveryInfo.Project_code = #{projectCode}
          and JSON_UNQUOTE(SiteDeliveryInfo.Check_Warning -> '$[0]') = 'Acceptance_Delay'
          and ((SiteDeliveryInfo.Start_Working_date &gt; CURDATE() - INTERVAL(#{warn}) DAY
            OR SiteDeliveryInfo.E_ATP_Pass is not null)
            or SiteItem.Site_item_status != JSON_ARRAY('unclose'))
    </select>

    <select id="selectSubconPoWarnTemp" resultType="com.pig4cloud.pig.yptt.entity.WarnTemp">
        SELECT SiteDeliveryInfo.id,
               un.uniqueness_field   'uniquenessField',
                SiteDeliveryInfo.Project_code   'projectCode',
               SiteItem.TPTT_Project 'projectName'
        FROM memm_e45cb01fc742457a85ed8243aff1aa28 SiteDeliveryInfo
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b SiteItem
                           ON SiteDeliveryInfo.uniqueness_field = SiteItem.uniqueness_field and SiteItem.is_deleted = 0
                                and SiteItem.Project_code = #{projectCode}
                 LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un
                           on un.id = JSON_UNQUOTE(SiteItem.uniqueness_field -> '$[0]') and un.is_deleted = 0
                 LEFT JOIN memm_157ac31323c34d46920918117cb577ad subconPoItem
                           on subconPoItem.uniqueness_field = SiteItem.uniqueness_field and subconPoItem.is_deleted = 0
                            and subconPoItem.Project_code = #{projectCode}
        where SiteDeliveryInfo.Site_belong_to != 'YPTT' and SiteDeliveryInfo.Project_code = #{projectCode}
         and SiteItem.Site_item_status = JSON_ARRAY('unclose')
        and SiteDeliveryInfo.is_deleted = 0
          and SiteDeliveryInfo.Start_Working_date &lt; CURDATE() - INTERVAL(#{warn}) DAY
          and subconPoItem.Subcon_PO is null
        ORDER BY SiteDeliveryInfo.id
        limit #{cur},#{size}
    </select>

    <select id="selectRemoveSubconPoWarnTemp" resultType="java.lang.Long">
        SELECT SiteDeliveryInfo.id
        FROM memm_e45cb01fc742457a85ed8243aff1aa28 SiteDeliveryInfo
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b SiteItem
                           ON SiteDeliveryInfo.uniqueness_field = SiteItem.uniqueness_field and SiteItem.is_deleted = 0
                            and SiteItem.Project_code = #{projectCode}
                 LEFT JOIN memm_157ac31323c34d46920918117cb577ad subconPoItem
                           on subconPoItem.uniqueness_field = SiteItem.uniqueness_field and subconPoItem.is_deleted = 0
                                and subconPoItem.Project_code = #{projectCode}
        where SiteDeliveryInfo.Project_code = #{projectCode}
          and SiteDeliveryInfo.is_deleted = 0
          and JSON_UNQUOTE(SiteDeliveryInfo.SubconPo_Warning -> '$[0]') = 'Subcon_PO_Delay'
          and ((SiteDeliveryInfo.Start_Working_date &gt; CURDATE() - INTERVAL(#{warn}) DAY
            OR subconPoItem.Subcon_PO is not null)
            or SiteItem.Site_item_status != JSON_ARRAY('unclose'))
    </select>

    <select id="selectYpttSettlementWarnTemp" resultType="com.pig4cloud.pig.yptt.entity.WarnTemp">
        SELECT YPTT_Settlement.id,YPTT_Settlement.Project_code 'projectCode', un.uniqueness_field 'uniquenessField', Site_Item.TPTT_Project 'projectName'
        FROM memm_4bf72c9a610c4b05a007f0f215b424a6 YPTT_Settlement
                 LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 Ready_For_Settlement
                           ON YPTT_Settlement.uniqueness_field = Ready_For_Settlement.uniqueness_field and
                              Ready_For_Settlement.is_deleted = 0 and Ready_For_Settlement.Project_code = #{projectCode}
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b Site_Item
                           ON Site_Item.uniqueness_field = Ready_For_Settlement.uniqueness_field and
                              Site_Item.is_deleted = 0 and Site_Item.Project_code = #{projectCode}
                LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un
                           on un.id = JSON_UNQUOTE(Site_Item.uniqueness_field -> '$[0]') and un.is_deleted = 0

        WHERE YPTT_Settlement.is_deleted = 0 and YPTT_Settlement.Project_code = #{projectCode}
          and Site_Item.Site_item_status = JSON_ARRAY('unclose')
        and YPTT_Settlement.Invoice_amount_gap != 0
          and (YPTT_Settlement.Invoice_amount_gap > 1 or -1 > YPTT_Settlement.Invoice_amount_gap)
          and GREATEST(
                      COALESCE(Ready_For_Settlement.settlement_4th, '0000-00-00'),
                      COALESCE(Ready_For_Settlement.settlement_3rd, '0000-00-00'),
                      COALESCE(Ready_For_Settlement.settlement_2nd, '0000-00-00'),
                      COALESCE(Ready_For_Settlement.settlement_1st, '0000-00-00')
                  ) &lt; CURDATE() - INTERVAL (#{warn}) DAY
                  and Ready_For_Settlement.settlement_1st is not null
        ORDER BY YPTT_Settlement.id
        limit #{cur},#{size}
    </select>

    <select id="selectRemoveYpttSettlementWarnTemp" resultType="java.lang.Long">
        SELECT YPTT_Settlement.id
        FROM memm_4bf72c9a610c4b05a007f0f215b424a6 YPTT_Settlement
                 LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 Ready_For_Settlement
                           ON YPTT_Settlement.uniqueness_field = Ready_For_Settlement.uniqueness_field and
                              Ready_For_Settlement.is_deleted = 0 and Ready_For_Settlement.Project_code = #{projectCode}
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b Site_Item
                           ON Site_Item.uniqueness_field = Ready_For_Settlement.uniqueness_field and
                              Site_Item.is_deleted = 0 and Site_Item.Project_code = #{projectCode}
        WHERE YPTT_Settlement.is_deleted = 0 and YPTT_Settlement.Project_code = #{projectCode}
          and JSON_UNQUOTE(YPTT_Settlement.Warning -> '$[0]') = 'Invoice_Delay'
          and (YPTT_Settlement.Invoice_amount_gap = 0
            OR (YPTT_Settlement.Invoice_amount_gap > -1 and 1 > YPTT_Settlement.Invoice_amount_gap)
            or Site_Item.Site_item_status != JSON_ARRAY('unclose')
            OR GREATEST(
                       COALESCE(Ready_For_Settlement.settlement_4th, '0000-00-00'),
                       COALESCE(Ready_For_Settlement.settlement_3rd, '0000-00-00'),
                       COALESCE(Ready_For_Settlement.settlement_2nd, '0000-00-00'),
                       COALESCE(Ready_For_Settlement.settlement_1st, '0000-00-00')
                   ) &gt; CURDATE() - INTERVAL (#{warn}) DAY
            or Ready_For_Settlement.settlement_1st is null)
    </select>

    <update id="updateYpttSettlementWarning">
        update memm_4bf72c9a610c4b05a007f0f215b424a6
        <choose>
            <when test="type == 'Invoice_Delay'">
                set Warning = JSON_ARRAY(CONCAT('',#{type,jdbcType=VARCHAR},''))
            </when>
            <when test="type == 'Invoice_Delay_Normal'">
                set Warning = JSON_ARRAY(CONCAT('','Normal',''))
            </when>
        </choose>
        where
        id in
        <foreach collection="ids" close=")" open="(" item="id" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="selectSubconPayWarnTemp" resultType="com.pig4cloud.pig.yptt.entity.WarnTemp">
        SELECT SubconPayment.id,SubconPayment.Project_code 'projectCode', un.uniqueness_field 'uniquenessField', SiteItem.TPTT_Project 'projectName'
        FROM memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
                 LEFT JOIN memm_218a6ab9959842099fd074c2b0ef685b SubconSettlement
                           ON SubconPayment.uniqueness_field = SubconSettlement.uniqueness_field and
                              SubconSettlement.is_deleted = 0  and SubconSettlement.Project_code = #{projectCode}
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b SiteItem
                           ON SubconSettlement.uniqueness_field = SiteItem.uniqueness_field and
                              SiteItem.is_deleted = 0  and SiteItem.Project_code = #{projectCode}
                 LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un
                           on un.id = JSON_UNQUOTE(SiteItem.uniqueness_field -> '$[0]') and
                              un.is_deleted = 0
        WHERE SubconPayment.is_deleted = 0 and SubconPayment.Project_code = #{projectCode}
          and SubconPayment.Totally_payment_gap != 0
          and (SubconPayment.Totally_payment_gap > 1 or -1 > SubconPayment.Totally_payment_gap)
          and SiteItem.Site_item_status = JSON_ARRAY('unclose')
          and GREATEST(
                        COALESCE(SubconSettlement.settlement_time_4th, '0000-00-00'),
                        COALESCE(SubconSettlement.settlement_time_3rd, '0000-00-00'),
                        COALESCE(SubconSettlement.settlement_time_2nd, '0000-00-00'),
                        COALESCE(SubconSettlement.settlement_time_1st, '0000-00-00')
                  ) &lt; CURDATE() - INTERVAL (#{warn}) DAY
        ORDER BY SubconPayment.id
        limit #{cur},#{size}
    </select>

    <select id="selectRemoveSubconPayWarnTemp" resultType="java.lang.Long">
        SELECT SubconPayment.id
        FROM memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
                 LEFT JOIN memm_218a6ab9959842099fd074c2b0ef685b SubconSettlement
                           ON SubconPayment.uniqueness_field = SubconSettlement.uniqueness_field and
                              SubconSettlement.is_deleted = 0 and SubconSettlement.Project_code = #{projectCode}
                 LEFT JOIN memm_e648652640b44b2092c93e1742e6171b SiteItem
                           ON SubconSettlement.uniqueness_field = SiteItem.uniqueness_field and
                              SiteItem.is_deleted = 0 and SiteItem.Project_code = #{projectCode}
        WHERE SubconPayment.is_deleted = 0 and SubconPayment.Project_code = #{projectCode}
          and JSON_UNQUOTE(SubconPayment.Warning -> '$[0]') = 'Subcon_Payment_Delay'
          and (SubconPayment.Totally_payment_gap = 0
            OR (SubconPayment.Totally_payment_gap > -1 and 1 > SubconPayment.Totally_payment_gap)
            OR SiteItem.Site_item_status != JSON_ARRAY('unclose')
            OR GREATEST(
                       COALESCE(SubconSettlement.settlement_time_4th, '0000-00-00'),
                       COALESCE(SubconSettlement.settlement_time_3rd, '0000-00-00'),
                       COALESCE(SubconSettlement.settlement_time_2nd, '0000-00-00'),
                       COALESCE(SubconSettlement.settlement_time_1st, '0000-00-00')
                   ) &gt; CURDATE() - INTERVAL (#{warn}) DAY)
    </select>

    <update id="updateSubconPayWarning">
        update memm_f562b5dbd2be42d99c4992dd2668ed74
        <choose>
            <when test="type == 'Subcon_Payment_Delay'">
                set Warning = JSON_ARRAY(CONCAT('',#{type,jdbcType=VARCHAR},''))
            </when>
            <when test="type == 'Subcon_Payment_Delay_Normal'">
                set Warning = JSON_ARRAY(CONCAT('','Normal',''))
            </when>
        </choose>
        where
        id in
        <foreach collection="ids" close=")" open="(" item="id" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="findWarnMsg" resultType="java.lang.Integer">
        SELECT 1
        FROM `memm_70848da039e44392bc6e066b5963ba1d`
        where warning_DataId = #{id,jdbcType=BIGINT}
          and JSON_UNQUOTE(warning_Type->'$[0]') = #{type,jdbcType=VARCHAR}
    </select>

    <insert id="saveMsg">
        INSERT INTO
            `memm_70848da039e44392bc6e066b5963ba1d`
        (id,warning_Type,warning_Msg,warning_DataId,uniqueness_field,projectName,viewId,viewGroupId,menuId,create_time,Project_code)
        VALUES
        <foreach collection="list" item="dto" separator=",">
            (#{dto.id},JSON_ARRAY(CONCAT('',#{dto.warningType},'')),#{dto.warningMsg},#{dto.warningDataId},#{dto.uniquenessField},#{dto.projectName},#{dto.viewId},#{dto.viewGroupId},#{dto.menuId},now(),#{dto.projectCode})
        </foreach>
    </insert>

    <delete id="deleteMsgByType">
        update `memm_70848da039e44392bc6e066b5963ba1d`
        set is_deleted = UNIX_TIMESTAMP()
        where warning_DataId in
        <foreach collection="list" item="dto" separator="," open="(" close=")">
            #{dto.warningDataId}
        </foreach>
        and JSON_UNQUOTE(warning_Type->'$[0]') = #{type}
    </delete>

    <select id="getProjectRole" resultType="com.pig4cloud.pig.yptt.entity.ProjectRole">
        SELECT member.Project_Role 'role', project.id 'projectName'
        FROM memm_7abc0f7fd9d84f67b4cd9b32575a6933 member
                 LEFT JOIN memm_YPTT_Project_YPTT_Project_Member_mdr_39scc rel ON rel.right_data_id = member.id
            AND rel.is_deleted = 0
                 LEFT JOIN memm_72a2450126dd41708a07374eff08b982 project ON project.id = rel.left_data_id
            AND project.is_deleted = 0
        WHERE member.is_deleted = 0
          AND JSON_UNQUOTE(member.Project_Member->'$[0]') = #{id,jdbcType=BIGINT}
    </select>

    <select id="warnPage" resultType="com.pig4cloud.pig.yptt.entity.WarningMessage">
        SELECT
        DISTINCT warn.warning_Type 'warningType',
        warn.warning_DataId 'warningDataId',
        warn.warning_Msg 'warningMsg',
        warn.uniqueness_field 'uniquenessField',
        warn.viewGroupId,
        warn.viewId,
        warn.menuId,
        warn.create_time,
        warn.id,
        p.YPTT_Project_name 'projectName',
        p.YPTT_Project_code 'projectCode'
        FROM memm_70848da039e44392bc6e066b5963ba1d warn
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 p
        on JSON_UNQUOTE(warn.projectName -> '$[0]') = p.id and p.is_deleted = 0
        WHERE warn.is_deleted = 0
        <if test="list != null and list.size != 0">
            and
            (
            <foreach collection="list" item="dto" separator="or">
                <foreach collection="dto.warnTypeList" item="warnType" separator="or">
                    (JSON_CONTAINS(warn.projectName,CONCAT('"',#{dto.projectId},'"'))
                    and JSON_UNQUOTE(warn.warning_Type->'$[0]') = #{warnType})
                </foreach>
            </foreach>
            )
        </if>
        <if test="typeList != null and typeList.size != 0">
            and JSON_UNQUOTE(warn.warning_Type->'$[0]') in
            <foreach collection="typeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="projectName != null and projectName != ''">
            and p.YPTT_Project_name like CONCAT('%', #{projectName,jdbcType=VARCHAR},'%')
        </if>
        <if test="projectCode != null and projectCode != ''">
            and p.YPTT_Project_code = #{projectCode,jdbcType=VARCHAR}
        </if>
        <if test="uniquenessField != null and uniquenessField != ''">
            and warn.uniqueness_field like CONCAT('%', #{uniquenessField,jdbcType=VARCHAR},'%')
        </if>
        <if test="startTime != null and endTime != null">
            and warn.create_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        </if>
        and warn.uniqueness_field is not null
    </select>

    <select id="warnRoleList" resultType="java.lang.String">
        SELECT warningType
        from memm_505e4855883045729256ad60b745f17e
        where is_deleted = 0
        and
        (
        <foreach collection="list" item="item" separator="or">
            role = JSON_ARRAY(CONCAT('',#{item},''))
        </foreach>
        )
    </select>

    <select id="warningStatistics" resultType="com.pig4cloud.pig.yptt.entity.vo.WarningStatisticsVO">
        SELECT
        COUNT(IF(JSON_UNQUOTE(warn.warning_Type -> '$[0]') = 'Start_Working_Delay',
        CONCAT(warn.warning_Type,warn.warning_DataId), null)) startWorkingDelayCount,
        COUNT(IF(JSON_UNQUOTE(warn.warning_Type -> '$[0]') = 'Invoice_Delay',
        CONCAT(warn.warning_Type,warn.warning_DataId), null)) invoiceDelayCount,
        COUNT(IF(JSON_UNQUOTE(warn.warning_Type -> '$[0]') = 'Amount_Error',
        CONCAT(warn.warning_Type,warn.warning_DataId), null)) amountErrorCount,
        COUNT(IF(JSON_UNQUOTE(warn.warning_Type -> '$[0]') = 'Acceptance_Delay',
        CONCAT(warn.warning_Type,warn.warning_DataId), null)) acceptanceDelayCount,
        COUNT(IF(JSON_UNQUOTE(warn.warning_Type -> '$[0]') = 'Subcon_PO_Delay',
        CONCAT(warn.warning_Type,warn.warning_DataId), null)) subconPoDelayCount,
        COUNT(IF(JSON_UNQUOTE(warn.warning_Type -> '$[0]') = 'Site_PO_Delay',
        CONCAT(warn.warning_Type,warn.warning_DataId), null)) sitePoDelayCount,
        COUNT(IF(JSON_UNQUOTE(warn.warning_Type -> '$[0]') = 'Site_Delay',
        CONCAT(warn.warning_Type,warn.warning_DataId), null)) siteDelayCount,
        COUNT(IF(JSON_UNQUOTE(warn.warning_Type -> '$[0]') = 'Subcon_Payment_Delay',
        CONCAT(warn.warning_Type,warn.warning_DataId), null)) subconPaymentDelayCount,
        p.YPTT_Project_name 'projectName',
        p.YPTT_Project_code 'projectCode'
        FROM memm_70848da039e44392bc6e066b5963ba1d warn
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 p
        ON JSON_UNQUOTE(warn.projectName -> '$[0]') = p.id AND p.is_deleted = 0
        WHERE warn.is_deleted = 0
        <if test="list != null and list.size > 0">
        and
        (
        <foreach collection="list" item="dto" separator="or">
            <foreach collection="dto.warnTypeList" item="warnType" separator="or">
                (JSON_CONTAINS(warn.projectName,CONCAT('"',#{dto.projectId},'"'))
                and JSON_UNQUOTE(warn.warning_Type->'$[0]') = #{warnType})
            </foreach>
        </foreach>
        )
        </if>
        <if test="typeList != null and typeList.size != 0">
            and JSON_UNQUOTE(warn.warning_Type->'$[0]') in
            <foreach collection="typeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="projectName != null and projectName != ''">
            and p.YPTT_Project_name like CONCAT('%', #{projectName,jdbcType=VARCHAR},'%')
        </if>
        <if test="projectCode != null and projectCode != ''">
            and p.YPTT_Project_code = #{projectCode,jdbcType=VARCHAR}
        </if>
        <if test="uniquenessField != null and uniquenessField != ''">
            and warn.uniqueness_field like CONCAT('%', #{uniquenessField,jdbcType=VARCHAR},'%')
        </if>
        <if test="startTime != null and endTime != null">
            and warn.create_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        </if>
        GROUP BY p.YPTT_Project_code, p.YPTT_Project_name
        order by p.YPTT_Project_code
    </select>

    <select id="listMap" resultType="java.util.Map">
        SELECT
        DISTINCT JSON_UNQUOTE(warn.warning_Type->'$[0]') 'warningType',
        warn.warning_DataId 'warningDataId',
        warn.warning_Msg 'warningMsg',
        warn.uniqueness_field 'uniquenessField',
        p.YPTT_Project_name 'projectName',
        p.YPTT_Project_code 'projectCode'
        FROM memm_70848da039e44392bc6e066b5963ba1d warn
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 p
        on JSON_UNQUOTE(warn.projectName -> '$[0]') = p.id and p.is_deleted = 0
        WHERE warn.is_deleted = 0
        and
        (
        <foreach collection="list" item="dto" separator="or">
            <foreach collection="dto.warnTypeList" item="warnType" separator="or">
                (JSON_CONTAINS(warn.projectName,CONCAT('"',#{dto.projectId},'"'))
                and JSON_UNQUOTE(warn.warning_Type->'$[0]') = #{warnType})
            </foreach>
        </foreach>
        )
        <if test="typeList != null and typeList.size != 0">
            and JSON_UNQUOTE(warn.warning_Type->'$[0]') in
            <foreach collection="typeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="projectName != null and projectName != ''">
            and p.YPTT_Project_name like CONCAT('%', #{projectName,jdbcType=VARCHAR},'%')
        </if>
        <if test="projectCode != null and projectCode != ''">
            and p.YPTT_Project_code = #{projectCode,jdbcType=VARCHAR}
        </if>
        <if test="uniquenessField != null and uniquenessField != ''">
            and warn.uniqueness_field like CONCAT('%', #{uniquenessField,jdbcType=VARCHAR},'%')
        </if>
        <if test="startTime != null and endTime != null">
            and warn.create_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <delete id="clearWarningInfo">
        delete from memm_70848da039e44392bc6e066b5963ba1d
    </delete>

    <select id="getThresholdByCode" resultType="java.lang.String">
        SELECT warn.${type}
        FROM memm_7345607a202c4e0eb52ffef451faa3aa warn
                 LEFT JOIN memm_YPTT_Project_Warning_Threshold_mdr_siv6q rel
                           on rel.right_data_id = warn.id and rel.is_deleted = 0
                 LEFT JOIN memm_72a2450126dd41708a07374eff08b982 project
                           on project.id = rel.left_data_id and project.is_deleted = 0
        WHERE project.YPTT_Project_code = #{code} and warn.is_deleted = 0
    </select>

    <select id="getWarnDataIdByCode" resultType="java.lang.Long">
        SELECT warning_DataId
        from  memm_70848da039e44392bc6e066b5963ba1d
        where Project_code  = #{code} and is_deleted = 0
            and JSON_UNQUOTE(warning_Type->'$[0]') = #{type}
    </select>
</mapper>
