<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.RoleMapper">
    <select id="getProjectIdByMemberId" resultType="java.lang.Long">
        SELECT project.id
        FROM memm_7abc0f7fd9d84f67b4cd9b32575a6933 member
        LEFT JOIN memm_YPTT_Project_YPTT_Project_Member_mdr_39scc rel ON rel.right_data_id = member.id
        AND rel.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 project ON project.id = rel.left_data_id
        AND project.is_deleted = 0
        WHERE member.is_deleted = 0
        AND member.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getBiRole" resultType="java.lang.String">
        SELECT biType
        from memm_5ea417b221054a1fbbec0f82d2d77326
        where is_deleted = 0 and
        (
        <foreach collection="list" separator="or" item="item">
            role = JSON_ARRAY(CONCAT('',#{item},''))
        </foreach>
        )
    </select>

    <select id="getRoleModuleList" resultType="com.pig4cloud.pig.yptt.entity.ModuleRolePer">
        SELECT model 'moduleList', query, `update`, del, role
        from memm_1ebcbae9c9a648abb5df95540846ff1e
        where is_deleted = 0 and
        (
        <foreach collection="list" item="item" separator="or">
            role = JSON_ARRAY(CONCAT('',#{item},''))
        </foreach>
        )
    </select>

    <resultMap id="ProjectRolePermissionMapDTO" type="com.pig4cloud.pig.yptt.entity.dto.ProjectRolePermissionMapDTO">
        <result column="project_id" property="projectId"/>
        <result column="yptt_project_code" property="ypttProjectCode"/>
        <result column="yptt_project_name" property="ypttProjectName"/>
        <result column="project_role" property="projectRole"
                typeHandler="com.pig4cloud.pig.yptt.entity.dto.ProjectRolePermissionMapDTO$ListStringHandler"/>
        <result column="project_member" property="projectMember"
                typeHandler="com.pig4cloud.pig.yptt.entity.dto.ProjectRolePermissionMapDTO$ListStringHandler"/>
        <result column="perm_model" property="permModel"
                typeHandler="com.pig4cloud.pig.yptt.entity.dto.ProjectRolePermissionMapDTO$ListStringHandler"/>
        <result column="perm_query" property="permQuery"/>
        <result column="perm_update" property="permUpdate"/>
        <result column="perm_del" property="permDel"/>
        <result column="perm_add" property="permAdd"/>
    </resultMap>

    <sql id="QueryProjectRolePermMapping">
        SELECT project.`id`                    AS project_id,
               project.`YPTT_Project_code`     AS yptt_project_code,
               project.`YPTT_Project_name`     AS yptt_project_name,
               project_member.`Project_role`   AS project_role,
               project_member.`Project_Member` AS project_member,
               role_data.`model`               AS perm_model,
               role_data.`query`               AS perm_query,
               role_data.`update`              AS perm_update,
               role_data.`del`                 AS perm_del,
               role_data.`add`                 AS perm_add
        FROM memm_72a2450126dd41708a07374eff08b982 project
                 LEFT JOIN memm_YPTT_Project_YPTT_Project_Member_mdr_39scc p2pm
                           ON p2pm.`is_deleted` = 0 AND p2pm.`left_data_id` = project.`id`
                 LEFT JOIN memm_7abc0f7fd9d84f67b4cd9b32575a6933 project_member
                           ON project_member.`is_deleted` = 0 AND p2pm.`right_data_id` = project_member.id
                 LEFT JOIN memm_1ebcbae9c9a648abb5df95540846ff1e role_data
                           ON role_data.`is_deleted` = 0 AND
                              JSON_CONTAINS(project_member.`Project_Role`, role_data.`role`)
    </sql>

    <select id="queryProjectRolesByProjectCode" resultMap="ProjectRolePermissionMapDTO">
        <include refid="QueryProjectRolePermMapping"/>
        WHERE project.`is_deleted` = 0
        AND project.`YPTT_Project_code` = #{code,jdbcType=VARCHAR}
        AND project_member.`Project_Member` = JSON_ARRAY(CONCAT(#{userId,jdbcType=BIGINT}))
    </select>

    <select id="queryProjectRolesByProjectId" resultMap="ProjectRolePermissionMapDTO">
        <include refid="QueryProjectRolePermMapping"/>
        WHERE project.`is_deleted` = 0
        AND project.`id` = #{projectId,jdbcType=BIGINT}
        AND project_member.`Project_Member` = JSON_ARRAY(CONCAT(#{userId,jdbcType=BIGINT}))
    </select>

    <select id="getSiteItem" resultType="com.pig4cloud.pig.yptt.entity.SiteItem">
        SELECT DISTINCT siteItem.id               'id',
        project.id                'projectId',
        project.YPTT_Project_name 'projectName',
        siteItem.Site_item_status 'status'
        FROM memm_7abc0f7fd9d84f67b4cd9b32575a6933 member
        LEFT JOIN memm_YPTT_Project_YPTT_Project_Member_mdr_39scc rel ON rel.right_data_id = member.id
        AND rel.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 project ON project.id = rel.left_data_id
        AND project.is_deleted = 0
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem
        ON project.id = JSON_UNQUOTE(siteItem.TPTT_Project -> '$[0]')
        AND siteItem.is_deleted = 0
        where JSON_CONTAINS(member.Project_Member, JSON_ARRAY(#{userId,jdbcType=BIGINT}))
        and project.id is not null
        and member.is_deleted = 0
        and siteItem.id is not null
    </select>

    <select id="getProject" resultType="com.pig4cloud.pig.yptt.entity.dto.ProjectStandingBookDTO">
        SELECT DISTINCT project.id 'projectId', project.YPTT_Project_name 'projectName'
        FROM memm_7abc0f7fd9d84f67b4cd9b32575a6933 member
        LEFT JOIN memm_YPTT_Project_YPTT_Project_Member_mdr_39scc rel ON rel.right_data_id = member.id
        AND rel.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 project ON project.id = rel.left_data_id
        AND project.is_deleted = 0
        where JSON_CONTAINS(member.Project_Member, JSON_ARRAY(#{userId,jdbcType=BIGINT}))
        and project.id is not null
        and member.is_deleted = 0
        <if test="projectId != null and projectId != ''">
            and project.id = #{projectId}
        </if>
    </select>

    <select id="getProjectById" resultType="java.util.Map">
        select *
        from memm_72a2450126dd41708a07374eff08b982
        where is_deleted = 0
        and id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getUserIdListByPerType" resultType="java.lang.String">
        SELECT per.`${perType}`
        FROM memm_439131c30ad445e6810ba53e13fd9cfb per
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 project
        ON project.id = JSON_UNQUOTE(per.project -> '$[0]')
        AND project.is_deleted = 0
        WHERE project.YPTT_Project_code = #{code,jdbcType=VARCHAR}
    </select>

    <select id="getProjectCodeBySiteId" resultType="java.lang.String">
        select Project_code
        from memm_e648652640b44b2092c93e1742e6171b
        where Site_ID = #{siteId,jdbcType=VARCHAR}
        and is_deleted = 0
    </select>

    <select id="getAllSiteItems" resultType="com.pig4cloud.pig.yptt.entity.SiteItem">
        SELECT DISTINCT siteItem.id               'id',
                project.id                'projectId',
                project.YPTT_Project_name 'projectName',
                siteItem.Site_item_status 'status'
        FROM memm_e648652640b44b2092c93e1742e6171b siteItem
                 left join memm_72a2450126dd41708a07374eff08b982 project
                           on project.id = JSON_UNQUOTE(siteItem.TPTT_Project -> '$[0]') and project.is_deleted = 0
        where project.id is not null
          and siteItem.site is not null
          and siteItem.is_deleted = 0
    </select>

    <select id="getAllProjectsById" resultType="com.pig4cloud.pig.yptt.entity.dto.ProjectStandingBookDTO">
        SELECT DISTINCT project.id 'projectId', project.YPTT_Project_name 'projectName'
        FROM memm_72a2450126dd41708a07374eff08b982 project
        where project.is_deleted = 0
        <if test="id != null and id != ''">
            and project.id = #{id}
        </if>
    </select>
</mapper>
