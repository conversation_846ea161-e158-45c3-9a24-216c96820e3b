<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.CustomerMapper">
    <update id="updateProjectCustomerCodeStatus">
        update ${modelTable} set
        <choose>
            <when test='company == "YPTT"'>
                yptt_is_use = '["0"]'
            </when>
            <when test='company == "YUIP"'>
                ypit_is_use = '["0"]'
            </when>
        </choose>
        where id = #{id}
    </update>

    <select id="isExistsCustomer" resultType="java.lang.Integer">
        select count(1) from ${modelTable} where name = #{name}
    </select>
</mapper>