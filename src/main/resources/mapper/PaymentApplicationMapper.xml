<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.PaymentApplicationMapper">

    <resultMap id="BaseResultMap" type="com.pig4cloud.pig.yptt.entity.PaymentApplication">
        <id property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="currProcInstId" column="curr_proc_inst_id"/>
        <result property="project" column="project"/>
        <result property="projectCode" column="project_code"/>
        <result property="code" column="code"/>
        <result property="sqr" column="sqr"/>
        <result property="sqrq" column="sqrq"/>
        <result property="sqbm" column="sqbm"/>
        <result property="sy" column="sy"/>
        <result property="currency" column="currency"/>
        <result property="account" column="account"/>
        <result property="bankNum" column="bank_num"/>
        <result property="bankName" column="bank_name"/>
        <result property="paymentMoney" column="payment_money"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_by,create_time,update_by,update_time,is_deleted,
        curr_proc_inst_id,project,project_code,code,sqr,
        sqrq,sqbm,sy,currency,account,
        bank_num,bank_name,payment_money,status
    </sql>

    <insert id="add">
        INSERT INTO ${modelTable} (
        <include refid="Base_Column_List"/>
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.isDeleted},
            #{item.currProcInstId},
            #{item.project},
            #{item.projectCode},
            #{item.code},
            #{item.sqr},
            #{item.sqrq},
            #{item.sqbm},
            #{item.sy},
            #{item.currency},
            #{item.account},
            #{item.bankNum},
            #{item.bankName},
            #{item.paymentMoney},
            #{item.status}
            )
        </foreach>
    </insert>
    <update id="updateStatus">
        UPDATE ${modelTable}
        SET status = #{status}
        WHERE id = #{dataId}
        and is_deleted = 0
    </update>

    <select id="getRealPaymentMoney" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(payment_money), 0)
        FROM ${modelTable}
        WHERE id IN
        <foreach collection="dataIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="excludeDataId != null">
            and id != #{excludeDataId}
        </if>
        and is_deleted = 0
    </select>

    <select id="getPaymentMoney" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(payment_money), 0)
        FROM ${modelTable} where id = #{dataId}
        and is_deleted = 0
    </select>

    <select id="getRealPaymentMoneyByList" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(payment_money), 0)
        FROM ${modelTable}
        WHERE id IN
        <foreach collection="dataIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="excludeDataId != null">
            and id NOT IN
            <foreach collection="excludeDataIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        and is_deleted = 0
    </select>
</mapper>
