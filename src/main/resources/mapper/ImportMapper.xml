<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.ImportMapper">
    <insert id="batchInsert">
        <foreach collection="dataList" item="item" index="index" separator=";">
            insert into
            <choose>
                <when test="type == 'y1'">
                    memm_336275a450394d9f8ce3f7e6f121aa3b
                </when>
                <when test="type == 'y2'">
                    memm_c9428c09f1914bd68df4bde20bb6fc0a
                </when>
                <when test="type == 'y3'">
                    memm_940817f9e4d842f5a7a089b37f96b176
                </when>
                <when test="type == 'y4'">
                    memm_9f1f8dfbea1a4308bc385560228b6665
                </when>
                <when test="type == 'y8'">
                    memm_de9f0d4541bf48009c8d527e26410b1c
                </when>
                <when test="type == 'y9'">
                    memm_19f2793e1d8a480bb7fd2e32eaa1eaea
                </when>
            </choose>
            (
            <foreach collection="item" index="key" item="value" separator=",">
                `${key}`
            </foreach>
            )
            values
            (
            <foreach collection="item" index="key" item="value" separator=",">
                #{value}
            </foreach>
            )
        </foreach>
    </insert>

    <insert id="batchInsertRel">
        <foreach collection="ids" item="id" separator=";">
            insert into
            <choose>
                <when test="type == 'y1'">
                    memm_Importing_Y1_Importing_Y1_Item_mdr_8rq9u
                </when>
                <when test="type == 'y2'">
                    memm_Importing_Y2_Importing_Y2_Item_mdr_jrk2n
                </when>
                <when test="type == 'y3'">
                    memm_Importing_Y3_Importing_Y3_Item_mdr_b9t0i
                </when>
                <when test="type == 'y4'">
                    memm_Importing_Y4_Importing_Y4_Item_mdr_ypurk
                </when>
                <when test="type == 'y8'">
                    memm_Importing_Y8_Importing_Y8_Item_mdr_ljy7q
                </when>
                <when test="type == 'y9'">
                    memm_Importing_Y9_Importing_Y9_Item_mdr_bqvgx
                </when>
            </choose>
            (id,left_data_id,right_data_id,is_deleted)
            values
            ((#{id}+20000),#{mainId,jdbcType=BIGINT},#{id},0)
        </foreach>
    </insert>

    <select id="selectRelData" resultType="java.util.Map">
        SELECT
        main.*
        FROM
        <choose>
            <when test="type == 'y1'">
                memm_336275a450394d9f8ce3f7e6f121aa3b
            </when>
            <when test="type == 'y2'">
                memm_c9428c09f1914bd68df4bde20bb6fc0a
            </when>
            <when test="type == 'y3'">
                memm_940817f9e4d842f5a7a089b37f96b176
            </when>
            <when test="type == 'y4'">
                memm_9f1f8dfbea1a4308bc385560228b6665
            </when>
            <when test="type == 'y8'">
                memm_de9f0d4541bf48009c8d527e26410b1c
            </when>
            <when test="type == 'y9'">
                memm_19f2793e1d8a480bb7fd2e32eaa1eaea
            </when>
        </choose>
        main
        LEFT JOIN
        <choose>
            <when test="type == 'y1'">
                memm_Importing_Y1_Importing_Y1_Item_mdr_8rq9u
            </when>
            <when test="type == 'y2'">
                memm_Importing_Y2_Importing_Y2_Item_mdr_jrk2n
            </when>
            <when test="type == 'y3'">
                memm_Importing_Y3_Importing_Y3_Item_mdr_b9t0i
            </when>
            <when test="type == 'y4'">
                memm_Importing_Y4_Importing_Y4_Item_mdr_ypurk
            </when>
            <when test="type == 'y8'">
                memm_Importing_Y8_Importing_Y8_Item_mdr_ljy7q
            </when>
            <when test="type == 'y9'">
                memm_Importing_Y9_Importing_Y9_Item_mdr_bqvgx
            </when>
        </choose>
        rel ON rel.right_data_id = main.id
        AND rel.is_deleted = 0
        WHERE
        rel.left_data_id = #{mainId,jdbcType=BIGINT} and main.Import_Status in('READY','RUNNING')
    </select>

    <select id="selectImport" resultType="java.util.Map">
        select *
        from
        <choose>
            <when test="type == 'y1'">
                memm_e61f1146eb994bf19c1425ae0378e20a
            </when>
            <when test="type == 'y2'">
                memm_315465c31eba471fa1d8e40ba7263d91
            </when>
            <when test="type == 'y3'">
                memm_f65ca48677d34bf8a0fbdeda4988554e
            </when>
            <when test="type == 'y4'">
                memm_42548c91d07842478da94f235d87f601
            </when>
            <when test="type == 'y8'">
                memm_e55281091b9f42789cc873ccd40c043c
            </when>
            <when test="type == 'y9'">
                memm_5e6512ba1d5e4c83a1fac1ea682d6c8c
            </when>
        </choose>
        where
        id = #{id,jdbcType=BIGINT}
    </select>

    <update id="updateImport">
        update
        <choose>
            <when test="type == 'y1'">
                memm_e61f1146eb994bf19c1425ae0378e20a
            </when>
            <when test="type == 'y2'">
                memm_315465c31eba471fa1d8e40ba7263d91
            </when>
            <when test="type == 'y3'">
                memm_f65ca48677d34bf8a0fbdeda4988554e
            </when>
            <when test="type == 'y4'">
                memm_42548c91d07842478da94f235d87f601
            </when>
            <when test="type == 'y8'">
                memm_e55281091b9f42789cc873ccd40c043c
            </when>
            <when test="type == 'y9'">
                memm_5e6512ba1d5e4c83a1fac1ea682d6c8c
            </when>
        </choose>
        set
        <foreach collection="map" separator="," item="item" index="key">
            `${key}` = #{item}
        </foreach>
        where
        id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateImportItem">
        <foreach collection="maps" item="map" separator=";">
            update
            <choose>
                <when test="type == 'y1'">
                    memm_336275a450394d9f8ce3f7e6f121aa3b
                </when>
                <when test="type == 'y2'">
                    memm_c9428c09f1914bd68df4bde20bb6fc0a
                </when>
                <when test="type == 'y3'">
                    memm_940817f9e4d842f5a7a089b37f96b176
                </when>
                <when test="type == 'y4'">
                    memm_9f1f8dfbea1a4308bc385560228b6665
                </when>
                <when test="type == 'y8'">
                    memm_de9f0d4541bf48009c8d527e26410b1c
                </when>
                <when test="type == 'y9'">
                    memm_19f2793e1d8a480bb7fd2e32eaa1eaea
                </when>
            </choose>
            set
            Import_Status = #{map.status},Wrong_Reason = #{map.msg}
            where
            id = #{map.id}
        </foreach>
    </update>

    <update id="updateSubconPoExternalCost">
        UPDATE
            memm_157ac31323c34d46920918117cb577ad
        SET additional_cost = #{total,jdbcType=DECIMAL}
        WHERE id = #{id,jdbcType=BIGINT}
          AND is_deleted = 0
    </update>
    <update id="updateY6">
        update memm_5c8c376451894fdfb7e751c91da66f16
        set
        KPI_Archive_date = #{eATPPass},
        KPI_Archive_amount = #{po_value}
        where
            id = #{id}
    </update>

    <select id="selectUnfinishedImportDataId" resultType="java.lang.Long">
        select id
        from
        <choose>
            <when test="type == 'y1'">
                memm_e61f1146eb994bf19c1425ae0378e20a
            </when>
            <when test="type == 'y2'">
                memm_315465c31eba471fa1d8e40ba7263d91
            </when>
            <when test="type == 'y3'">
                memm_f65ca48677d34bf8a0fbdeda4988554e
            </when>
            <when test="type == 'y4'">
                memm_42548c91d07842478da94f235d87f601
            </when>
            <when test="type == 'y8'">
                memm_e55281091b9f42789cc873ccd40c043c
            </when>
            <when test="type == 'y9'">
                memm_5e6512ba1d5e4c83a1fac1ea682d6c8c
            </when>
        </choose>
        where
        is_deleted = 0 and `Status` in ('READY','RUNNING')
    </select>

    <select id="selectUnfinishedImportUserId" resultType="java.lang.Long">
        select create_by
        from
        <choose>
            <when test="type == 'y1'">
                memm_e61f1146eb994bf19c1425ae0378e20a
            </when>
            <when test="type == 'y2'">
                memm_315465c31eba471fa1d8e40ba7263d91
            </when>
            <when test="type == 'y3'">
                memm_f65ca48677d34bf8a0fbdeda4988554e
            </when>
            <when test="type == 'y4'">
                memm_42548c91d07842478da94f235d87f601
            </when>
            <when test="type == 'y8'">
                memm_e55281091b9f42789cc873ccd40c043c
            </when>
            <when test="type == 'y9'">
                memm_5e6512ba1d5e4c83a1fac1ea682d6c8c
            </when>
        </choose>
        where
        is_deleted = 0 and id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectItemNumByMainId" resultType="com.pig4cloud.pig.yptt.entity.ImportRecord">
        SELECT
        COUNT(1) AS total,
        SUM(IF(Import_Status='FAILED', 1, 0)) AS failNum,
        SUM(IF(Import_Status='SUCCEED', 1, 0)) AS successNum
        FROM
        <choose>
            <when test="type == 'y1'">
                memm_336275a450394d9f8ce3f7e6f121aa3b
            </when>
            <when test="type == 'y2'">
                memm_c9428c09f1914bd68df4bde20bb6fc0a
            </when>
            <when test="type == 'y3'">
                memm_940817f9e4d842f5a7a089b37f96b176
            </when>
            <when test="type == 'y4'">
                memm_9f1f8dfbea1a4308bc385560228b6665
            </when>
            <when test="type == 'y8'">
                memm_de9f0d4541bf48009c8d527e26410b1c
            </when>
            <when test="type == 'y9'">
                memm_19f2793e1d8a480bb7fd2e32eaa1eaea
            </when>
        </choose>
        main
        LEFT JOIN
        <choose>
            <when test="type == 'y1'">
                memm_Importing_Y1_Importing_Y1_Item_mdr_8rq9u
            </when>
            <when test="type == 'y2'">
                memm_Importing_Y2_Importing_Y2_Item_mdr_jrk2n
            </when>
            <when test="type == 'y3'">
                memm_Importing_Y3_Importing_Y3_Item_mdr_b9t0i
            </when>
            <when test="type == 'y4'">
                memm_Importing_Y4_Importing_Y4_Item_mdr_ypurk
            </when>
            <when test="type == 'y8'">
                memm_Importing_Y8_Importing_Y8_Item_mdr_ljy7q
            </when>
            <when test="type == 'y9'">
                memm_Importing_Y9_Importing_Y9_Item_mdr_bqvgx
            </when>
        </choose>
        rel ON rel.right_data_id = main.id
        AND rel.is_deleted = 0
        WHERE
        rel.left_data_id = #{mainId,jdbcType=BIGINT}
    </select>

    <select id="selectSubconPoExternalCost" resultType="java.util.Map">
        select cost.*
        from memm_040cb23251f740f5afa2dc9e4b66e5e3 cost
                 left join memm_Subcon_PO_Item_SubconPoItemExCost_mdr_zmp2c rel
                           on cost.id = rel.right_data_id and rel.is_deleted = 0
                 left join memm_157ac31323c34d46920918117cb577ad subcon
                           on rel.left_data_id = subcon.id and subcon.is_deleted = 0
        where subcon.id = #{rootId,jdbcType=BIGINT}
          and cost.is_deleted = 0
    </select>
    <select id="selectY2Amount" resultType="java.util.Map">
        select y2.PO_value,un.uniqueness_field,y2.uniqueness_field as unIdArray,y6.id from memm_f37920ed96f942fb8f4b1bf16f79e39c as y2
        left join memm_562ace74337e462289972ce20939e9a7 as un on un.id = JSON_UNQUOTE(y2.uniqueness_field -> '$[0]')
        left join memm_5c8c376451894fdfb7e751c91da66f16 as y6 on y2.uniqueness_field = y6.uniqueness_field
        where y2.is_deleted = 0
        and y6.is_deleted = 0
        and un.uniqueness_field = #{unId}
    </select>
</mapper>
