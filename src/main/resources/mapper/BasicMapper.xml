<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.BasicMapper">
    <select id="findProjectPerById" resultType="java.util.Map">
        select *
        from memm_439131c30ad445e6810ba53e13fd9cfb
        where is_deleted = 0
        and JSON_CONTAINS(project, JSON_ARRAY(CONCAT('', #{id,jdbcType=BIGINT}, '')))
    </select>

    <select id="findYpttProjectByCode" resultType="java.util.Map">
        select *
        from memm_72a2450126dd41708a07374eff08b982
        where is_deleted = 0
        and YPTT_Project_code = #{code,jdbcType=VARCHAR}
    </select>

    <select id="findSiteBySiteID" resultType="java.util.Map">
        select *
        from memm_448208a319fa4d7ab3d77ee54e10c066
        where is_deleted = 0
        and Site_Serial_number = #{siteId,jdbcType=VARCHAR}
    </select>

    <select id="findSiteItemByUniquenessField" resultType="java.util.Map">
        select *
        from memm_e648652640b44b2092c93e1742e6171b
        where is_deleted = 0
        and JSON_CONTAINS(uniqueness_field, JSON_ARRAY(CONCAT('', #{unField,jdbcType=BIGINT}, '')))
    </select>

    <select id="findUniquenessByUniquenessField" resultType="java.util.Map">
        select *
        from memm_562ace74337e462289972ce20939e9a7
        where is_deleted = 0
        and uniqueness_field = #{unField,jdbcType=VARCHAR}
    </select>

    <select id="findIncomeExpenditureByUniquenessField" resultType="java.util.Map">
        select *
        from memm_c58db11c81d3403ab9e59ce72b815ade
        where is_deleted = 0
        and JSON_CONTAINS(uniqueness_field, JSON_ARRAY(CONCAT('', #{unField,jdbcType=BIGINT}, '')))
    </select>

    <select id="findPOItemByUniquenessId" resultType="java.util.Map">
        select *
        from memm_f37920ed96f942fb8f4b1bf16f79e39c
        where is_deleted = 0
        and JSON_CONTAINS(uniqueness_field, JSON_ARRAY(CONCAT('', #{unField,jdbcType=BIGINT}, '')))
    </select>

    <select id="findSubconPOItemByUniquenessId" resultType="java.util.Map">
        select *
        from memm_157ac31323c34d46920918117cb577ad
        where is_deleted = 0
        and JSON_CONTAINS(uniqueness_field, JSON_ARRAY(CONCAT('', #{unField,jdbcType=BIGINT}, '')))
    </select>

    <select id="findSiteDeliveryInfoByUniquenessId" resultType="java.util.Map">
        select *
        from memm_e45cb01fc742457a85ed8243aff1aa28
        where is_deleted = 0
        and JSON_CONTAINS(uniqueness_field, JSON_ARRAY(CONCAT('', #{unField,jdbcType=BIGINT}, '')))
    </select>

    <select id="findSubconPaymentByUniquenessIdJson" resultType="java.util.Map">
        select *
        from memm_f562b5dbd2be42d99c4992dd2668ed74
        where is_deleted = 0
        and JSON_CONTAINS(uniqueness_field, JSON_ARRAY(CONCAT('', #{unField,jdbcType=BIGINT}, '')))
    </select>

    <select id="findYpttSettlementByUniquenessIdJson" resultType="java.util.Map">
        select *
        from memm_4bf72c9a610c4b05a007f0f215b424a6
        where is_deleted = 0
        and JSON_CONTAINS(uniqueness_field, JSON_ARRAY(CONCAT('', #{unField,jdbcType=BIGINT}, '')))
    </select>

    <select id="findReadySettlementByUniquenessId" resultType="java.util.Map">
        select *
        from memm_abdf4191a91e436a9b7e04351042f757
        where is_deleted = 0
        and JSON_CONTAINS(uniqueness_field, JSON_ARRAY(CONCAT('', #{unField,jdbcType=BIGINT}, '')))
    </select>

    <select id="findProductivityReportByUniquenessId" resultType="java.util.Map">
        select *
        from memm_5c8c376451894fdfb7e751c91da66f16
        where is_deleted = 0
        and JSON_CONTAINS(uniqueness_field, JSON_ARRAY(CONCAT('', #{unField,jdbcType=BIGINT}, '')))
    </select>

    <select id="findSubconSettlementByUniquenessId" resultType="java.util.Map">
        select *
        from memm_218a6ab9959842099fd074c2b0ef685b
        where is_deleted = 0
        and JSON_CONTAINS(uniqueness_field, JSON_ARRAY(CONCAT('', #{unField,jdbcType=BIGINT}, '')))
    </select>

    <select id="findCustomerProjectByContractNumber" resultType="java.util.Map">
        select *
        from memm_f15b45017dee432daf88693b3d13b60b
        where is_deleted = 0
        and Contract_number = #{number,jdbcType=VARCHAR}
        <if test="name != null and name != ''">
            and Custom_project_name = #{name}
        </if>
    </select>

    <select id="findPoByPoNumber" resultType="java.util.Map">
        select *
        from memm_ed87f18383f04a8f836cea32a1628fc9
        where is_deleted = 0
        and PO_number = #{number,jdbcType=VARCHAR}
    </select>

    <select id="findSubconBySubconName" resultType="java.util.Map">
        select *
        from memm_134d9474dc244b26bfd7f013a0534710
        where is_deleted = 0
        and Subcon_name = #{name,jdbcType=VARCHAR}
    </select>

    <select id="findSubconPOBySubconPONumber" resultType="java.util.Map">
        select *
        from memm_ff802d120a12430db18a68deb783b9c6
        where is_deleted = 0
        and Subcon_PO_number = #{number,jdbcType=VARCHAR}
    </select>

    <select id="reportForm" resultType="java.util.Map">
        SELECT DISTINCT
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y1')">
            IFNULL(
            DATE_FORMAT( uf.create_time, '%Y-%m-%d' ),
            DATE_FORMAT( NOW(), '%Y-%m-%d' )) AS Site_register_date,
            site.Site_Serial_number,
            site.site_name,
            DATE_FORMAT( siteItem.site_allocation_date, '%Y-%m-%d' ) AS site_allocation_date,
            site.Area,
            uf.Region,
            siteItem.Site_model,
            siteItem.BOQ_item,
            CASE
            siteItem.Site_item_status
            WHEN '["unclose"]' THEN
            '未关闭'
            WHEN '["close"]' THEN
            '已关闭'
            WHEN '["invalid"]' THEN
            '无效' ELSE '未知'
            END AS Site_item_status,
            siteItem.Item_code,
            siteItem.Phase,
            dept.`name`,
            siteItem.Type_of_service,

            siteItem.Quantity,

            siteItem.Unit_price,
            siteItem.Site_value,
            siteItem.Remark siteItemRemark,
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y2')">
            DATE_FORMAT( po.PO_Received_date, '%Y-%m-%d' ) AS PO_Received_date,
            po.PO_number,
            poItem.Quantity poItemQuantity,
            poItem.quantity_reduce poItemQuantityReduce,
            cusProject.Contract_number,
            cusProject.Custom_project_name,
            poItem.Unit_price poUp,
            poItem.PO_value,
            poItem.PO_gap,
            poItem.BOQ_item po_BOQ_item,
            poItem.Pre_payment,
            poItem.Milestone_1st 'SiteSettlementMilestone1st%',
            poItem.Milestone_2nd 'SiteSettlementMilestone2nd%',
            poItem.Milestone_3rd 'SiteSettlementMilestone3rd%',
            poItem.Milestone_4th 'SiteSettlementMilestone4th%',
            poItem.Remark poItemRemark,
            IF
            ( poItem.PO IS NULL, NULL, poItem.Item_code ) poItemcode,
            IF
            ( poItem.PO IS NULL, NULL, poItem.Site_ID ) poItemSiteID,
            IF
            ( poItem.PO IS NULL, NULL, poItem.Project_code ) poItemProjectcode,
            IF
            ( poItem.PO IS NULL, NULL, poItem.Phase ) poItemPhase,
            IF
            ( poItem.PO IS NULL, NULL, site.site_name ) poItemSiteName,
            IF
            ( poItem.PO IS NULL, NULL, poItem.Region ) poItemRegion,
            IF
            ( poItem.PO IS NULL, NULL, uf.uniqueness_field ) 'poItemUn',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y3')">
            siteDelivery.Site_belong_to,
            siteDelivery.Team_Leader_DT,
            siteDelivery.engineer_DTA_SPV,
            siteDelivery.PLO_PC_Others,
            siteDelivery.PIC_PC_PM,
            DATE_FORMAT( siteDelivery.Start_Working_date, '%Y-%m-%d' ) 'Start_Working_date',
            DATE_FORMAT( siteDelivery.Completed_work_date, '%Y-%m-%d' ) 'Completed_work_date',
            DATE_FORMAT( siteDelivery.air_CI_Report_submit, '%Y-%m-%d' ) 'air_CI_Report_submit',
            DATE_FORMAT( siteDelivery.Site_manager_Report, '%Y-%m-%d' ) 'Site_manager_Report',
            DATE_FORMAT( siteDelivery.E_ATP_Pass, '%Y-%m-%d' ) 'E_ATP_Pass',
            DATE_FORMAT( siteDelivery.F_PAC_Pass, '%Y-%m-%d' ) 'F_PAC_Pass',
            DATE_FORMAT( siteDelivery.G_FAC, '%Y-%m-%d' ) 'G_FAC',
            siteDelivery.remark deliveryRemark,
            DATE_FORMAT( siteDelivery.settlement_1st, '%Y-%m-%d' ) 'site-ReadyForSettlement-1st',
            DATE_FORMAT( siteDelivery.settlement_2nd, '%Y-%m-%d' ) 'site-ReadyForSettlement-2nd',
            DATE_FORMAT( siteDelivery.settlement_3rd, '%Y-%m-%d' ) 'site-ReadyForSettlement-3rd',
            DATE_FORMAT( siteDelivery.settlement_4th, '%Y-%m-%d' ) 'site-ReadyForSettlement-4th',
            DATE_FORMAT( siteDelivery.SubconSettlement_1st, '%Y-%m-%d' ) 'site-Subcon-settlementDate-1st',
            DATE_FORMAT( siteDelivery.SubconSettlement_2nd, '%Y-%m-%d' ) 'site-Subcon-settlementDate-2nd',
            DATE_FORMAT( siteDelivery.SubconSettlement_3rd, '%Y-%m-%d' ) 'site-Subcon-settlementDate-3rd',
            DATE_FORMAT( siteDelivery.SubconSettlement_4th, '%Y-%m-%d' ) 'site-Subcon-settlementDate-4th',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y4')">
            sub.Subcon_name,
            subPo.Subcon_PO_number,
            DATE_FORMAT( subPo.release_date, '%Y-%m-%d' ) 'release_date',
            subItem.Subcon_PO_amount,
            subItem.Quantity subQuantity,
            subItem.Unit_price subUp,
            subItem.quantity_reduce subQuantityReduce,
            subItem.Milestone_1st 'SubconSettlementMilestone1st',
            subItem.Milestone_2nd 'SubconSettlementMilestone2nd',
            subItem.Milestone_3rd 'SubconSettlementMilestone3rd',
            subItem.Milestone_4th 'SubconSettlementMilestone4th',
            subItem.remark subRemark,
            IF
            ( subItem.Subcon_PO IS NULL, NULL, subItem.Site_ID ) subSiteID,
            IF
            ( subItem.Subcon_PO IS NULL, NULL, subItem.BOQ_item ) subBOQitem,
            IF
            ( subItem.Subcon_PO IS NULL, NULL, subItem.Item_code ) subItemcode,
            IF
            ( subItem.Subcon_PO IS NULL, NULL, site.site_name ) subSiteName,
            subItem.additional_cost,
            CONCAT( sub.Subcon_name, '_', subPo.Subcon_PO_number, '_', site.Site_Serial_number, '_', subItem.BOQ_item,
            '_', subItem.Subcon_PO_amount ) subconUniquenessField,
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y5')">
            DATE_FORMAT( ReadySettlement.Pre_Settlement_date, '%Y-%m-%d' ) 'Pre_Settlement_date',
            ReadySettlement.Pre_payment_ratio,
            ReadySettlement.Pre_payment_amount,
            DATE_FORMAT( ReadySettlement.settlement_1st, '%Y-%m-%d' ) 'ReadyForSettlement-1st',
            ReadySettlement.Settlement_ratio_1st 'settlement%-1st',
            ReadySettlement.amount_1st 'settlementAmount-1st',
            DATE_FORMAT( ReadySettlement.settlement_2nd, '%Y-%m-%d' ) 'ReadyForSettlement-2nd',
            ReadySettlement.Settlement_ratio_2nd 'settlement%-2nd',
            ReadySettlement.amount_2nd 'settlementAmount-2nd',
            DATE_FORMAT( ReadySettlement.settlement_3rd, '%Y-%m-%d' ) 'ReadyForSettlement-3rd',
            ReadySettlement.Settlement_ratio_3rd 'settlement%-3rd',
            ReadySettlement.amount_3rd 'settlementAmount-3rd',
            DATE_FORMAT( ReadySettlement.settlement_4th, '%Y-%m-%d' ) 'ReadyForSettlement-4th',
            ReadySettlement.Settlement_ratio_4th 'settlement%-4th',
            ReadySettlement.amount_4th 'settlementAmount-4th',
            ReadySettlement.settlement_Amount 'ReadySettlementAmount',
            ReadySettlement.settlement_amountGap 'readySettlementAmountGap',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y6')">
            DATE_FORMAT( ProductivityReport.report_date_1st, '%Y-%m-%d' ) '1stProductivityReportDate',
            ProductivityReport.report_amount_1st '1stProductivityReportAmount',
            DATE_FORMAT( ProductivityReport.report_date_2nd, '%Y-%m-%d' ) '2ndProductivityReportDate',
            ProductivityReport.report_amount_2nd '2ndProductivityReportAmount',
            DATE_FORMAT( ProductivityReport.report_date_3rd, '%Y-%m-%d' ) '3rdProductivityReportDate',
            ProductivityReport.report_amount_3rd '3rdProductivityReportAmount',
            DATE_FORMAT( ProductivityReport.report_date_4th, '%Y-%m-%d' ) '4thProductivityReportDate',
            ProductivityReport.report_amount_4th '4thProductivityReportAmount',
            ProductivityReport.Productivity_Amount 'ProductivityAmount',
            ProductivityReport.declaration_ratio 'ProductivityDeclarationRatio%',
            ProductivityReport.KPI_Archive_date as 'KPI-Archive-date',
            ProductivityReport.KPI_Archive_amount as 'KPI-Archive-amount',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y7')">
            DATE_FORMAT( SubconSettlement.settlement_time_1st, '%Y-%m-%d' ) 'Subcon-settlement-time-1st',
            SubconSettlement.settlement_ratio_1st 'Subcon-settlement%-1st',
            SubconSettlement.settlementAmount_1st 'Subcon-settlement-amount-1st',
            DATE_FORMAT( SubconSettlement.settlement_time_2nd, '%Y-%m-%d' ) 'Subcon-settlement-time-2nd',
            SubconSettlement.settlement_ratio_2nd 'Subcon-settlement%-2nd',
            SubconSettlement.settlementAmount_2nd 'Subcon-settlement-amount-2nd',
            DATE_FORMAT( SubconSettlement.settlement_time_3rd, '%Y-%m-%d' ) 'Subcon-settlement-time-3rd',
            SubconSettlement.settlement_ratio_3rd 'Subcon-settlement%-3rd',
            SubconSettlement.settlementAmount_3rd 'Subcon-settlement-amount-3rd',
            DATE_FORMAT( SubconSettlement.settlement_time_4th, '%Y-%m-%d' ) 'Subcon-settlement-time-4th',
            SubconSettlement.settlement_ratio_4th 'Subcon-settlement%-4st',
            SubconSettlement.settlementAmount_4th 'Subcon-settlement-amount-4th',
            SubconSettlement.Totally_Amount 'SubconTotallySettlementAmount',
            SubconSettlement.Totally_amount_Gap 'SubconTotallySettlementGap',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y8')">
            DATE_FORMAT( SubconPayment.Payment_time_1st, '%Y-%m-%d' ) 'Subcon-Payment-time-1st',
            SubconPayment.payment_amount_1st 'Subcon-payment-amount-1st',
            DATE_FORMAT( SubconPayment.Payment_time_2st, '%Y-%m-%d' ) 'Subcon-Payment-time-2nd',
            SubconPayment.payment_amount_2st 'Subcon-payment-amount-2nd',
            DATE_FORMAT( SubconPayment.Payment_time_3st, '%Y-%m-%d' ) 'Subcon-Payment-time-3rd',
            SubconPayment.payment_amount_3st 'Subcon-payment-amount-3rd',
            DATE_FORMAT( SubconPayment.Payment_time_4st, '%Y-%m-%d' ) 'Subcon-Payment-time-4st',
            SubconPayment.payment_amount_4st 'Subcon-payment-amount-4st',
            SubconPayment.payment_number_1st 'Subcon-payment-number-1st',
            SubconPayment.payment_number_2st 'Subcon-payment-number-2st',
            SubconPayment.payment_number_3st 'Subcon-payment-number-3st',
            SubconPayment.payment_number_4st 'Subcon-payment-number-4st',
            SubconPayment.remark subPayRemark,
            SubconPayment.Totally_payment 'SubconTotallyPaymentAmount',
            SubconPayment.Totally_payment_gap 'SubconTotallyPaymentAmountGap',
            SubconPayment.CN_amount_1st as 'subcon-CN-amount-1st',
            SubconPayment.CN_remark_1st as 'subcon-CN-remark-1st',
            SubconPayment.CN_amount_2nd as 'subcon-CN-amount-2nd',
            SubconPayment.CN_remark_2nd as 'subcon-CN-remark-2nd',
            SubconPayment.CN_amount_3rd as 'subcon-CN-amount-3rd',
            SubconPayment.CN_remark_3rd as 'subcon-CN-remark-3rd',
            SubconPayment.CN_amount_4st as 'subcon-CN-amount-4st',
            SubconPayment.CN_remark_4st as 'subcon-CN-remark-4st',
            SubconPayment.Totally_CN_amount as 'subcon-Totally-CN-amount',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y9')">
            DATE_FORMAT( YPTTSettlement.Invoice_date_1st, '%Y-%m-%d' ) 'Invoice-date-1st',
            YPTTSettlement.Invoice_number_1st 'Invoice-number-1st',
            YPTTSettlement.Invoice_Amount_1st 'Invoice-Amount-1st',
            YPTTSettlement.Invoice_Amount_diff_1st 'Invoice-Amount-Diff-1st',
            YPTTSettlement.Invoice_remark_1st 'Invoice-remark-1st',
            DATE_FORMAT( YPTTSettlement.Invoice_date_2st, '%Y-%m-%d' ) 'Invoice-date-2nd',
            YPTTSettlement.Invoice_number_2st 'Invoice-number-2nd',
            YPTTSettlement.Invoice_Amount_2st 'Invoice-Amount-2nd',
            YPTTSettlement.Invoice_Amount_diff_2st 'Invoice-Amount-Diff-2nd',
            YPTTSettlement.Invoice_remark_2st 'Invoice-remark-2nd',
            DATE_FORMAT( YPTTSettlement.Invoice_date_3st, '%Y-%m-%d' ) 'Invoice-date-3rd',
            YPTTSettlement.Invoice_number_3st 'Invoice-number-3rd',
            YPTTSettlement.Invoice_Amount_3st 'Invoice-Amount-3rd',
            YPTTSettlement.Invoice_Amount_diff_3st 'Invoice-Amount-Diff-3rd',
            YPTTSettlement.Invoice_remark_3st 'Invoice-remark-3rd',
            DATE_FORMAT( YPTTSettlement.Invoice_date_4st, '%Y-%m-%d' ) 'Invoice-date-4th',
            YPTTSettlement.Invoice_number_4st 'Invoice-number-4th',
            YPTTSettlement.Invoice_Amount_4st 'Invoice-Amount-4th',
            YPTTSettlement.Invoice_Amount_diff_4st 'Invoice-Amount-Diff-4th',
            YPTTSettlement.Invoice_remark_4st 'Invoice-remark-4th',
            YPTTSettlement.remark ypttRemark,
            YPTTSettlement.Invoice_amount 'TotallyInvoiceAmount',
            YPTTSettlement.Invoice_amount_gap 'InvoiceAmountGAP',
            YPTTSettlement.CN_amount_1st as 'ypttsettlement-CN-amount-1st',
            YPTTSettlement.CN_remark_1st as 'ypttsettlement-CN-remark-1st',
            YPTTSettlement.CN_amount_2nd as 'ypttsettlement-CN-amount-2nd',
            YPTTSettlement.CN_remark_2nd as 'ypttsettlement-CN-remark-2nd',
            YPTTSettlement.CN_amount_3rd as 'ypttsettlement-CN-amount-3rd',
            YPTTSettlement.CN_remark_3rd as 'ypttsettlement-CN-remark-3rd',
            YPTTSettlement.CN_amount_4st as 'ypttsettlement-CN-amount-4st',
            YPTTSettlement.CN_remark_4st as 'ypttsettlement-CN-remark-4st',
            YPTTSettlement.Totally_CN_amount as 'ypttsettlement-Totally-CN-amount',
        </if>
        uf.uniqueness_field 'siteUn',
        YPTTProject.YPTT_Project_name,
        YPTTProject.YPTT_Project_code
        FROM
        memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem
        on JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field and siteItem.is_deleted = 0
        LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site
        on uf.Site_ID = site.Site_Serial_number and site.is_deleted = 0
        LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
        on poItem.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and poItem.is_deleted = 0
        LEFT JOIN memm_f15b45017dee432daf88693b3d13b60b cusProject
        on JSON_ARRAY(CONCAT(cusProject.id)) = poItem.Customer_project and cusProject.is_deleted = 0
        LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
        on JSON_ARRAY(CONCAT(po.id)) = poItem.PO and po.is_deleted = 0
        LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 siteDelivery
        on siteDelivery.uniqueness_field = poItem.uniqueness_field and siteDelivery.is_deleted = 0
        LEFT JOIN memm_157ac31323c34d46920918117cb577ad subItem
        on subItem.uniqueness_field = siteItem.uniqueness_field and subItem.Subcon_PO IS NOT NULL and
        subItem.is_deleted = 0
        LEFT JOIN memm_ff802d120a12430db18a68deb783b9c6 subPo
        on subItem.Subcon_PO = JSON_ARRAY(CONCAT(subPo.id)) and subPo.is_deleted = 0
        LEFT JOIN memm_134d9474dc244b26bfd7f013a0534710 sub
        on subItem.Subcon = JSON_ARRAY(CONCAT(sub.id)) and sub.is_deleted = 0
        LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 ReadySettlement
        on ReadySettlement.uniqueness_field = siteItem.uniqueness_field and
        ReadySettlement.is_deleted = 0
        LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
        on ProductivityReport.uniqueness_field = siteItem.uniqueness_field and
        ProductivityReport.is_deleted = 0
        LEFT JOIN memm_218a6ab9959842099fd074c2b0ef685b SubconSettlement
        on SubconSettlement.uniqueness_field = siteItem.uniqueness_field and
        SubconSettlement.is_deleted = 0
        LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
        on SubconPayment.uniqueness_field = siteItem.uniqueness_field and
        SubconPayment.is_deleted = 0
        LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 YPTTSettlement
        on YPTTSettlement.uniqueness_field = siteItem.uniqueness_field and
        YPTTSettlement.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        on uf.Project_code = YPTTProject.YPTT_Project_code and YPTTProject.is_deleted = 0
        LEFT JOIN sys_dept dept
        on JSON_ARRAY(CONCAT(dept.dept_id)) = YPTTProject.Department and dept.is_deleted = 0
        where uf.is_deleted = 0
        and siteItem.site is not null
        <if test="list != null and list.size != 0">
            and YPTTProject.id in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dateType != null and dateType == 'site_allocation_date'">
            and (DATE_FORMAT( siteItem.site_allocation_date, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'PO_Received_date'">
            and (DATE_FORMAT( po.PO_Received_date, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Start_Working_date'">
            and (DATE_FORMAT( siteDelivery.Start_Working_date, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'E_ATP_Pass'">
            and (DATE_FORMAT( siteDelivery.E_ATP_Pass, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'site_ReadyForSettlement_1st'">
            and (DATE_FORMAT( siteDelivery.settlement_1st, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_ReadyForSettlement_2nd'">
            and (DATE_FORMAT( siteDelivery.settlement_2nd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_ReadyForSettlement_3rd'">
            and (DATE_FORMAT( siteDelivery.settlement_3rd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_ReadyForSettlement_4th'">
            and (DATE_FORMAT( siteDelivery.settlement_4th, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'site_Subcon_settlementDate_1st'">
            and (DATE_FORMAT( siteDelivery.SubconSettlement_1st, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_Subcon_settlementDate_2nd'">
            and (DATE_FORMAT( siteDelivery.SubconSettlement_2nd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_Subcon_settlementDate_3rd'">
            and (DATE_FORMAT( siteDelivery.SubconSettlement_3rd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_Subcon_settlementDate_4th'">
            and (DATE_FORMAT( siteDelivery.SubconSettlement_4th, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'release_date' ">
            and (DATE_FORMAT( subPo.release_date, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'Subcon_Payment_time_1st'">
            and (DATE_FORMAT( SubconPayment.Payment_time_1st, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Subcon_Payment_time_2nd' ">
            and (DATE_FORMAT( SubconPayment.Payment_time_2nd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Subcon_Payment_time_3rd'">
            and (DATE_FORMAT( SubconPayment.Payment_time_3rd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Subcon_Payment_time_4st'">
            and (DATE_FORMAT( SubconPayment.Payment_time_4st, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'Invoice_date_1st'">
            and (DATE_FORMAT( YPTTSettlement.Invoice_date_1st, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Invoice_date_2nd'">
            and (DATE_FORMAT( YPTTSettlement.Invoice_date_2nd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Invoice_date_3rd'">
            and (DATE_FORMAT( YPTTSettlement.Invoice_date_3rd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Invoice_date_4th'">
            and (DATE_FORMAT( YPTTSettlement.Invoice_date_4th, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="area != null and area != ''">
            and site.Area = #{area}
        </if>
        <if test="nation != null and nation != ''">
            and YPTTProject.branch = #{nation}
        </if>
    </select>

    <select id="findPoRelPoItem" resultType="java.lang.Long">
        SELECT id
        FROM memm_PO_PO_Item_mdr_l096d
        WHERE is_deleted = 0
        and right_data_id = #{poItemId,jdbcType=BIGINT}
        and left_data_id = #{poId,jdbcType=BIGINT}
    </select>

    <insert id="addPoRelPoItem">
        INSERT INTO memm_PO_PO_Item_mdr_l096d
        (id, left_data_id, right_data_id, create_by, create_time, is_deleted,
        update_by, update_time)
        VALUES (#{id,jdbcType=BIGINT}, #{poId,jdbcType=BIGINT}, #{poItemId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},
        now(), 0, #{userId,jdbcType=BIGINT}, now())
    </insert>

    <select id="findSubconPoRelPoItem" resultType="java.lang.Long">
        SELECT id
        FROM memm_Subcon_PO_Subcon_PO_Item_mdr_mr6as
        WHERE is_deleted = 0
        and right_data_id = #{right,jdbcType=BIGINT}
        and left_data_id = #{left,jdbcType=BIGINT}
    </select>

    <insert id="addSubconPoRelPoItem">
        INSERT INTO memm_Subcon_PO_Subcon_PO_Item_mdr_mr6as
        (id, left_data_id, right_data_id, create_by, create_time, is_deleted,
        update_by, update_time)
        VALUES (#{id,jdbcType=BIGINT}, #{left,jdbcType=BIGINT}, #{right,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},
        now(), 0, #{userId,jdbcType=BIGINT}, now())
    </insert>

    <select id="getIncomeExpenditure" resultType="java.util.Map">
        select m.*,project.YPTT_Project_name,
        ypttSettlement.CN_amount_1st,
        ypttSettlement.CN_date_1st,
        ypttSettlement.CN_amount_2nd,
        ypttSettlement.CN_date_2nd,
        ypttSettlement.CN_amount_3rd,
        ypttSettlement.CN_date_3rd,
        ypttSettlement.CN_amount_4st,
        ypttSettlement.CN_date_4st
        from memm_c58db11c81d3403ab9e59ce72b815ade m
        left join memm_72a2450126dd41708a07374eff08b982 project on JSON_ARRAY(CONCAT(project.id)) = m.project_name and
        project.is_deleted = 0
        left join memm_4bf72c9a610c4b05a007f0f215b424a6 as ypttSettlement on ypttSettlement.uniqueness_field = m.uniqueness_field
        and ypttSettlement.is_deleted = 0
        where m.is_deleted = 0
        <if test="projectId != null and projectId != ''">
            and JSON_CONTAINS(m.project_name, CONCAT('"',#{projectId,jdbcType=BIGINT},'"'))
        </if>
        <if test="startTime != null and endTime != null">
            and (
            m.Invoice_amount_date between #{startTime} and #{endTime}
            or m.Subcon_payment_date between #{startTime} and #{endTime}
            or m.Subcon_PO_amount_d between #{startTime} and #{endTime}
            or m.PO_Value_date between #{startTime} and #{endTime}
            or m.Item_Value_date between #{startTime} and #{endTime}
            or m.Ready_settlement_d between #{startTime} and #{endTime}
            or m.Productivity_AmountD between #{startTime} and #{endTime}
            or m.Subcon_settlement_d between #{startTime} and #{endTime}
            or m.Ready_settlement_d2 between #{startTime} and #{endTime}
            or m.Ready_settlement_d3 between #{startTime} and #{endTime}
            or m.Ready_settlement_d4 between #{startTime} and #{endTime}
            or m.Productivity_AmountD2 between #{startTime} and #{endTime}
            or m.ProductivityAmountD3 between #{startTime} and #{endTime}
            or m.ProductivityAmountD4 between #{startTime} and #{endTime}
            or m.Subcon_settlement_d2 between #{startTime} and #{endTime}
            or m.Subcon_settlement_d3 between #{startTime} and #{endTime}
            or m.Subcon_settlement_d4 between #{startTime} and #{endTime}
            or m.Subcon_payment_date2 between #{startTime} and #{endTime}
            or m.Subcon_payment_date3 between #{startTime} and #{endTime}
            or m.Subcon_payment_date4 between #{startTime} and #{endTime}
            or m.Pre_Settlement_date between #{startTime} and #{endTime}
            or m.Invoice_amount_date2 between #{startTime} and #{endTime}
            or m.Invoice_amount_date3 between #{startTime} and #{endTime}
            or m.Invoice_amount_date4 between #{startTime} and #{endTime}
            )
        </if>
    </select>
    <select id="getSiteItemIdByProjectId" resultType="java.lang.Long">
        SELECT DISTINCT siteItem.id
        FROM memm_e648652640b44b2092c93e1742e6171b siteItem
        WHERE siteItem.is_deleted = 0
        and JSON_CONTAINS(siteItem.TPTT_Project, CONCAT('"',#{projectId,jdbcType=BIGINT},'"'))
    </select>

    <select id="getIdsByProjectId" resultType="java.lang.Long">
        SELECT DISTINCT info.id
        FROM
        <choose>
            <when test="type == 'y2'">
                memm_f37920ed96f942fb8f4b1bf16f79e39c
            </when>
            <when test="type == 'y3'">
                memm_e45cb01fc742457a85ed8243aff1aa28
            </when>
            <when test="type == 'y4'">
                memm_157ac31323c34d46920918117cb577ad
            </when>
            <when test="type == 'y5'">
                memm_abdf4191a91e436a9b7e04351042f757
            </when>
            <when test="type == 'y6'">
                memm_5c8c376451894fdfb7e751c91da66f16
            </when>
            <when test="type == 'y7'">
                memm_218a6ab9959842099fd074c2b0ef685b
            </when>
            <when test="type == 'y8'">
                memm_f562b5dbd2be42d99c4992dd2668ed74
            </when>
            <when test="type == 'y9'">
                memm_4bf72c9a610c4b05a007f0f215b424a6
            </when>
        </choose>
        info
        left join memm_e648652640b44b2092c93e1742e6171b siteItem
        on siteItem.uniqueness_field = info.uniqueness_field and info.is_deleted = 0
        WHERE info.is_deleted = 0
        and JSON_CONTAINS(siteItem.TPTT_Project, CONCAT('"',#{projectId,jdbcType=BIGINT},'"'))
    </select>

    <select id="getSiteIdByProjectId" resultType="java.lang.Long">
        SELECT DISTINCT site.id
        FROM memm_448208a319fa4d7ab3d77ee54e10c066 site
        left join memm_e648652640b44b2092c93e1742e6171b siteItem
        on site.id = JSON_UNQUOTE(siteItem.site -> '$[0]') and siteItem.is_deleted = 0
        WHERE site.is_deleted = 0
        and JSON_CONTAINS(siteItem.TPTT_Project, CONCAT('"',#{projectId,jdbcType=BIGINT},'"'))
    </select>

    <select id="getPoIdByProjectId" resultType="java.lang.Long">
        SELECT DISTINCT po.id
        FROM memm_ed87f18383f04a8f836cea32a1628fc9 po
        left join memm_f37920ed96f942fb8f4b1bf16f79e39c poItem on po.id = JSON_UNQUOTE(poItem.PO -> '$[0]')
        left join memm_e648652640b44b2092c93e1742e6171b siteItem
        on poItem.uniqueness_field = siteItem.uniqueness_field and siteItem.is_deleted = 0
        WHERE po.is_deleted = 0
        and JSON_CONTAINS(siteItem.TPTT_Project, CONCAT('"',#{projectId,jdbcType=BIGINT},'"'))
    </select>

    <select id="getSubconPoIdByProjectId" resultType="java.lang.Long">
        SELECT DISTINCT subconPo.id
        FROM memm_ff802d120a12430db18a68deb783b9c6 subconPo
        left join memm_157ac31323c34d46920918117cb577ad subconPoItem
        on subconPo.id = JSON_UNQUOTE(subconPoItem.Subcon_PO -> '$[0]')
        left join memm_e648652640b44b2092c93e1742e6171b siteItem
        on subconPoItem.uniqueness_field = siteItem.uniqueness_field and siteItem.is_deleted = 0
        WHERE subconPo.is_deleted = 0
        and JSON_CONTAINS(siteItem.TPTT_Project, CONCAT('"',#{projectId,jdbcType=BIGINT},'"'))
    </select>

    <select id="findUniquenessById" resultType="java.util.Map">
        select *
        from memm_562ace74337e462289972ce20939e9a7
        where is_deleted = 0
        and id = #{id,jdbcType=BIGINT}
    </select>

    <select id="findUniquenessIdByStrField" resultType="java.lang.Long">
        select id
        from memm_562ace74337e462289972ce20939e9a7
        where is_deleted = 0
        and uniqueness_field = #{str,jdbcType=VARCHAR}
    </select>

    <update id="batchUpdateY3">
        <foreach collection="list" item="map" separator=";">
            update
            memm_e45cb01fc742457a85ed8243aff1aa28
            set
            is_deleted = 0
            <if test="map.settlement_1st != null and map.settlement_1st != ''">
                ,settlement_1st = #{map.settlement_1st}
            </if>
            <if test="map.settlement_2nd != null and map.settlement_2nd != ''">
            ,settlement_2nd = #{map.settlement_2nd}
            </if>
            <if test="map.settlement_3rd != null and map.settlement_3rd != ''">
            ,settlement_3rd = #{map.settlement_3rd}
            </if>
            <if test="map.settlement_4th != null and map.settlement_4th != ''">
            ,settlement_4th = #{map.settlement_4th}
            </if>
            <if test="map.SubconSettlement_1st != null and map.SubconSettlement_1st != ''">
            ,SubconSettlement_1st = #{map.SubconSettlement_1st}
            </if>
            <if test="map.SubconSettlement_2nd != null and map.SubconSettlement_2nd != ''">
            ,SubconSettlement_2nd = #{map.SubconSettlement_2nd}
            </if>
            <if test="map.SubconSettlement_3rd != null and map.SubconSettlement_3rd != ''">
            ,SubconSettlement_3rd =#{map.SubconSettlement_3rd}
            </if>
            <if test="map.SubconSettlement_4th != null and map.SubconSettlement_4th != ''">
            ,SubconSettlement_4th = #{map.SubconSettlement_4th}
            </if>
            where
            id = #{map.id}
        </foreach>
    </update>

    <update id="updateProjectPerByProjectId">
        update memm_439131c30ad445e6810ba53e13fd9cfb
        set y1_query  = #{map.y1_query},
        y2_query  = #{map.y2_query},
        y3_query  = #{map.y3_query},
        y4_query  = #{map.y4_query},
        y5_query  = #{map.y5_query},
        y6_query  = #{map.y6_query},
        y7_query  = #{map.y7_query},
        y8_query  = #{map.y8_query},
        y9_query  = #{map.y9_query},
        y1_update = #{map.y1_update},
        y2_update = #{map.y2_update},
        y3_update = #{map.y3_update},
        y4_update = #{map.y4_update},
        y5_update = #{map.y5_update},
        y6_update = #{map.y6_update},
        y7_update = #{map.y7_update},
        y8_update = #{map.y8_update},
        y9_update = #{map.y9_update},
        y1_del    = #{map.y1_del},
        y2_del    = #{map.y2_del},
        y3_del    = #{map.y3_del},
        y4_del    = #{map.y4_del},
        y5_del    = #{map.y5_del},
        y6_del    = #{map.y6_del},
        y7_del    = #{map.y7_del},
        y8_del    = #{map.y8_del},
        y9_del    = #{map.y9_del}
        where JSON_UNQUOTE(project -> '$[0]') = #{map.project}
        and is_deleted = 0
    </update>

    <select id="getUserIdByMemberId" resultType="com.pig4cloud.pig.yptt.entity.UserRoleInfo">
        select JSON_UNQUOTE(Project_Member -> '$[0]') userId, id projectMemberId
        from memm_7abc0f7fd9d84f67b4cd9b32575a6933
        where
        id in
        <foreach collection="list" separator="," item="id" open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="getUserRoleInfo" resultType="com.pig4cloud.pig.yptt.entity.UserRoleInfo">
        SELECT JSON_UNQUOTE(m.Project_Member -> '$[0]') userId, m.Project_Role roleIds, m.id projectMemberId
        FROM memm_7abc0f7fd9d84f67b4cd9b32575a6933 m
        LEFT JOIN memm_YPTT_Project_YPTT_Project_Member_mdr_39scc rel
        on m.id = rel.right_data_id and rel.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 p on p.id = rel.left_data_id and p.is_deleted = 0
        WHERE p.id = #{projectId,jdbcType=BIGINT}
        and m.is_deleted = 0
    </select>

    <insert id="batchSaveDataLog">
        <foreach collection="list" item="dto" separator=";">
            insert into me_data_log
            (id, type, `source`, model_id, data_id, group_key, meta_attr_id, before_data,
            after_data, `comment`, tenant_id, create_by, create_time, update_by, update_time)
            VALUES
            (#{dto.id}, #{dto.type}, #{dto.source}, #{dto.modelId}, #{dto.dataId}, #{dto.groupKey}, #{dto.metaAttrId},
            #{dto.beforeData}, #{dto.afterData}, #{dto.comment}, #{dto.tenantId}, #{dto.createBy}, #{dto.createTime},
            #{dto.updateBy}, #{dto.updateTime})
        </foreach>
    </insert>

    <select id="findY3ById" resultType="java.util.Map">
        select *
        from memm_e45cb01fc742457a85ed8243aff1aa28
        where id = #{id,jdbcType=BIGINT}
        and is_deleted = 0
    </select>

    <select id="reportFormPage" resultType="java.util.Map">
        SELECT DISTINCT
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y1')">
            IFNULL(
            DATE_FORMAT( uf.create_time, '%Y-%m-%d' ),
            DATE_FORMAT( NOW(), '%Y-%m-%d' )) AS Site_register_date,
            site.Site_Serial_number,
            site.site_name,
            DATE_FORMAT( siteItem.site_allocation_date, '%Y-%m-%d' ) AS site_allocation_date,
            site.Area,
            uf.Region,
            siteItem.Site_model,
            siteItem.BOQ_item,
            CASE
            siteItem.Site_item_status
            WHEN '["unclose"]' THEN
            '未关闭'
            WHEN '["close"]' THEN
            '已关闭'
            WHEN '["invalid"]' THEN
            '无效' ELSE '未知'
            END AS Site_item_status,
            siteItem.Item_code,
            siteItem.Phase,
            dept.`name`,
            siteItem.Type_of_service,

            siteItem.Quantity,

            siteItem.Unit_price,
            siteItem.Site_value,
            siteItem.Remark siteItemRemark,
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y2')">
            DATE_FORMAT( po.PO_Received_date, '%Y-%m-%d' ) AS PO_Received_date,
            po.PO_number,
            poItem.Quantity poItemQuantity,
            poItem.quantity_reduce poItemQuantityReduce,
            cusProject.Contract_number,
            cusProject.Custom_project_name,
            poItem.Unit_price poUp,
            poItem.PO_value,
            poItem.PO_gap,
            poItem.BOQ_item po_BOQ_item,
            poItem.Pre_payment,
            poItem.Milestone_1st 'SiteSettlementMilestone1st%',
            poItem.Milestone_2nd 'SiteSettlementMilestone2nd%',
            poItem.Milestone_3rd 'SiteSettlementMilestone3rd%',
            poItem.Milestone_4th 'SiteSettlementMilestone4th%',
            poItem.Remark poItemRemark,
            IF
            ( poItem.PO IS NULL, NULL, poItem.Item_code ) poItemcode,
            IF
            ( poItem.PO IS NULL, NULL, poItem.Site_ID ) poItemSiteID,
            IF
            ( poItem.PO IS NULL, NULL, poItem.Project_code ) poItemProjectcode,
            IF
            ( poItem.PO IS NULL, NULL, poItem.Phase ) poItemPhase,
            IF
            ( poItem.PO IS NULL, NULL, site.site_name ) poItemSiteName,
            IF
            ( poItem.PO IS NULL, NULL, poItem.Region ) poItemRegion,
            IF
            ( poItem.PO IS NULL, NULL, uf.uniqueness_field ) 'poItemUn',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y3')">
            siteDelivery.Site_belong_to,
            siteDelivery.Team_Leader_DT,
            siteDelivery.engineer_DTA_SPV,
            siteDelivery.PLO_PC_Others,
            siteDelivery.PIC_PC_PM,
            DATE_FORMAT( siteDelivery.Start_Working_date, '%Y-%m-%d' ) 'Start_Working_date',
            DATE_FORMAT( siteDelivery.Completed_work_date, '%Y-%m-%d' ) 'Completed_work_date',
            DATE_FORMAT( siteDelivery.air_CI_Report_submit, '%Y-%m-%d' ) 'air_CI_Report_submit',
            DATE_FORMAT( siteDelivery.Site_manager_Report, '%Y-%m-%d' ) 'Site_manager_Report',
            DATE_FORMAT( siteDelivery.E_ATP_Pass, '%Y-%m-%d' ) 'E_ATP_Pass',
            DATE_FORMAT( siteDelivery.F_PAC_Pass, '%Y-%m-%d' ) 'F_PAC_Pass',
            DATE_FORMAT( siteDelivery.G_FAC, '%Y-%m-%d' ) 'G_FAC',
            siteDelivery.remark deliveryRemark,
            DATE_FORMAT( siteDelivery.settlement_1st, '%Y-%m-%d' ) 'site-ReadyForSettlement-1st',
            DATE_FORMAT( siteDelivery.settlement_2nd, '%Y-%m-%d' ) 'site-ReadyForSettlement-2nd',
            DATE_FORMAT( siteDelivery.settlement_3rd, '%Y-%m-%d' ) 'site-ReadyForSettlement-3rd',
            DATE_FORMAT( siteDelivery.settlement_4th, '%Y-%m-%d' ) 'site-ReadyForSettlement-4th',
            DATE_FORMAT( siteDelivery.SubconSettlement_1st, '%Y-%m-%d' ) 'site-Subcon-settlementDate-1st',
            DATE_FORMAT( siteDelivery.SubconSettlement_2nd, '%Y-%m-%d' ) 'site-Subcon-settlementDate-2nd',
            DATE_FORMAT( siteDelivery.SubconSettlement_3rd, '%Y-%m-%d' ) 'site-Subcon-settlementDate-3rd',
            DATE_FORMAT( siteDelivery.SubconSettlement_4th, '%Y-%m-%d' ) 'site-Subcon-settlementDate-4th',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y4')">
            sub.Subcon_name,
            subPo.Subcon_PO_number,
            DATE_FORMAT( subPo.release_date, '%Y-%m-%d' ) 'release_date',
            subItem.Subcon_PO_amount,
            subItem.Quantity subQuantity,
            subItem.Unit_price subUp,
            subItem.quantity_reduce subQuantityReduce,
            subItem.Milestone_1st 'SubconSettlementMilestone1st',
            subItem.Milestone_2nd 'SubconSettlementMilestone2nd',
            subItem.Milestone_3rd 'SubconSettlementMilestone3rd',
            subItem.Milestone_4th 'SubconSettlementMilestone4th',
            subItem.remark subRemark,
            IF
            ( subItem.Subcon_PO IS NULL, NULL, subItem.Site_ID ) subSiteID,
            IF
            ( subItem.Subcon_PO IS NULL, NULL, subItem.BOQ_item ) subBOQitem,
            IF
            ( subItem.Subcon_PO IS NULL, NULL, subItem.Item_code ) subItemcode,
            IF
            ( subItem.Subcon_PO IS NULL, NULL, site.site_name ) subSiteName,
            subItem.additional_cost,
            CONCAT( sub.Subcon_name, '_', subPo.Subcon_PO_number, '_', site.Site_Serial_number, '_', subItem.BOQ_item,
            '_', subItem.Subcon_PO_amount ) subconUniquenessField,
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y5')">
            DATE_FORMAT( ReadySettlement.Pre_Settlement_date, '%Y-%m-%d' ) 'Pre_Settlement_date',
            ReadySettlement.Pre_payment_ratio,
            ReadySettlement.Pre_payment_amount,
            DATE_FORMAT( ReadySettlement.settlement_1st, '%Y-%m-%d' ) 'ReadyForSettlement-1st',
            ReadySettlement.Settlement_ratio_1st 'settlement%-1st',
            ReadySettlement.amount_1st 'settlementAmount-1st',
            DATE_FORMAT( ReadySettlement.settlement_2nd, '%Y-%m-%d' ) 'ReadyForSettlement-2nd',
            ReadySettlement.Settlement_ratio_2nd 'settlement%-2nd',
            ReadySettlement.amount_2nd 'settlementAmount-2nd',
            DATE_FORMAT( ReadySettlement.settlement_3rd, '%Y-%m-%d' ) 'ReadyForSettlement-3rd',
            ReadySettlement.Settlement_ratio_3rd 'settlement%-3rd',
            ReadySettlement.amount_3rd 'settlementAmount-3rd',
            DATE_FORMAT( ReadySettlement.settlement_4th, '%Y-%m-%d' ) 'ReadyForSettlement-4th',
            ReadySettlement.Settlement_ratio_4th 'settlement%-4th',
            ReadySettlement.amount_4th 'settlementAmount-4th',
            ReadySettlement.settlement_Amount 'ReadySettlementAmount',
            ReadySettlement.settlement_amountGap 'readySettlementAmountGap',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y6')">
            DATE_FORMAT( ProductivityReport.report_date_1st, '%Y-%m-%d' ) '1stProductivityReportDate',
            ProductivityReport.report_amount_1st '1stProductivityReportAmount',
            DATE_FORMAT( ProductivityReport.report_date_2nd, '%Y-%m-%d' ) '2ndProductivityReportDate',
            ProductivityReport.report_amount_2nd '2ndProductivityReportAmount',
            DATE_FORMAT( ProductivityReport.report_date_3rd, '%Y-%m-%d' ) '3rdProductivityReportDate',
            ProductivityReport.report_amount_3rd '3rdProductivityReportAmount',
            DATE_FORMAT( ProductivityReport.report_date_4th, '%Y-%m-%d' ) '4thProductivityReportDate',
            ProductivityReport.report_amount_4th '4thProductivityReportAmount',
            ProductivityReport.Productivity_Amount 'ProductivityAmount',
            ProductivityReport.declaration_ratio 'ProductivityDeclarationRatio%',
            ProductivityReport.KPI_Archive_date as 'KPI-Archive-date',
            ProductivityReport.KPI_Archive_amount as 'KPI-Archive-amount',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y7')">
            DATE_FORMAT( SubconSettlement.settlement_time_1st, '%Y-%m-%d' ) 'Subcon-settlement-time-1st',
            SubconSettlement.settlement_ratio_1st 'Subcon-settlement%-1st',
            SubconSettlement.settlementAmount_1st 'Subcon-settlement-amount-1st',
            DATE_FORMAT( SubconSettlement.settlement_time_2nd, '%Y-%m-%d' ) 'Subcon-settlement-time-2nd',
            SubconSettlement.settlement_ratio_2nd 'Subcon-settlement%-2nd',
            SubconSettlement.settlementAmount_2nd 'Subcon-settlement-amount-2nd',
            DATE_FORMAT( SubconSettlement.settlement_time_3rd, '%Y-%m-%d' ) 'Subcon-settlement-time-3rd',
            SubconSettlement.settlement_ratio_3rd 'Subcon-settlement%-3rd',
            SubconSettlement.settlementAmount_3rd 'Subcon-settlement-amount-3rd',
            DATE_FORMAT( SubconSettlement.settlement_time_4th, '%Y-%m-%d' ) 'Subcon-settlement-time-4th',
            SubconSettlement.settlement_ratio_4th 'Subcon-settlement%-4st',
            SubconSettlement.settlementAmount_4th 'Subcon-settlement-amount-4th',
            SubconSettlement.Totally_Amount 'SubconTotallySettlementAmount',
            SubconSettlement.Totally_amount_Gap 'SubconTotallySettlementGap',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y8')">
            DATE_FORMAT( SubconPayment.Payment_time_1st, '%Y-%m-%d' ) 'Subcon-Payment-time-1st',
            SubconPayment.payment_amount_1st 'Subcon-payment-amount-1st',
            DATE_FORMAT( SubconPayment.Payment_time_2st, '%Y-%m-%d' ) 'Subcon-Payment-time-2nd',
            SubconPayment.payment_amount_2st 'Subcon-payment-amount-2nd',
            DATE_FORMAT( SubconPayment.Payment_time_3st, '%Y-%m-%d' ) 'Subcon-Payment-time-3rd',
            SubconPayment.payment_amount_3st 'Subcon-payment-amount-3rd',
            DATE_FORMAT( SubconPayment.Payment_time_4st, '%Y-%m-%d' ) 'Subcon-Payment-time-4st',
            SubconPayment.payment_amount_4st 'Subcon-payment-amount-4st',
            SubconPayment.payment_number_1st 'Subcon-payment-number-1st',
            SubconPayment.payment_number_2st 'Subcon-payment-number-2st',
            SubconPayment.payment_number_3st 'Subcon-payment-number-3st',
            SubconPayment.payment_number_4st 'Subcon-payment-number-4st',
            SubconPayment.remark subPayRemark,
            SubconPayment.Totally_payment 'SubconTotallyPaymentAmount',
            SubconPayment.Totally_payment_gap 'SubconTotallyPaymentAmountGap',
            SubconPayment.CN_amount_1st as 'subcon-CN-amount-1st',
            SubconPayment.CN_remark_1st as 'subcon-CN-remark-1st',
            SubconPayment.CN_amount_2nd as 'subcon-CN-amount-2nd',
            SubconPayment.CN_remark_2nd as 'subcon-CN-remark-2nd',
            SubconPayment.CN_amount_3rd as 'subcon-CN-amount-3rd',
            SubconPayment.CN_remark_3rd as 'subcon-CN-remark-3rd',
            SubconPayment.CN_amount_4st as 'subcon-CN-amount-4st',
            SubconPayment.CN_remark_4st as 'subcon-CN-remark-4st',
            SubconPayment.Totally_CN_amount as 'subcon-Totally-CN-amount',
        </if>
        <if test="moduleTypes == null or moduleTypes == '' or moduleTypes.contains('Y9')">
            DATE_FORMAT( YPTTSettlement.Invoice_date_1st, '%Y-%m-%d' ) 'Invoice-date-1st',
            YPTTSettlement.Invoice_number_1st 'Invoice-number-1st',
            YPTTSettlement.Invoice_Amount_1st 'Invoice-Amount-1st',
            YPTTSettlement.Invoice_Amount_diff_1st 'Invoice-Amount-Diff-1st',
            YPTTSettlement.Invoice_remark_1st 'Invoice-remark-1st',
            DATE_FORMAT( YPTTSettlement.Invoice_date_2st, '%Y-%m-%d' ) 'Invoice-date-2nd',
            YPTTSettlement.Invoice_number_2st 'Invoice-number-2nd',
            YPTTSettlement.Invoice_Amount_2st 'Invoice-Amount-2nd',
            YPTTSettlement.Invoice_Amount_diff_2st 'Invoice-Amount-Diff-2nd',
            YPTTSettlement.Invoice_remark_2st 'Invoice-remark-2nd',
            DATE_FORMAT( YPTTSettlement.Invoice_date_3st, '%Y-%m-%d' ) 'Invoice-date-3rd',
            YPTTSettlement.Invoice_number_3st 'Invoice-number-3rd',
            YPTTSettlement.Invoice_Amount_3st 'Invoice-Amount-3rd',
            YPTTSettlement.Invoice_Amount_diff_3st 'Invoice-Amount-Diff-3rd',
            YPTTSettlement.Invoice_remark_3st 'Invoice-remark-3rd',
            DATE_FORMAT( YPTTSettlement.Invoice_date_4st, '%Y-%m-%d' ) 'Invoice-date-4th',
            YPTTSettlement.Invoice_number_4st 'Invoice-number-4th',
            YPTTSettlement.Invoice_Amount_4st 'Invoice-Amount-4th',
            YPTTSettlement.Invoice_Amount_diff_4st 'Invoice-Amount-Diff-4th',
            YPTTSettlement.Invoice_remark_4st 'Invoice-remark-4th',
            YPTTSettlement.remark ypttRemark,
            YPTTSettlement.Invoice_amount 'TotallyInvoiceAmount',
            YPTTSettlement.Invoice_amount_gap 'InvoiceAmountGAP',
            YPTTSettlement.CN_amount_1st as 'ypttsettlement-CN-amount-1st',
            YPTTSettlement.CN_remark_1st as 'ypttsettlement-CN-remark-1st',
            YPTTSettlement.CN_amount_2nd as 'ypttsettlement-CN-amount-2nd',
            YPTTSettlement.CN_remark_2nd as 'ypttsettlement-CN-remark-2nd',
            YPTTSettlement.CN_amount_3rd as 'ypttsettlement-CN-amount-3rd',
            YPTTSettlement.CN_remark_3rd as 'ypttsettlement-CN-remark-3rd',
            YPTTSettlement.CN_amount_4st as 'ypttsettlement-CN-amount-4st',
            YPTTSettlement.CN_remark_4st as 'ypttsettlement-CN-remark-4st',
            YPTTSettlement.Totally_CN_amount as 'ypttsettlement-Totally-CN-amount',
        </if>
        uf.uniqueness_field 'siteUn',
        YPTTProject.YPTT_Project_name,
        YPTTProject.YPTT_Project_code
        FROM
        memm_562ace74337e462289972ce20939e9a7 uf
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem
        ON JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field and siteItem.is_deleted = 0
        LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site
        on uf.Site_ID = site.Site_Serial_number and site.is_deleted = 0
        LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
        on poItem.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and poItem.is_deleted = 0
        LEFT JOIN memm_f15b45017dee432daf88693b3d13b60b cusProject
        on JSON_ARRAY(CONCAT(cusProject.id)) = poItem.Customer_project and cusProject.is_deleted = 0
        LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
        on JSON_ARRAY(CONCAT(po.id)) = poItem.PO and po.is_deleted = 0
        LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 siteDelivery
        on siteDelivery.uniqueness_field = poItem.uniqueness_field and siteDelivery.is_deleted = 0
        LEFT JOIN memm_157ac31323c34d46920918117cb577ad subItem
        on subItem.uniqueness_field = siteItem.uniqueness_field and subItem.Subcon_PO IS NOT NULL and
        subItem.is_deleted = 0
        LEFT JOIN memm_ff802d120a12430db18a68deb783b9c6 subPo
        on subItem.Subcon_PO = JSON_ARRAY(CONCAT(subPo.id)) and subPo.is_deleted = 0
        LEFT JOIN memm_134d9474dc244b26bfd7f013a0534710 sub
        on subItem.Subcon = JSON_ARRAY(CONCAT(sub.id)) and sub.is_deleted = 0
        LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 ReadySettlement
        on ReadySettlement.uniqueness_field = siteItem.uniqueness_field and
        ReadySettlement.is_deleted = 0
        LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
        on ProductivityReport.uniqueness_field = siteItem.uniqueness_field and
        ProductivityReport.is_deleted = 0
        LEFT JOIN memm_218a6ab9959842099fd074c2b0ef685b SubconSettlement
        on SubconSettlement.uniqueness_field = siteItem.uniqueness_field and
        SubconSettlement.is_deleted = 0
        LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
        on SubconPayment.uniqueness_field = siteItem.uniqueness_field and
        SubconPayment.is_deleted = 0
        LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 YPTTSettlement
        on YPTTSettlement.uniqueness_field = siteItem.uniqueness_field and
        YPTTSettlement.is_deleted = 0
        LEFT JOIN memm_72a2450126dd41708a07374eff08b982 YPTTProject
        on uf.Project_code = YPTTProject.YPTT_Project_code and YPTTProject.is_deleted = 0
        LEFT JOIN sys_dept dept
        on JSON_ARRAY(CONCAT(dept.dept_id)) = YPTTProject.Department and dept.is_deleted = 0
        where uf.is_deleted = 0
        and siteItem.site is not null
        <if test="list != null and list.size != 0">
            and YPTTProject.id in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dateType != null and dateType == 'site_allocation_date'">
            and (DATE_FORMAT( siteItem.site_allocation_date, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'PO_Received_date'">
            and (DATE_FORMAT( po.PO_Received_date, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Start_Working_date'">
            and (DATE_FORMAT( siteDelivery.Start_Working_date, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'E_ATP_Pass'">
            and (DATE_FORMAT( siteDelivery.E_ATP_Pass, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'site_ReadyForSettlement_1st'">
            and (DATE_FORMAT( siteDelivery.settlement_1st, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_ReadyForSettlement_2nd'">
            and (DATE_FORMAT( siteDelivery.settlement_2nd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_ReadyForSettlement_3rd'">
            and (DATE_FORMAT( siteDelivery.settlement_3rd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_ReadyForSettlement_4th'">
            and (DATE_FORMAT( siteDelivery.settlement_4th, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'site_Subcon_settlementDate_1st'">
            and (DATE_FORMAT( siteDelivery.SubconSettlement_1st, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_Subcon_settlementDate_2nd'">
            and (DATE_FORMAT( siteDelivery.SubconSettlement_2nd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_Subcon_settlementDate_3rd'">
            and (DATE_FORMAT( siteDelivery.SubconSettlement_3rd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'site_Subcon_settlementDate_4th'">
            and (DATE_FORMAT( siteDelivery.SubconSettlement_4th, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'release_date' ">
            and (DATE_FORMAT( subPo.release_date, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'Subcon_Payment_time_1st'">
            and (DATE_FORMAT( SubconPayment.Payment_time_1st, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Subcon_Payment_time_2nd' ">
            and (DATE_FORMAT( SubconPayment.Payment_time_2nd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Subcon_Payment_time_3rd'">
            and (DATE_FORMAT( SubconPayment.Payment_time_3rd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Subcon_Payment_time_4st'">
            and (DATE_FORMAT( SubconPayment.Payment_time_4st, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="dateType != null and dateType == 'Invoice_date_1st'">
            and (DATE_FORMAT( YPTTSettlement.Invoice_date_1st, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Invoice_date_2nd'">
            and (DATE_FORMAT( YPTTSettlement.Invoice_date_2nd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Invoice_date_3rd'">
            and (DATE_FORMAT( YPTTSettlement.Invoice_date_3rd, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>
        <if test="dateType != null and dateType == 'Invoice_date_4th'">
            and (DATE_FORMAT( YPTTSettlement.Invoice_date_4th, '%Y-%m-%d' ) between #{dateStrStart} and #{dateStrEnd})
        </if>

        <if test="area != null and area != ''">
            and site.Area = #{area}
        </if>
        <if test="nation != null and nation != ''">
            and YPTTProject.branch = #{nation}
        </if>

    </select>
    <sql id="getY1">
        SELECT
            un.id,
            un.uniqueness_field,
            obj.Project_code,
            obj.Region,
            obj.Site_ID,
            obj.Phase,
            obj.Item_code,
            obj.BOQ_item,
            obj.Quantity,
            obj.Unit_price,
            obj.Site_value,
            obj.Type_of_service,
            site.site_name,
            CASE
                obj.Site_item_status
                WHEN '["unclose"]' THEN
                    '未关闭'
                WHEN '["close"]' THEN
                    '已关闭'
                WHEN '["invalid"]' THEN
                    '无效' ELSE '未知'
                END Site_item_status,
            obj.site_allocation_date
        FROM
            memm_e648652640b44b2092c93e1742e6171b obj
                LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un ON un.id = JSON_UNQUOTE( obj.uniqueness_field -> '$[0]' )
                AND un.is_deleted = 0
                LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site ON site.id = JSON_UNQUOTE( obj.site -> '$[0]' )
                AND site.is_deleted = 0
    </sql>
    <sql id="getY2">
        SELECT
            un.id,
            un.uniqueness_field,
            obj.Project_code,
            obj.Region,
            obj.Site_ID,
            obj.Phase,
            obj.Item_code,
            obj.BOQ_item,
            obj.Quantity,
            obj.Unit_price,
            obj.PO_value,
            c.Custom_project_name,
            site.Site_Name,
            obj.Pre_payment,
            obj.PO_gap,
            po.PO_number,
            obj.Milestone_1st,
            obj.Milestone_2nd,
            obj.Milestone_3rd,
            obj.Milestone_4th,
            obj.Remark
        FROM
            memm_f37920ed96f942fb8f4b1bf16f79e39c obj
                LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un ON CAST(un.id AS CHAR) = JSON_UNQUOTE( obj.uniqueness_field -> '$[0]' )
                AND un.is_deleted = 0
                LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site ON CAST(site.id AS CHAR) = JSON_UNQUOTE( obj.site -> '$[0]' )
                AND site.is_deleted = 0
                LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po ON CAST(po.id AS CHAR) = JSON_UNQUOTE( obj.PO -> '$[0]' )
                AND po.is_deleted = 0
                LEFT JOIN memm_f15b45017dee432daf88693b3d13b60b c ON CAST(c.id AS CHAR) = JSON_UNQUOTE( obj.Customer_project -> '$[0]' )
                AND c.is_deleted = 0
    </sql>
    <sql id="getY3">
        SELECT
            un.id,
            un.uniqueness_field,
            obj.Project_code,
            un.Region,
            obj.Site_ID,
            obj.Phase,
            obj.Item_code,
            obj.BOQ_item,
            obj.Site_belong_to,
            obj.site_name,
            obj.Site_Model,
            obj.settlement_1st,
            obj.settlement_2nd,
            obj.settlement_3rd,
            obj.settlement_4th,
            obj.Team_Leader_DT,
            obj.engineer_DTA_SPV,
            obj.PLO_PC_Others,
            obj.Start_Working_date,
            obj.Completed_work_date,
            obj.air_CI_Report_submit,
            obj.Site_manager_Report,
            obj.E_ATP_Pass,
            obj.F_PAC_Pass,
            obj.G_FAC,
            s.Subcon_name,
            obj.SubconSettlement_1st,
            obj.SubconSettlement_2nd,
            obj.SubconSettlement_3rd,
            obj.SubconSettlement_4th,
            obj.remark
        FROM
            memm_e45cb01fc742457a85ed8243aff1aa28 obj
                LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un ON un.id = JSON_UNQUOTE( obj.uniqueness_field -> '$[0]' )
                AND un.is_deleted = 0
                LEFT JOIN memm_134d9474dc244b26bfd7f013a0534710 s ON s.id = JSON_UNQUOTE( obj.Subcon -> '$[0]' )
                AND s.is_deleted = 0
    </sql>
    <sql id="getY4">
        SELECT
            un.id,
            un.uniqueness_field,
            un.Project_code,
            un.Region,
            un.Site_ID,
            un.Phase,
            un.Item_code,
            s.Subcon_name,
            sp.Subcon_PO_number,
            site.site_name,
            obj.additional_cost,
            obj.Quantity,
            obj.Unit_price,
            obj.Subcon_PO_amount,
            obj.quantity_reduce,
            obj.BOQ_item,
            obj.Milestone_1st,
            obj.Milestone_2nd,
            obj.Milestone_3rd,
            obj.Milestone_4th,
            obj.remark
        FROM
            memm_157ac31323c34d46920918117cb577ad obj
                LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un ON un.id = JSON_UNQUOTE( obj.uniqueness_field -> '$[0]' )
                AND un.is_deleted = 0
                LEFT JOIN memm_ff802d120a12430db18a68deb783b9c6 sp ON sp.id = JSON_UNQUOTE( obj.Subcon_PO -> '$[0]' )
                AND sp.is_deleted = 0
                LEFT JOIN memm_134d9474dc244b26bfd7f013a0534710 s ON s.id = JSON_UNQUOTE( sp.Subcom -> '$[0]' )
                AND s.is_deleted = 0
                LEFT JOIN memm_448208a319fa4d7ab3d77ee54e10c066 site ON site.id = JSON_UNQUOTE( obj.Site -> '$[0]' )
                AND site.is_deleted = 0
    </sql>
    <sql id="getY5">
        SELECT
            un.id,
            un.uniqueness_field,
            un.Project_code,
            un.Region,
            un.Site_ID,
            un.Phase,
            un.Item_code,
            obj.Pre_Settlement_date,
            obj.Pre_payment_amount,
            obj.Pre_payment_ratio,
            obj.amount_1st,
            obj.settlement_1st,
            obj.Settlement_ratio_1st,
            obj.amount_2nd,
            obj.settlement_2nd,
            obj.Settlement_ratio_2nd,
            obj.amount_3rd,
            obj.settlement_3rd,
            obj.Settlement_ratio_3rd,
            obj.amount_4th,
            obj.settlement_4th,
            obj.Settlement_ratio_4th,
            obj.settlement_Amount,
            obj.settlement_amountGap
        FROM
            memm_abdf4191a91e436a9b7e04351042f757 obj
                LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un ON un.id = JSON_UNQUOTE( obj.uniqueness_field -> '$[0]' )
                AND un.is_deleted = 0
    </sql>
    <sql id="getY6">
        SELECT
            un.id,
            un.uniqueness_field,
            un.Project_code,
            un.Region,
            un.Site_ID,
            un.Phase,
            un.Item_code,
            obj.report_date_1st,
            obj.report_amount_1st,
            obj.report_date_2nd,
            obj.report_amount_2nd,
            obj.report_date_3rd,
            obj.report_amount_3rd,
            obj.report_date_4th,
            obj.report_amount_4th,
            obj.declaration_ratio,
            obj.Productivity_Amount,
            obj.report_date_1st
        FROM
            memm_5c8c376451894fdfb7e751c91da66f16 obj
                LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un ON un.id = JSON_UNQUOTE( obj.uniqueness_field -> '$[0]' )
                AND un.is_deleted = 0
    </sql>
    <sql id="getY7">
        SELECT
            un.id,
            un.uniqueness_field,
            un.Project_code,
            un.Region,
            un.Site_ID,
            un.Phase,
            un.Item_code,
            obj.Subcon_name,
            obj.Subcon_PO_number,
            obj.settlement_time_1st,
            obj.settlementAmount_1st,
            obj.settlement_ratio_1st,
            obj.settlement_time_2nd,
            obj.settlementAmount_2nd,
            obj.settlement_ratio_2nd,
            obj.settlement_time_3rd,
            obj.settlementAmount_3rd,
            obj.settlement_ratio_3rd,
            obj.settlement_time_4th,
            obj.settlementAmount_4th,
            obj.settlement_ratio_4th,
            obj.Totally_Amount,
            obj.Totally_amount_Gap
        FROM
            memm_218a6ab9959842099fd074c2b0ef685b obj
                LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un ON un.id = JSON_UNQUOTE( obj.uniqueness_field -> '$[0]' )
                AND un.is_deleted = 0
    </sql>
    <sql id="getY8">
        SELECT
            un.id,
            un.uniqueness_field,
            un.Project_code,
            un.Region,
            un.Site_ID,
            un.Phase,
            un.Item_code,
            obj.Payment_time_1st,
            obj.payment_amount_1st,
            obj.payment_number_1st,
            obj.Payment_time_2st,
            obj.payment_amount_2st,
            obj.payment_number_2st,
            obj.Payment_time_3st,
            obj.payment_amount_3st,
            obj.payment_number_3st,
            obj.Payment_time_4st,
            obj.payment_amount_4st,
            obj.payment_number_4st,
            obj.Totally_payment,
            obj.Totally_payment_gap
        FROM
            memm_f562b5dbd2be42d99c4992dd2668ed74 obj
                LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un ON un.id = JSON_UNQUOTE( obj.uniqueness_field -> '$[0]' )
                AND un.is_deleted = 0
    </sql>
    <sql id="getY9">
        SELECT
            un.id,
            un.uniqueness_field,
            un.Project_code,
            un.Region,
            un.Site_ID,
            un.Phase,
            un.Item_code,
            obj.Invoice_date_1st,
            obj.PO_number,
            obj.Contract_number,
            obj.Invoice_date_1st,
            obj.Invoice_Amount_1st,
            obj.Invoice_number_1st,
            obj.Invoice_Amount_diff_1st,
            obj.Invoice_remark_1st,
            obj.Invoice_date_2st,
            obj.Invoice_Amount_2st,
            obj.Invoice_number_2st,
            obj.Invoice_Amount_diff_2st,
            obj.Invoice_remark_2st,
            obj.Invoice_date_3st,
            obj.Invoice_Amount_3st,
            obj.Invoice_number_3st,
            obj.Invoice_Amount_diff_3st,
            obj.Invoice_remark_3st,
            obj.Invoice_date_4st,
            obj.Invoice_Amount_4st,
            obj.Invoice_number_4st,
            obj.Invoice_Amount_diff_4st,
            obj.Invoice_remark_4st,
            obj.Invoice_amount,
            obj.Invoice_amount_gap
        FROM
            memm_4bf72c9a610c4b05a007f0f215b424a6 obj
                LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un ON un.id = JSON_UNQUOTE( obj.uniqueness_field -> '$[0]' )
                AND un.is_deleted = 0
    </sql>
    <select id="deletePreview" resultType="java.util.Map">
        <choose>
            <when test="type == 'y1'">
                <include refid="getY1"/>
            </when>
            <when test="type == 'y2'">
                <include refid="getY2"/>
            </when>
            <when test="type == 'y3'">
                <include refid="getY3"/>
            </when>
            <when test="type == 'y4'">
                <include refid="getY4"/>
            </when>
            <when test="type == 'y5'">
                <include refid="getY5"/>
            </when>
            <when test="type == 'y6'">
                <include refid="getY6"/>
            </when>
            <when test="type == 'y7'">
                <include refid="getY7"/>
            </when>
            <when test="type == 'y8'">
                <include refid="getY8"/>
            </when>
            <when test="type == 'y9'">
                <include refid="getY9"/>
            </when>
        </choose>
        WHERE
        un.Project_code = #{projectCode,jdbcType=VARCHAR} and obj.is_deleted = 0
        <if test="unId != null and unId != ''">
            AND un.uniqueness_field = #{unId,jdbcType=VARCHAR}
        </if>
        <if test="itemCode != null and itemCode != ''">
            AND un.Item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="phase != null and phase != ''">
            AND un.Phase = #{phase,jdbcType=VARCHAR}
        </if>
        <if test="region != null and region != ''">
            AND un.Region = #{region,jdbcType=VARCHAR}
        </if>
        <if test="siteId != null and siteId != ''">
            AND un.Site_ID = #{siteId,jdbcType=VARCHAR}
        </if>
    </select>

    <update id="deleteData">
        update
        <choose>
            <when test="type == 'y1'">
                memm_e648652640b44b2092c93e1742e6171b
            </when>
            <when test="type == 'y2'">
                memm_f37920ed96f942fb8f4b1bf16f79e39c
            </when>
            <when test="type == 'y3'">
                memm_e45cb01fc742457a85ed8243aff1aa28
            </when>
            <when test="type == 'y4'">
                memm_157ac31323c34d46920918117cb577ad
            </when>
            <when test="type == 'y5'">
                memm_abdf4191a91e436a9b7e04351042f757
            </when>
            <when test="type == 'y6'">
                memm_5c8c376451894fdfb7e751c91da66f16
            </when>
            <when test="type == 'y7'">
                memm_218a6ab9959842099fd074c2b0ef685b
            </when>
            <when test="type == 'y8'">
                memm_f562b5dbd2be42d99c4992dd2668ed74
            </when>
            <when test="type == 'y9'">
                memm_4bf72c9a610c4b05a007f0f215b424a6
            </when>
        </choose>
        obj
        set obj.is_deleted = UNIX_TIMESTAMP()
        where
        JSON_UNQUOTE(obj.uniqueness_field -> '$[0]') in
        (
        SELECT
        id
        FROM
        memm_562ace74337e462289972ce20939e9a7 un
        WHERE
        un.Project_code = #{projectCode,jdbcType=VARCHAR} and un.is_deleted = 0
        <if test="unId != null and unId != ''">
            AND un.uniqueness_field = #{unId,jdbcType=VARCHAR}
        </if>
        <if test="itemCode != null and itemCode != ''">
            AND un.Item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="phase != null and phase != ''">
            AND un.Phase = #{phase,jdbcType=VARCHAR}
        </if>
        <if test="region != null and region != ''">
            AND un.Region = #{region,jdbcType=VARCHAR}
        </if>
        <if test="siteId != null and siteId != ''">
            AND un.Site_ID = #{siteId,jdbcType=VARCHAR}
        </if>
        )
    </update>

    <delete id="deleteUniqueness">
        update memm_562ace74337e462289972ce20939e9a7 un
        set un.is_deleted = UNIX_TIMESTAMP()
        where un.Project_code = #{projectCode,jdbcType=VARCHAR}
        and un.is_deleted = 0
        <if test="unId != null and unId != ''">
            AND un.uniqueness_field = #{unId,jdbcType=VARCHAR}
        </if>
        <if test="itemCode != null and itemCode != ''">
            AND un.Item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="phase != null and phase != ''">
            AND un.Phase = #{phase,jdbcType=VARCHAR}
        </if>
        <if test="region != null and region != ''">
            AND un.Region = #{region,jdbcType=VARCHAR}
        </if>
        <if test="siteId != null and siteId != ''">
            AND un.Site_ID = #{siteId,jdbcType=VARCHAR}
        </if>
    </delete>

    <select id="getDataByProjectCode" resultType="java.lang.Integer">
        select 1
        from
        <choose>
            <when test="type == 'y1'">
                memm_e648652640b44b2092c93e1742e6171b
            </when>
            <when test="type == 'y2'">
                memm_f37920ed96f942fb8f4b1bf16f79e39c
            </when>
            <when test="type == 'y3'">
                memm_e45cb01fc742457a85ed8243aff1aa28
            </when>
            <when test="type == 'y4'">
                memm_157ac31323c34d46920918117cb577ad
            </when>
            <when test="type == 'y5'">
                memm_abdf4191a91e436a9b7e04351042f757
            </when>
            <when test="type == 'y6'">
                memm_5c8c376451894fdfb7e751c91da66f16
            </when>
            <when test="type == 'y7'">
                memm_218a6ab9959842099fd074c2b0ef685b
            </when>
            <when test="type == 'y8'">
                memm_f562b5dbd2be42d99c4992dd2668ed74
            </when>
            <when test="type == 'y9'">
                memm_4bf72c9a610c4b05a007f0f215b424a6
            </when>
        </choose>
        obj
        LEFT JOIN memm_562ace74337e462289972ce20939e9a7 un ON un.id = JSON_UNQUOTE( obj.uniqueness_field -> '$[0]' )
        AND un.is_deleted = 0
        WHERE
        un.Project_code = #{projectCode,jdbcType=VARCHAR} and obj.is_deleted = 0
        <if test="unId != null and unId != ''">
            AND un.uniqueness_field = #{unId,jdbcType=VARCHAR}
        </if>
        <if test="itemCode != null and itemCode != ''">
            AND un.Item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="phase != null and phase != ''">
            AND un.Phase = #{phase,jdbcType=VARCHAR}
        </if>
        <if test="region != null and region != ''">
            AND un.Region = #{region,jdbcType=VARCHAR}
        </if>
        <if test="siteId != null and siteId != ''">
            AND un.Site_ID = #{siteId,jdbcType=VARCHAR}
        </if>
        limit 1
    </select>

    <update id="deleteAllWarnInfo">
        update memm_70848da039e44392bc6e066b5963ba1d
        set is_deleted = UNIX_TIMESTAMP()
        where JSON_UNQUOTE(projectName -> '$[0]') = #{projectId} and is_deleted = 0
    </update>

    <update id="deleteIncomeExpenditure">
        update memm_c58db11c81d3403ab9e59ce72b815ade
        set is_deleted = UNIX_TIMESTAMP()
        where
        JSON_UNQUOTE(uniqueness_field -> '$[0]') in
        (
        select id
        from memm_562ace74337e462289972ce20939e9a7 un
        where un.Project_code = #{projectCode,jdbcType=VARCHAR} and un.is_deleted = 0
        <if test="unId != null and unId != ''">
            AND un.uniqueness_field = #{unId,jdbcType=VARCHAR}
        </if>
        <if test="itemCode != null and itemCode != ''">
            AND un.Item_code = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="phase != null and phase != ''">
            AND un.Phase = #{phase,jdbcType=VARCHAR}
        </if>
        <if test="region != null and region != ''">
            AND un.Region = #{region,jdbcType=VARCHAR}
        </if>
        <if test="siteId != null and siteId != ''">
            AND un.Site_ID = #{siteId,jdbcType=VARCHAR}
        </if>
        )
        and is_deleted = 0
    </update>

    <update id="deleteIncomeExpenditureByUniquenessId">
        update memm_c58db11c81d3403ab9e59ce72b815ade
        set
        <choose>
            <when test="type == 'y1'">
                Item_Value = null,Item_Value_date = null
            </when>
            <when test="type == 'y2'">
                PO_Value = null,PO_Value_date = null
            </when>
            <when test="type == 'y4'">
                Subcon_PO_amount = null,Subcon_PO_amount_d = null
            </when>
            <when test="type == 'y5'">
                Ready_settlement = null,Ready_settlement_d = null
            </when>
            <when test="type == 'y6'">
                Productivity_Amount = null,Productivity_AmountD = null
            </when>
            <when test="type == 'y7'">
                Subcon_settlement = null,Subcon_settlement_d = null
            </when>
            <when test="type == 'y8'">
                Subcon_payment = null,Subcon_payment_date = null
            </when>
            <when test="type == 'y9'">
                Invoice_amount = null,Invoice_amount_date = null
            </when>
        </choose>
        where
        JSON_UNQUOTE(uniqueness_field -> '$[0]') in
        <foreach collection="list" item="un" separator="," open="(" close=")">
            #{un}
        </foreach>
        and is_deleted = 0
    </update>

    <select id="findPOItem" resultType="java.util.Map">
        select * from memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
        where poItem.is_deleted = 0
        and poItem.Phase = #{phase,jdbcType=VARCHAR} and poItem.Item_code = #{itemCode,jdbcType=VARCHAR}
        and JSON_CONTAINS(poItem.PO, CONCAT('"',#{poDataId},'"'))
        and JSON_CONTAINS(poItem.site, CONCAT('"',#{siteDataId},'"'))
    </select>

    <select id="findSubconPOItem" resultType="java.util.Map">
        select * from memm_157ac31323c34d46920918117cb577ad item
        where item.is_deleted = 0
        and JSON_CONTAINS(item.Subcon_PO, CONCAT('"',#{subconPoDataId},'"'))
        and JSON_CONTAINS(item.Subcon, CONCAT('"',#{subconDataId},'"'))
        and JSON_CONTAINS(item.site, CONCAT('"',#{siteDataId},'"'))
        and item.Item_code = #{itemCode}
        and item.Phase = #{phase}
    </select>

    <select id="findUniquenessIdByY3Id" resultType="java.lang.Long">
        select JSON_UNQUOTE(item.uniqueness_field -> '$[0]')
        from memm_e45cb01fc742457a85ed8243aff1aa28 item
        where item.is_deleted = 0 and item.id = #{id}
    </select>

    <insert id="saveItemData">
        insert into ${table}
        (
        <foreach collection="map" index="key" item="value" separator=",">
            `${key}`
        </foreach>
        )
        values
        (
        <foreach collection="map" index="key" item="value" separator=",">
            #{value}
        </foreach>
        )
    </insert>

    <update id="updateItemData">
        update ${table}
        set
        <foreach collection="map" index="key" item="value" separator=",">
            `${key}` = #{value}
        </foreach>
        where id = #{map.id} and is_deleted = 0
    </update>

    <select id="findWarningThreshold" resultType="java.lang.Object">
        select warn.id
        from memm_7345607a202c4e0eb52ffef451faa3aa warn
        left join memm_YPTT_Project_Warning_Threshold_mdr_siv6q rel on warn.id = rel.right_data_id and rel.is_deleted = 0
        left join memm_72a2450126dd41708a07374eff08b982 project on project.id = rel.left_data_id and project.is_deleted = 0
        where project.id = #{projectId} and warn.is_deleted = 0
    </select>

    <insert id="saveRelProject">
        insert into memm_YPTT_Project_Warning_Threshold_mdr_siv6q
        (id, left_data_id, right_data_id, create_by, create_time, is_deleted,
        update_by, update_time)
        VALUES (#{id,jdbcType=BIGINT}, #{projectId}, #{dataId}, 1,
        now(), 0, 1, now())
    </insert>

    <insert id="saveRelSite">
        insert into memm_Site_Site_Item_mdr_uvykk
        (id, left_data_id, right_data_id, create_by, create_time, is_deleted,
        update_by, update_time)
        VALUES (#{id,jdbcType=BIGINT}, #{siteDataId}, #{dataId}, 1,
        now(), 0, 1, now())
    </insert>

    <insert id="saveRelPo">
        insert into memm_PO_PO_Item_mdr_l096d
        (id, left_data_id, right_data_id, create_by, create_time, is_deleted,
        update_by, update_time)
        VALUES (#{id,jdbcType=BIGINT}, #{poDataId}, #{dataId}, 1,
        now(), 0, 1, now())
    </insert>

    <insert id="saveRelSubconPoItemExCost">
        insert into memm_Subcon_PO_Item_SubconPoItemExCost_mdr_zmp2c
        (id, left_data_id, right_data_id, create_by, create_time, is_deleted,
        update_by, update_time)
        VALUES (#{id,jdbcType=BIGINT}, #{subconPoDataId}, #{dataId}, 1,
        now(), 0, 1, now())
    </insert>
    <insert id="addPermissPro">
        insert into memm_439131c30ad445e6810ba53e13fd9cfb
        (id, project, create_by, create_time, is_deleted,
        update_by, update_time)
        VALUES (#{id,jdbcType=BIGINT}, #{projectId}, 1,
        now(), 0, 1, now())
    </insert>

    <select id="exportY3" resultType="java.util.Map">
        select
        concat(sdi.id) id,
        sdi.Site_belong_to,
        sdi.Team_Leader_DT,
        sdi.engineer_DTA_SPV,
        sdi.PLO_PC_Others,
        DATE_FORMAT(sdi.Start_Working_date, '%Y-%m-%d') Start_Working_date,
        DATE_FORMAT(sdi.Completed_work_date, '%Y-%m-%d') Completed_work_date,
        DATE_FORMAT(sdi.air_CI_Report_submit, '%Y-%m-%d') air_CI_Report_submit,
        DATE_FORMAT(sdi.Site_manager_Report, '%Y-%m-%d') Site_manager_Report,
        DATE_FORMAT(sdi.E_ATP_Pass, '%Y-%m-%d') E_ATP_Pass,
        DATE_FORMAT(sdi.F_PAC_Pass, '%Y-%m-%d') F_PAC_Pass,
        DATE_FORMAT(sdi.G_FAC, '%Y-%m-%d') G_FAC,
        sdi.remark,
        DATE_FORMAT(sdi.settlement_1st, '%Y-%m-%d') settlement_1st,
        DATE_FORMAT(sdi.settlement_2nd, '%Y-%m-%d') settlement_2nd,
        DATE_FORMAT(sdi.settlement_3rd, '%Y-%m-%d') settlement_3rd,
        DATE_FORMAT(sdi.settlement_4th, '%Y-%m-%d') settlement_4th,
        DATE_FORMAT(sdi.SubconSettlement_1st, '%Y-%m-%d') SubconSettlement_1st,
        DATE_FORMAT(sdi.SubconSettlement_2nd, '%Y-%m-%d') SubconSettlement_2nd,
        DATE_FORMAT(sdi.SubconSettlement_3rd, '%Y-%m-%d') SubconSettlement_3rd,
        DATE_FORMAT(sdi.SubconSettlement_4th, '%Y-%m-%d') SubconSettlement_4th,
        un.uniqueness_field as uniqueness_field,
        s.Subcon_name Subcon,
        sdi.PIC_PC_PM,
        sdi.BOQ_item,
        sdi.re_record,
        sdi.Project_code,
        sdi.Site_ID,
        sdi.Project_name,
        sdi.Site_Model,
        sdi.Item_code,
        sdi.Phase,
        sdi.site_name
        from memm_e45cb01fc742457a85ed8243aff1aa28 sdi
        left join memm_562ace74337e462289972ce20939e9a7 un on un.id = JSON_UNQUOTE(sdi.uniqueness_field -> '$[0]') and
        un.is_deleted = 0
        left join memm_134d9474dc244b26bfd7f013a0534710 s on JSON_UNQUOTE(sdi.Subcon -> '$[0]') = s.id and s.is_deleted
        = 0
        where
        sdi.is_deleted = 0 and sdi.Site_belong_to is not null
        AND (
        1694550407313264642 IN
        <foreach collection="roleList" open="(" close=")" separator="," item="roleId">
            #{roleId}
        </foreach>
        OR
        EXISTS (
        SELECT project.id
        FROM memm_72a2450126dd41708a07374eff08b982 project
        LEFT JOIN memm_439131c30ad445e6810ba53e13fd9cfb per on project.id = JSON_UNQUOTE(per.project->'$[0]') and
        per.is_deleted = 0
        WHERE project.is_deleted = 0
        AND JSON_CONTAINS(per.y3_query, JSON_ARRAY(#{userId}))
        AND project.YPTT_Project_code = sdi.Project_code
        )
        )
        <if test="map != null and map.size != 0 ">
            <foreach collection="map" index="key" item="value" separator="and" open="and" close="">
                <choose>
                    <when test="key == 'Subcon' || key == 'uniqueness_field'">
                        JSON_UNQUOTE(sdi.${key}->'$[0]') = #{value}
                    </when>
                    <otherwise>
                        sdi.${key} = #{value}
                    </otherwise>
                </choose>
            </foreach>
        </if>
    </select>

    <select id="findPoById" resultType="java.util.Map">
        select * from memm_ed87f18383f04a8f836cea32a1628fc9 where id = #{poId} and is_deleted = 0
    </select>

    <update id="incomeCorrection">
        <foreach collection="maps" separator=";" item="map">
            UPDATE memm_c58db11c81d3403ab9e59ce72b815ade income
            SET
            <foreach collection="map" index="key" item="value" separator=",">
                `${key}` = #{value}
            </foreach>
            WHERE
            income.is_deleted = 0 and income.id = #{map.id}
        </foreach>
    </update>

    <select id="incomeTotal" resultType="java.util.Map">
        SELECT
        income.id,
        yptt.Invoice_Amount_1st 'Invoice_amount',
        yptt.Invoice_Amount_2st 'Invoice_amount_2',
        yptt.Invoice_Amount_3st 'Invoice_amount_3',
        yptt.Invoice_Amount_4st 'Invoice_amount_4',
        yptt.Invoice_date_4st 'Invoice_amount_date4',
        yptt.Invoice_date_3st 'Invoice_amount_date3',
        yptt.Invoice_date_2st 'Invoice_amount_date2',
        yptt.Invoice_date_1st 'Invoice_amount_date',
        siteItem.Site_value 'Item_Value',
        siteItem.site_allocation_date 'Item_Value_date',
        poItem.PO_value 'PO_Value',
        po.PO_Received_date 'PO_Value_date',
        subItem.Subcon_PO_amount 'Subcon_PO_amount',
        subPo.release_date 'Subcon_PO_amount_d',
        ready.amount_1st 'Ready_settlement',
        ready.amount_2nd 'Ready_settlement_2',
        ready.amount_3rd 'Ready_settlement_3',
        ready.amount_4th 'Ready_settlement_4',
        ready.Pre_payment_amount 'Pre_payment_amount',
        ready.Pre_Settlement_date 'Pre_Settlement_date',
        ready.settlement_4th 'Ready_settlement_d4',
        ready.settlement_3rd 'Ready_settlement_d3',
        ready.settlement_2nd 'Ready_settlement_d2',
        ready.settlement_1st 'Ready_settlement_d',
        report.report_amount_1st 'Productivity_Amount',
        report.report_amount_2nd 'Productivity_Amount2',
        report.report_amount_3rd 'Productivity_Amount3',
        report.report_amount_4th 'Productivity_Amount4',
        report.report_date_4th 'ProductivityAmountD4',
        report.report_date_3rd 'ProductivityAmountD3',
        report.report_date_2nd 'Productivity_AmountD2',
        report.report_date_1st 'Productivity_AmountD',
        subSettle.settlementAmount_1st 'Subcon_settlement',
        subSettle.settlementAmount_2nd 'Subcon_settlement_2',
        subSettle.settlementAmount_3rd 'Subcon_settlement_3',
        subSettle.settlementAmount_4th 'Subcon_settlement_4',
        subSettle.settlement_time_4th 'Subcon_settlement_d4',
        subSettle.settlement_time_3rd 'Subcon_settlement_d3',
        subSettle.settlement_time_2nd 'Subcon_settlement_d2',
        subSettle.settlement_time_1st 'Subcon_settlement_d',
        subPay.payment_amount_1st 'Subcon_payment',
        subPay.payment_amount_2st 'Subcon_payment_2',
        subPay.payment_amount_3st 'Subcon_payment_3',
        subPay.payment_amount_4st 'Subcon_payment_4',
        subPay.Payment_time_4st 'Subcon_payment_date4',
        subPay.Payment_time_3st 'Subcon_payment_date3',
        subPay.Payment_time_2st 'Subcon_payment_date2',
        subPay.Payment_time_1st 'Subcon_payment_date'
        FROM
        memm_e648652640b44b2092c93e1742e6171b siteItem
        LEFT JOIN memm_c58db11c81d3403ab9e59ce72b815ade income ON JSON_UNQUOTE( income.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( siteItem.uniqueness_field -> '$[0]' )
        LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem ON JSON_UNQUOTE( siteItem.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( poItem.uniqueness_field -> '$[0]' )
        AND poItem.is_deleted = 0
        LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po ON po.id = JSON_UNQUOTE( poItem.PO -> '$[0]' )
        AND po.is_deleted = 0
        LEFT JOIN memm_157ac31323c34d46920918117cb577ad subItem ON JSON_UNQUOTE( poItem.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( subItem.uniqueness_field -> '$[0]' )
        AND subItem.is_deleted = 0
        LEFT JOIN memm_ff802d120a12430db18a68deb783b9c6 subPo ON subPo.id = JSON_UNQUOTE( subItem.Subcon_PO -> '$[0]' )
        AND subPo.is_deleted = 0
        LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 ready ON JSON_UNQUOTE( subItem.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( ready.uniqueness_field -> '$[0]' )
        AND ready.is_deleted = 0
        LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 report ON JSON_UNQUOTE( ready.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( report.uniqueness_field -> '$[0]' )
        AND report.is_deleted = 0
        LEFT JOIN memm_218a6ab9959842099fd074c2b0ef685b subSettle ON JSON_UNQUOTE( report.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( subSettle.uniqueness_field -> '$[0]' )
        AND subSettle.is_deleted = 0
        LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 subPay ON JSON_UNQUOTE( subSettle.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( subPay.uniqueness_field -> '$[0]' )
        AND subPay.is_deleted = 0
        LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 yptt ON JSON_UNQUOTE( subPay.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( yptt.uniqueness_field -> '$[0]' )
        AND yptt.is_deleted = 0
        WHERE
        income.is_deleted = 0
        and siteItem.Site_item_status != JSON_ARRAY('invalid')
        AND siteItem.is_deleted = 0
        ORDER BY
        income.id
        limit #{page},#{size}
    </select>

    <select id="selectYPTTSiteStateList" resultType="java.util.Map">
        SELECT
        siteItem.id,
        poItem.PO_value,
        siteItem.Site_value,
        ready.settlement_Amount,
        yptt.Invoice_amount
        FROM
        memm_e45cb01fc742457a85ed8243aff1aa28 sdi
        LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem ON JSON_UNQUOTE( sdi.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( poItem.uniqueness_field -> '$[0]' )
        AND poItem.is_deleted = 0
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON JSON_UNQUOTE( sdi.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( siteItem.uniqueness_field -> '$[0]' )
        AND siteItem.is_deleted = 0
        LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 ready ON JSON_UNQUOTE( ready.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( siteItem.uniqueness_field -> '$[0]' )
        AND ready.is_deleted = 0
        LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 yptt ON JSON_UNQUOTE( yptt.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( siteItem.uniqueness_field -> '$[0]' )
        AND yptt.is_deleted = 0
        WHERE
        sdi.is_deleted = 0
        AND sdi.Site_belong_to = 'YPTT'
        AND JSON_UNQUOTE(siteItem.Site_item_status->'$[0]') = 'unclose'
        ORDER BY
        siteItem.id
        LIMIT #{page},#{size}
    </select>

    <update id="updateSiteState">
        UPDATE memm_e648652640b44b2092c93e1742e6171b
        SET Site_item_status = JSON_ARRAY(CONCAT('close'))
        where
        id in
        <foreach collection="list" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </update>

    <select id="selectSiteStateList" resultType="java.util.Map">
        SELECT
        siteItem.id,
        poItem.PO_value,
        siteItem.Site_value,
        ready.settlement_Amount,
        yptt.Invoice_amount,
        spp.Totally_payment,
        spi.Subcon_PO_amount
        FROM
        memm_e45cb01fc742457a85ed8243aff1aa28 sdi
        LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c poItem ON JSON_UNQUOTE( sdi.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( poItem.uniqueness_field -> '$[0]' )
        AND poItem.is_deleted = 0
        LEFT JOIN memm_e648652640b44b2092c93e1742e6171b siteItem ON JSON_UNQUOTE( sdi.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( siteItem.uniqueness_field -> '$[0]' )
        AND siteItem.is_deleted = 0
        LEFT JOIN memm_157ac31323c34d46920918117cb577ad spi ON JSON_UNQUOTE( spi.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( siteItem.uniqueness_field -> '$[0]' )
        AND siteItem.is_deleted = 0
        LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 spp ON JSON_UNQUOTE( spp.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( spi.uniqueness_field -> '$[0]' )
        AND siteItem.is_deleted = 0
        LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 ready ON JSON_UNQUOTE( ready.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( siteItem.uniqueness_field -> '$[0]' )
        AND ready.is_deleted = 0
        LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 yptt ON JSON_UNQUOTE( yptt.uniqueness_field -> '$[0]' ) = JSON_UNQUOTE( siteItem.uniqueness_field -> '$[0]' )
        AND yptt.is_deleted = 0
        WHERE
        sdi.is_deleted = 0
        AND sdi.Site_belong_to != 'YPTT'
        AND JSON_UNQUOTE( siteItem.Site_item_status -> '$[0]' ) = 'unclose'
        ORDER BY
        siteItem.id
        LIMIT #{page},#{size}
    </select>

    <select id="getProjectIdList" resultType="java.lang.Long">
        select id
        from memm_72a2450126dd41708a07374eff08b982
        where is_deleted = 0
        and (id IN (
        SELECT
        p.id
        FROM memm_72a2450126dd41708a07374eff08b982 p
        LEFT JOIN memm_YPTT_Project_YPTT_Project_Member_mdr_39scc rel ON p.id = rel.left_data_id and rel.is_deleted = 0
        LEFT JOIN memm_7abc0f7fd9d84f67b4cd9b32575a6933 m ON rel.right_data_id = m.id and m.is_deleted = 0
        WHERE
        m.Project_Member = JSON_ARRAY(CONCAT( #{userId} )))
        OR 1694550407313264642 in
        <foreach collection="list" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        )
    </select>

    <select id="getProjectCodes" resultType="java.lang.String">
        select YPTT_Project_code
        from memm_72a2450126dd41708a07374eff08b982
        where is_deleted = 0
        order by id
    </select>
    <select id="totalY2" resultType="com.pig4cloud.pig.yptt.entity.vo.AmountY2VO">
        SELECT
        SUM(poItem.PO_value) AS totalPOValue
        FROM
        memm_f37920ed96f942fb8f4b1bf16f79e39c poItem
        INNER JOIN
        memm_562ace74337e462289972ce20939e9a7 uf
        ON poItem.uniqueness_field = JSON_ARRAY(CONCAT(uf.id))
        INNER JOIN
        memm_72a2450126dd41708a07374eff08b982 YPTTProject
        ON uf.Project_code = YPTTProject.YPTT_Project_code
        LEFT JOIN memm_ed87f18383f04a8f836cea32a1628fc9 po
        on JSON_ARRAY(CONCAT(po.id)) = poItem.PO and po.is_deleted = 0
        <if test="dateType != null and dateType != ''">
            <if test="dateType == 'Productivity_report_date'">
                LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
                on ProductivityReport.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                ProductivityReport.is_deleted = 0
            </if>
            <if test="dateType == 'KPI_Archive_date'">
                LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
                on ProductivityReport.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                ProductivityReport.is_deleted = 0
            </if>
            <if test="dateType == 'Subcon_Payment_Time'">
                LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
                on SubconPayment.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                SubconPayment.is_deleted = 0
            </if>
            <if test="dateType == 'Invoice_Date'">
                LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 YPTTSettlement
                on YPTTSettlement.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                YPTTSettlement.is_deleted = 0
            </if>
        </if>
        WHERE
        poItem.is_deleted = 0
        AND uf.is_deleted = 0
        AND YPTTProject.is_deleted = 0
        AND YPTTProject.id = #{projectId}
        <if test="dateType != null and dateType != ''">
            <if test="dateType == 'Productivity_report_date'">
                (ProductivityReport.report_date_1st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                ProductivityReport.report_date_2nd BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                ProductivityReport.report_date_3rd BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                ProductivityReport.report_date_4th BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                )
            </if>
            <if test="dateType == 'KPI_Archive_date'">
                ProductivityReport.KPI_Archive_date BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="dateType == 'Subcon_Payment_Time'">
                (SubconPayment.Payment_time_1st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                SubconPayment.Payment_time_2st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                SubconPayment.Payment_time_3st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                SubconPayment.Payment_time_4st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                )
            </if>
            <if test="dateType == 'Invoice_Date'">
                (YPTTSettlement.Invoice_date_1st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                YPTTSettlement.Invoice_date_2st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                YPTTSettlement.Invoice_date_3st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                YPTTSettlement.Invoice_date_4st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                )
            </if>
        </if>
    </select>
    <select id="totalY6" resultType="com.pig4cloud.pig.yptt.entity.vo.AmountY6VO">
        SELECT
        SUM(ProductivityReport.KPI_Archive_amount) AS totalKPIArchiveAmount,
        SUM(ProductivityReport.report_amount_1st) AS total1stProductivityReportAmount,
        SUM(ProductivityReport.report_amount_2nd) AS total2ndProductivityReportAmount,
        SUM(ProductivityReport.report_amount_3rd) AS total3rdProductivityReportAmount,
        SUM(ProductivityReport.report_amount_4th) AS total4thProductivityReportAmount,
        SUM(ProductivityReport.Productivity_Amount) AS totalProductivityAmount
        FROM
        memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
        INNER JOIN
        memm_e648652640b44b2092c93e1742e6171b siteItem
        ON ProductivityReport.uniqueness_field = siteItem.uniqueness_field
        INNER JOIN
        memm_562ace74337e462289972ce20939e9a7 uf
        ON JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field
        INNER JOIN
        memm_72a2450126dd41708a07374eff08b982 YPTTProject
        ON uf.Project_code = YPTTProject.YPTT_Project_code
        <if test="dateType != null and dateType != ''">
            <if test="dateType == 'Subcon_Payment_Time'">
                LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
                on SubconPayment.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                SubconPayment.is_deleted = 0
            </if>
            <if test="dateType == 'Invoice_Date'">
                LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 YPTTSettlement
                on YPTTSettlement.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                YPTTSettlement.is_deleted = 0
            </if>

        </if>
        WHERE
        ProductivityReport.is_deleted = 0
        AND siteItem.is_deleted = 0
        AND uf.is_deleted = 0
        AND YPTTProject.is_deleted = 0
        AND YPTTProject.id = #{projectId}
        <if test="dateType != null and dateType != ''">
            <if test="dateType == 'Productivity_report_date'">
                (ProductivityReport.report_date_1st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                ProductivityReport.report_date_2nd BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                ProductivityReport.report_date_3rd BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                ProductivityReport.report_date_4th BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                )
            </if>
            <if test="dateType == 'KPI_Archive_date'">
                ProductivityReport.KPI_Archive_date BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="dateType == 'Subcon_Payment_Time'">
                (SubconPayment.Payment_time_1st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                SubconPayment.Payment_time_2st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                SubconPayment.Payment_time_3st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                SubconPayment.Payment_time_4st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                )
            </if>
            <if test="dateType == 'Invoice_Date'">
                (YPTTSettlement.Invoice_date_1st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                YPTTSettlement.Invoice_date_2st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                YPTTSettlement.Invoice_date_3st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                YPTTSettlement.Invoice_date_4st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                )
            </if>
        </if>
    </select>
    <select id="totalY9" resultType="com.pig4cloud.pig.yptt.entity.vo.AmountY9VO">
        SELECT
        SUM(YPTTSettlement.Invoice_Amount_1st) AS totalInvoiceAmount1st,
        SUM(YPTTSettlement.Invoice_Amount_2st) AS totalInvoiceAmount2st,
        SUM(YPTTSettlement.Invoice_Amount_3st) AS totalInvoiceAmount3st,
        SUM(YPTTSettlement.Invoice_Amount_4st) AS totalInvoiceAmount4st,
        SUM(YPTTSettlement.Invoice_amount) AS totalInvoiceAmount,
        SUM(YPTTSettlement.Totally_CN_amount) AS totalCNAmount
        FROM
        memm_4bf72c9a610c4b05a007f0f215b424a6 YPTTSettlement
        INNER JOIN
        memm_e648652640b44b2092c93e1742e6171b siteItem
        ON YPTTSettlement.uniqueness_field = siteItem.uniqueness_field
        INNER JOIN
        memm_562ace74337e462289972ce20939e9a7 uf
        ON JSON_ARRAY(CONCAT(uf.id)) = siteItem.uniqueness_field
        INNER JOIN
        memm_72a2450126dd41708a07374eff08b982 YPTTProject
        ON uf.Project_code = YPTTProject.YPTT_Project_code
        <if test="dateType != null and dateType != ''">
            <if test="dateType == 'Productivity_report_date'">
                LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
                on ProductivityReport.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                ProductivityReport.is_deleted = 0
            </if>
            <if test="dateType == 'KPI_Archive_date'">
                LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 ProductivityReport
                on ProductivityReport.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                ProductivityReport.is_deleted = 0
            </if>
            <if test="dateType == 'Subcon_Payment_Time'">
                LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 SubconPayment
                on SubconPayment.uniqueness_field = JSON_ARRAY(CONCAT(uf.id)) and
                SubconPayment.is_deleted = 0
            </if>
        </if>
        where
        YPTTSettlement.is_deleted = 0
        AND siteItem.is_deleted = 0
        AND uf.is_deleted = 0
        AND YPTTProject.is_deleted = 0
        AND YPTTProject.id = #{projectId}
        <if test="dateType != null and dateType != ''">
            <if test="dateType == 'Productivity_report_date'">
                (ProductivityReport.report_date_1st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                ProductivityReport.report_date_2nd BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                ProductivityReport.report_date_3rd BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                ProductivityReport.report_date_4th BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                )
            </if>
            <if test="dateType == 'KPI_Archive_date'">
                ProductivityReport.KPI_Archive_date BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="dateType == 'Subcon_Payment_Time'">
                (SubconPayment.Payment_time_1st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                SubconPayment.Payment_time_2st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                SubconPayment.Payment_time_3st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                SubconPayment.Payment_time_4st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                )
            </if>
            <if test="dateType == 'Invoice_Date'">
                (YPTTSettlement.Invoice_date_1st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                YPTTSettlement.Invoice_date_2st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                YPTTSettlement.Invoice_date_3st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                or
                YPTTSettlement.Invoice_date_4st BETWEEN STR_TO_DATE(#{dateStrStart}, '%Y-%m-%d') AND
                STR_TO_DATE(CONCAT(#{dateStrEnd}, ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
                )
            </if>
        </if>

    </select>
    <select id="selectPermissByProjectId" resultType="java.lang.Integer">
        select count(*)
        from memm_439131c30ad445e6810ba53e13fd9cfb
        where is_deleted = 0
        and JSON_CONTAINS(project, JSON_ARRAY(CONCAT('', #{projectId}, '')))
    </select>
    <select id="exportY3V2" resultType="java.util.Map">
        select
        concat(sdi.id) id,
        sdi.Site_belong_to,
        sdi.Team_Leader_DT,
        sdi.engineer_DTA_SPV,
        sdi.PLO_PC_Others,
        DATE_FORMAT(sdi.Start_Working_date, '%Y-%m-%d') Start_Working_date,
        DATE_FORMAT(sdi.Completed_work_date, '%Y-%m-%d') Completed_work_date,
        DATE_FORMAT(sdi.air_CI_Report_submit, '%Y-%m-%d') air_CI_Report_submit,
        DATE_FORMAT(sdi.Site_manager_Report, '%Y-%m-%d') Site_manager_Report,
        DATE_FORMAT(sdi.E_ATP_Pass, '%Y-%m-%d') E_ATP_Pass,
        DATE_FORMAT(sdi.F_PAC_Pass, '%Y-%m-%d') F_PAC_Pass,
        DATE_FORMAT(sdi.G_FAC, '%Y-%m-%d') G_FAC,
        sdi.remark,
        DATE_FORMAT(sdi.settlement_1st, '%Y-%m-%d') settlement_1st,
        DATE_FORMAT(sdi.settlement_2nd, '%Y-%m-%d') settlement_2nd,
        DATE_FORMAT(sdi.settlement_3rd, '%Y-%m-%d') settlement_3rd,
        DATE_FORMAT(sdi.settlement_4th, '%Y-%m-%d') settlement_4th,
        DATE_FORMAT(sdi.SubconSettlement_1st, '%Y-%m-%d') SubconSettlement_1st,
        DATE_FORMAT(sdi.SubconSettlement_2nd, '%Y-%m-%d') SubconSettlement_2nd,
        DATE_FORMAT(sdi.SubconSettlement_3rd, '%Y-%m-%d') SubconSettlement_3rd,
        DATE_FORMAT(sdi.SubconSettlement_4th, '%Y-%m-%d') SubconSettlement_4th,
        un.uniqueness_field as uniqueness_field,
        s.Subcon_name Subcon,
        sdi.PIC_PC_PM,
        sdi.BOQ_item,
        sdi.re_record,
        sdi.Project_code,
        sdi.Site_ID,
        sdi.Project_name,
        sdi.Site_Model,
        sdi.Item_code,
        sdi.Phase,
        sdi.site_name
        from memm_e45cb01fc742457a85ed8243aff1aa28 sdi
        left join memm_562ace74337e462289972ce20939e9a7 un on un.id = JSON_UNQUOTE(sdi.uniqueness_field -> '$[0]') and
        un.is_deleted = 0
        left join memm_134d9474dc244b26bfd7f013a0534710 s on JSON_UNQUOTE(sdi.Subcon -> '$[0]') = s.id and s.is_deleted
        = 0
        where
        sdi.is_deleted = 0 and sdi.Site_belong_to is not null
        AND (
        1694550407313264642 IN
        <foreach collection="roleList" open="(" close=")" separator="," item="roleId">
            #{roleId}
        </foreach>
        OR
        EXISTS (
        SELECT project.id
        FROM memm_72a2450126dd41708a07374eff08b982 project
        LEFT JOIN memm_439131c30ad445e6810ba53e13fd9cfb per on project.id = JSON_UNQUOTE(per.project->'$[0]') and
        per.is_deleted = 0
        WHERE project.is_deleted = 0
        AND JSON_CONTAINS(per.y3_query, JSON_ARRAY(#{userId}))
        AND project.YPTT_Project_code = sdi.Project_code
        )
        )
        <if test="conditions != null and conditions.size != 0 ">
            <foreach collection="conditions" item="item" separator="and" open="and" close="">
                <choose>
                    <when test="item.name == 'Subcon' || item.name == 'uniqueness_field'">
                        JSON_UNQUOTE(sdi.${item.name}->'$[0]') = #{item.value}
                    </when>
                    <when test="DATE_FILED_LIST.contains(item.name)">
                        <choose>
                            <when test="item.symbol == 'range' and item.value instanceof java.util.List and !item.value.empty">
                                <![CDATA[
                            sdi.${item.name} >= STR_TO_DATE(#{item.value[0]}, '%Y-%m-%d')
                            AND sdi.${item.name} <= STR_TO_DATE(#{item.value[1]}, '%Y-%m-%d')
                            ]]>
                            </when>
                            <otherwise>
                                <![CDATA[
                            sdi.${item.name} ${item.symbol} STR_TO_DATE(#{item.value}, '%Y-%m-%d')
                            ]]>
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        sdi.${item.name} LIKE CONCAT('%', #{item.value}, '%')
                    </otherwise>
                </choose>
            </foreach>
        </if>
    </select>
</mapper>
