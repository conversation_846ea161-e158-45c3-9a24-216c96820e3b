<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.SubconPoStandingBookMapper">
    <select id="generateSubconPoStandingBookList"
            resultType="com.pig4cloud.pig.yptt.entity.dto.SubconPoStandingBookDTO">
        SELECT subconPo.id                                   AS subconPoId,
               SUM(IFNULL(subconPoItem.Subcon_PO_amount, 0)) AS totalPrice,
               COUNT(DISTINCT subconPoItem.id)                        AS itemQuantity
        FROM memm_ff802d120a12430db18a68deb783b9c6 AS subconPo
                 LEFT JOIN memm_157ac31323c34d46920918117cb577ad AS subconPoItem
                           ON subconPoItem.is_deleted = 0. AND
                              subconPo.id = JSON_UNQUOTE(subconPoItem.Subcon_PO -> '$[0]')
        WHERE subconPo.is_deleted = 0
        GROUP BY subconPo.id
        LIMIT #{cur}, #{size}
    </select>


    <update id="update">
        UPDATE
            memm_ff802d120a12430db18a68deb783b9c6
        SET Total_Price = #{dto.totalPrice,jdbcType=DECIMAL},
            Item_Quantity  = #{dto.itemQuantity,jdbcType=DECIMAL}
        WHERE id = #{dto.subconPoId,jdbcType=BIGINT}
    </update>
</mapper>
