<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.MoneyExchangeMapper">


    <select id="selectByParis" resultType="java.util.Map">
        SELECT
          *
        FROM
          memm_money_exchange_rate_1lv4ftwbtt402
          WHERE is_deleted = 0
          and out_money = JSON_ARRAY(CONCAT(#{moneyOutput}))
          and in_money = JSON_ARRAY(CONCAT(#{moneyInput}))
    </select>
</mapper>
