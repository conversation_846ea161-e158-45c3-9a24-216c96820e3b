<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pig.yptt.mapper.SubconStandingBookMapper">
    <select id="generateSubconStandingBookList" resultType="com.pig4cloud.pig.yptt.entity.dto.SubconStandingBookDTO">
        SELECT subcon.id                                          AS subcon_id,
               subcon.Subcon_name                                 AS subcon_name,
               SUM(IFNULL(subcon_payment.Totally_payment, 0))     AS total_payment,
               SUM(IFNULL(subcon_payment.Totally_payment_gap, 0)) AS total_payment_gap,
               COUNT(DISTINCT (subcon_po.id))                     AS subcon_po_count,
               COUNT(DISTINCT subcon_po_item.id)                           AS subcon_po_item_count
        FROM memm_134d9474dc244b26bfd7f013a0534710 AS subcon
                 LEFT JOIN memm_ff802d120a12430db18a68deb783b9c6 AS subcon_po
                           ON subcon_po.is_deleted = 0. AND subcon.id = JSON_UNQUOTE(subcon_po.Subcom -> '$[0]')
                 LEFT JOIN memm_157ac31323c34d46920918117cb577ad AS subcon_po_item
                           ON subcon_po_item.is_deleted = 0 AND
                              subcon.id = JSON_UNQUOTE(subcon_po_item.Subcon -> '$[0]')
                 LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 AS subcon_payment
                           ON subcon_payment.is_deleted = 0 AND
                              subcon_po_item.uniqueness_field = subcon_payment.uniqueness_field
        WHERE subcon.is_deleted = 0
        GROUP BY subcon.id
        LIMIT #{cur}, #{size}
    </select>


    <update id="update">
        UPDATE
            memm_134d9474dc244b26bfd7f013a0534710 AS subcon
        SET total_payment        = #{dto.totalPayment, jdbcType=DECIMAL},
            total_payment_gap    = #{dto.totalPaymentGap, jdbcType=DECIMAL},
            subcon_po_count      = #{dto.subconPOCount, jdbcType=INTEGER},
            subcon_po_item_count = #{dto.subconPOItemCount, jdbcType=INTEGER}
        WHERE id = #{dto.subconId, jdbcType=BIGINT}
    </update>

</mapper>
