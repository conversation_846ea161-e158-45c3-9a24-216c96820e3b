SELECT project.`id` AS project_id,
       project.`YPTT_Project_code` AS yptt_project_code,
       project.`YPTT_Project_name` AS yptt_project_name,
       project_member.`Project_role` AS project_role,
       project_member.`Project_Member` AS project_member,
       role_data.`model` AS perm_model,
       role_data.`query` AS perm_query,
       role_data.`update` AS perm_update,
       role_data.`del` AS perm_del,
       role_data.`add` AS perm_add
FROM memm_72a2450126dd41708a07374eff08b982 project
         LEFT JOIN memm_YPTT_Project_YPTT_Project_Member_mdr_39scc p2pm
                   ON p2pm.`is_deleted` = 0 AND p2pm.`left_data_id` = project.`id`
         LEFT JOIN memm_7abc0f7fd9d84f67b4cd9b32575a6933 project_member
                   ON project_member.`is_deleted` = 0 AND p2pm.`right_data_id` = project_member.id
         LEFT JOIN memm_1ebcbae9c9a648abb5df95540846ff1e role_data
                   ON role_data.`is_deleted` = 0 AND role_data.`role` = project_member.`Project_Role`
WHERE project.`is_deleted` = 0
  AND project.`id` = ?
  AND project_member.`Project_Member` = JSON_ARRAY(CONCAT(?))