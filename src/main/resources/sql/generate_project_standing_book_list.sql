SELECT project.id                                            AS ProjectId,
       project.YPTT_Project_name                             AS ProjectName,
       COUNT(site_item.id)                                   AS CountOfSiteItem,
       COUNT(po_item.id)                                     AS CountOfPoItem,
       SUM(IFNULL(site_item.Site_value, 0))                  AS TotalSiteValue,
       SUM(IFNULL(po_item.PO_value, 0))                      AS TotalPoValue,
       COUNT(subcon_po_item.id)                              AS CountOfSubconPoItem,
       SUM(IFNULL(subcon_po_item.Subcon_PO_amount, 0))       AS SubconPoAmount,
       SUM(IFNULL(subcon_po_item.additional_cost, 0))        AS SubconPoAddiCost,
       SUM(IFNULL(ready_for_settle.settlement_Amount, 0))    AS ReadyForSettleAmount,
       SUM(IFNULL(ready_for_settle.settlement_amountGap, 0)) AS NotReadySettleAmount,
       SUM(IFNULL(prod_report.Productivity_Amount, 0))       AS ReporttedProdAmount,
       SUM(IFNULL(subcon_settle.Totally_Amount, 0))          AS SubconSettleAmount,
       SUM(IFNULL(subcon_settle.Totally_amount_Gap, 0))      AS SubconSettleGap,
       SUM(IFNULL(subcon_payment.Totally_payment, 0))        AS SubconPayAmount,
       SUM(IFNULL(subcon_payment.Totally_payment_Gap, 0))    AS SubconPayAmountGap,
       SUM(IFNULL(yptt_settlement.Invoice_amount, 0))        AS InvoiceAmount,
       SUM(IFNULL(yptt_settlement.Invoice_amount_gap, 0))    AS InvoiceAmountGap


FROM memm_72a2450126dd41708a07374eff08b982 AS project
         LEFT JOIN memm_e648652640b44b2092c93e1742e6171b AS site_item
                   ON site_item.TPTT_Project = JSON_ARRAY(CONCAT(project.id))
         LEFT JOIN memm_f37920ed96f942fb8f4b1bf16f79e39c AS po_item
                   ON po_item.is_deleted = 0 AND po_item.uniqueness_field = site_item.uniqueness_field
         LEFT JOIN memm_e45cb01fc742457a85ed8243aff1aa28 As site_item_deli
                   ON site_item_deli.is_deleted = 0 AND site_item_deli.uniqueness_field = site_item.uniqueness_field
         LEFT JOIN memm_157ac31323c34d46920918117cb577ad AS subcon_po_item
                   ON subcon_po_item.is_deleted = 0 AND subcon_po_item.uniqueness_field = site_item.uniqueness_field
         LEFT JOIN memm_abdf4191a91e436a9b7e04351042f757 AS ready_for_settle
                   ON ready_for_settle.is_deleted = 0 AND ready_for_settle.uniqueness_field = site_item.uniqueness_field
         LEFT JOIN memm_5c8c376451894fdfb7e751c91da66f16 AS prod_report
                   ON prod_report.is_deleted = 0 AND prod_report.uniqueness_field = site_item.uniqueness_field
         LEFT JOIN memm_f562b5dbd2be42d99c4992dd2668ed74 AS subcon_payment
                   ON subcon_payment.is_deleted = 0 AND subcon_payment.uniqueness_field = site_item.uniqueness_field
         LEFT JOIN memm_218a6ab9959842099fd074c2b0ef685b AS subcon_settle
                   ON subcon_settle.is_deleted = 0 AND subcon_settle.uniqueness_field = site_item.uniqueness_field
         LEFT JOIN memm_4bf72c9a610c4b05a007f0f215b424a6 AS yptt_settlement
                   ON yptt_settlement.is_deleted = 0 AND yptt_settlement.uniqueness_field = site_item.uniqueness_field
WHERE project.is_deleted = 0
GROUP BY project.id
LIMIT ?, ?