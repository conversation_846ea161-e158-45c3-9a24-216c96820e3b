package com.pig4cloud.pig.yptt;

import com.pig4cloud.pig.common.core.constant.enums.CommonBizCode;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.yptt.entity.dto.CustomerProjectStandingBookDTO;
import com.pig4cloud.pig.yptt.service.standingbook.CustomerProjectStandingBookUpdater;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/13
 */
@Slf4j
@SpringBootTest
public class CustomerProjectStandingBookUpdaterTests {

	@Autowired
	private CustomerProjectStandingBookUpdater customerProjectStandingBookUpdater;

	@Test
	void testQueryRoleData() {
		List<R<CustomerProjectStandingBookDTO>> generatedList = customerProjectStandingBookUpdater.update();
		for (R<CustomerProjectStandingBookDTO> generatedResult : generatedList) {
			Assertions.assertEquals(generatedResult.getCode(), CommonBizCode.SUCCESS.name(),
					"should success, but actual got " + CommonBizCode.FAIL);
			log.info("code: {}, dto: {}", generatedResult.getCode(), generatedResult.getData());
		}
	}

}
