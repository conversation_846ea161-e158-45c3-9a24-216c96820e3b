package com.pig4cloud.pig.yptt;

import com.alibaba.fastjson.JSON;
import com.pig4cloud.pig.yptt.entity.dto.ProjectRolePermissionMapDTO;
import com.pig4cloud.pig.yptt.service.DataPermissionsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/13
 */
@SpringBootTest
public class DataPermissionsServiceTests {

	@Autowired
	private DataPermissionsService dataPermissionsService;

	@Test
	void testQueryRoleData() {
		List<ProjectRolePermissionMapDTO> roleDataPermMap = dataPermissionsService
			.queryProjectRolePermMapList("邱凯测试项目004", 1719607453526986753L);
		System.out.println(JSON.toJSONString(roleDataPermMap, true));
	}

}
