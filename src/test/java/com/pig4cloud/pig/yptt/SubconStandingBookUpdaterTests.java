package com.pig4cloud.pig.yptt;

import com.pig4cloud.pig.common.core.constant.enums.CommonBizCode;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.yptt.entity.dto.SubconStandingBookDTO;
import com.pig4cloud.pig.yptt.service.standingbook.SubconStandingBookUpdater;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/13
 */
@Slf4j
@SpringBootTest
public class SubconStandingBookUpdaterTests {

	@Autowired
	private SubconStandingBookUpdater subconStandingBookUpdater;

	@Test
	void testQueryRoleData() {
		List<R<SubconStandingBookDTO>> generatedList = subconStandingBookUpdater.update();
		for (R<SubconStandingBookDTO> generatedResult : generatedList) {
			Assertions.assertEquals(generatedResult.getCode(), CommonBizCode.SUCCESS.name(),
					"should success, but actual got " + CommonBizCode.FAIL);
			log.info("code: {}, dto: {}", generatedResult.getCode(), generatedResult.getData());
		}
	}

}
