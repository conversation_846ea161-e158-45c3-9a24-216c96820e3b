package com.pig4cloud.pig.yptt;

import com.pig4cloud.pig.common.core.constant.enums.CommonBizCode;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.yptt.entity.dto.StandingBook;
import com.pig4cloud.pig.yptt.entity.dto.StandingBookUpdateTaskResultDTO;
import com.pig4cloud.pig.yptt.service.standingbook.StandingBookUpdateTaskService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/13
 */
@Slf4j
@SpringBootTest
public class StandingBookUpdateTaskServiceTests {

	@Autowired
	private StandingBookUpdateTaskService standingBookUpdateTaskService;

	@Test
	void testQueryRoleData() {
		StandingBookUpdateTaskResultDTO updateResult = standingBookUpdateTaskService.update();
		for (Map.Entry<String, R<List<R<StandingBook>>>> entry : updateResult.entrySet()) {
			String name = entry.getKey();
			R<List<R<StandingBook>>> value = entry.getValue();
			Assertions.assertEquals(value.getCode(), CommonBizCode.SUCCESS.name(),
					name + " should success, but actual got " + value.getData());

		}

	}

}
