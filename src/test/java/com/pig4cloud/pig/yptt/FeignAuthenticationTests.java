package com.pig4cloud.pig.yptt;

import com.pig4cloud.pig.admin.api.entity.SysRole;
import com.pig4cloud.pig.common.core.constant.SecurityConstants;
import com.pig4cloud.pig.common.security.service.PigUser;
import com.pig4cloud.pig.common.security.util.ProxyAuthenticateContext;
import com.pig4cloud.pig.common.security.util.ProxyAuthenticateContextHolder;
import com.pig4cloud.pig.me.api.feign.RemoteAppService;
import com.pig4cloud.pig.me.api.vo.ViewGroupConfVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/01/03
 */
@SpringBootTest
public class FeignAuthenticationTests {

	@Autowired
	private RemoteAppService remoteAppService;

	@Test
	void test() {
		SysRole role = new SysRole();
		role.setRoleId(1694550407313264642L);
		role.setTenantId(null);
		role.setRoleCode("SYSTEM_ADMIN");
		role.setRoleName("系统管理员");
		role.setRoleDesc("系统管理员");
		role.setIsDeleted(0L);

		PigUser user = new PigUser(1597490624222318594L, 1725347324349890561L, Collections.singletonList(role),
				Collections.emptyList(), 1694550407300681729L, "N/A", "18223799766", true, true, true, true,
				Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + role.getRoleId())));
		ProxyAuthenticateContextHolder.set(new ProxyAuthenticateContext(user));

		Long viewGroupId = 1L;
		Long viewId = 1L;

		ViewGroupConfVO viewGroupConfVO = remoteAppService.getViewGroupConfVO(viewGroupId, viewId,
				SecurityConstants.FROM_IN);

		System.out.println(viewGroupConfVO);
	}

}
