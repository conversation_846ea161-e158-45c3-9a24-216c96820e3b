package com.pig4cloud.pig.yptt;

import com.pig4cloud.pig.yptt.service.RefreshPerRetryService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2024/01/25
 */
@SpringBootTest
public class RefreshPerRetryServiceTest {

	@Autowired
	private RefreshPerRetryService refreshPerRetryService;

	@Test
	void testRefreshPerRetry() {
		refreshPerRetryService.refreshPer(1749673659752165376L);
	}

}
