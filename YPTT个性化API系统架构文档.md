# YPTT个性化API系统架构文档

## 1. 项目概述

### 1.1 项目基本信息
- **项目名称**: yptt-personalized-api (海外项目自定义开发模块)
- **技术栈**: Java 8 + Spring Boot 2.7.10 + Spring Cloud 2021.0.6 + Spring Cloud Alibaba 2021.0.5.0
- **数据库**: MySQL 8.0.31
- **ORM框架**: MyBatis Plus 3.5.3.1
- **服务端口**: 20001
- **注册中心**: Nacos
- **配置中心**: Nacos

### 1.2 核心依赖
| 依赖 | 版本 | 说明 |
|------|------|------|
| Spring Boot | 2.7.10 | 基础框架 |
| Spring Cloud | 2021.0.6 | 微服务框架 |
| Spring Cloud Alibaba | 2021.0.5.0 | 阿里云微服务组件 |
| MyBatis Plus | 3.5.3.1 | ORM框架 |
| EasyExcel | 3.0.5 | Excel处理 |
| Hutool | 5.8.15 | 工具类库 |

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    YPTT个性化API系统                          │
├─────────────────────────────────────────────────────────────┤
│  Controller层 (控制器层)                                      │
│  ├── 数据管理控制器 (DataMangeController)                     │
│  ├── BI面板控制器 (BiPanelController)                        │
│  ├── 报表控制器 (ReportController)                           │
│  ├── 警告信息控制器 (WarningInfoController)                   │
│  ├── 工作流控制器 (WorkFlow相关)                              │
│  ├── 财务支付控制器 (PaymentApplicationController)            │
│  └── 其他业务控制器                                           │
├─────────────────────────────────────────────────────────────┤
│  Service层 (业务逻辑层)                                       │
│  ├── 数据管理服务 (DataMangeService)                         │
│  ├── BI面板服务 (BiPanelService)                            │
│  ├── 报表服务 (ReportService)                               │
│  ├── 警告信息服务 (WarningInfoService)                       │
│  ├── 工作流服务 (WorkFlow相关)                               │
│  ├── 数据转换服务 (Transform相关)                            │
│  └── 台账更新服务 (StandingBook相关)                         │
├─────────────────────────────────────────────────────────────┤
│  Mapper层 (数据访问层)                                        │
│  ├── BasicMapper (基础数据操作)                              │
│  ├── ReportMapper (报表数据)                                │
│  ├── WarningMapper (警告数据)                               │
│  └── 其他业务Mapper                                          │
├─────────────────────────────────────────────────────────────┤
│  Entity层 (实体层)                                           │
│  ├── 基础实体 (BaseEntity)                                  │
│  ├── 业务实体 (PaymentApplication, SiteItem等)              │
│  ├── DTO (数据传输对象)                                      │
│  └── VO (视图对象)                                           │
├─────────────────────────────────────────────────────────────┤
│  Config层 (配置层)                                           │
│  ├── 视图配置 (ViewConfProperties)                          │
│  ├── 线程池配置 (AsyncConfig)                               │
│  └── 其他配置                                               │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块划分

#### 2.2.1 核心业务模块
- **Y1模块**: 站点条目管理 (SiteItem)
- **Y2模块**: 采购订单条目管理 (POItem)  
- **Y3模块**: 生产力报告管理 (ProductivityReport)
- **Y4模块**: 分包商PO条目管理 (SubconPOItem)
- **Y6模块**: 决算管理
- **Y8模块**: 分包商支付管理 (SubconPayment)
- **Y9模块**: 开票管理 (Invoice)

#### 2.2.2 支撑功能模块
- **数据导入导出模块**: Excel数据批量导入导出
- **BI面板模块**: 数据统计分析展示
- **警告信息模块**: 业务预警提醒
- **工作流模块**: 审批流程管理
- **财务支付模块**: 付款申请管理
- **权限管理模块**: 数据权限控制

## 3. 核心接口详情

### 3.1 数据管理接口 (DataMangeController)

#### 3.1.1 数据导入相关
```http
POST /data-mange/import/upload-data-table
功能: 上传数据表进行导入
参数: YPTTBatchImportDTO
返回: R<Object>

POST /data-mange/import/check-upload-data  
功能: 检查上传的数据
参数: YPTTBatchImportDTO
返回: R<Object>

GET /data-mange/import/download-template
功能: 下载导入模板
参数: moduleType (模块类型)
返回: ResponseEntity<byte[]>
```

#### 3.1.2 Y3模块数据导入
```http
POST /data-mange/import/update-y3
功能: Y3模块数据导入
参数: MultipartFile file, String key
返回: R<List<ImportResultVO>>

GET /data-mange/import/query-progress-y3
功能: 查询Y3导入进度
参数: String key
返回: R<ProgressY3VO>
```

#### 3.1.3 分包商外部成本管理
```http
POST /data-mange/subcon/external-cost/add
功能: 新增分包商外部成本
参数: OperationInsertDTO
返回: ApiRes

POST /data-mange/subcon/external-cost/update
功能: 更新分包商外部成本
参数: OperationUpdateDTO  
返回: ApiRes
```

### 3.2 BI面板接口 (BiPanelController)

#### 3.2.1 收支状态统计
```http
GET /Bi-panel/income-expenditure-status
功能: 获取收支状态统计数据
参数: 
- startTime: 开始时间
- endTime: 结束时间  
- projectId: 项目ID
- type: 类型
- projectCycle: 项目周期
返回: R<List<IncomeAndExpenditureVO>>
```

#### 3.2.2 站点统计
```http
GET /Bi-panel/site-item-statistics
功能: 站点条目统计
参数:
- appId: 应用ID
- projectId: 项目ID
返回: R<List<SiteItemStatisticsVO>>
```

#### 3.2.3 金额统计
```http
GET /Bi-panel/total-amount
功能: 统计总金额(产值、决算、开票、PO金额)
参数:
- projectId: 项目ID
- dateStrStart: 开始日期
- dateStrEnd: 结束日期
- dateType: 日期类型
返回: R<TotalAmountVO>
```

#### 3.2.4 数据导出
```http
GET /Bi-panel/export-data
功能: 导出BI面板数据
参数: 查询条件参数
返回: void (文件下载)
```

### 3.3 报表接口 (ReportController)

#### 3.3.1 用户操作统计
```http
POST /report/statistics/user-operate
功能: 统计用户Y1-Y9模块操作数据
参数: StatisticsUserOperateDTO
返回: R<StatisticsUserOperateVO>
```

#### 3.3.2 数据导出
```http
GET /report/export/user-operate
功能: 导出用户操作统计数据
参数: StatisticsUserOperateDTO
返回: void (Excel文件下载)
```

#### 3.3.3 项目查询
```http
GET /report/get/project
功能: 获取项目数据
参数:
- name: 项目名称(可选)
- current: 当前页(默认1)
- size: 页大小(默认10)
返回: R<?>
```

### 3.4 警告信息接口 (WarningInfoController)

#### 3.4.1 警告状态更新
```http
GET /warning/update
功能: 更新警告信息状态
参数:
- listCode: 项目代码列表(可选)
- yptt-task-token: 任务令牌
返回: R<Boolean>
```

#### 3.4.2 警告信息分页查询
```http
GET /warning/page
功能: 分页查询警告信息
参数: WarningPageDTO
返回: R<IPage<WarningMessage>>
```

#### 3.4.3 警告统计
```http
GET /warning/statistics
功能: 警告信息统计
参数:
- startTime: 开始时间
- endTime: 结束时间
返回: R<WarningStatisticsVO>
```

#### 3.4.4 警告数据导出
```http
GET /warning/export
功能: 导出警告信息数据
参数:
- startTime: 开始时间
- endTime: 结束时间
返回: void (Excel文件下载)
```

### 3.5 金额调整接口 (AdjustController)

#### 3.5.1 站点条目金额调整
```http
POST /adjust/amount-siteItem
功能: 调整Y1站点条目金额
参数: 
- OperationUpdateDTO: 更新数据
- yptt-task-token: 任务令牌
返回: ApiRes
```

#### 3.5.2 PO条目金额调整
```http
POST /adjust/amount-poItem
功能: 调整Y2 PO条目金额
参数:
- OperationUpdateDTO: 更新数据
- yptt-task-token: 任务令牌
返回: ApiRes
```

#### 3.5.3 分包商PO金额调整
```http
POST /adjust/amount-subPoItem
功能: 调整Y4分包商PO条目金额
参数:
- OperationUpdateDTO: 更新数据
- yptt-task-token: 任务令牌
返回: ApiRes
```

### 3.6 Excel调整接口 (AdjustExcelController)

#### 3.6.1 Y1数据导入更新
```http
POST /adjust-excel/import/update-y1
功能: 通过Excel导入更新Y1数据
参数:
- file: Excel文件
- key: 任务标识
返回: R<List<ImportResultVO>>

GET /adjust-excel/import/query-progress-y1
功能: 查询Y1导入进度
参数: key: 任务标识
返回: R<ProgressY1VO>
```

#### 3.6.2 Y2数据导入更新
```http
POST /adjust-excel/import/update-y2
功能: 通过Excel导入更新Y2数据
参数:
- file: Excel文件
- key: 任务标识
返回: R<List<ImportResultVO>>

GET /adjust-excel/import/query-progress-y2
功能: 查询Y2导入进度
参数: key: 任务标识
返回: R<ProgressY2VO>
```

#### 3.6.3 Y4数据导入更新
```http
POST /adjust-excel/import/update-y4
功能: 通过Excel导入更新Y4数据
参数:
- file: Excel文件
- key: 任务标识
返回: R<List<ImportResultVO>>

GET /adjust-excel/import/query-progress-y4
功能: 查询Y4导入进度
参数: key: 任务标识
返回: R<ProgressY4VO>
```

#### 3.6.4 模板下载
```http
GET /adjust-excel/download-template-y1
功能: 下载Y1调整模板
返回: void (Excel文件下载)

GET /adjust-excel/download-template-y2
功能: 下载Y2调整模板
返回: void (Excel文件下载)

GET /adjust-excel/download-template-y4
功能: 下载Y4调整模板
返回: void (Excel文件下载)
```

### 3.7 工作流接口

#### 3.7.1 工作流提交拦截 (WorkFlowSubmitInterceptController)
```http
POST /workflow/fs/submit-intercept/other-reimbursement
功能: 综合报销申请流程提交拦截
参数: WorkFlowDTO
返回: WorkFlowApiRes

POST /workflow/fs/submit-intercept/sub-con-and-material-Reimbursement
功能: 劳务/材料报销申请提交拦截
参数: WorkFlowDTO
返回: WorkFlowApiRes

POST /workflow/fs/submit-intercept/travel-Reimbursement
功能: 差旅报销申请提交拦截
参数: WorkFlowDTO
返回: WorkFlowApiRes

POST /workflow/fs/submit-intercept/financial-payments
功能: 财务付款申请提交拦截
参数: WorkFlowDTO
返回: WorkFlowApiRes
```

#### 3.7.2 工作流结束拦截 (WorkFlowEndInterceptController)
```http
POST /workflow/end-intercept/other-reimbursement
功能: 综合报销流程结束拦截
参数: WorkFlowDTO
返回: WorkFlowApiRes

POST /workflow/end-intercept/sub-con-and-material-reimbursement
功能: 劳务/材料报销流程结束拦截
参数: WorkFlowDTO
返回: WorkFlowApiRes

POST /workflow/end-intercept/advance-payments-and-personal-loans
功能: 预借付款申请流程结束拦截
参数: WorkFlowDTO
返回: WorkFlowApiRes

POST /workflow/end-intercept/financial-payments
功能: 财务付款申请流程结束拦截
参数: WorkFlowDTO
返回: WorkFlowApiRes
```

#### 3.7.3 用户入职工作流 (WorkFlowUserEntryController)
```http
POST /workflow-user-entry/add
功能: 新增用户入职流程
参数: WorkFlowDTO
返回: WorkFlowApiRes
```

### 3.8 财务支付接口 (PaymentApplicationController)

#### 3.8.1 付款状态管理
```http
POST /fs/payment-application/payment-end-by-after-add
功能: 新增后完成付款并修改状态
参数:
- BatchOperationParams.Payload: 操作参数
- yptt-task-token: 任务令牌
返回: ApiRes

POST /fs/payment-application/payment-end-by-after-del
功能: 删除后完成付款并修改状态
参数:
- BatchOperationParams.Payload: 操作参数
- yptt-task-token: 任务令牌
返回: ApiRes

POST /fs/payment-application/payment-end-by-after-update
功能: 更新后完成付款并修改状态
参数:
- BatchOperationParams.Payload: 操作参数
- yptt-task-token: 任务令牌
返回: ApiRes
```

### 3.9 任务管理接口 (TaskController)

#### 3.9.1 台账更新任务
```http
GET /task/update-project-standing-book
GET /task/update-standing-books
功能: 更新项目台账
参数: yptt-task-token: 任务令牌
返回: R<StandingBookUpdateTaskResultDTO>
```

#### 3.9.2 导入任务检查
```http
GET /task/check-import-task
功能: 检查导入任务状态
参数: yptt-task-token: 任务令牌
返回: void
```

#### 3.9.3 收入修正任务
```http
GET /task/income-correction-task
功能: 执行收入修正任务
参数: yptt-task-token: 任务令牌
返回: R<Boolean>
```

#### 3.9.4 站点状态更新任务
```http
GET /task/update-site-state
功能: 更新站点状态
参数: yptt-task-token: 任务令牌
返回: R<Boolean>
```

### 3.10 数据连接器接口 (Connector2codeController)

#### 3.10.1 Y1数据连接器
```http
POST /connector2code/y1
功能: Y1模块数据连接器(异步处理)
参数:
- OperationUpdateDTO: 操作数据
- yptt-task-token: 任务令牌
返回: ApiRes
```

#### 3.10.2 Y2数据连接器
```http
POST /connector2code/y2
功能: Y2模块数据连接器(异步处理)
参数:
- OperationUpdateDTO: 操作数据
- yptt-task-token: 任务令牌
返回: ApiRes
```

#### 3.10.3 Y3数据连接器
```http
POST /connector2code/y3
功能: Y3模块数据连接器
参数:
- OperationUpdateDTO: 操作数据
- yptt-task-token: 任务令牌
返回: ApiRes
```

#### 3.10.4 Y4数据连接器
```http
POST /connector2code/y4
功能: Y4模块数据连接器(异步处理)
参数:
- OperationUpdateDTO: 操作数据
- yptt-task-token: 任务令牌
返回: ApiRes
```

### 3.11 货币兑换接口 (MoneyExchangeController)

#### 3.11.1 货币转换
```http
GET /money-exchange/transfer
功能: 货币金额转换
参数:
- moneyInput: 输入货币类型
- moneyOutput: 输出货币类型
- Amount: 转换金额
返回: ApiRes
```

### 3.12 数据锁定接口 (LockDataTimeController)

#### 3.12.1 数据锁定管理
```http
POST /lock-data-time/lock
功能: 锁定数据
参数: LockDataDTO
返回: ApiRes

POST /lock-data-time/unlock
功能: 解锁数据
参数: LockDataDTO
返回: ApiRes

GET /lock-data-time/list
功能: 获取锁定数据列表
参数:
- page: 页码(默认1)
- size: 页大小(默认10)
返回: R<Page<LockDataVO>>
```

#### 3.12.2 操作时间段管理
```http
POST /lock-data-time/abilityOperateDate
功能: 添加能够操作的时间段
参数: AbilityOperateDateDTO
返回: ApiRes

GET /lock-data-time/listV2
功能: 获取锁定时间列表V2
参数:
- page: 页码(默认1)
- size: 页大小(默认10)
- projectCode: 项目代码(可选)
返回: R<Page<LockDataTimeVo>>
```

### 3.13 PR管理接口 (PRMangeController)

#### 3.13.1 PR查询
```http
GET /pr-mange/search-detail
功能: PR查询条目下载详情
参数:
- ufId: 唯一标识ID
- page: 页码(默认1)
- size: 页大小(默认10)
返回: R<Page<Map<String, Object>>>

GET /pr-mange/search-total
功能: PR查询汇总
参数:
- projectCode: 项目代码(可选)
- userId: 用户ID(可选)
- page: 页码(默认1)
- size: 页大小(默认10)
返回: R<Page<Map<String, Object>>>
```

### 3.14 测试接口 (TestController)

#### 3.14.1 测试数据导出
```http
GET /test/data-export
功能: 导出测试数据
参数:
- subconPoNum: 分包商PO数量
- poNum: PO数量
- projectCode: 项目代码
- cusContract: 客户合同
返回: void (Excel文件下载)
```

## 4. 核心业务逻辑

### 4.1 数据导入流程

#### 4.1.1 导入流程概述
1. **文件上传**: 用户上传Excel文件
2. **数据校验**: 校验文件格式和数据完整性
3. **异步处理**: 使用线程池异步处理导入任务
4. **数据转换**: 根据模块类型进行数据转换
5. **批量保存**: 分批次保存到数据库
6. **状态更新**: 更新导入状态和进度
7. **消息通知**: 发送导入完成通知

#### 4.1.2 支持的模块类型
- **Y1**: 站点条目 (SiteItem)
- **Y2**: PO条目 (POItem)
- **Y3**: 生产力报告 (ProductivityReport)
- **Y4**: 分包商PO条目 (SubconPOItem)
- **Y8**: 分包商支付 (SubconPayment)
- **Y9**: 开票 (Invoice)

#### 4.1.3 数据转换逻辑
每个模块都有对应的Transformer类处理数据转换:
- **Y1Transformer**: 处理站点条目数据转换
- **Y2Transformer**: 处理PO条目数据转换
- **Y3Transformer**: 处理生产力报告数据转换
- **Y4Transformer**: 处理分包商PO条目数据转换
- **Y8Transformer**: 处理分包商支付数据转换
- **Y9Transformer**: 处理开票数据转换

### 4.2 BI面板数据统计

#### 4.2.1 收支状态统计
- 统计指定时间范围内的收入和支出情况
- 支持按项目、时间类型进行筛选
- 提供年度、月度、日度统计维度

#### 4.2.2 站点统计
- 统计站点关闭率
- 按项目分组统计站点状态
- 提供已关闭、未关闭、无效站点数量统计

#### 4.2.3 金额统计
- 统计产值及决算金额(Y6)
- 统计开票金额(Y9)
- 统计PO金额(Y2)
- 支持按日期类型筛选统计

### 4.3 警告信息管理

#### 4.3.1 警告类型
- **站点条目警告**: 站点交付超期警告
- **PO警告**: 采购订单异常警告
- **分包商支付警告**: 支付超期警告
- **YPTT结算警告**: 结算异常警告

#### 4.3.2 警告更新机制
- 定时任务自动更新警告状态
- 支持手动触发警告状态更新
- 按项目代码批量更新警告信息

### 4.4 工作流管理

#### 4.4.1 支持的工作流类型
- **综合报销申请**: 其他类型报销流程
- **劳务/材料报销**: 分包商相关报销流程
- **差旅报销**: 差旅费用报销流程
- **财务付款申请**: 付款申请审批流程
- **预借付款申请**: 借款和预付款申请流程

#### 4.4.2 工作流拦截机制
- **提交拦截**: 在流程提交时进行业务校验
- **结束拦截**: 在流程结束时执行后续业务逻辑
- **数据同步**: 工作流数据与业务数据同步

### 4.5 台账更新机制

#### 4.5.1 台账类型
- **项目台账**: 项目级别的数据汇总
- **站点台账**: 站点级别的数据汇总
- **PO台账**: 采购订单级别的数据汇总
- **分包商台账**: 分包商级别的数据汇总
- **客户项目台账**: 客户项目级别的数据汇总

#### 4.5.2 更新策略
- 定时任务自动更新台账数据
- 支持手动触发台账更新
- 分批次处理大量数据更新
- 异步处理提高性能

## 5. 数据模型

### 5.1 核心实体类

#### 5.1.1 基础实体 (BaseEntity)
```java
public class BaseEntity {
    private Long id;                    // 主键ID
    private Long createBy;              // 创建人
    private LocalDateTime createTime;   // 创建时间
    private Long updateBy;              // 更新人
    private LocalDateTime updateTime;   // 更新时间
    private Long isDeleted;             // 删除标识
    private String currProcInstId;      // 当前流程实例ID
}
```

#### 5.1.2 付款申请实体 (PaymentApplication)
```java
public class PaymentApplication {
    private Long id;                    // 记录ID
    private String project;             // 一级项目名称
    private String projectCode;         // 一级项目编号
    private String code;                // 流程编号
    private String sqr;                 // 申请人
    private Date sqrq;                  // 申请日期
    private String sqbm;                // 申请部门
    private String sy;                  // 事由
    private String currency;            // 币种
    private String account;             // 账户信息
    private String bankNum;             // 银行账号
    private BigDecimal paymentMoney;    // 付款金额
    private String status;              // 状态
    // ... 其他字段
}
```

#### 5.1.3 警告信息实体 (WarningMessage)
```java
public class WarningMessage {
    private String warningType;         // 警告类型
    private Long id;                    // ID
    private String uniquenessField;     // 唯一标识字段
    private String warningMsg;          // 警告消息
    private Long warningDataId;         // 警告数据ID
    private Long viewId;                // 视图ID
    private Long viewGroupId;           // 视图组ID
    private Long menuId;                // 菜单ID
    private String projectName;         // 项目名称
    private String projectCode;         // 项目代码
    private String createTime;          // 创建时间
}
```

#### 5.1.4 站点条目实体 (SiteItem)
```java
public class SiteItem {
    private Long id;                    // ID
    private Long projectId;             // 项目ID
    private String projectName;         // 项目名称
    private String status;              // 状态
}
```

### 5.2 数据传输对象 (DTO)

#### 5.2.1 工作流DTO (WorkFlowDTO)
```java
public class WorkFlowDTO {
    private Object children;            // 子节点
    private List<WorkFlowDataDTO> data; // 数据列表
    private String dataId;              // 数据ID
    private Integer isCreated;          // 是否已创建
    private List<WorkFlowDTO> sub;      // 子工作流
    private String viewGroupId;         // 视图组ID
    private String viewId;              // 视图ID
}
```

#### 5.2.2 批量导入DTO (YPTTBatchImportDTO)
```java
public class YPTTBatchImportDTO {
    private String moduleType;          // 模块类型
    private MultipartFile file;         // 上传文件
    private String appId;               // 应用ID
    private Long viewGroupId;           // 视图组ID
    private Long viewId;                // 视图ID
    // ... 其他字段
}
```

#### 5.2.3 用户操作统计DTO (StatisticsUserOperateDTO)
```java
public class StatisticsUserOperateDTO {
    private String startDate;           // 开始日期
    private String endDate;             // 结束日期
    private String projectCode;         // 项目代码
    private Long userId;                // 用户ID
    private List<String> modules;       // 模块列表
}
```

### 5.3 视图对象 (VO)

#### 5.3.1 收支状态VO (IncomeAndExpenditureVO)
```java
public class IncomeAndExpenditureVO {
    private String projectName;         // 项目名称
    private BigDecimal income;          // 收入
    private BigDecimal expenditure;     // 支出
    private BigDecimal balance;         // 余额
    private String period;              // 期间
}
```

#### 5.3.2 站点统计VO (SiteItemStatisticsVO)
```java
public class SiteItemStatisticsVO {
    private String projectName;         // 项目名称
    private Long close;                 // 已关闭数量
    private Long unclose;               // 未关闭数量
    private Long invalid;               // 无效数量
    private Long total;                 // 总数量
}
```

#### 5.3.3 总金额VO (TotalAmountVO)
```java
public class TotalAmountVO {
    private AmountY2VO y2Amount;        // Y2金额(PO金额)
    private AmountY6VO y6Amount;        // Y6金额(产值决算金额)
    private AmountY9VO y9Amount;        // Y9金额(开票金额)
}
```

## 6. 配置管理

### 6.1 视图配置 (ViewConfProperties)

#### 6.1.1 基础配置
```yaml
view-conf:
  msg-link: "https://ms.ypttglobal.net/app/{appId}/menu/{menuId}/view/{viewId}/data/{dataId}/read"
  tenant-id: 1694550407300681729
  admin-id: 1544515583013392385
  app-id: "1694587608560046082"
  page-size: 500
  operation-size: 1000
```

#### 6.1.2 模块配置
```yaml
view-conf:
  site-item:
    table-name: "memm_e648652640b44b2092c93e1742e6171b"
    view-id: 1698891624814911489
    view-group-id: 1698891625108512770
    warn-view-id: 1712008310353346561
    warn-view-group-id: 1712008310458204162
    menu-id: 1712009850107510785

  po-item:
    table-name: "memm_f37920ed96f942fb8f4b1bf16f79e39c"
    view-id: 1694905080818876417
    view-group-id: 1694905080885985282
    warn-view-id: 1712007063973969922
    warn-view-group-id: 1712007064137547777
    menu-id: 1712010218023469058
```

### 6.2 任务配置 (YpttPersonalizedApiProperties)

#### 6.2.1 任务令牌配置
```yaml
yptt:
  task-token: "e2ff13fc7f0c4a20a618fe08877f9c8d"
```

#### 6.2.2 定时任务配置
```yaml
yptt:
  schedule:
    warning-info-update-task: "-"      # 警告信息更新任务
    standing-book-update-task: "-"     # 台账更新任务
    import-task: "-"                   # 导入任务检查
    update-site-state: "-"             # 站点状态更新
    income-correction-task: "-"        # 收入修正任务
    clear-warning-info: "-"            # 清理警告信息任务
```

### 6.3 线程池配置 (ThreadPoolExecutorProperties)

```yaml
thread-param:
  core-pool-size: 3                    # 核心线程数
  maximum-pool-size: 3                 # 最大线程数
  keep-alive-time: 60                  # 线程存活时间(秒)
  queue-capacity: 3                    # 队列容量
  thread-name-prefix: "yptt-thread-"   # 线程名前缀
```

### 6.4 应用配置

#### 6.4.1 服务配置
```yaml
server:
  port: 20001                          # 服务端口

spring:
  application:
    name: yptt-personalized-api        # 应用名称
  servlet:
    multipart:
      max-file-size: 200MB             # 最大文件上传大小
```

#### 6.4.2 Nacos配置
```yaml
spring:
  cloud:
    nacos:
      username: nacos                  # Nacos用户名
      password: nacos                  # Nacos密码
      discovery:
        server-addr: pig-register:8848 # 注册中心地址
      config:
        server-addr: pig-register:8848 # 配置中心地址
```

#### 6.4.3 MyBatis Plus配置
```yaml
mybatis-plus:
  configuration:
    call-setters-on-nulls: true        # 空值调用setter
```

#### 6.4.4 Feign配置
```yaml
feign:
  client:
    config:
      default:
        connect-timeout: 300000        # 连接超时时间
        read-timeout: 300000           # 读取超时时间
```

## 7. 常量定义

### 7.1 全局常量 (GlobalConstants)

#### 7.1.1 更新权限类型
```java
interface UpdatePerType {
    String QUERY = "query";            // 查询
    String INSERT = "insert";          // 新增
    String UPDATE = "update";          // 更新
    String DEL = "del";                // 删除
}
```

#### 7.1.2 BI面板常量
```java
interface BiPanel {
    String YEAR = "year";              // 年度
    String DAY = "day";                // 日度
    String MONTH = "month";            // 月度
    String B1 = "BI-1";                // BI面板1
    String B2 = "BI-2";                // BI面板2
}
```

#### 7.1.3 模块常量
```java
interface Y1 {
    String NAME = "y1";                // Y1模块名称
    List<String> FILED_LIST = Arrays.asList(
        "Department", "YPTT_Project_code", "YPTT_Project_name",
        "Region", "Area", "Site_ID", "Site_Name", "site_allocation_date",
        "Phase", "Type_of_service", "Site_Model", "Item_code",
        "BOQ_item", "quantity", "Unit_price", "Site_value",
        "Remark", "re_record", "uniqueness_field"
    );
}

interface Y2 {
    String NAME = "y2";                // Y2模块名称
    List<String> FILED_LIST = Arrays.asList(
        "YPTT_Project_code", "Region", "Site_ID", "Phase",
        "Item_code", "uniqueness_field", "BOQ_item", "Quantity",
        "Unit_price", "PO_value", "PO_number", "PO_Received_date",
        "Pre_payment", "PO_gap", "Milestone_1st", "Milestone_2nd",
        "Milestone_3rd", "Milestone_4th", "Remark", "re_record"
    );
}
```

### 7.2 视图配置枚举
```java
@Getter
@RequiredArgsConstructor
enum ViewConfEnum {
    Y1_ITEM(GlobalConstants.Y1.NAME, GlobalConstants.Y1.FILED_LIST),
    Y2_ITEM(GlobalConstants.Y2.NAME, GlobalConstants.Y2.FILED_LIST),
    Y3_ITEM(GlobalConstants.Y3.NAME, GlobalConstants.Y3.FILED_LIST),
    Y4_ITEM(GlobalConstants.Y4.NAME, GlobalConstants.Y4.FILED_LIST),
    Y8_ITEM(GlobalConstants.Y8.NAME, GlobalConstants.Y8.FILED_LIST),
    Y9_ITEM(GlobalConstants.Y9.NAME, GlobalConstants.Y9.FILED_LIST);

    private final String module;       // 模块名称
    private final List<String> filed;  // 字段列表
}

## 8. 定时任务

### 8.1 任务调度器 (ScheduleRegistrar)

#### 8.1.1 警告信息更新任务
​```java
@Scheduled(cron = "${yptt.schedule.warningInfoUpdateTask:-}")
public void warningInfoUpdateTask() {
    // 更新警告信息状态
    // 检查站点条目、PO、分包商支付等警告状态
}
```

#### 8.1.2 台账更新任务
```java
@Scheduled(cron = "${yptt.schedule.standingBookUpdateTask:-}")
public void standingBookUpdateTask() {
    // 更新项目台账数据
    // 包括项目台账、站点台账、PO台账等
}
```

#### 8.1.3 导入任务检查
```java
@Scheduled(cron = "${yptt.schedule.importTask:-}")
public void importTask() {
    // 检查导入任务状态
    // 清理超时的导入任务
}
```

#### 8.1.4 收入修正任务
```java
@Scheduled(cron = "${yptt.schedule.incomeCorrectionTask:-}")
public void incomeCorrectionTask() {
    // 执行收入数据修正
    // 修正Y6模块的收入数据
}
```

#### 8.1.5 站点状态更新任务
```java
@Scheduled(cron = "${yptt.schedule.updateSiteState:-}")
public void updateSiteState() {
    // 更新站点状态
    // 根据业务规则自动更新站点状态
}
```

#### 8.1.6 警告信息清理任务
```java
@Scheduled(cron = "${yptt.schedule.clearWarningInfo:-}")
public void clearWarningInfo() {
    // 清理过期的警告信息
    // 删除超过保留期限的警告数据
}
```

### 8.2 导入任务 (ImportTask)

#### 8.2.1 任务执行流程
1. **任务初始化**: 设置安全上下文和用户信息
2. **数据获取**: 从数据库获取待导入的数据
3. **状态更新**: 更新主数据状态为"进行中"
4. **分批处理**: 按批次大小分批处理数据
5. **数据转换**: 调用对应的Transformer进行数据转换
6. **保存数据**: 保存转换后的数据到目标表
7. **更新状态**: 更新子数据和主数据状态
8. **发送通知**: 发送任务完成通知

#### 8.2.2 错误处理
- 捕获并记录导入过程中的异常
- 保存错误原因到导入历史记录
- 更新导入状态为失败
- 发送错误通知给用户

## 9. 工具类

### 9.1 Excel工具类 (ExcelUtil)

#### 9.1.1 Excel读取
```java
public static List<Map<String, Object>> readExcelToMap(MultipartFile file)
// 功能: 将Excel文件解析为Map列表
// 参数: MultipartFile file - 上传的Excel文件
// 返回: List<Map<String, Object>> - 解析后的数据列表
```

#### 9.1.2 Excel导出
```java
public static void exportNoModel(HttpServletResponse response, String sheetName,
                                List<List<Object>> list, List<String> headMap)
// 功能: 导出数据到Excel文件(无模型)
// 参数: response - HTTP响应, sheetName - 工作表名称, list - 数据列表, headMap - 表头映射

public static void exportTemplateList(HttpServletResponse response, String sheetName,
                                     ClassPathResource resource, List<?> moduleList)
// 功能: 使用模板导出Excel文件
// 参数: response - HTTP响应, sheetName - 工作表名称, resource - 模板资源, moduleList - 数据列表
```

### 9.2 元数据工具类 (MetaDataUtil)

#### 9.2.1 数据ID处理
```java
public static String handleDataId2Json(Long id)
// 功能: 将Long型数据ID转换为JSON字符串
// 参数: Long id - 数据ID
// 返回: String - JSON格式的数据ID

public static Long handleDataIdJson2Long(String jsonStr)
// 功能: 将JSON格式的数据ID转换为Long型
// 参数: String jsonStr - JSON字符串
// 返回: Long - 数据ID
```

#### 9.2.2 数据类型转换
```java
public static BigDecimal percentageStr2BigDecimal(String percentageStr, int scale, boolean blank2Zero)
// 功能: 将百分比字符串转换为BigDecimal
// 参数: percentageStr - 百分比字符串, scale - 精度, blank2Zero - 空值是否转为0

public static LocalDateTime dateStr2LocalDateTime(String dateStr)
// 功能: 将日期字符串转换为LocalDateTime
// 参数: String dateStr - 日期字符串
// 返回: LocalDateTime - 日期时间对象
```

### 9.3 PR工具类 (PRUtil)

#### 9.3.1 PR号码生成
```java
public String createPrNo(String projectCode, String userName)
// 功能: 创建PR号码
// 参数: projectCode - 项目代码, userName - 用户名
// 返回: String - 生成的PR号码
// 格式: yyyyMMddHHmmss + projectCode + 随机码
```

### 9.4 锁定时间工具类 (LockTimeV2Util)

#### 9.4.1 Redis键管理
```java
public String getRedisKey(String projectCode)
// 功能: 获取Redis键
// 参数: String projectCode - 项目代码
// 返回: String - Redis键名
```

## 10. 安全与权限

### 10.1 权限控制

#### 10.1.1 接口权限
- 使用`@Inner(value = false)`注解标识内部接口
- 通过`yptt-task-token`参数验证任务权限
- 集成Spring Security进行用户认证

#### 10.1.2 数据权限
- 基于用户角色控制数据访问范围
- 项目级别的数据隔离
- 部门级别的数据权限控制

#### 10.1.3 BI面板权限
```java
public Boolean authorityCheck(String type, Long projectId)
// 功能: BI面板权限验证
// 参数: type - 面板类型, projectId - 项目ID
// 返回: Boolean - 是否有权限
// 逻辑: 系统管理员拥有所有权限，普通用户根据项目角色判断
```

### 10.2 安全配置

#### 10.2.1 XSS防护
- 集成`pig-common-xss`模块
- 自动过滤用户输入的恶意脚本

#### 10.2.2 SQL注入防护
- 使用MyBatis Plus的参数化查询
- 避免动态SQL拼接

#### 10.2.3 文件上传安全
- 限制文件大小(最大200MB)
- 验证文件类型和格式
- 文件内容安全检查

## 11. 性能优化

### 11.1 异步处理

#### 11.1.1 导入任务异步化
- 使用自定义线程池处理导入任务
- 避免长时间阻塞用户请求
- 提供任务进度查询接口

#### 11.1.2 数据连接器异步化
- Y1、Y2、Y4数据连接器使用异步处理
- 使用`CompletableFuture`提高并发性能

### 11.2 数据库优化

#### 11.2.1 分页查询
- 统一使用MyBatis Plus的分页插件
- 避免一次性加载大量数据

#### 11.2.2 批量操作
- 导入数据使用批量插入
- 更新操作使用批量更新
- 减少数据库交互次数

#### 11.2.3 索引优化
- 在关键查询字段上建立索引
- 优化复杂查询的执行计划

### 11.3 缓存策略

#### 11.3.1 Redis缓存
- 缓存频繁查询的配置数据
- 缓存用户权限信息
- 缓存BI面板统计结果

#### 11.3.2 本地缓存
- 缓存视图配置信息
- 缓存常量数据

## 12. 监控与日志

### 12.1 日志管理

#### 12.1.1 日志级别
```yaml
logging:
  level:
    root: info                         # 根日志级别
    com.pig4cloud.pig.yptt: debug      # 业务日志级别
```

#### 12.1.2 操作日志
- 使用`@OperationLogger`注解记录关键操作
- 记录用户操作轨迹
- 便于问题排查和审计

### 12.1.3 错误日志
- 统一异常处理和日志记录
- 记录详细的错误堆栈信息
- 便于快速定位问题

### 12.2 性能监控

#### 12.2.1 线程池监控
- 自定义`RuntimeVisibleThreadPoolExecutor`
- 监控线程池运行状态
- 记录任务执行情况

#### 12.2.2 接口性能监控
- 记录接口响应时间
- 监控慢查询和异常接口
- 提供性能优化依据

## 13. 部署与运维

### 13.1 容器化部署

#### 13.1.1 Docker配置
```dockerfile
# Dockerfile示例
FROM openjdk:8-jre-alpine
COPY target/yptt-personalized-api.jar app.jar
EXPOSE 20001
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### 13.1.2 Kubernetes部署
- 使用`kubernetes.tpl.yaml`模板
- 支持水平扩展
- 配置健康检查

### 13.2 配置管理

#### 13.2.1 环境配置
- 开发环境、测试环境、生产环境配置分离
- 使用Nacos配置中心统一管理
- 支持配置热更新

#### 13.2.2 数据库配置
- 主从数据库配置
- 连接池参数优化
- 事务管理配置

### 13.3 运维监控

#### 13.3.1 健康检查
- Spring Boot Actuator健康检查
- 数据库连接状态检查
- 外部服务依赖检查

#### 13.3.2 指标监控
- JVM内存使用情况
- 线程池状态监控
- 接口调用统计

## 14. 总结

YPTT个性化API系统是一个功能完善的海外项目管理系统，主要特点包括：

### 14.1 技术特点
- **微服务架构**: 基于Spring Cloud构建，支持分布式部署
- **异步处理**: 大量使用异步处理提高系统性能
- **数据导入导出**: 完善的Excel数据处理能力
- **工作流集成**: 支持复杂的业务审批流程
- **权限控制**: 细粒度的数据权限管理

### 14.2 业务特点
- **多模块管理**: 支持Y1-Y9等多个业务模块
- **BI数据分析**: 提供丰富的数据统计和分析功能
- **警告预警**: 智能的业务预警机制
- **台账管理**: 自动化的台账数据维护
- **财务管理**: 完整的付款申请和审批流程

### 14.3 运维特点
- **容器化部署**: 支持Docker和Kubernetes部署
- **配置中心**: 使用Nacos进行配置管理
- **监控告警**: 完善的监控和日志体系
- **性能优化**: 多层次的性能优化策略

该系统为海外项目管理提供了完整的数字化解决方案，能够有效提高项目管理效率和数据准确性。
```

```